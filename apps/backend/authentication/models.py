from django.contrib.auth.models import AbstractUser
from django.db import models
from simple_history.models import HistoricalRecords


class User(AbstractUser):
    """Custom User model for MTBRMG ERP system"""

    class Role(models.TextChoices):
        ADMIN = 'admin', 'مدير النظام'
        SALES_MANAGER = 'sales_manager', 'مدير المبيعات'
        MEDIA_BUYER = 'media_buyer', 'مشتري الإعلانات'
        DEVELOPER = 'developer', 'مطور'
        DESIGNER = 'designer', 'مصمم'
        WORDPRESS_DEVELOPER = 'wordpress_developer', 'مطور ووردبريس'

    class Status(models.TextChoices):
        ACTIVE = 'active', 'نشط'
        INACTIVE = 'inactive', 'غير نشط'
        SUSPENDED = 'suspended', 'موقوف'

    # Additional fields
    role = models.CharField(
        max_length=20,
        choices=Role.choices,
        default=Role.DEVELOPER,
        verbose_name='الدور'
    )
    status = models.CharField(
        max_length=10,
        choices=Status.choices,
        default=Status.ACTIVE,
        verbose_name='الحالة'
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        verbose_name='الصورة الشخصية'
    )
    bio = models.TextField(
        blank=True,
        null=True,
        verbose_name='نبذة شخصية'
    )

    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_role_display()})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.username

    def has_role(self, role):
        """Check if user has specific role"""
        return self.role == role

    def is_admin(self):
        """Check if user is admin"""
        return self.role == self.Role.ADMIN

    def is_sales_manager(self):
        """Check if user is sales manager"""
        return self.role == self.Role.SALES_MANAGER

    def can_manage_clients(self):
        """Check if user can manage clients"""
        return self.role in [self.Role.ADMIN, self.Role.SALES_MANAGER]

    def can_manage_projects(self):
        """Check if user can manage projects"""
        return self.role in [
            self.Role.ADMIN,
            self.Role.SALES_MANAGER,
            self.Role.DEVELOPER,
            self.Role.DESIGNER,
            self.Role.WORDPRESS_DEVELOPER
        ]
