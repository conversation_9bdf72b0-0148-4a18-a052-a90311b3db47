from django.db import models
from django.conf import settings
from simple_history.models import HistoricalRecords
from djmoney.models.fields import MoneyField


class Client(models.Model):
    """Client model for managing agency clients"""

    class Mood(models.TextChoices):
        HAPPY = 'happy', 'سعيد 😊'
        NEUTRAL = 'neutral', 'محايد 😐'
        CONCERNED = 'concerned', 'قلق 😟'
        ANGRY = 'angry', 'غاضب 😠'

    class Governorate(models.TextChoices):
        CAIRO = 'cairo', 'القاهرة'
        ALEXANDRIA = 'alexandria', 'الإسكندرية'
        GIZA = 'giza', 'الجيزة'
        QALYUBIA = 'qalyubia', 'القليوبية'
        PORT_SAID = 'port_said', 'بورسعيد'
        SUEZ = 'suez', 'السويس'
        LUXOR = 'luxor', 'الأقصر'
        ASWAN = 'aswan', 'أسوان'
        ASYUT = 'asyut', 'أسيوط'
        BEHEIRA = 'beheira', 'البحيرة'
        BENI_SUEF = 'beni_suef', 'بني سويف'
        DAKAHLIA = 'dakahlia', 'الدقهلية'
        DAMIETTA = 'damietta', 'دمياط'
        FAYYUM = 'fayyum', 'الفيوم'
        GHARBIA = 'gharbia', 'الغربية'
        ISMAILIA = 'ismailia', 'الإسماعيلية'
        KAFR_EL_SHEIKH = 'kafr_el_sheikh', 'كفر الشيخ'
        MATROUH = 'matrouh', 'مطروح'
        MINYA = 'minya', 'المنيا'
        MONUFIA = 'monufia', 'المنوفية'
        NEW_VALLEY = 'new_valley', 'الوادي الجديد'
        NORTH_SINAI = 'north_sinai', 'شمال سيناء'
        QENA = 'qena', 'قنا'
        RED_SEA = 'red_sea', 'البحر الأحمر'
        SHARQIA = 'sharqia', 'الشرقية'
        SOHAG = 'sohag', 'سوهاج'
        SOUTH_SINAI = 'south_sinai', 'جنوب سيناء'

    # Basic Information
    name = models.CharField(max_length=200, verbose_name='اسم العميل')
    email = models.EmailField(verbose_name='البريد الإلكتروني')
    phone = models.CharField(max_length=20, verbose_name='رقم الهاتف')
    company = models.CharField(max_length=200, blank=True, null=True, verbose_name='الشركة')
    website = models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')

    # Address Information
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    governorate = models.CharField(
        max_length=20,
        choices=Governorate.choices,
        blank=True,
        null=True,
        verbose_name='المحافظة'
    )

    # Relationship Management
    mood = models.CharField(
        max_length=10,
        choices=Mood.choices,
        default=Mood.NEUTRAL,
        verbose_name='مزاج العميل'
    )
    sales_rep = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='clients',
        verbose_name='مندوب المبيعات'
    )
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')

    # Business Metrics
    total_projects = models.PositiveIntegerField(default=0, verbose_name='إجمالي المشاريع')
    total_revenue = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        default=0,
        verbose_name='إجمالي الإيرادات'
    )

    # Tracking
    last_contact_date = models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر تواصل')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'عميل'
        verbose_name_plural = 'العملاء'
        ordering = ['-created_at']
        unique_together = ['email']

    def __str__(self):
        return f"{self.name} - {self.company or 'فردي'}"

    @property
    def mood_emoji(self):
        """Get emoji for client mood"""
        mood_emojis = {
            self.Mood.HAPPY: '😊',
            self.Mood.NEUTRAL: '😐',
            self.Mood.CONCERNED: '😟',
            self.Mood.ANGRY: '😠',
        }
        return mood_emojis.get(self.mood, '😐')

    def update_metrics(self):
        """Update client business metrics"""
        from projects.models import Project
        projects = Project.objects.filter(client=self)
        self.total_projects = projects.count()
        self.total_revenue = sum(p.budget or 0 for p in projects)
        self.save(update_fields=['total_projects', 'total_revenue'])


class ClientCommunication(models.Model):
    """Track all communications with clients"""

    class Type(models.TextChoices):
        CALL = 'call', 'مكالمة هاتفية'
        EMAIL = 'email', 'بريد إلكتروني'
        WHATSAPP = 'whatsapp', 'واتساب'
        MEETING = 'meeting', 'اجتماع'
        OTHER = 'other', 'أخرى'

    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='communications',
        verbose_name='العميل'
    )
    type = models.CharField(
        max_length=10,
        choices=Type.choices,
        verbose_name='نوع التواصل'
    )
    subject = models.CharField(max_length=200, verbose_name='الموضوع')
    content = models.TextField(verbose_name='المحتوى')
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='المستخدم'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')

    class Meta:
        verbose_name = 'تواصل مع العميل'
        verbose_name_plural = 'التواصل مع العملاء'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.client.name} - {self.get_type_display()} - {self.subject}"
