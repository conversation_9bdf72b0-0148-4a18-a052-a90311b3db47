from django.db import models
from django.conf import settings
from simple_history.models import HistoricalRecords
from djmoney.models.fields import MoneyField
from django.core.validators import MinValueValidator, MaxValueValidator


class Project(models.Model):
    """Project model for managing client projects"""

    class Status(models.TextChoices):
        PLANNING = 'planning', 'التخطيط'
        DEVELOPMENT = 'development', 'التطوير'
        TESTING = 'testing', 'الاختبار'
        DEPLOYMENT = 'deployment', 'النشر'
        MAINTENANCE = 'maintenance', 'الصيانة'
        COMPLETED = 'completed', 'مكتمل'
        CANCELLED = 'cancelled', 'ملغي'

    class Priority(models.TextChoices):
        LOW = 'low', 'منخفض'
        MEDIUM = 'medium', 'متوسط'
        HIGH = 'high', 'عالي'
        URGENT = 'urgent', 'عاجل'

    class Type(models.TextChoices):
        WEBSITE = 'website', 'موقع إلكتروني'
        MOBILE_APP = 'mobile_app', 'تطبيق جوال'
        WEB_APP = 'web_app', 'تطبيق ويب'
        ECOMMERCE = 'ecommerce', 'متجر إلكتروني'
        WORDPRESS = 'wordpress', 'ووردبريس'
        MAINTENANCE = 'maintenance', 'صيانة'
        MARKETING = 'marketing', 'تسويق'

    # Basic Information
    name = models.CharField(max_length=200, verbose_name='اسم المشروع')
    description = models.TextField(blank=True, null=True, verbose_name='الوصف')
    type = models.CharField(
        max_length=20,
        choices=Type.choices,
        verbose_name='نوع المشروع'
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PLANNING,
        verbose_name='حالة المشروع'
    )
    priority = models.CharField(
        max_length=10,
        choices=Priority.choices,
        default=Priority.MEDIUM,
        verbose_name='الأولوية'
    )

    # Relationships
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        related_name='projects',
        verbose_name='العميل'
    )
    assigned_team = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='assigned_projects',
        blank=True,
        verbose_name='الفريق المكلف'
    )
    project_manager = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_projects',
        verbose_name='مدير المشروع'
    )

    # Timeline
    start_date = models.DateField(verbose_name='تاريخ البداية')
    end_date = models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')
    deadline = models.DateField(blank=True, null=True, verbose_name='الموعد النهائي')

    # Financial
    budget = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        blank=True,
        null=True,
        verbose_name='الميزانية'
    )
    actual_cost = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='EGP',
        blank=True,
        null=True,
        verbose_name='التكلفة الفعلية'
    )

    # Progress
    progress = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='نسبة الإنجاز'
    )

    # Technical Details
    domains = models.JSONField(
        default=list,
        blank=True,
        verbose_name='النطاقات'
    )
    repository_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط المستودع'
    )
    staging_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط الاختبار'
    )
    production_url = models.URLField(
        blank=True,
        null=True,
        verbose_name='رابط الإنتاج'
    )

    # Tracking
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    # History tracking
    history = HistoricalRecords()

    class Meta:
        verbose_name = 'مشروع'
        verbose_name_plural = 'المشاريع'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.client.name}"

    @property
    def is_overdue(self):
        """Check if project is overdue"""
        if self.deadline and self.status not in [self.Status.COMPLETED, self.Status.CANCELLED]:
            from django.utils import timezone
            return timezone.now().date() > self.deadline
        return False

    @property
    def days_remaining(self):
        """Get days remaining until deadline"""
        if self.deadline:
            from django.utils import timezone
            delta = self.deadline - timezone.now().date()
            return delta.days
        return None

    def update_progress(self):
        """Update project progress based on completed tasks"""
        from tasks.models import Task
        tasks = Task.objects.filter(project=self)
        if tasks.exists():
            completed_tasks = tasks.filter(status=Task.Status.COMPLETED).count()
            self.progress = int((completed_tasks / tasks.count()) * 100)
            self.save(update_fields=['progress'])


class ProjectCredentials(models.Model):
    """Store encrypted server credentials for projects"""

    project = models.OneToOneField(
        Project,
        on_delete=models.CASCADE,
        related_name='credentials',
        verbose_name='المشروع'
    )
    host = models.CharField(max_length=200, verbose_name='الخادم')
    username = models.CharField(max_length=100, verbose_name='اسم المستخدم')
    password = models.CharField(max_length=200, verbose_name='كلمة المرور')  # Should be encrypted
    port = models.PositiveIntegerField(default=22, verbose_name='المنفذ')
    database_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='اسم قاعدة البيانات')
    database_user = models.CharField(max_length=100, blank=True, null=True, verbose_name='مستخدم قاعدة البيانات')
    database_password = models.CharField(max_length=200, blank=True, null=True, verbose_name='كلمة مرور قاعدة البيانات')

    # Additional credentials
    ftp_host = models.CharField(max_length=200, blank=True, null=True, verbose_name='خادم FTP')
    ftp_username = models.CharField(max_length=100, blank=True, null=True, verbose_name='مستخدم FTP')
    ftp_password = models.CharField(max_length=200, blank=True, null=True, verbose_name='كلمة مرور FTP')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'بيانات اعتماد المشروع'
        verbose_name_plural = 'بيانات اعتماد المشاريع'

    def __str__(self):
        return f"بيانات اعتماد {self.project.name}"
