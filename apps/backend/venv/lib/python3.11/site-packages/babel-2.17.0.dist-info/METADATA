Metadata-Version: 2.2
Name: babel
Version: 2.17.0
Summary: Internationalization utilities
Home-page: https://babel.pocoo.org/
Author: <PERSON><PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON><PERSON><PERSON>
Maintainer-email: <EMAIL>
License: BSD-3-Clause
Project-URL: Source, https://github.com/python-babel/babel
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
License-File: LICENSE
Requires-Dist: pytz>=2015.7; python_version < "3.9"
Provides-Extra: dev
Requires-Dist: tzdata; sys_platform == "win32" and extra == "dev"
Requires-Dist: backports.zoneinfo; python_version < "3.9" and extra == "dev"
Requires-Dist: freezegun~=1.0; extra == "dev"
Requires-Dist: jinja2>=3.0; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: pytest>=6.0; extra == "dev"
Requires-Dist: pytz; extra == "dev"
Requires-Dist: setuptools; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: maintainer
Dynamic: maintainer-email
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

A collection of tools for internationalizing Python applications.
