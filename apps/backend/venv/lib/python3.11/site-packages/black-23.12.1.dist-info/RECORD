../../../bin/black,sha256=nY-C32hVuYFNrHtpUqOAFAEEcFLexuOOyTm6A7edLF8,287
../../../bin/blackd,sha256=czb9mQAs582TSv1eNfbJCzPyCaJJY7kDKkD2FzP-zyE,288
629853fdff261ed89b74__mypyc.cpython-311-darwin.so,sha256=R9Rd4Echs-8xioID3FfX0jOCXNBU9-ZoaMFwB-CIbuQ,3691630
__pycache__/_black_version.cpython-311.pyc,,
_black_version.py,sha256=d4NlDZh2CYyujROCjZ2C5HCEywF6U4dWZKsVWOSg9FI,20
black-23.12.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
black-23.12.1.dist-info/METADATA,sha256=1Ww6b43o8GWWNB_DD2K8TrV0NKngwCZ8DLgd1ORkWFs,68986
black-23.12.1.dist-info/RECORD,,
black-23.12.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black-23.12.1.dist-info/WHEEL,sha256=AfSw4J-V3vxqlRUPqJfHYSAb6B6I4RLfjAia4eKvnLs,105
black-23.12.1.dist-info/entry_points.txt,sha256=qBIyywHwGRkJj7kieq86kqf77rz3qGC4Joj36lHnxwc,78
black-23.12.1.dist-info/licenses/AUTHORS.md,sha256=8drxTtCp41j9z9NFJ9U37R1m9qL0zwTMELvgHFFkwao,8092
black-23.12.1.dist-info/licenses/LICENSE,sha256=nAQo8MO0d5hQz1vZbhGqqK_HLUqG1KNiI9erouWNbgA,1080
black/__init__.cpython-311-darwin.so,sha256=y9RJAT3axv26Wry0kpJBHSQU_qJ_mEfpug18m_Qn2zQ,50139
black/__init__.py,sha256=yB5SaPC3t9R7bATnnD2sdyPIks34Roakk2EiJ-00taM,50074
black/__main__.py,sha256=mogeA4o9zt4w-ufKvaQjSEhtSgQkcMVLK9ChvdB5wH8,47
black/__pycache__/__init__.cpython-311.pyc,,
black/__pycache__/__main__.cpython-311.pyc,,
black/__pycache__/_width_table.cpython-311.pyc,,
black/__pycache__/brackets.cpython-311.pyc,,
black/__pycache__/cache.cpython-311.pyc,,
black/__pycache__/comments.cpython-311.pyc,,
black/__pycache__/concurrency.cpython-311.pyc,,
black/__pycache__/const.cpython-311.pyc,,
black/__pycache__/debug.cpython-311.pyc,,
black/__pycache__/files.cpython-311.pyc,,
black/__pycache__/handle_ipynb_magics.cpython-311.pyc,,
black/__pycache__/linegen.cpython-311.pyc,,
black/__pycache__/lines.cpython-311.pyc,,
black/__pycache__/mode.cpython-311.pyc,,
black/__pycache__/nodes.cpython-311.pyc,,
black/__pycache__/numerics.cpython-311.pyc,,
black/__pycache__/output.cpython-311.pyc,,
black/__pycache__/parsing.cpython-311.pyc,,
black/__pycache__/ranges.cpython-311.pyc,,
black/__pycache__/report.cpython-311.pyc,,
black/__pycache__/rusty.cpython-311.pyc,,
black/__pycache__/strings.cpython-311.pyc,,
black/__pycache__/trans.cpython-311.pyc,,
black/_width_table.cpython-311-darwin.so,sha256=tomHhZOpNITR_AF-0ugyQIG_z47-zcw4t6LYlDMwgUM,50143
black/_width_table.py,sha256=2lSnE4s_nVXXfIj9hP2qWASqX8I003WxBM5xPnelDrQ,10761
black/brackets.cpython-311-darwin.so,sha256=MKyOoxaO0p27PNzgQcvApsojd8nSw4lfm4w3xoYktjQ,50139
black/brackets.py,sha256=pIavHXe4tm7mLWMhfqXRiLI52-8XI9b3szyBF3kR0Mk,12474
black/cache.cpython-311-darwin.so,sha256=nlzx-esqWpxkdO9OpwsN9Cmbgz2GMi2hKldxMlZqPYQ,50136
black/cache.py,sha256=p2oJ1pPUrlGhYZtGovdT9UWbkF5f18EyXjU1Ef1Nbks,4579
black/comments.cpython-311-darwin.so,sha256=KbesPjqx4F3O1WL2Nh0rF_Gdj4DAGp4G0SehpJ0ZPLk,50139
black/comments.py,sha256=O4mcLYzeM-lNccdifrxVbCP3hdRRQVqlWMzEkXzbd_0,15816
black/concurrency.py,sha256=oyFRSg5wisTi2dNLKig6Cm7R2uHWVwkfLbI6pCZRNCo,6410
black/const.cpython-311-darwin.so,sha256=8ttjenJodlAeKksrR70uxAZSGRbCd7LgQLdJat8r6lQ,50136
black/const.py,sha256=U7cDnhWljmrieOtPBUdO2Vcz69J_VXB6-Br94wuCVuo,321
black/debug.py,sha256=HmpJna5KhwLpgQkqEGR2FX6GRmJ2tFh4Jl6X2aMR3ak,1906
black/files.py,sha256=m_FTnA6cTrQGIGz8YGrWctxTacy4GdmX4ySvma3MkCs,13853
black/handle_ipynb_magics.cpython-311-darwin.so,sha256=_mPhpHrAO25RycPD4gQQaIOIr_TyqzPt6UN8bjrOcQ4,50166
black/handle_ipynb_magics.py,sha256=eAULCQCmQRbgluSRu5jFQNtILMpXnjZWNoOVQLiaEJ8,13364
black/linegen.cpython-311-darwin.so,sha256=1awX7zflcTxxlngxCZFTCxrJNjz76_f2gdTyX3NgFgE,50138
black/linegen.py,sha256=Ku5qtkwCdmc2EaeDHusMzhLorYM3CcAdJl0gBTaRKPM,67673
black/lines.cpython-311-darwin.so,sha256=FyJCMVIC_0p0KwLldOvcbyLdETU_XfYKmNeDJT1QM8Q,50136
black/lines.py,sha256=AGM0Plc5O4rnSbCEnmBMlYlJKMPVkIjlS_fSVpCGm8A,40778
black/mode.cpython-311-darwin.so,sha256=RgoYoKUYGPpVxQfGMYpLt-D_Q5EqseJqHCheUOF8qbs,50119
black/mode.py,sha256=StOUYD4kmtk6UK6Sa3iIFh_LTsm1wnuRFA9uCPC4mnY,8408
black/nodes.cpython-311-darwin.so,sha256=OtKdkinY9k29nzK8ueLYTasyMDgwYz3C9uQUBwQJV1s,50136
black/nodes.py,sha256=mpocQXHW5N_4jG2fj7Oq_R8_tR3h90zYffNXnErZhko,28273
black/numerics.cpython-311-darwin.so,sha256=yXF12nLXL4KQir7B1LUaGct4D6aYnxYwK7vnFAqcMpQ,50139
black/numerics.py,sha256=xRGnTSdMVbTaA9IGechc8JM-cIuJGCc326s71hkXJIw,1655
black/output.py,sha256=hhk6746lZBMbGuJ1VLgjl_Jrn7Yvb24ZBFaCfpO_ijM,3939
black/parsing.cpython-311-darwin.so,sha256=1f8JfIa7wqHMwZRSHgKqIQY0Aipuj0l7aV12Ppgftxg,50138
black/parsing.py,sha256=yu6lioYFJNrmhaP2FCYJZnxo8vbVq1JvOKjjSt7keC0,7336
black/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
black/ranges.cpython-311-darwin.so,sha256=ITTtHODpuOYy0ADB6splxxcMBiieUMmbGP_FdB7f2GA,50137
black/ranges.py,sha256=DbBqPEyqR1m3AEUn0rd4cS_kHHr13gx5mucn_5RpMLM,18874
black/report.py,sha256=igkNi8iR5FqSa1sXddS-HnoqW7Cq7PveCmFCfd-pN6w,3452
black/rusty.cpython-311-darwin.so,sha256=90WlcDdNYcSGl3n3M3fXnT6lPulgXXKOWINDM1O_Kx4,50136
black/rusty.py,sha256=4LKo3KTUWYZ2cN6QKmwwZVbsCNt2fpu5lzue2V-uxIA,557
black/strings.cpython-311-darwin.so,sha256=oE0Jx3-AFyV66orYr3vn_D5JslKNQGk61Djk92ftIow,50138
black/strings.py,sha256=Ra4sHKZWmNGLA7QIYH4e6EApGn40Bt-7ipbwsbMoGz8,11098
black/trans.cpython-311-darwin.so,sha256=cEB_CoB1jY2YrPya_cQciTrM9qdqyGoEW_rIVm1IMWo,50136
black/trans.py,sha256=Ul5pjpjEGEgoAzrmartJIaSSSJhzfxNxNQOGUQqPySs,92609
blackd/__init__.py,sha256=i_Hf0Q6R8TpsZ_5MVA36D9CXW8ecFTNNM-Hn46179pE,8148
blackd/__main__.py,sha256=L4xAcDh1K5zb6SsJB102AewW2G13P9-w2RiEwuFj8WA,37
blackd/__pycache__/__init__.cpython-311.pyc,,
blackd/__pycache__/__main__.cpython-311.pyc,,
blackd/__pycache__/middlewares.cpython-311.pyc,,
blackd/middlewares.py,sha256=QS7cs86Ojuaqh64dGneimhJ-f30rDI646c27ts4Dwh0,1585
blib2to3/Grammar.txt,sha256=qUL_B_u7lqX-83Bas6u8Ckw8q4ea-cQS3Jlv3_i0hrI,11351
blib2to3/LICENSE,sha256=V4mIG4rrnJH1g19bt8q-hKD-zUuyvi9UyeaVenjseZ0,12762
blib2to3/PatternGrammar.txt,sha256=7lul2ztnIqDi--JWDrwciD5yMo75w7TaHHxdHMZJvOM,793
blib2to3/README,sha256=QYZYIfb1NXTTYqDV4kn8oRcNG_qlTFYH1sr3V1h65ko,1074
blib2to3/__init__.py,sha256=9_8wL9Scv8_Cs8HJyJHGvx1vwXErsuvlsAqNZLcJQR0,8
blib2to3/__pycache__/__init__.cpython-311.pyc,,
blib2to3/__pycache__/pygram.cpython-311.pyc,,
blib2to3/__pycache__/pytree.cpython-311.pyc,,
blib2to3/pgen2/__init__.py,sha256=hY6w9QUzvTvRb-MoFfd_q_7ZLt6IUHC2yxWCfsZupQA,143
blib2to3/pgen2/__pycache__/__init__.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/conv.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/driver.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/grammar.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/literals.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/parse.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/pgen.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/token.cpython-311.pyc,,
blib2to3/pgen2/__pycache__/tokenize.cpython-311.pyc,,
blib2to3/pgen2/conv.cpython-311-darwin.so,sha256=3XJvQjpmWLVoI0BNmU9ZMxGzt_3ygJkSrDxMTj56NcY,50119
blib2to3/pgen2/conv.py,sha256=vH8a_gkalWRNxuNPRxkoigw8_UobdHHSw-PyUcUuH8I,9587
blib2to3/pgen2/driver.cpython-311-darwin.so,sha256=IO8rY0xCp2_g2V9sNsEsiSZ4CIeiYN53awOZtLL5LUM,50137
blib2to3/pgen2/driver.py,sha256=HloDYPfu8iVKAvZi-8aknr5T6xJgimBUNv9AbS4qFu8,10631
blib2to3/pgen2/grammar.cpython-311-darwin.so,sha256=yA7MOE5sSGSoSgK4VrrPzAohpI3QVo8250unAY5AJcI,50138
blib2to3/pgen2/grammar.py,sha256=WQBX_vZFq8RNVNPX49J8oNdwXeWhXhizUu5vPwD0ZVM,6858
blib2to3/pgen2/literals.cpython-311-darwin.so,sha256=4jYfKvK2bl_OAVa9UhbJ2uXZlO2jb1NGXSMQ3Ntpvz4,50139
blib2to3/pgen2/literals.py,sha256=_LyRryELzqarFkW3OAEZzZ-yppCTm9g0mjqqQ2XygKE,1614
blib2to3/pgen2/parse.cpython-311-darwin.so,sha256=-yfPh4uqdV6YohsdnG73kJ0WZtULde5sNhi-n_F_uqw,50136
blib2to3/pgen2/parse.py,sha256=ufCEykU-ujdLEei9o1z5fl5ohkGpid4FVRegwe0WhMA,15657
blib2to3/pgen2/pgen.cpython-311-darwin.so,sha256=vLYNX7l3BDy29c34Kfoo1obPdRKuS95-RDGbWdQ2wdw,50119
blib2to3/pgen2/pgen.py,sha256=iQH8W999TKUT5AhuOpW38ZynwSACkVNV-I6z8kyQozY,15428
blib2to3/pgen2/token.cpython-311-darwin.so,sha256=1J4D65j_4VqtUqaMiGAXCO9GfoUUZp3eQJ7d4MR0Ids,50136
blib2to3/pgen2/token.py,sha256=iT30kH8_qqhvxuzyUpiIiO3SGxuxqopZBBg-s1x8Vzo,1805
blib2to3/pgen2/tokenize.cpython-311-darwin.so,sha256=YvepvEHDjtfe1xU-mkOHW10oiaoCsbUdPtcbICVW2_A,50139
blib2to3/pgen2/tokenize.py,sha256=BAqfjwwY_9OwlD3gBVMOPpp2tm_eIoOkxqdIiEGLhkk,23006
blib2to3/pygram.cpython-311-darwin.so,sha256=tDuY0CtnzxrbSJBJse5t0iOyCOmnCVg48URTxym9yyk,50137
blib2to3/pygram.py,sha256=7C4ciX12W3SKWNI9WkTQRhoXMJkROSk1GMS18snC51o,4810
blib2to3/pytree.cpython-311-darwin.so,sha256=GMZpL9iIf1Ex7fwk7DlDNbWFclb42M-qXEfwz42KPXU,50137
blib2to3/pytree.py,sha256=dedSbfx56FTkyTOA1A-I4eTVyDuZ0VRZ_eq0H5HmgLc,32569
