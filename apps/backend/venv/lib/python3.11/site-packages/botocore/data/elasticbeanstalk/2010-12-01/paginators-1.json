{"pagination": {"DescribeEvents": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "Events"}, "DescribeApplicationVersions": {"input_token": "NextToken", "limit_key": "MaxRecords", "output_token": "NextToken", "result_key": "ApplicationVersions"}, "DescribeEnvironmentManagedActionHistory": {"input_token": "NextToken", "limit_key": "MaxItems", "output_token": "NextToken", "result_key": "ManagedActionHistoryItems"}, "DescribeEnvironments": {"input_token": "NextToken", "limit_key": "MaxRecords", "output_token": "NextToken", "result_key": "Environments"}, "ListPlatformVersions": {"input_token": "NextToken", "limit_key": "MaxRecords", "output_token": "NextToken", "result_key": "PlatformSummaryList"}}}