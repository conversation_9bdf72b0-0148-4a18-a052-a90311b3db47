{"pagination": {"GetAccountAuthorizationDetails": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": ["UserDetailList", "GroupDetailList", "RoleDetailList", "Policies"]}, "GetGroup": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Users", "non_aggregate_keys": ["Group"]}, "ListAccessKeys": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "AccessKeyMetadata"}, "ListAccountAliases": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "AccountAliases"}, "ListAttachedGroupPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "AttachedPolicies"}, "ListAttachedRolePolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "AttachedPolicies"}, "ListAttachedUserPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "AttachedPolicies"}, "ListEntitiesForPolicy": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": ["PolicyGroups", "PolicyUsers", "PolicyRoles"]}, "ListGroupPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "PolicyNames"}, "ListGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Groups"}, "ListGroupsForUser": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Groups"}, "ListInstanceProfiles": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "InstanceProfiles"}, "ListInstanceProfilesForRole": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "InstanceProfiles"}, "ListMFADevices": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "MFADevices"}, "ListPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Policies"}, "ListPolicyVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Versions"}, "ListRolePolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "PolicyNames"}, "ListRoles": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Roles"}, "ListServerCertificates": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "ServerCertificateMetadataList"}, "ListSigningCertificates": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Certificates"}, "ListSSHPublicKeys": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "SSHPublicKeys"}, "ListUserPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "PolicyNames"}, "ListUsers": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Users"}, "ListVirtualMFADevices": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "VirtualMFADevices"}, "SimulateCustomPolicy": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "EvaluationResults"}, "SimulatePrincipalPolicy": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "EvaluationResults"}, "ListUserTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListInstanceProfileTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListMFADeviceTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListOpenIDConnectProviderTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListPolicyTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListRoleTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListSAMLProviderTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}, "ListServerCertificateTags": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxItems", "more_results": "IsTruncated", "output_token": "<PERSON><PERSON>", "result_key": "Tags"}}}