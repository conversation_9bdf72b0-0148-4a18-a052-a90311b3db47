# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
#
# Translators:
# <PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2014
# <PERSON> <<EMAIL>>, 2013
# <PERSON> <<EMAIL>>, 2021
# <PERSON>, 2009
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Django Debug Toolbar\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-20 17:23+0100\n"
"PO-Revision-Date: 2021-09-30 09:21+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: French (http://www.transifex.com/django-debug-toolbar/django-"
"debug-toolbar/language/fr/)\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: apps.py:15
msgid "Debug Toolbar"
msgstr "Barre d'outils de débogage"

#: panels/cache.py:180
msgid "Cache"
msgstr "Cache"

#: panels/cache.py:186
#, python-format
msgid "%(cache_calls)d call in %(time).2fms"
msgid_plural "%(cache_calls)d calls in %(time).2fms"
msgstr[0] "%(cache_calls)d appel en %(time).2fms"
msgstr[1] "%(cache_calls)d appels en %(time).2fms"

#: panels/cache.py:195
#, python-format
msgid "Cache calls from %(count)d backend"
msgid_plural "Cache calls from %(count)d backends"
msgstr[0] "Appels au cache depuis %(count)d moteur"
msgstr[1] "Appels au cache depuis %(count)d moteurs"

#: panels/headers.py:31
msgid "Headers"
msgstr "En-têtes"

#: panels/history/panel.py:18 panels/history/panel.py:19
msgid "History"
msgstr "Historique"

#: panels/profiling.py:140
msgid "Profiling"
msgstr "Profilage"

#: panels/redirects.py:14
msgid "Intercept redirects"
msgstr "Interception des redirections"

#: panels/request.py:16
msgid "Request"
msgstr "Requête"

#: panels/request.py:36
msgid "<no view>"
msgstr "<pas de vue>"

#: panels/request.py:53
msgid "<unavailable>"
msgstr "<indisponible>"

#: panels/settings.py:17
msgid "Settings"
msgstr "Paramètres"

#: panels/settings.py:20
#, python-format
msgid "Settings from %s"
msgstr "Paramètres de %s"

#: panels/signals.py:57
#, python-format
msgid "%(num_receivers)d receiver of 1 signal"
msgid_plural "%(num_receivers)d receivers of 1 signal"
msgstr[0] "%(num_receivers)d receveur d'un signal"
msgstr[1] "%(num_receivers)d receveurs d'un signal"

#: panels/signals.py:62
#, python-format
msgid "%(num_receivers)d receiver of %(num_signals)d signals"
msgid_plural "%(num_receivers)d receivers of %(num_signals)d signals"
msgstr[0] "%(num_receivers)d receveur de %(num_signals)d signaux"
msgstr[1] "%(num_receivers)d receveurs de %(num_signals)d signaux"

#: panels/signals.py:67
msgid "Signals"
msgstr "Signaux"

#: panels/sql/panel.py:23
msgid "Autocommit"
msgstr "Auto validation"

#: panels/sql/panel.py:24
msgid "Read uncommitted"
msgstr "Lecture non validée"

#: panels/sql/panel.py:25
msgid "Read committed"
msgstr "Lecture validée"

#: panels/sql/panel.py:26
msgid "Repeatable read"
msgstr "Lecture répétable"

#: panels/sql/panel.py:27
msgid "Serializable"
msgstr "Sérialisable"

#: panels/sql/panel.py:39
msgid "Idle"
msgstr "Inactif"

#: panels/sql/panel.py:40
msgid "Active"
msgstr "Actif"

#: panels/sql/panel.py:41
msgid "In transaction"
msgstr "Transaction en cours"

#: panels/sql/panel.py:42
msgid "In error"
msgstr "Erreur"

#: panels/sql/panel.py:43
msgid "Unknown"
msgstr "Indéterminé"

#: panels/sql/panel.py:130
msgid "SQL"
msgstr "SQL"

#: panels/sql/panel.py:135
#, python-format
msgid "%(query_count)d query in %(sql_time).2fms"
msgid_plural "%(query_count)d queries in %(sql_time).2fms"
msgstr[0] "%(query_count)d requête en %(sql_time).2f ms"
msgstr[1] "%(query_count)d requêtes en %(sql_time).2f ms"

#: panels/sql/panel.py:147
#, python-format
msgid "SQL queries from %(count)d connection"
msgid_plural "SQL queries from %(count)d connections"
msgstr[0] "requêtes SQL venant de %(count)d connexion"
msgstr[1] "Requêtes SQL venant de %(count)d connexions"

#: panels/staticfiles.py:84
#, python-format
msgid "Static files (%(num_found)s found, %(num_used)s used)"
msgstr "Fichiers statiques (%(num_found)s trouvé(s), %(num_used)s utilisé(s))"

#: panels/staticfiles.py:105
msgid "Static files"
msgstr "Fichiers statiques"

#: panels/staticfiles.py:111
#, python-format
msgid "%(num_used)s file used"
msgid_plural "%(num_used)s files used"
msgstr[0] "%(num_used)s fichier utilisé"
msgstr[1] "%(num_used)s fichiers utilisés"

#: panels/templates/panel.py:143
msgid "Templates"
msgstr "Gabarits"

#: panels/templates/panel.py:148
#, python-format
msgid "Templates (%(num_templates)s rendered)"
msgstr "Gabarits (%(num_templates)s affichés)"

#: panels/templates/panel.py:180
msgid "No origin"
msgstr "Sans Origine"

#: panels/timer.py:25
#, python-format
msgid "CPU: %(cum)0.2fms (%(total)0.2fms)"
msgstr "CPU: %(cum)0.2fms (%(total)0.2fms)"

#: panels/timer.py:30
#, python-format
msgid "Total: %0.2fms"
msgstr "Total : %0.2fms"

#: panels/timer.py:36 templates/debug_toolbar/panels/history.html:9
#: templates/debug_toolbar/panels/sql_explain.html:11
#: templates/debug_toolbar/panels/sql_profile.html:12
#: templates/debug_toolbar/panels/sql_select.html:11
msgid "Time"
msgstr "Temps"

#: panels/timer.py:44
msgid "User CPU time"
msgstr "Temps CPU de l'utilisateur"

#: panels/timer.py:44
#, python-format
msgid "%(utime)0.3f msec"
msgstr "%(utime)0.3f ms"

#: panels/timer.py:45
msgid "System CPU time"
msgstr "Temps CPU du système"

#: panels/timer.py:45
#, python-format
msgid "%(stime)0.3f msec"
msgstr "%(stime)0.3f ms"

#: panels/timer.py:46
msgid "Total CPU time"
msgstr "Temps total du CPU"

#: panels/timer.py:46
#, python-format
msgid "%(total)0.3f msec"
msgstr "%(total)0.3f ms"

#: panels/timer.py:47
msgid "Elapsed time"
msgstr "Temps écoulé"

#: panels/timer.py:47
#, python-format
msgid "%(total_time)0.3f msec"
msgstr "%(total_time)0.3f ms"

#: panels/timer.py:49
msgid "Context switches"
msgstr "Basculements de contexte"

#: panels/timer.py:50
#, python-format
msgid "%(vcsw)d voluntary, %(ivcsw)d involuntary"
msgstr "%(vcsw)d volontaire, %(ivcsw)d involontaire"

#: panels/versions.py:19
msgid "Versions"
msgstr "Versions"

#: templates/debug_toolbar/base.html:22
msgid "Hide toolbar"
msgstr "Masquer la barre d'outils"

#: templates/debug_toolbar/base.html:22
msgid "Hide"
msgstr "Masquer"

#: templates/debug_toolbar/base.html:29
msgid "Show toolbar"
msgstr "Afficher la barre d'outils"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Disable for next and successive requests"
msgstr "Désactiver pour les requêtes suivantes"

#: templates/debug_toolbar/includes/panel_button.html:4
msgid "Enable for next and successive requests"
msgstr "Activer pour les requêtes suivantes"

#: templates/debug_toolbar/panels/cache.html:2
msgid "Summary"
msgstr "Résumé"

#: templates/debug_toolbar/panels/cache.html:6
msgid "Total calls"
msgstr "Nombre total d'appels"

#: templates/debug_toolbar/panels/cache.html:7
msgid "Total time"
msgstr "Temps total"

#: templates/debug_toolbar/panels/cache.html:8
msgid "Cache hits"
msgstr "Succès de cache"

#: templates/debug_toolbar/panels/cache.html:9
msgid "Cache misses"
msgstr "Défauts de cache"

#: templates/debug_toolbar/panels/cache.html:21
msgid "Commands"
msgstr "Commandes"

#: templates/debug_toolbar/panels/cache.html:39
msgid "Calls"
msgstr "Appels"

#: templates/debug_toolbar/panels/cache.html:43
#: templates/debug_toolbar/panels/sql.html:36
msgid "Time (ms)"
msgstr "Temps (ms)"

#: templates/debug_toolbar/panels/cache.html:44
msgid "Type"
msgstr "Type"

#: templates/debug_toolbar/panels/cache.html:45
#: templates/debug_toolbar/panels/request.html:8
msgid "Arguments"
msgstr "Paramètres"

#: templates/debug_toolbar/panels/cache.html:46
#: templates/debug_toolbar/panels/request.html:9
msgid "Keyword arguments"
msgstr "Paramètres nommés"

#: templates/debug_toolbar/panels/cache.html:47
msgid "Backend"
msgstr "Moteur"

#: templates/debug_toolbar/panels/headers.html:3
msgid "Request headers"
msgstr "En-têtes de requête"

#: templates/debug_toolbar/panels/headers.html:8
#: templates/debug_toolbar/panels/headers.html:27
#: templates/debug_toolbar/panels/headers.html:48
msgid "Key"
msgstr "Clé"

#: templates/debug_toolbar/panels/headers.html:9
#: templates/debug_toolbar/panels/headers.html:28
#: templates/debug_toolbar/panels/headers.html:49
#: templates/debug_toolbar/panels/history_tr.html:23
#: templates/debug_toolbar/panels/request_variables.html:12
#: templates/debug_toolbar/panels/settings.html:6
#: templates/debug_toolbar/panels/timer.html:11
msgid "Value"
msgstr "Valeur"

#: templates/debug_toolbar/panels/headers.html:22
msgid "Response headers"
msgstr "En-têtes de réponse"

#: templates/debug_toolbar/panels/headers.html:41
msgid "WSGI environ"
msgstr "Environnement WSGI"

#: templates/debug_toolbar/panels/headers.html:43
msgid ""
"Since the WSGI environ inherits the environment of the server, only a "
"significant subset is shown below."
msgstr ""
"Comme l'environnement WSGI hérite de celui du serveur, seul un sous-ensemble "
"pertinent est affiché ci-dessous."

#: templates/debug_toolbar/panels/history.html:10
msgid "Method"
msgstr "Méthode"

#: templates/debug_toolbar/panels/history.html:11
#: templates/debug_toolbar/panels/staticfiles.html:43
msgid "Path"
msgstr "Chemin"

#: templates/debug_toolbar/panels/history.html:12
msgid "Request Variables"
msgstr "Variables de Requête"

#: templates/debug_toolbar/panels/history.html:13
msgid "Status"
msgstr "État"

#: templates/debug_toolbar/panels/history.html:14
#: templates/debug_toolbar/panels/sql.html:37
msgid "Action"
msgstr "Action"

#: templates/debug_toolbar/panels/history_tr.html:22
#: templates/debug_toolbar/panels/request_variables.html:11
msgid "Variable"
msgstr "Variable"

#: templates/debug_toolbar/panels/profiling.html:5
msgid "Call"
msgstr "Appel"

#: templates/debug_toolbar/panels/profiling.html:6
msgid "CumTime"
msgstr "Temps cumulé"

#: templates/debug_toolbar/panels/profiling.html:7
#: templates/debug_toolbar/panels/profiling.html:9
msgid "Per"
msgstr "Par"

#: templates/debug_toolbar/panels/profiling.html:8
msgid "TotTime"
msgstr "Temps total"

#: templates/debug_toolbar/panels/profiling.html:10
msgid "Count"
msgstr "Compte"

#: templates/debug_toolbar/panels/request.html:3
msgid "View information"
msgstr "Afficher l'information"

#: templates/debug_toolbar/panels/request.html:7
msgid "View function"
msgstr "Fonction de vue"

#: templates/debug_toolbar/panels/request.html:10
msgid "URL name"
msgstr "Nom d'URL"

#: templates/debug_toolbar/panels/request.html:24
msgid "Cookies"
msgstr "Cookies"

#: templates/debug_toolbar/panels/request.html:27
msgid "No cookies"
msgstr "Pas de cookies"

#: templates/debug_toolbar/panels/request.html:31
msgid "Session data"
msgstr "Données de session"

#: templates/debug_toolbar/panels/request.html:34
msgid "No session data"
msgstr "Pas de données de session"

#: templates/debug_toolbar/panels/request.html:38
msgid "GET data"
msgstr "Données GET"

#: templates/debug_toolbar/panels/request.html:41
msgid "No GET data"
msgstr "Aucune donnée GET"

#: templates/debug_toolbar/panels/request.html:45
msgid "POST data"
msgstr "Données POST"

#: templates/debug_toolbar/panels/request.html:48
msgid "No POST data"
msgstr "Aucune donnée POST"

#: templates/debug_toolbar/panels/settings.html:5
msgid "Setting"
msgstr "Paramètre"

#: templates/debug_toolbar/panels/signals.html:5
msgid "Signal"
msgstr "Signal"

#: templates/debug_toolbar/panels/signals.html:6
msgid "Receivers"
msgstr "Receveurs"

#: templates/debug_toolbar/panels/sql.html:6
#, python-format
msgid "%(num)s query"
msgid_plural "%(num)s queries"
msgstr[0] "%(num)s requête"
msgstr[1] "%(num)s requêtes"

#: templates/debug_toolbar/panels/sql.html:8
#, python-format
msgid ""
"including <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similar</abbr>"
msgstr ""
"comprenant <abbr title=\"Similar queries are queries with the same SQL, but "
"potentially different parameters.\">%(count)s similaires</abbr>"

#: templates/debug_toolbar/panels/sql.html:12
#, python-format
msgid ""
"and <abbr title=\"Duplicate queries are identical to each other: they "
"execute exactly the same SQL and parameters.\">%(dupes)s duplicates</abbr>"
msgstr ""
"et <abbr title=\"Duplicate queries are identical to each other: they execute "
"exactly the same SQL and parameters.\">%(dupes)s en double</abbr>"

#: templates/debug_toolbar/panels/sql.html:34
msgid "Query"
msgstr "Requête"

#: templates/debug_toolbar/panels/sql.html:35
#: templates/debug_toolbar/panels/timer.html:36
msgid "Timeline"
msgstr "Chronologie"

#: templates/debug_toolbar/panels/sql.html:52
#, python-format
msgid "%(count)s similar queries."
msgstr "%(count)s requêtes similaires."

#: templates/debug_toolbar/panels/sql.html:58
#, python-format
msgid "Duplicated %(dupes)s times."
msgstr "Dupliqué %(dupes)s fois."

#: templates/debug_toolbar/panels/sql.html:95
msgid "Connection:"
msgstr "Connexion :"

#: templates/debug_toolbar/panels/sql.html:97
msgid "Isolation level:"
msgstr "Niveau d'isolation :"

#: templates/debug_toolbar/panels/sql.html:100
msgid "Transaction status:"
msgstr "État de la transaction :"

#: templates/debug_toolbar/panels/sql.html:114
msgid "(unknown)"
msgstr "(indéterminé)"

#: templates/debug_toolbar/panels/sql.html:123
msgid "No SQL queries were recorded during this request."
msgstr "Aucune requête SQL n'a été enregistrée durant cette requête."

#: templates/debug_toolbar/panels/sql_explain.html:4
msgid "SQL explained"
msgstr "SQL expliqué"

#: templates/debug_toolbar/panels/sql_explain.html:9
#: templates/debug_toolbar/panels/sql_profile.html:10
#: templates/debug_toolbar/panels/sql_select.html:9
msgid "Executed SQL"
msgstr "SQL Exécuté"

#: templates/debug_toolbar/panels/sql_explain.html:13
#: templates/debug_toolbar/panels/sql_profile.html:14
#: templates/debug_toolbar/panels/sql_select.html:13
msgid "Database"
msgstr "Base de données"

#: templates/debug_toolbar/panels/sql_profile.html:4
msgid "SQL profiled"
msgstr "SQL profilé"

#: templates/debug_toolbar/panels/sql_profile.html:37
msgid "Error"
msgstr "Erreur"

#: templates/debug_toolbar/panels/sql_select.html:4
msgid "SQL selected"
msgstr "SQL sélectionné"

#: templates/debug_toolbar/panels/sql_select.html:36
msgid "Empty set"
msgstr "Ensemble vide"

#: templates/debug_toolbar/panels/staticfiles.html:3
msgid "Static file path"
msgid_plural "Static file paths"
msgstr[0] "Chemin de fichier statique"
msgstr[1] "Chemins de fichiers statiques"

#: templates/debug_toolbar/panels/staticfiles.html:7
#, python-format
msgid "(prefix %(prefix)s)"
msgstr "(préfixe %(prefix)s)"

#: templates/debug_toolbar/panels/staticfiles.html:11
#: templates/debug_toolbar/panels/staticfiles.html:22
#: templates/debug_toolbar/panels/staticfiles.html:34
#: templates/debug_toolbar/panels/templates.html:10
#: templates/debug_toolbar/panels/templates.html:30
#: templates/debug_toolbar/panels/templates.html:47
msgid "None"
msgstr "Aucun"

#: templates/debug_toolbar/panels/staticfiles.html:14
msgid "Static file app"
msgid_plural "Static file apps"
msgstr[0] "Application de fichiers statiques"
msgstr[1] "Applications de fichiers statiques"

#: templates/debug_toolbar/panels/staticfiles.html:25
msgid "Static file"
msgid_plural "Static files"
msgstr[0] ""
msgstr[1] "Fichiers statiques"

#: templates/debug_toolbar/panels/staticfiles.html:39
#, python-format
msgid "%(payload_count)s file"
msgid_plural "%(payload_count)s files"
msgstr[0] "%(payload_count)s fichier"
msgstr[1] "%(payload_count)s fichiers"

#: templates/debug_toolbar/panels/staticfiles.html:44
msgid "Location"
msgstr "Emplacement"

#: templates/debug_toolbar/panels/template_source.html:4
msgid "Template source:"
msgstr "Source du gabarit :"

#: templates/debug_toolbar/panels/templates.html:2
msgid "Template path"
msgid_plural "Template paths"
msgstr[0] ""
msgstr[1] "Chemin du gabarit"

#: templates/debug_toolbar/panels/templates.html:13
msgid "Template"
msgid_plural "Templates"
msgstr[0] ""
msgstr[1] "Gabarit"

#: templates/debug_toolbar/panels/templates.html:22
#: templates/debug_toolbar/panels/templates.html:40
msgid "Toggle context"
msgstr "Afficher/masquer le contexte"

#: templates/debug_toolbar/panels/templates.html:33
msgid "Context processor"
msgid_plural "Context processors"
msgstr[0] "Processeur de contexte"
msgstr[1] "Processeurs de contexte"

#: templates/debug_toolbar/panels/timer.html:2
msgid "Resource usage"
msgstr "Utilisation des ressources"

#: templates/debug_toolbar/panels/timer.html:10
msgid "Resource"
msgstr "Ressource"

#: templates/debug_toolbar/panels/timer.html:26
msgid "Browser timing"
msgstr "Chronologie du navigateur"

#: templates/debug_toolbar/panels/timer.html:35
msgid "Timing attribute"
msgstr "Attribut mesuré"

#: templates/debug_toolbar/panels/timer.html:37
msgid "Milliseconds since navigation start (+length)"
msgstr "Millisecondes depuis le début de la navigation (+longueur)"

#: templates/debug_toolbar/panels/versions.html:10
msgid "Package"
msgstr "Paquet"

#: templates/debug_toolbar/panels/versions.html:11
msgid "Name"
msgstr "Nom"

#: templates/debug_toolbar/panels/versions.html:12
msgid "Version"
msgstr "Version"

#: templates/debug_toolbar/redirect.html:10
msgid "Location:"
msgstr "Emplacement :"

#: templates/debug_toolbar/redirect.html:12
msgid ""
"The Django Debug Toolbar has intercepted a redirect to the above URL for "
"debug viewing purposes. You can click the above link to continue with the "
"redirect as normal."
msgstr ""
"La barre de débogage Django a intercepté une redirection vers l'URL ci-"
"dessus afin de permettre la consultation des messages de débogage.  Vous "
"pouvez cliquer sur le lien ci-dessus pour continuer normalement avec la "
"redirection."

#: views.py:16
msgid ""
"Data for this panel isn't available anymore. Please reload the page and "
"retry."
msgstr ""
"Les données de ce panneau ne sont plus disponibles. Rechargez la page et "
"essayez à nouveau."
