{% load i18n %}{% load static %}
<form method="get" action="{% url 'djdt:history_refresh' %}">
    {{ refresh_form }}
    <button class="refreshHistory">Refresh</button>
</form>
<table class="djdt-max-height-100">
    <thead>
        <tr>
            <th>{% trans "Time" %}</th>
            <th>{% trans "Method" %}</th>
            <th>{% trans "Path" %}</th>
            <th>{% trans "Request Variables" %}</th>
            <th>{% trans "Status" %}</th>
            <th>{% trans "Action" %}</th>
        </tr>
    </thead>
    <tbody id="djdtHistoryRequests">
        {% for id, store_context in stores.items %}
            {% include "debug_toolbar/panels/history_tr.html" %}
        {% endfor %}
    </tbody>
</table>
