# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# Nicat <PERSON> <<EMAIL>>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2022-07-24 20:19+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Azerbaijani (http://www.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administrativ Dokumentasiya"

msgid "Home"
msgstr "Ev"

msgid "Documentation"
msgstr "Sənədləşmə"

msgid "Bookmarklets"
msgstr "Əlfəcinciklər (Bookmarklets)"

msgid "Documentation bookmarklets"
msgstr "Sənədləşmə əlfəcincikləri (bookmarklets)"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "Bu səhifənin sənədləşməsi"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Hər hansı səhifəni əmələ gətirən funksiyanın sənədləşməsini göstərir."

msgid "Tags"
msgstr ""

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr "Filterlər"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr ""

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr ""

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr ""

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Fields"
msgstr ""

msgid "Field"
msgstr ""

msgid "Type"
msgstr ""

msgid "Description"
msgstr ""

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr ""

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr ""

#, python-format
msgid "Template: %(name)s"
msgstr ""

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr ""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr ""

msgid "(does not exist)"
msgstr "(mövcud deyil)"

msgid "Back to Documentation"
msgstr ""

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr ""

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr ""

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "tag:"
msgstr "teq:"

msgid "filter:"
msgstr "süzgəc:"

msgid "view:"
msgstr "baxış:"

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "%(model_name)r modelini %(app_label)r tətbiqetməsində tapa bilmədik"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "bağlı \"%(app_label)s.%(data_type)s\" obyekti"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "bağlı \"%(app_label)s.%(object_name)s\" obyektləri"

#, python-format
msgid "all %s"
msgstr "bütün %s"

#, python-format
msgid "number of %s"
msgstr "%s-in sayı"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s urlpattern obyekti deyil"
