# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2015-01-18 08:34+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Luxembourgish (http://www.transifex.com/projects/p/django/"
"language/lb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr ""

msgid "Home"
msgstr ""

msgid "Documentation"
msgstr ""

msgid "Bookmarklets"
msgstr ""

msgid "Documentation bookmarklets"
msgstr ""

msgid ""
"\n"
"<p class=\"help\">To install bookmarklets, drag the link to your bookmarks\n"
"toolbar, or right-click the link and add it to your bookmarks. Now you can\n"
"select the bookmarklet from any page in the site.  Note that some of these\n"
"bookmarklets require you to be viewing the site from a computer designated\n"
"as \"internal\" (talk to your system administrator if you aren't sure if\n"
"your computer is \"internal\").</p>\n"
msgstr ""

msgid "Documentation for this page"
msgstr ""

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""

msgid "Show object ID"
msgstr ""

msgid ""
"Shows the content-type and unique ID for pages that represent a single "
"object."
msgstr ""

msgid "Edit this object (current window)"
msgstr ""

msgid "Jumps to the admin page for pages that represent a single object."
msgstr ""

msgid "Edit this object (new window)"
msgstr ""

msgid "As above, but opens the admin page in a new window."
msgstr ""

msgid "Tags"
msgstr ""

msgid "List of all the template tags and their functions."
msgstr ""

msgid "Filters"
msgstr ""

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""

msgid "Models"
msgstr ""

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""

msgid "Views"
msgstr ""

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""

msgid "Please install docutils"
msgstr ""

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""

#, python-format
msgid "Model: %(name)s"
msgstr ""

msgid "Field"
msgstr ""

msgid "Type"
msgstr ""

msgid "Description"
msgstr ""

msgid "Back to Model Documentation"
msgstr ""

msgid "Model documentation"
msgstr ""

msgid "Model groups"
msgstr ""

msgid "Templates"
msgstr ""

#, python-format
msgid "Template: %(name)s"
msgstr ""

#, python-format
msgid "Template: \"%(name)s\""
msgstr ""

#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr ""

msgid "(does not exist)"
msgstr ""

msgid "Back to Documentation"
msgstr ""

msgid "Template filters"
msgstr ""

msgid "Template filter documentation"
msgstr ""

msgid "Built-in filters"
msgstr ""

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""

msgid "Template tags"
msgstr ""

msgid "Template tag documentation"
msgstr ""

msgid "Built-in tags"
msgstr ""

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""

#, python-format
msgid "View: %(name)s"
msgstr ""

msgid "Context:"
msgstr ""

msgid "Templates:"
msgstr ""

msgid "Back to Views Documentation"
msgstr ""

msgid "View documentation"
msgstr ""

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""

msgid "Boolean (Either True or False)"
msgstr ""

#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

msgid "tag:"
msgstr ""

msgid "filter:"
msgstr ""

msgid "view:"
msgstr ""

#, python-format
msgid "App %(app_label)r not found"
msgstr ""

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr ""

msgid "model:"
msgstr ""

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr ""

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr ""

#, python-format
msgid "all %s"
msgstr ""

#, python-format
msgid "number of %s"
msgstr ""

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr ""
