# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011-2013,2015,2017
# <PERSON><PERSON>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (http://www.transifex.com/django/django/language/"
"pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentação Administrativa"

msgid "Home"
msgstr "Início"

msgid "Documentation"
msgstr "Documentação"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Documentação dos bookmarklets"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Para instalar bookmarklets, arraste o link da barra dos bookmarks, ou "
"pressione do lado direito no link e adicione aos bookmarks.\n"
"Agora poderá selecionar o bookmarklet de qualquer página do site."

msgid "Documentation for this page"
msgstr "Documentação desta página"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Vai de qualquer página para a documentação da view que gera essa página."

msgid "Tags"
msgstr "Tags"

msgid "List of all the template tags and their functions."
msgstr "Lista de todas as template tags e suas funções."

msgid "Filters"
msgstr "Filtros"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Os filtros são as acções que podem ser aplicadas às variáveis ​​de uma "
"template para alterar o output."

msgid "Models"
msgstr "Models"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Models são descrições de todos os objetos no sistema e seus campos "
"associados. Cada model tem uma lista de campos que podem ser acedidos como "
"variáveis ​​de template"

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada página no site público é gerada por uma view. A view define qual "
"template será usado para gerar a página e quais objetos estarão disponíveis "
"para esse template."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Ferramentas para o seu navegador para aceder rapidamente a funcionalidade de "
"administração."

msgid "Please install docutils"
msgstr "Por favor, instale o docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"A documentação do sistema de admin requer a biblioteca <a href=\"%(link)s"
"\">docutils</a> do Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Por favor, peça aos seu administradores para instalar o <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Campos"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descrição"

msgid "Methods with arguments"
msgstr "Métodos com argumentos"

msgid "Method"
msgstr "Métodos"

msgid "Arguments"
msgstr "Argumentos"

msgid "Back to Model documentation"
msgstr "Voltar para a documentação dos Models"

msgid "Model documentation"
msgstr "Documentação dos Models"

msgid "Model groups"
msgstr "Grupos de Models"

msgid "Templates"
msgstr "Templates"

#, python-format
msgid "Template: %(name)s"
msgstr "Template: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Template: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "Caminho de pesquisa para o template \"%(name)s\":"

msgid "(does not exist)"
msgstr "(Não existe)"

msgid "Back to Documentation"
msgstr "Voltar para a Documentação"

msgid "Template filters"
msgstr "Filtros de template"

msgid "Template filter documentation"
msgstr "Documentação de filtros de template"

msgid "Built-in filters"
msgstr "Filtros built-in"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Para utilizar estes filtros, coloque <code>%(code)s</code> na sua template "
"antes de utilizar o filtro."

msgid "Template tags"
msgstr "Tags de template"

msgid "Template tag documentation"
msgstr "Documentação de tags de template"

msgid "Built-in tags"
msgstr "Tags built-in"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Para utilizar estas tags, coloque <code>%(code)s</code> na sua template "
"antes de usar a tag."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Contexto:"

msgid "Templates:"
msgstr "Templates:"

msgid "Back to View documentation"
msgstr "Voltar para a documentação das Views"

msgid "View documentation"
msgstr "Ver documentação"

msgid "Jump to namespace"
msgstr "Saltar para namespace"

msgid "Empty namespace"
msgstr "Namespace vazio"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views por namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Views por namespace vazio"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Função de view: <code>%(full_name)s</code>. Nome: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "A aplicação %(app_label)r não foi encontrada"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "O Model %(model_name)r não foi encontrado na aplicação %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "o objeto `%(app_label)s.%(data_type)s` relacionado"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "os objetos `%(app_label)s.%(object_name)s` relacionados"

#, python-format
msgid "all %s"
msgstr "todos %s"

#, python-format
msgid "number of %s"
msgstr "número de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s não parece ser um objeto urlpattern"
