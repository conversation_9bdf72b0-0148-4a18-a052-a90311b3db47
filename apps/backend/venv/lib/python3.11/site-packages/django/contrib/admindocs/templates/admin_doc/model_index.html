{% extends "admin/base_site.html" %}
{% load i18n %}

{% block coltype %}colSM{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-docroot' %}">{% translate 'Documentation' %}</a>
&rsaquo; {% translate 'Models' %}
</div>
{% endblock %}

{% block title %}{% translate 'Models' %}{% endblock %}

{% block content %}

<h1>{% translate 'Model documentation' %}</h1>

{% regroup models by app_config as grouped_models %}

<div id="content-main">
{% for group in grouped_models %}
<div class="module">
<h2 id="app-{{ group.grouper.label }}">{{ group.grouper.verbose_name }} ({{ group.grouper.name }})</h2>

<table class="xfull">
{% for model in group.list %}
<tr>
<th><a href="{% url 'django-admindocs-models-detail' app_label=model.app_label model_name=model.model_name %}">{{ model.object_name }}</a></th>
</tr>
{% endfor %}
</table>
</div>
{% endfor %}

</div>
{% endblock %}

{% block sidebar %}
<div id="content-related" class="sidebar">
<div class="module">
<h2>{% translate 'Model groups' %}</h2>
<ul>
{% regroup models by app_config as grouped_models %}
{% for group in grouped_models %}
    <li><a href="#app-{{ group.grouper.label }}">{{ group.grouper.verbose_name }}</a></li>
{% endfor %}
</ul>
</div>
</div>
{% endblock %}
