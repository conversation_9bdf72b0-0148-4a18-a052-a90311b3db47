# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-17 03:19-0500\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: contrib/auth/admin.py:49
msgid "Personal info"
msgstr ""

#: contrib/auth/admin.py:51
msgid "Permissions"
msgstr ""

#: contrib/auth/admin.py:62
msgid "Important dates"
msgstr ""

#: contrib/auth/admin.py:156
#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

#: contrib/auth/admin.py:168
msgid "Password changed successfully."
msgstr ""

#: contrib/auth/admin.py:189
#, python-format
msgid "Change password: %s"
msgstr ""

#: contrib/auth/apps.py:16
msgid "Authentication and Authorization"
msgstr ""

#: contrib/auth/base_user.py:58
msgid "password"
msgstr ""

#: contrib/auth/base_user.py:59
msgid "last login"
msgstr ""

#: contrib/auth/forms.py:41
msgid "No password set."
msgstr ""

#: contrib/auth/forms.py:49
msgid "Invalid password format or unknown hashing algorithm."
msgstr ""

#: contrib/auth/forms.py:91 contrib/auth/forms.py:379 contrib/auth/forms.py:457
msgid "The two password fields didn’t match."
msgstr ""

#: contrib/auth/forms.py:94 contrib/auth/forms.py:166 contrib/auth/forms.py:201
#: contrib/auth/forms.py:461
msgid "Password"
msgstr ""

#: contrib/auth/forms.py:100
msgid "Password confirmation"
msgstr ""

#: contrib/auth/forms.py:103 contrib/auth/forms.py:472
msgid "Enter the same password as before, for verification."
msgstr ""

#: contrib/auth/forms.py:168
msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""

#: contrib/auth/forms.py:208
#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""

#: contrib/auth/forms.py:211
msgid "This account is inactive."
msgstr ""

#: contrib/auth/forms.py:276
msgid "Email"
msgstr ""

#: contrib/auth/forms.py:382
msgid "New password"
msgstr ""

#: contrib/auth/forms.py:388
msgid "New password confirmation"
msgstr ""

#: contrib/auth/forms.py:425
msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""

#: contrib/auth/forms.py:429
msgid "Old password"
msgstr ""

#: contrib/auth/forms.py:469
msgid "Password (again)"
msgstr ""

#: contrib/auth/hashers.py:327 contrib/auth/hashers.py:420
#: contrib/auth/hashers.py:510 contrib/auth/hashers.py:605
#: contrib/auth/hashers.py:665 contrib/auth/hashers.py:707
#: contrib/auth/hashers.py:765 contrib/auth/hashers.py:820
#: contrib/auth/hashers.py:878
msgid "algorithm"
msgstr ""

#: contrib/auth/hashers.py:328
msgid "iterations"
msgstr ""

#: contrib/auth/hashers.py:329 contrib/auth/hashers.py:426
#: contrib/auth/hashers.py:512 contrib/auth/hashers.py:609
#: contrib/auth/hashers.py:666 contrib/auth/hashers.py:708
#: contrib/auth/hashers.py:879
msgid "salt"
msgstr ""

#: contrib/auth/hashers.py:330 contrib/auth/hashers.py:427
#: contrib/auth/hashers.py:610 contrib/auth/hashers.py:667
#: contrib/auth/hashers.py:709 contrib/auth/hashers.py:766
#: contrib/auth/hashers.py:821 contrib/auth/hashers.py:880
msgid "hash"
msgstr ""

#: contrib/auth/hashers.py:421
msgid "variety"
msgstr ""

#: contrib/auth/hashers.py:422
msgid "version"
msgstr ""

#: contrib/auth/hashers.py:423
msgid "memory cost"
msgstr ""

#: contrib/auth/hashers.py:424
msgid "time cost"
msgstr ""

#: contrib/auth/hashers.py:425 contrib/auth/hashers.py:608
msgid "parallelism"
msgstr ""

#: contrib/auth/hashers.py:511 contrib/auth/hashers.py:606
msgid "work factor"
msgstr ""

#: contrib/auth/hashers.py:513
msgid "checksum"
msgstr ""

#: contrib/auth/hashers.py:607
msgid "block size"
msgstr ""

#: contrib/auth/models.py:62 contrib/auth/models.py:116
msgid "name"
msgstr ""

#: contrib/auth/models.py:66
msgid "content type"
msgstr ""

#: contrib/auth/models.py:68
msgid "codename"
msgstr ""

#: contrib/auth/models.py:73
msgid "permission"
msgstr ""

#: contrib/auth/models.py:74 contrib/auth/models.py:119
msgid "permissions"
msgstr ""

#: contrib/auth/models.py:126
msgid "group"
msgstr ""

#: contrib/auth/models.py:127 contrib/auth/models.py:258
msgid "groups"
msgstr ""

#: contrib/auth/models.py:249
msgid "superuser status"
msgstr ""

#: contrib/auth/models.py:252
msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""

#: contrib/auth/models.py:261
msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

#: contrib/auth/models.py:269
msgid "user permissions"
msgstr ""

#: contrib/auth/models.py:271
msgid "Specific permissions for this user."
msgstr ""

#: contrib/auth/models.py:345
msgid "username"
msgstr ""

#: contrib/auth/models.py:349
msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

#: contrib/auth/models.py:353
msgid "A user with that username already exists."
msgstr ""

#: contrib/auth/models.py:356
msgid "first name"
msgstr ""

#: contrib/auth/models.py:357
msgid "last name"
msgstr ""

#: contrib/auth/models.py:358
msgid "email address"
msgstr ""

#: contrib/auth/models.py:360
msgid "staff status"
msgstr ""

#: contrib/auth/models.py:362
msgid "Designates whether the user can log into this admin site."
msgstr ""

#: contrib/auth/models.py:365
msgid "active"
msgstr ""

#: contrib/auth/models.py:368
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

#: contrib/auth/models.py:372
msgid "date joined"
msgstr ""

#: contrib/auth/models.py:381
msgid "user"
msgstr ""

#: contrib/auth/models.py:382
msgid "users"
msgstr ""

#: contrib/auth/password_validation.py:111
#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
msgstr[1] ""

#: contrib/auth/password_validation.py:123
#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""
msgstr[1] ""

#: contrib/auth/password_validation.py:206
#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr ""

#: contrib/auth/password_validation.py:213
msgid "Your password can’t be too similar to your other personal information."
msgstr ""

#: contrib/auth/password_validation.py:245
msgid "This password is too common."
msgstr ""

#: contrib/auth/password_validation.py:250
msgid "Your password can’t be a commonly used password."
msgstr ""

#: contrib/auth/password_validation.py:261
msgid "This password is entirely numeric."
msgstr ""

#: contrib/auth/password_validation.py:266
msgid "Your password can’t be entirely numeric."
msgstr ""

#: contrib/auth/templates/registration/password_reset_subject.txt:2
#, python-format
msgid "Password reset on %(site_name)s"
msgstr ""

#: contrib/auth/validators.py:12
msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""

#: contrib/auth/validators.py:22
msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

#: contrib/auth/views.py:178
msgid "Logged out"
msgstr ""

#: contrib/auth/views.py:237
msgid "Password reset"
msgstr ""

#: contrib/auth/views.py:264
msgid "Password reset sent"
msgstr ""

#: contrib/auth/views.py:274
msgid "Enter new password"
msgstr ""

#: contrib/auth/views.py:346
msgid "Password reset unsuccessful"
msgstr ""

#: contrib/auth/views.py:355
msgid "Password reset complete"
msgstr ""

#: contrib/auth/views.py:367
msgid "Password change"
msgstr ""

#: contrib/auth/views.py:390
msgid "Password change successful"
msgstr ""
