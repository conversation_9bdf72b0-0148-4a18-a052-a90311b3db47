# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <inactive+<PERSON>@transifex.com>, 2011-2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012-2015,2017,2019
# <AUTHOR> <EMAIL>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-19 11:14+0000\n"
"Last-Translator: אורי רודברג <<EMAIL>>\n"
"Language-Team: Hebrew (http://www.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Personal info"
msgstr "מידע אישי"

msgid "Permissions"
msgstr "הרשאות"

msgid "Important dates"
msgstr "תאריכים חשובים"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "הפריט %(name)s עם המפתח הראשי %(key)r אינו קיים."

msgid "Password changed successfully."
msgstr "הסיסמה שונתה בהצלחה."

#, python-format
msgid "Change password: %s"
msgstr "שינוי סיסמה: %s"

msgid "Authentication and Authorization"
msgstr "אימות והרשאות"

msgid "password"
msgstr "סיסמה"

msgid "last login"
msgstr "כניסה אחרונה"

msgid "No password set."
msgstr "לא נקבעה סיסמה."

msgid "Invalid password format or unknown hashing algorithm."
msgstr "תחביר סיסמה בלתי-חוקי או אלגוריתם גיבוב לא ידוע."

msgid "The two password fields didn’t match."
msgstr "שני שדות הסיסמה אינם זהים."

msgid "Password"
msgstr "סיסמה"

msgid "Password confirmation"
msgstr "אימות סיסמה"

msgid "Enter the same password as before, for verification."
msgstr "יש להזין את אותה סיסמה כמו קודם, לאימות."

msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""
"הסיסמאות אינן נשמרות באופן חשוף, כך שאין דרך לראות את סיסמת המשתמש, אבל ניתן "
"לשנות את הסיסמה בעזרת <a href=\"{}\">טופס זה</a>."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"נא להזין  %(username)s וסיסמה נכונים. נא לשים לב כי שני השדות רגישים לאותיות "
"גדולות/קטנות."

msgid "This account is inactive."
msgstr "חשבון זה אינו פעיל."

msgid "Email"
msgstr "דוא\"ל"

msgid "New password"
msgstr "סיסמה חדשה"

msgid "New password confirmation"
msgstr "אימות סיסמה חדשה"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "סיסמתך הישנה הוזנה בצורה שגויה. נא להזינה שוב."

msgid "Old password"
msgstr "סיסמה ישנה"

msgid "Password (again)"
msgstr "סיסמה (שוב)"

msgid "algorithm"
msgstr "אלגוריתם"

msgid "iterations"
msgstr "חזרות"

msgid "salt"
msgstr "salt"

msgid "hash"
msgstr "גיבוב"

msgid "variety"
msgstr "מגוון"

msgid "version"
msgstr "גרסה"

msgid "memory cost"
msgstr "עלות זכרון"

msgid "time cost"
msgstr "עלות זמן"

msgid "parallelism"
msgstr "מקבילות"

msgid "work factor"
msgstr "work factor"

msgid "checksum"
msgstr "סיכום ביקורת"

msgid "name"
msgstr "שם"

msgid "content type"
msgstr "סוג תוכן"

msgid "codename"
msgstr "שם קוד"

msgid "permission"
msgstr "הרשאה"

msgid "permissions"
msgstr "הרשאות"

msgid "group"
msgstr "קבוצה"

msgid "groups"
msgstr "קבוצות"

msgid "superuser status"
msgstr "סטטוס משתמש על"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "מציין שלמשתמש זה יש את כל ההרשאות ללא הצורך המפורש בהענקתן."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"הקבוצות שמשתמש זה שייך אליהן. משתמש יקבל את כל ההרשאות המוקצות לכל אחת "
"מהקבוצות שלו/שלה."

msgid "user permissions"
msgstr "הרשאות משתמש"

msgid "Specific permissions for this user."
msgstr "הרשאות ספציפיות למשתמש זה."

msgid "username"
msgstr "שם משתמש"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr "שדה חובה. 150 תווים או פחות. אותיות, ספרות ו-@/./+/-/_ בלבד."

msgid "A user with that username already exists."
msgstr "משתמש עם שם משתמש זה קיים כבר"

msgid "first name"
msgstr "שם פרטי"

msgid "last name"
msgstr "שם משפחה"

msgid "email address"
msgstr "כתובת דוא\"ל"

msgid "staff status"
msgstr "סטטוס איש צוות"

msgid "Designates whether the user can log into this admin site."
msgstr "מציין האם המשתמש יכול להתחבר לאתר הניהול."

msgid "active"
msgstr "פעיל"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"מציין האם יש להתייחס למשתמש כפעיל. יש לבטל בחירה זו במקום למחוק חשבונות "
"משתמשים."

msgid "date joined"
msgstr "תאריך הצטרפות"

msgid "user"
msgstr "משתמש"

msgid "users"
msgstr "משתמשים"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] "סיסמה זו קצרה מדי. היא חייבת להכיל לפחות תו %(min_length)d."
msgstr[1] "סיסמה זו קצרה מדי. היא חייבת להכיל לפחות %(min_length)d תווים."
msgstr[2] "סיסמה זו קצרה מדי. היא חייבת להכיל לפחות %(min_length)d תווים."
msgstr[3] "סיסמה זו קצרה מדי. היא חייבת להכיל לפחות %(min_length)d תווים."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "הסיסמה שלך חייבת להכיל לפחות תו %(min_length)d."
msgstr[1] "הסיסמה שלך חייבת להכיל לפחות %(min_length)d תווים."
msgstr[2] "הסיסמה שלך חייבת להכיל לפחות %(min_length)d תווים."
msgstr[3] "הסיסמה שלך חייבת להכיל לפחות %(min_length)d תווים."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "סיסמה זו דומה מדי ל-%(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "הסיסמה שלך לא יכולה להיות דומה מדי למידע אישי אחר שלך."

msgid "This password is too common."
msgstr "סיסמה זו נפוצה מדי."

msgid "Your password can’t be a commonly used password."
msgstr "הסיסמה שלך לא יכולה להיות סיסמה שכיחה."

msgid "This password is entirely numeric."
msgstr "סיסמה זו מכילה רק ספרות."

msgid "Your password can’t be entirely numeric."
msgstr "הסיסמה שלך לא יכולה להכיל רק ספרות."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "החלפת הסיסמה ב-%(site_name)s"

msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""
"יש להזין שם משתמש חוקי. ערך זה יכול להכיל אותיות אנגליות, ספרות והתווים @/./"
"+/-/_ בלבד."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"יש להזין שם משתמש חוקי. ערך זה יכול להכיל אותיות, ספרות והתווים @/./+/-/_ "
"בלבד."

msgid "Logged out"
msgstr "יצאת מהמערכת"

msgid "Password reset"
msgstr "איפוס סיסמה"

msgid "Password reset sent"
msgstr "איפוס הסיסמה נשלח."

msgid "Enter new password"
msgstr "הזנת סיסמה חדשה"

msgid "Password reset unsuccessful"
msgstr "איפוס הסיסמה נכשל"

msgid "Password reset complete"
msgstr "איפוס הסיסמה הושלם"

msgid "Password change"
msgstr "שינוי סיסמה"

msgid "Password change successful"
msgstr "הסיסמה שונתה בהצלחה"
