# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-10-16 18:24+0000\n"
"Last-Translator: znotdead <<EMAIL>>\n"
"Language-Team: Belarusian (http://www.transifex.com/django/django/language/"
"be/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: be\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n"
"%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Content Types"
msgstr "Тыпы кантэнту"

msgid "python model class name"
msgstr "назва клясы пітонавае мадэлі"

msgid "content type"
msgstr "від зьмесьціва"

msgid "content types"
msgstr "віды зьмесьціва"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Аб’ект са зьмесьцівам віду %(ct_id)s не зьвязалі з мадэльлю"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Аб’ект %(obj_id)s са зьмесьцівам віду %(ct_id)s не існуе"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Аб’екты %(ct_name)s ня маюць спосабу «get_absolute_url()»"
