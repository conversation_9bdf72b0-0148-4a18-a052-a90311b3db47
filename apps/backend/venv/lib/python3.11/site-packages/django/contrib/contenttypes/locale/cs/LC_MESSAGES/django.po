# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2012,2014
# V<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-19 09:23+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech (http://www.transifex.com/django/django/language/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

msgid "Content Types"
msgstr "Typy obsahu"

msgid "python model class name"
msgstr "název třídy modelu v Pythonu"

msgid "content type"
msgstr "typ obsahu"

msgid "content types"
msgstr "typy obsahu"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Typ obsahu %(ct_id)s nemá přidružený model."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Objekt %(obj_id)s typu obsahu %(ct_id)s neexistuje."

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objektům typu %(ct_name)s chybí metoda get_absolute_url()."
