# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-12-28 01:46+0000\n"
"Last-Translator: Ragnar Rebase <<EMAIL>>\n"
"Language-Team: Estonian (http://www.transifex.com/django/django/language/"
"et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Sisutüübid"

msgid "python model class name"
msgstr "pythoni mudeli klassinimi"

msgid "content type"
msgstr "sisutüüp"

msgid "content types"
msgstr "sisutüübid"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Sisutüübi %(ct_id)s objektil puudub seos mudeliga"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Sisutüübi %(ct_id)s objekti %(obj_id)s pole olemas"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s objektidel pole get_absolute_url() meetodit"
