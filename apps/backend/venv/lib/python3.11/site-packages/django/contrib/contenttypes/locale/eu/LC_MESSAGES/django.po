# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012
# Enek<PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: Enek<PERSON> <<EMAIL>>\n"
"Language-Team: Basque (http://www.transifex.com/django/django/language/eu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Eduki Motak"

msgid "python model class name"
msgstr "python model class izena"

msgid "content type"
msgstr "eduki mota"

msgid "content types"
msgstr "eduki motak"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "%(ct_id)s eduki motak ez dauka lotutako eredurik"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "%(ct_id)s eduki motako %(obj_id)s objekturik ez da existitzen"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s objektuek ez daukate get_absolute_url() metodorik"
