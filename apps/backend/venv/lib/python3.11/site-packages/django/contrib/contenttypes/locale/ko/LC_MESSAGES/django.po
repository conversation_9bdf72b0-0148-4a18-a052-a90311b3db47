# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON>uffe <<EMAIL>>, 2014
# <PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-17 08:01+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Korean (http://www.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Content Types"
msgstr "콘텐츠 타입"

msgid "python model class name"
msgstr "python 모델 클래스 명"

msgid "content type"
msgstr "콘텐츠 타입"

msgid "content types"
msgstr "콘텐츠 타입(들)"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "콘텐츠 타입 %(ct_id)s 객체는 관련 모델이 없습니다"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "콘텐츠 타입 %(ct_id)s객체%(obj_id)s는 존재하지 않습니다."

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s객체들은 get_absolute_url() 메소드가 없습니다."
