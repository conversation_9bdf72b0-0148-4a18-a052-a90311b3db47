# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: Matas Dailyda <<EMAIL>>\n"
"Language-Team: Lithuanian (http://www.transifex.com/django/django/language/"
"lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

msgid "Content Types"
msgstr "Turinio tipai"

msgid "python model class name"
msgstr "python modelio klasės vardas"

msgid "content type"
msgstr "turinio tipas"

msgid "content types"
msgstr "turinio tipai"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Tūrinio tipo %(ct_id)s objektas neturi priskirto modelio"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Tūrinio tipo %(ct_id)s objektas %(obj_id)s neegzistuoja"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s objektai neturi get_absolute_url() metodo"
