# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011,2013-2017,2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-17 23:00+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: German (http://www.transifex.com/django/django/language/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Erweiterte Optionen"

msgid "Flat Pages"
msgstr "Flat Pages"

msgid "URL"
msgstr "Adresse (URL)"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Beispiel: „/about/contact/“. Wichtig: Am Anfang und Ende muss ein "
"Schrägstrich („/“) stehen."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Dieser Wert darf nur Buchstaben, Ziffern, Punkte, Unterstriche, "
"Bindestriche, Schrägstriche und Tilden enthalten."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Beispiel: „/about/contact“. Wichtig: Am Anfang muss ein Schrägstrich („/“) "
"stehen."

msgid "URL is missing a leading slash."
msgstr "Der URL fehlt ein vorangestellter Schrägstrich."

msgid "URL is missing a trailing slash."
msgstr "Der URL fehlt ein abschließender Schrägstrich."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""
"Flatpage mit der URL %(url)s ist für die Website %(site)s bereits vorhanden"

msgid "title"
msgstr "Titel"

msgid "content"
msgstr "Inhalt"

msgid "enable comments"
msgstr "Kommentare aktivieren"

msgid "template name"
msgstr "Name des Templates"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Beispiel: „flatpages/contact_page.html“. Wenn dieses Feld nicht gesetzt ist, "
"wird standardmäßig „flatpages/default.html“ benutzt."

msgid "registration required"
msgstr "Registrierung erforderlich"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Wenn hier ein Haken gesetzt ist, können nur angemeldete Benutzer die Seite "
"sehen."

msgid "sites"
msgstr "Websites"

msgid "flat page"
msgstr "Flat Page"

msgid "flat pages"
msgstr "Flat Pages"
