# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON>, 2011-2012,2014-2015,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-10-01 10:25+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Spanish (Argentina) (http://www.transifex.com/django/django/"
"language/es_AR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_AR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opciones avanzadas"

msgid "Flat Pages"
msgstr "Páginas Estáticas"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Ejemplo: “/about/contact/”. Asegúrese de usar barras '/' al principio y al "
"final."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Este valor debe contener solamente letras, números, puntos, guiones bajos, "
"guiones (-), barras (/) o tildes."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Ejemplo: “/about/contact”. Asegúrese de usar una barra ('/') al principio."

msgid "URL is missing a leading slash."
msgstr "A la URL le falta una / al principio."

msgid "URL is missing a trailing slash."
msgstr "A la URL le falta una / al final."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Ya existe una flatpage con url %(url)s para el sitio %(site)s"

msgid "title"
msgstr "título"

msgid "content"
msgstr "contenido"

msgid "enable comments"
msgstr "activar comentarios"

msgid "template name"
msgstr "nombre de plantilla"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Ejemplo: “flatpages/contact_page.html”. Si no lo proporciona, el sistema "
"usará “flatpages/default.html”."

msgid "registration required"
msgstr "debe estar registrado"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "Si está marcado, sólo los usuarios registrados podrán ver la página."

msgid "sites"
msgstr "sitios"

msgid "flat page"
msgstr "página estática"

msgid "flat pages"
msgstr "páginas estáticas"
