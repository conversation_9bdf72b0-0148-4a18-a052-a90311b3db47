# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON><PERSON>h <<EMAIL>>, 2019-2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <raj<PERSON><PERSON><EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-13 21:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Malayalam (http://www.transifex.com/django/django/language/"
"ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "സങ്കീർണ്ണമായ ഓപ്ഷനുകൾ"

msgid "Flat Pages"
msgstr "ഫ്ലാറ്റ് പേജുകൾ"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"ഉദാഹരണം: “/about/contact/”.  തുടക്കത്തിലും അവസാനവും സ്ലാഷുകൾ ഉണ്ടെന്ന് ഉറപ്പുവരുത്തുക."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"ഇതിൽ അക്ഷരങ്ങൾ, നമ്പറുകൾ, കുത്തുകൾ, അണ്ടർസ്കോറുകൾ, ഡാഷുകൾ, സ്ലാഷുകൾ അതുമല്ലെങ്കിൽ ടിൽഡുകൾ "
"എന്നിവ മാത്രമേ അനുവദിക്കുള്ളൂ."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "ഉദാഹരണം: “/about/contact”.  തുടക്കത്തിൽ ഒരു സ്ലാഷ് ഉണ്ടെന്ന് ഉറപ്പുവരുത്തുക."

msgid "URL is missing a leading slash."
msgstr "URLന്റെ മുന്‍വശത്ത് ഒരു സ്ലാഷിന്റെ കുറവുണ്ട്."

msgid "URL is missing a trailing slash."
msgstr "URLന്റെ പിന്‍വശത്ത് ഒരു സ്ലാഷിന്റെ കുറവുണ്ട്."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(site)s എന്ന സൈറ്റിനു %(url)s എന്ന url ഉള്ള ഫ്ലാറ്റ്പേജ് നിലവിലുണ്ട്"

msgid "title"
msgstr "ശീര്‍ഷകം"

msgid "content"
msgstr "ഉള്ളടക്കം"

msgid "enable comments"
msgstr "അഭിപ്രായങ്ങള്‍ അനുവദിക്കുക"

msgid "template name"
msgstr "ടെമ്പ്ലേറ്റിന്റെ പേര്"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"ഉദാ: 'flatpages/contact_page.html'. ഇതു നല്കിയില്ലെങ്കില്‍, പകരമായി 'flatpages/"
"default.html' ആയിരിക്കും ഉപയോഗിക്കുക."

msgid "registration required"
msgstr "രജിസ്ട്രേഷന്‍ ആവശ്യമാണ്"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "ഇതു ടിക് ചെയ്താല്‍ പിന്നെ ലോഗ്-ഇന്‍ ചെയ്ത യൂസര്‍ക്കു മാത്രമേ ഈ പേജ് കാണാന്‍ കഴിയൂ."

msgid "sites"
msgstr "സൈറ്റുകൾ"

msgid "flat page"
msgstr "ഫ്ളാറ്റ് പേജ്"

msgid "flat pages"
msgstr "ഫ്ളാറ്റ് പേജുകള്‍"
