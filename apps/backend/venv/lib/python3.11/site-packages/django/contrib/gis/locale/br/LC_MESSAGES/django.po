# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# Irriep <PERSON>la <PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2019-03-12 14:10+0000\n"
"Last-Translator: Irriep <PERSON>la <PERSON> <<EMAIL>>\n"
"Language-Team: Breton (http://www.transifex.com/django/django/language/br/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: br\n"
"Plural-Forms: nplurals=5; plural=((n%10 == 1) && (n%100 != 11) && (n%100 !"
"=71) && (n%100 !=91) ? 0 :(n%10 == 2) && (n%100 != 12) && (n%100 !=72) && (n"
"%100 !=92) ? 1 :(n%10 ==3 || n%10==4 || n%10==9) && (n%100 < 10 || n% 100 > "
"19) && (n%100 < 70 || n%100 > 79) && (n%100 < 90 || n%100 > 99) ? 2 :(n != 0 "
"&& n % 1000000 == 0) ? 3 : 4);\n"

msgid "GIS"
msgstr ""

msgid "The base GIS field."
msgstr ""

msgid ""
"The base Geometry field -- maps to the OpenGIS Specification Geometry type."
msgstr ""

msgid "Point"
msgstr "Pik"

msgid "Line string"
msgstr "Chadenn segmant"

msgid "Polygon"
msgstr "Poligon"

msgid "Multi-point"
msgstr "Liespik"

msgid "Multi-line string"
msgstr "Lies chadenn segmant"

msgid "Multi polygon"
msgstr "Liespoligon"

msgid "Geometry collection"
msgstr "Dastumad mentoniezh"

msgid "Extent Aggregate Field"
msgstr ""

msgid "Raster Field"
msgstr ""

msgid "No geometry value provided."
msgstr "Talvoudegezh mentoniezh roet ebet."

msgid "Invalid geometry value."
msgstr "Talvoudegezh mentoniezh direizh."

msgid "Invalid geometry type."
msgstr "Doare mentoniezh direizh."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""
"Ur fazi a zo c'hoarvezet da vare treuzfurmadur an objed mentoniezhel e-barzh "
"ar vaezienn stumm mentoniezhel SRID."

msgid "Delete all Features"
msgstr ""

msgid "WKT debugging window:"
msgstr ""

msgid "Debugging window (serialized value)"
msgstr ""

msgid "No feeds are registered."
msgstr ""

#, python-format
msgid "Slug %r isn't registered."
msgstr "Neket enrollet ar \"slug\" %r."
