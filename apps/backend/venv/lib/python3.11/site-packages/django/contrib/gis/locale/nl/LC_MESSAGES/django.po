# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <ilja<PERSON><PERSON>@dreamsolution.nl>, 2015
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2015
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-17 08:54+0000\n"
"Last-Translator: Tonnes <<EMAIL>>\n"
"Language-Team: Dutch (http://www.transifex.com/django/django/language/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "GIS"
msgstr "GIS"

msgid "The base GIS field."
msgstr "Het basis-GIS-veld."

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr ""
"Het basis-Geometrie-veld – correspondeert met het Geometrie-type van de "
"OpenGIS-specificatie."

msgid "Point"
msgstr "Punt"

msgid "Line string"
msgstr "Tekenreeks"

msgid "Polygon"
msgstr "Polygoon"

msgid "Multi-point"
msgstr "Multipunt"

msgid "Multi-line string"
msgstr "Multi-tekenreeks"

msgid "Multi polygon"
msgstr "Multi-polygoon"

msgid "Geometry collection"
msgstr "Geometrie-verzameling"

msgid "Extent Aggregate Field"
msgstr "Gebieds-aggregatieveld"

msgid "Raster Field"
msgstr "Rasterveld"

msgid "No geometry value provided."
msgstr "Geen geometriewaarde opgegeven."

msgid "Invalid geometry value."
msgstr "Ongeldige geometriewaarde."

msgid "Invalid geometry type."
msgstr "Ongeldig geometrietype."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""
"Er is een fout opgetreden bij het omvormen van de geometrie naar de SRID van "
"het geometrieveld."

msgid "Delete all Features"
msgstr "Alle kenmerken verwijderen"

msgid "WKT debugging window:"
msgstr "WKT-debugvenster:"

msgid "Debugging window (serialized value)"
msgstr "Debugvenster (geserialiseerde waarde)"

msgid "No feeds are registered."
msgstr "Er zijn geen feeds geregistreerd."

#, python-format
msgid "Slug %r isn’t registered."
msgstr "Slug %r is niet geregistreerd."
