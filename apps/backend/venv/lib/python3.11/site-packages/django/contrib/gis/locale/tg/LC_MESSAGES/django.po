# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <sirius<PERSON><EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-05-15 01:08+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "GIS"
msgstr "GIS"

msgid "The base GIS field."
msgstr "Майдони асосии GIS"

msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr ""

msgid "Point"
msgstr ""

msgid "Line string"
msgstr ""

msgid "Polygon"
msgstr ""

msgid "Multi-point"
msgstr ""

msgid "Multi-line string"
msgstr ""

msgid "Multi polygon"
msgstr ""

msgid "Geometry collection"
msgstr ""

msgid "Extent Aggregate Field"
msgstr ""

msgid "Raster Field"
msgstr ""

msgid "No geometry value provided."
msgstr ""

msgid "Invalid geometry value."
msgstr ""

msgid "Invalid geometry type."
msgstr "Шакли нодурусти геометрӣ"

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""

msgid "Delete all Features"
msgstr ""

msgid "WKT debugging window:"
msgstr ""

msgid "Debugging window (serialized value)"
msgstr ""

msgid "No feeds are registered."
msgstr ""

#, python-format
msgid "Slug %r isn’t registered."
msgstr ""
