# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <v<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2013-2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 19:05+0000\n"
"Last-Translator: dekomote <<EMAIL>>\n"
"Language-Team: Macedonian (http://www.transifex.com/django/django/language/"
"mk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mk\n"
"Plural-Forms: nplurals=2; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : 1;\n"

msgid "Humanize"
msgstr "Хуманизирање"

msgid "th"
msgstr "ти"

msgid "st"
msgstr "ви"

msgid "nd"
msgstr "ри"

msgid "rd"
msgstr "ти"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f милион"
msgstr[1] "%(value).1f милиони"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s милион"
msgstr[1] "%(value)s милиони"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f милијарда"
msgstr[1] "%(value).1f милијарди"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s милијарда"
msgstr[1] "%(value)s милијарди"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f трилион"
msgstr[1] "%(value).1f трилиони"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s трилион"
msgstr[1] "%(value)s трилиони"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "%(value).1f  квадрилион"
msgstr[1] "%(value).1f  квадрилиони"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s квадрилион"
msgstr[1] "%(value)s квадрилиони"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "%(value).1f квинтилион"
msgstr[1] "%(value).1f квинтилиони"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s квинтилион"
msgstr[1] "%(value)s квинтилиони"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "%(value).1f секстилион"
msgstr[1] "%(value).1f секстилиони"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s секстилион"
msgstr[1] "%(value)s секстилиони"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "%(value).1f септилион"
msgstr[1] "%(value).1f септилиони"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s септилион"
msgstr[1] "%(value)s септилиони"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f октилион"
msgstr[1] "%(value).1f октилиони"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s октилион"
msgstr[1] "%(value)s октилиони"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "%(value).1f нонилион"
msgstr[1] "%(value).1f нонилиони"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s нонилион"
msgstr[1] "%(value)s нонилиони"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "%(value).1f децилион"
msgstr[1] "%(value).1f децилиони"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s децилион"
msgstr[1] "%(value)s децилиони"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "%(value).1f гугол"
msgstr[1] "%(value).1f гуголи"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s гугол"
msgstr[1] "%(value)s гуголи"

msgid "one"
msgstr "еден"

msgid "two"
msgstr "две"

msgid "three"
msgstr "три"

msgid "four"
msgstr "четри"

msgid "five"
msgstr "пет"

msgid "six"
msgstr "шест"

msgid "seven"
msgstr "седум"

msgid "eight"
msgstr "осум"

msgid "nine"
msgstr "девет"

msgid "today"
msgstr "денес"

msgid "tomorrow"
msgstr "утре"

msgid "yesterday"
msgstr "вчера"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s ago"
msgstr "пред %(delta)s"

msgid "now"
msgstr "сега"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "пред %(count)s секундa"
msgstr[1] "пред %(count)s секунди"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "пред %(count)s минутa"
msgstr[1] "пред %(count)s минути"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "пред %(count)s час"
msgstr[1] "пред %(count)s часа"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s from now"
msgstr "%(delta)s од сега"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "за %(count)s секундa од сега"
msgstr[1] "за %(count)s секунди од сега"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "за %(count)s минута од сега"
msgstr[1] "за %(count)s минути од сега"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "за %(count)s час од сега"
msgstr[1] "за %(count)s часа од сега"
