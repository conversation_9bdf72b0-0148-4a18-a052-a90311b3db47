# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-10-09 12:26+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Ka<PERSON><PERSON> (http://www.transifex.com/django/django/language/"
"kab/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: kab\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Iceggiɛen"

msgid "site"
msgstr "asmel"

msgid "redirect from"
msgstr "yettwaceggeɛ seg"

msgid ""
"This should be an absolute path, excluding the domain name. Example: '/"
"events/search/'."
msgstr ""

msgid "redirect to"
msgstr "aceggeɛ ar"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"'http://'."
msgstr ""

msgid "redirect"
msgstr "aceggeɛ"

msgid "redirects"
msgstr "iceggiɛen"
