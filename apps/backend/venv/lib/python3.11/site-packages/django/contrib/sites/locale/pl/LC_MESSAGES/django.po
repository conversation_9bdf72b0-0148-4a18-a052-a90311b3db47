# This file is distributed under the same license as the Django package.
#
# Translators:
# angularcircle, 2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2014
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/django/django/language/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n"
"%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n"
"%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

msgid "Sites"
msgstr "Strony"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Nazwa domeny nie może zawierać spacji ani tabulatorów."

msgid "domain name"
msgstr "nazwa domeny"

msgid "display name"
msgstr "nazwa wyświetlana"

msgid "site"
msgstr "strona"

msgid "sites"
msgstr "strony"
