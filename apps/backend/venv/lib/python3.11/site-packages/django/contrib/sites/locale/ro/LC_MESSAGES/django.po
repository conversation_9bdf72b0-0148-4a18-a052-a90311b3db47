# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2011
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <razvan.s<PERSON><PERSON><PERSON>@gmail.com>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <razvan.ste<PERSON><PERSON>@gmail.com>\n"
"Language-Team: Romanian (http://www.transifex.com/django/django/language/"
"ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?"
"2:1));\n"

msgid "Sites"
msgstr "Pagini web"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Numele de domeniu nu poate conține spații sau tab-uri."

msgid "domain name"
msgstr "nume domeniu"

msgid "display name"
msgstr "nume afișat"

msgid "site"
msgstr "pagină web"

msgid "sites"
msgstr "pagini web"
