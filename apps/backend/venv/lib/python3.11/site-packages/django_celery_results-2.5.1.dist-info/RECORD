django_celery_results-2.5.1.dist-info/AUTHORS,sha256=76xMxZHLjE1Oh_WrSawvvP-OsHfZ8lcnf8HxtAaCPzg,3190
django_celery_results-2.5.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_celery_results-2.5.1.dist-info/LICENSE,sha256=Hl5VSZEcAysExfovSxLF9akg7mI5Q1_xdqR_8G3spyM,2693
django_celery_results-2.5.1.dist-info/METADATA,sha256=F_DhIA9rkVN22OX52sxHyx532HGpZGjnWrkBlCNBB38,6107
django_celery_results-2.5.1.dist-info/RECORD,,
django_celery_results-2.5.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_celery_results-2.5.1.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
django_celery_results-2.5.1.dist-info/entry_points.txt,sha256=mwTrbStFAmYpX2kA9YD-xCk5nrh_WkH_bnX2kU2Gorc,144
django_celery_results-2.5.1.dist-info/top_level.txt,sha256=jGgO_NNWPKOquUSnX4N45E1DrB569qegND1ZHeD4_b8,22
django_celery_results/__init__.py,sha256=aosAs3RNurPp55gGYe8iz-iGTHDPSNPkp5jSnWRPpek,1007
django_celery_results/__pycache__/__init__.cpython-311.pyc,,
django_celery_results/__pycache__/admin.cpython-311.pyc,,
django_celery_results/__pycache__/apps.cpython-311.pyc,,
django_celery_results/__pycache__/managers.cpython-311.pyc,,
django_celery_results/__pycache__/models.cpython-311.pyc,,
django_celery_results/__pycache__/urls.cpython-311.pyc,,
django_celery_results/__pycache__/utils.cpython-311.pyc,,
django_celery_results/__pycache__/views.cpython-311.pyc,,
django_celery_results/admin.py,sha256=FRB6beT_2KY85OQTHvUhBc5VlCCRQljOwr4uYJzeH-8,2362
django_celery_results/apps.py,sha256=M-EArRi2YsQx8RrM7XeXqItBmnrbYwsjTW6vHhxm2fU,428
django_celery_results/backends/__init__.py,sha256=Nw2bR_8kiZ_krX8kKLSqJ_UuTTBqC-KRumuWtJUd1N8,117
django_celery_results/backends/__pycache__/__init__.cpython-311.pyc,,
django_celery_results/backends/__pycache__/cache.cpython-311.pyc,,
django_celery_results/backends/__pycache__/database.cpython-311.pyc,,
django_celery_results/backends/cache.py,sha256=_EACVZmhGtaMbS6v7SitWBFraieV7iYSkJRsmLHH3NU,1129
django_celery_results/backends/database.py,sha256=KovHYPvNtu0NYvhumNpgIBJm9G1g_jh-K0A2ix-mN7Q,11181
django_celery_results/locale/es/LC_MESSAGES/django.po,sha256=UiRXNh2WWUJWT_evLj_JF6y-8tZMW6kno1jwkik4KK8,4381
django_celery_results/locale/pt_BR/LC_MESSAGES/django.po,sha256=0CEv0Snx_GnjpF11x3Tk5oeuLvgVW7gNOoW89a72RKU,5177
django_celery_results/locale/ru/LC_MESSAGES/django.po,sha256=ynYk8n4B5hc6wMNIEh_NDk7vzoOCRH89Ci7eGlffHXY,6714
django_celery_results/locale/zh_Hans/LC_MESSAGES/django.po,sha256=qfdzyOGdlRmyA2G4gA_xUjm9BlCyLmtmmsWlfyj-Sz0,5308
django_celery_results/managers.py,sha256=at7mo_oIWAGkX0iX-LzS5VJz4f2XEQKGtMc_is-lyzY,7798
django_celery_results/migrations/0001_initial.py,sha256=XAPjo3qvROZgp4zIT4IKaYlxGGk7p55ACywOPwSWE8Q,2585
django_celery_results/migrations/0002_add_task_name_args_kwargs.py,sha256=6JGhKQ-L-0DNQAxllMRnbYTiRjhQ1MCG6GS8xtjOA5g,872
django_celery_results/migrations/0003_auto_20181106_1101.py,sha256=VhECH8nXaDSioanV-LFi5uP6SSZ-1eAGjxP140v3DnE,506
django_celery_results/migrations/0004_auto_20190516_0412.py,sha256=PU7Fbu1I4enmpZuHqpQ48m4TBdlRjU3HEgBeXiYI1CE,4254
django_celery_results/migrations/0005_taskresult_worker.py,sha256=jsSFljk3GGPKmtoiwWHIAFLKfNHoK46lbLikJl12HWk,688
django_celery_results/migrations/0006_taskresult_date_created.py,sha256=CQyvmTdl3jna1q8H6Ipsw_P7sWdSH_sILeKki8YhitE,1399
django_celery_results/migrations/0007_remove_taskresult_hidden.py,sha256=FVSgtxdU03X9HK05-X8tFPS7yoTggIqnb0PuHp66SjA,423
django_celery_results/migrations/0008_chordcounter.py,sha256=ORN2yy9anyUczlPLCaQPGwSzNK1hLE346P4T8j-RItw,1403
django_celery_results/migrations/0009_groupresult.py,sha256=vXvyTICRMEV7_MJBsSghKOvWWnTqbmM-GWP-jusLceo,8003
django_celery_results/migrations/0010_remove_duplicate_indices.py,sha256=tP5-hn0waGcooNtz4neInJ_E3EjXtSYZzlgP5ZCOw54,1378
django_celery_results/migrations/0011_taskresult_periodic_task_name.py,sha256=QiUshPBoUtRoK9XVNyFPoBJ3ORjscljVsgnBexbdsHE,585
django_celery_results/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_celery_results/migrations/__pycache__/0001_initial.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0002_add_task_name_args_kwargs.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0003_auto_20181106_1101.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0004_auto_20190516_0412.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0005_taskresult_worker.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0006_taskresult_date_created.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0007_remove_taskresult_hidden.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0008_chordcounter.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0009_groupresult.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0010_remove_duplicate_indices.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/0011_taskresult_periodic_task_name.cpython-311.pyc,,
django_celery_results/migrations/__pycache__/__init__.cpython-311.pyc,,
django_celery_results/models.py,sha256=Tfc1hk4d0PbelpX4LuVIvdKNBq2_GBWUsJbomnPxwZ8,8100
django_celery_results/urls.py,sha256=P9eowoD7Xmkc34eFbPCht4NDZP0ZlthVb5G_DHWuvT4,2141
django_celery_results/utils.py,sha256=OrJ8Y5JNnkl3yE1o2HjUbwKS5CeaC648pdFiPIQY64M,477
django_celery_results/views.py,sha256=PLIxO7qMj0_11A0WFk9Gz-TBZG3sIN_5ZuhD5jsMMIM,1789
