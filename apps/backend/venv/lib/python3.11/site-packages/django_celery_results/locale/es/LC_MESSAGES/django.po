# Spanish translation strings for django-celery-results.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as django-celery-results.
# <<EMAIL>>, 2020.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version:\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-26 18:34+0100\n"
"PO-Revision-Date: 2020-02-26 20:25-0015\n"
"Last-Translator: <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: django_celery_results/admin.py:39
msgid "Parameters"
msgstr "Parámetros"

#: django_celery_results/admin.py:46
msgid "Result"
msgstr "Resultado"

#: django_celery_results/apps.py:15
msgid "Celery Results"
msgstr "Resultados Celery"

#: django_celery_results/models.py:28
msgid "Task ID"
msgstr "ID de Tarea"

#: django_celery_results/models.py:29
msgid "Celery ID for the Task that was run"
msgstr "ID de Celery para la tarea que fue ejecutada"

#: django_celery_results/models.py:32
msgid "Task Name"
msgstr "Nombre de Tarea"

#: django_celery_results/models.py:33
msgid "Name of the Task which was run"
msgstr "Nombre de la Tarea que fue ejecutada"

#: django_celery_results/models.py:36
msgid "Task Positional Arguments"
msgstr "Argumentos posicionales de la Tarea"

#: django_celery_results/models.py:37
msgid "JSON representation of the positional arguments used with the task"
msgstr "Representación JSON de los argumentos posicionales usados en la tarea"

#: django_celery_results/models.py:41
msgid "Task Named Arguments"
msgstr "Argumentos opcionales de la tarea"

#: django_celery_results/models.py:42
msgid "JSON representation of the named arguments used with the task"
msgstr "Representación JSON de los argumentos opcionales usados en la tarea"

#: django_celery_results/models.py:47
msgid "Task State"
msgstr "Estado de la Tarea"

#: django_celery_results/models.py:48
msgid "Current state of the task being run"
msgstr "Estado actual en el que se encuentra la tarea en ejecución"

#: django_celery_results/models.py:51
msgid "Worker"
msgstr "Worker"

#: django_celery_results/models.py:51
msgid "Worker that executes the task"
msgstr "Worker que ejecuta la tarea"

#: django_celery_results/models.py:55
msgid "Result Content Type"
msgstr "Content Type del resultado"

#: django_celery_results/models.py:56
msgid "Content type of the result data"
msgstr "Atributo Content type de los datos del resultado"

#: django_celery_results/models.py:59
msgid "Result Encoding"
msgstr "Codificación del resultado"

#: django_celery_results/models.py:60
msgid "The encoding used to save the task result data"
msgstr "La codificación usada para guardar los datos del resultado"

#: django_celery_results/models.py:63
msgid "Result Data"
msgstr "Datos del resultado"

#: django_celery_results/models.py:64
msgid ""
"The data returned by the task.  Use content_encoding and content_type fields"
" to read."
msgstr ""
"Datos devueltos por la tarea. Usa los campos content_encoding y content_type"
" para leerlos."

#: django_celery_results/models.py:68
msgid "Created DateTime"
msgstr "Fecha de creación"

#: django_celery_results/models.py:69
msgid "Datetime field when the task result was created in UTC"
msgstr "Fecha de creación de la tarea en UTC"

#: django_celery_results/models.py:72
msgid "Completed DateTime"
msgstr "Fecha de terminación"

#: django_celery_results/models.py:73
msgid "Datetime field when the task was completed in UTC"
msgstr "Fecha de completitud de la tarea en UTC"

#: django_celery_results/models.py:76
msgid "Traceback"
msgstr "Traceback"

#: django_celery_results/models.py:77
msgid "Text of the traceback if the task generated one"
msgstr "Texto del traceback si la tarea generó uno"

#: django_celery_results/models.py:80
msgid "Task Meta Information"
msgstr "Metadatos de la tarea"

#: django_celery_results/models.py:81
msgid ""
"JSON meta information about the task, such as information on child tasks"
msgstr ""
"Metainformación sobre la tarea en formato JSON, como la información de las "
"tareas hijas"

#: django_celery_results/models.py:91
msgid "task result"
msgstr "resultado de la tarea"

#: django_celery_results/models.py:92
msgid "task results"
msgstr "resultados de tareas"
