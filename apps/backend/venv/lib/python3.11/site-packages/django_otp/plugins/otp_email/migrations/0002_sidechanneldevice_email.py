# Generated by Django 3.0.2 on 2020-04-10 02:36

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('otp_email', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='emaildevice',
            name='key',
        ),
        migrations.AddField(
            model_name='emaildevice',
            name='token',
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name='emaildevice',
            name='valid_until',
            field=models.DateTimeField(default=django.utils.timezone.now, help_text='The timestamp of the moment of expiry of the saved token.'),
        ),
    ]
