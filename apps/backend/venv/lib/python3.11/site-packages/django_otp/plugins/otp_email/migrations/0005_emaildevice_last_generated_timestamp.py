# Generated by Django 3.2.16 on 2023-05-12 11:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('otp_email', '0004_throttling'),
    ]

    operations = [
        migrations.AddField(
            model_name='emaildevice',
            name='last_generated_timestamp',
            field=models.DateTimeField(blank=True, help_text='The last time a token was generated for this device.', null=True),
        ),
    ]
