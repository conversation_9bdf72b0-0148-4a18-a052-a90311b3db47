{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/login.css" %}" />
    {{ form.media }}

    <style type="text/css">
        input#id_otp_token,
        select#id_otp_device
        {
            clear: both;
            padding: 6px;
            width: 100%;
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
                    box-sizing: border-box;
        }
    </style>
{% endblock %}

{% block bodyclass %}{{ block.super }} login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content %}
{% if form.errors and not form.non_field_errors %}
<p class="errornote">
{% if form.errors.items|length == 1 %}{% trans "Please correct the error below." %}{% else %}{% trans "Please correct the errors below." %}{% endif %}
</p>
{% endif %}

{% if form.non_field_errors %}
{% for error in form.non_field_errors %}
<p class="errornote">
    {{ error }}
</p>
{% endfor %}
{% endif %}

<div id="content-main">

{% if user.is_authenticated %}
<p class="errornote">
{% blocktrans trimmed %}
    You are authenticated as {{ username }}, but are not authorized to
    access this page. Would you like to login to a different account?
{% endblocktrans %}
</p>
{% endif %}

<form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
  <div class="form-row">
    {{ form.username.errors }}
    {{ form.username.label_tag }} {{ form.username }}
  </div>
  <div class="form-row">
    {{ form.password.errors }}
    {{ form.password.label_tag }} {{ form.password }}
    <input type="hidden" name="next" value="{{ next }}" />
  </div>
  {% if form.get_user %}
  <div class="form-row">
    {{ form.otp_device.errors }}
    <label for="id_otp_device">{% trans 'OTP Device:' %}</label> {{ form.otp_device }}
  </div>
  {% endif %}
  <div class="form-row">
    {{ form.otp_token.errors }}
    <label for="id_otp_token" class="required">{% trans 'OTP Token:' %}</label> {{ form.otp_token }}
  </div>
  {% url 'admin_password_reset' as password_reset_url %}
  {% if password_reset_url %}
  <div class="password-reset-link">
    <a href="{{ password_reset_url }}">{% trans 'Forgotten your password or username?' %}</a>
  </div>
  {% endif %}
  <div class="submit-row">
    <label>&nbsp;</label><input type="submit" value="{% trans 'Log in' %}" />
    {% if form.get_user %}
    <label>&nbsp;</label><input type="submit" name="otp_challenge" value="{% trans 'Get OTP Challenge' %}" />
    {% endif %}
  </div>
</form>

<script type="text/javascript">
document.getElementById('id_username').focus()
</script>
</div>
{% endblock %}
