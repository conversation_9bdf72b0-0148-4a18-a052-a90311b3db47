Maintainers
===========

- <PERSON> (`barm <https://github.com/barm>`_)
- <PERSON> (`ThePumpingLemma <https://github.com/ThePumpingLemma>`_)
- <PERSON> (`kseever <https://github.com/kseever>`_)
- <PERSON> (`macro1 <https://github.com/macro1>`_)
- <PERSON> (`rossmechanic <https://github.com/rossmechanic>`_)
- <PERSON> (`treyhunner <https://github.com/treyhunner>`_)

Authors
=======

- <PERSON><PERSON> (`uadnan <https://github.com/uadnan>`_)
- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON> (`AmandaCLNg <https://github.com/AmandaCLNg>`_)
- <PERSON><PERSON> (`<PERSON><PERSON> <https://github.com/amartis>`_)
- <PERSON> (`blawson <https://github.com/blawson>`_)
- <PERSON> (`bma<PERSON><PERSON><PERSON> <https://github.com/bma<PERSON>aey>`_)
- <PERSON><PERSON><PERSON> Persaud (`bheesham <https://github.com/bheesham>`_)
- `bradford281 <https://github.com/bradford281>`_
- Brian <PERSON> (`barm <https://github.com/barm>`_)
- Brian Dixon
- Brian Mesick (`bmedx <https://github.com/bmedx>`_)
- Buddy Lindsey, Jr.
- Carlos San Emeterio (`Carlos-San-Emeterio <https://github.com/Carlos-San-Emeterio>`_)
- Christopher Broderick (`uhurusurfa <https://github.com/uhurusurfa>`_)
- Christopher Johns (`tyrantwave <https://github.com/tyrantwave>`_)
- Conrad (`creyD <https://github.com/creyD>`_)
- Corey Bertram
- Craig Maloney (`craigmaloney <https://github.com/craigmaloney>`_)
- Damien Nozay
- Daniel Gilge
- Daniel Levy
- Daniel Roschka
- Daniil Skrobov (`yetanotherape <https://github.com/yetanotherape>`_)
- David Grochowski (`ThePumpingLemma <https://github.com/ThePumpingLemma>`_)
- David Hite
- David Smith
- `ddabble <https://github.com/ddabble>`_
- Dmytro Shyshov (`xahgmah <https://github.com/xahgmah>`_)
- Edouard Richard (`vied12 <https://github.com/vied12>` _)
- Eduardo Cuducos
- Erik van Widenfelt (`erikvw <https://github.com/erikvw>`_)
- Fábio Capuano (`fabiocapsouza <https://github.com/fabiocapsouza`_)
- Filipe Pina (@fopina)
- Florian Eßer
- François Martin (`martinfrancois <https://github.com/martinfrancois>`_)
- Frank Sachsenheim
- George Kettleborough (`georgek <https://github.com/georgek>`_)
- George Vilches
- Gregory Bataille
- Grzegorz Bialy
- Guillermo Eijo (`guilleijo <https://github.com/guilleijo>`_)
- Hamish Downer
- Hans de Jong (`sult <https://github.com/sult>`_)
- Hanyin Zhang
- Héctor Durán (`hector97i <https://github.com/hector97i>`)
- Hernan Esteves (`sevetseh28 <https://github.com/sevetseh28>`_)
- Hielke Walinga (`hwalinga <https://github.com/hwalinga>`_)
- Hugo van Kemenade (`hugovk <https://github.com/hugovk>`_)
- Jack Cushman (`jcushman <https://github.com/jcushman>`_)
- Jake Howard (`RealOrangeOne <https://github.com/realorangeone>`_)
- James Muranga (`jamesmura <https://github.com/jamesmura>`_)
- James Pulec
- Jesse Shapiro
- Jihoon Baek (`jihoon796 <https://github.com/jihoon796>`_)
- Jim Gomez
- Jim King (`jeking3 <https://github.com/jeking3>`_)
- Joao Junior (`joaojunior <https://github.com/joaojunior>`_)
- Joao Pedro Francese
- `jofusa <https://github.com/jofusa>`_
- John Whitlock
- Jonathan Leroy
- Jonathan Loo (`alpha1d3d <https://github.com/alpha1d3d>`_)
- Jonathan Sanchez
- Jonathan Zvesper (`zvesp <https://github.com/zvesp>`_)
- Jordon Wing  (`jordonwii <https://github.com/jordonwii`_)
- Josh Fyne
- Josh Thomas (`joshuadavidthomas <https://github.com/joshuadavidthomas>`_)
- Keith Hackbarth
- Kevin Foster
- Kira (`kiraware <https://github.com/kiraware>`_)
- Klaas van Schelven
- Kris Neuharth
- Kyle Seever (`kseever <https://github.com/kseever>`_)
- Léni Gauffier (`legau <https://github.com/legau>`_)
- Leticia Portella
- Lucas Wiman
- Maciej "RooTer" Urbański
- Marcelo Canina (`marcanuy <https://github.com/marcanuy>`_)
- Mark Davidoff
- Martin Bachwerk
- Marty Alchin
- Matheus Cansian (`mscansian <https://github.com/mscansian>`_)
- Matthew Somerville (`dracos <https://github.com/dracos>`_)
- Mauricio de Abreu Antunes
- Maxim Zemskov (`MaximZemskov <https://github.com/MaximZemskov>`_)
- Micah Denbraver
- Michael England
- Miguel Vargas
- Mike Spainhower
- Muneeb Shahid (`muneeb706 <https://github.com/muneeb706>`_)
- Nathan Villagaray-Carski (`ncvc <https://github.com/ncvc>`_)
- Nianpeng Li
- Nick Träger
- Phillip Marshall
- Prakash Venkatraman (`dopatraman <https://github.com/dopatraman>`_)
- Rajesh Pappula
- Ray Logel
- Raynald de Lahondes
- Renaud Perrin (`leminaw <https://github.com/leminaw>`_)
- Roberto Aguilar
- Rod Xavier Bondoc
- Ross Lote
- Ross Mechanic (`rossmechanic <https://github.com/rossmechanic>`_)
- Ross Rogers
- Sergey Ozeranskiy (`ozeranskiy <https://github.com/ozeranskiy>`_)
- Shane Engelman
- Steeve Chailloux
- Stefan Borer (`sbor23 <https://github.com/sbor23>`_)
- Steven Buss (`sbuss <https://github.com/sbuss>`_)
- Steven Klass
- Thijs Kramer (`thijskramer <https://github.com/thijskramer>`_)
- Tim Schilling (`tim-schilling <https://github.com/tim-schilling>`_)
- Todd Wolfson (`twolfson <https://github.com/twolfson>`_)
- Tommy Beadle (`tbeadle <https://github.com/tbeadle>`_)
- Trey Hunner (`treyhunner <https://github.com/treyhunner>`_)
- Ulysses Vilela
- `vnagendra <https://github.com/vnagendra>`_
- `yakimka <https://github.com/yakimka>`_
- `Paulo Peres <https://github.com/PauloPeres>`_
- `Alex Todorov <https://github.com/atodorov>`_
- David Smith (`smithdc1 <https://github.com/smithdc1>`_)
- Shi Han Ng (`shihanng <https://github.com/shihanng>`_)
- `ddusi <https://github.com/ddusi>`_
- `DanialErfanian <https://github.com/DanialErfanian>`_
- `Sridhar Marella <https://github.com/sridhar562345>`_

Background
==========

This code originally comes from Pro Django, published by Apress, Inc.
in December 2008. The author of the book and primary author
of the code is Marty Alchin <<EMAIL>>, who
may be found online at <http://martyalchin.com/>.

As part of the technical review process, additional code
modifications were provided by the technical reviewer,
George Vilches <<EMAIL>>.

This code was originally extended, licensed, and improved by
Corey Bertram <<EMAIL>> with the permission of Marty Alchin.
