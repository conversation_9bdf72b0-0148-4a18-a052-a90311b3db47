from typing import Optional

from .. import Provider as AddressProvider


class Provider(AddressProvider):
    city_formats = ("{{first_name}}",)
    city_prefixes = ("ք.",)
    city_suffixes = ("",)
    street_prefixes = ("փողոց", "պողոտա")
    street_suffixes = ("",)
    village_prefixes = ("գ.",)

    address_formats = (
        "{{city_prefix}} {{city}}, {{street_name}} {{building_number}}",
        "{{city_prefix}} {{city}}, {{street_name}} {{building_number}}, {{secondary_address}}",
        "{{city_prefix}} {{city}}, {{postcode}}, {{street_name}} {{building_number}}",
        "{{city_prefix}} {{city}}, {{postcode}}, {{street_name}} {{building_number}}, {{secondary_address}}",
        "{{village_prefix}} {{village}}, {{state}}ի մարզ, {{postcode}}, {{street_name}} {{building_number}}",
    )
    building_number_formats = ("#", "##", "###")
    postcode_formats = ("0###", "1###", "2###", "3###", "4###")
    secondary_address_formats = ("բն. #", "բն. ##", "բն. ##")
    street_address_formats = ("{{street_name}} {{building_number}}",)
    street_name_formats = ("{{street}}",)

    # Source: List of cities and towns in Armenia (Wikipedia)
    # https://en.wikipedia.org/wiki/List_of_cities_and_towns_in_Armenia
    cities = (
        "Աբովյան",
        "Ագարակ",
        "Ալավերդի",
        "Ախթալա",
        "Այրում",
        "Աշտարակ",
        "Ապարան",
        "Արարատ",
        "Արթիկ",
        "Արմավիր",
        "Արտաշատ",
        "Բերդ",
        "Բյուրեղավան",
        "Գավառ",
        "Գյումրի",
        "Գորիս",
        "Դաստակերտ",
        "Դիլիջան",
        "Եղեգնաձոր",
        "Եղվարդ",
        "Երևան",
        "Վաղարշապատ",
        "Թալին",
        "Թումանյան",
        "Իջևան",
        "Ծաղկաձոր",
        "Կապան",
        "Հրազդան",
        "Ճամբարակ",
        "Մասիս",
        "Մարալիկ",
        "Մարտունի",
        "Մեծամոր",
        "Մեղրի",
        "Նոր Հաճն",
        "Նոյեմբերյան",
        "Շամլուղ",
        "Չարենցավան",
        "Ջերմուկ",
        "Սիսիան",
        "Սպիտակ",
        "Ստեփանավան",
        "Սևան",
        "Վայք",
        "Վանաձոր",
        "Վարդենիս",
        "Վեդի",
        "Տաշիր",
        "Քաջարան",
    )

    # Source: Wikipedia's list of sovereign states
    # https://en.wikipedia.org/wiki/List_of_sovereign_states
    countries = (
        "Աֆղանստան",
        "Ալբանիա",
        "Ալժիր",
        "Ամերիկյան Սամոա",
        "Անդորրա",
        "Անգոլա",
        "Անգիլիա",
        "Անտարկտիկա",
        "Անտիգուա և Բարբուդա",
        "Արգենտինա",
        "Հայաստան",
        "Արուբա",
        "Ավստրալիա",
        "Ավստրիա",
        "Ադրբեջան",
        "Բահամներ",
        "Բահրեյն",
        "Բանգլադեշ",
        "Բարբադոս",
        "Բելառուս",
        "Բելգիա",
        "Բելիզ",
        "Բենին",
        "Բերմուդա",
        "Բութան",
        "Բոլիվիա",
        "Բոսնիա և Հերցեգովինա",
        "Բոտսվանա",
        "Բրազիլիա",
        "Բրունեյ Դարուսսալամ",
        "Բուլղարիա",
        "Բուրկինա Ֆասո",
        "Բուրունդի",
        "Կամբոջա",
        "Կամերուն",
        "Կանադա",
        "Կաբո Վերդե",
        "Կայման Կղզիներ",
        "Կենտրոնական Աֆրիկյան Հանրապետություն",
        "Չադ",
        "Չիլի",
        "Չինաստան",
        "Սուրբ Ծննդյան Կղզի",
        "Կոկոս Կղզիներ",
        "Կոլումբիա",
        "Կոմորյան Կղզիներ",
        "Կոնգո",
        "Կուկի Կղզիներ",
        "Կոստա Ռիկա",
        "Կոտ դ'Իվուար",
        "Խորվաթիա",
        "Կուբա",
        "Կիպրոս",
        "Չեխիայի Հանրապետություն",
        "Դանիա",
        "Ջիբութի",
        "Դոմինիկա",
        "Դոմինիկյան Հանրապետություն",
        "Էկվադոր",
        "Եգիպտոս",
        "Սալվադոր",
        "Հասարակածային Գվինեա",
        "Էրիտրեա",
        "Էստոնիա",
        "Եթովպիա",
        "Ֆարերյան Կղզիներ",
        "Ֆոլկլենդյան Կղզիներ",
        "Ֆիջի",
        "Ֆինլանդիա",
        "Ֆրանսիա",
        "Ֆրանսիական Գվիանա",
        "Ֆրանսիական Պոլինեզիա",
        "Ֆրանսիական Հարավային Տարածքներ",
        "Գաբոն",
        "Գամբիա",
        "Վրաստան",
        "Գերմանիա",
        "Գանա",
        "Ջիբրալթար",
        "Հունաստան",
        "Գրենլանդիա",
        "Գրենադա",
        "Գվադելուպա",
        "Գուամ",
        "Գվատեմալա",
        "Գերնսի",
        "Գվինեա",
        "Գվինեա Բիսաու",
        "Գայանա",
        "Հաիթի",
        "Վատիկան",
        "Հոնդուրաս",
        "Հոնգ Կոնգ",
        "Հունգարիա",
        "Իսլանդիա",
        "Հնդկաստան",
        "Ինդոնեզիա",
        "Իրան",
        "Իրաք",
        "Իռլանիա",
        "Իսրայել",
        "Իտալիա",
        "Ջամայկա",
        "Ճապոնիա",
        "Հորդանան",
        "Ղազախստան",
        "Քենիա",
        "Կիրիբատի",
        "Հյուսիսային Կորեա",
        "Հարավային Կորեա",
        "Կոսովո",
        "Քուվեյթ",
        "Ղրղզստան",
        "Լաոս",
        "Լատվիա",
        "Լիբանան",
        "Լեսոտո",
        "Լիբերիա",
        "Լիբիական Արաբական Ջամահիրիա",
        "Լիխտենշտեյն",
        "Լիտվա",
        "Լյուքսեմբուրգ",
        "Մակաո",
        "Հյուսիսային Մակեդոնիա",
        "Մադագասկար",
        "Մալավի",
        "Մալազիա",
        "Մալդիվներ",
        "Մալի",
        "Մալթա",
        "Մարշալյան Կղզիներ",
        "Մարտինիկ",
        "Մավրիտանիա",
        "Մավրիկիոս",
        "Մայոտտե",
        "Մեքսիկա",
        "Միկրոնեզիա",
        "Մոլդովա",
        "Մոնակո",
        "Մոնղոլիա",
        "Չեռնոգորիա",
        "Մոնսերատ",
        "Մարոկկո",
        "Մոզամբիկ",
        "Մյանմա",
        "Նամիբիա",
        "Նաուրու",
        "Նեպալ",
        "Նիդեռլանդական Անտիլներ",
        "Նիդերլանդներ",
        "Նոր Կալեդոնիա",
        "Նոր Զելանդիա",
        "Նիկարագուա",
        "Նիգեր",
        "Նիգերիա",
        "Նիուե",
        "Նորֆոլկ Կղզի",
        "Հյուսիսային Մարիանյան Կղզիներ",
        "Նորվեգիա",
        "Օման",
        "Պակիստան",
        "Պալաու",
        "Պաղեստին",
        "Պանամա",
        "Պապուա Նոր Գվինեա",
        "Պարագվայ",
        "Պերու",
        "Ֆիլիպիններ",
        "Պիտկիրնյան Կղզիներ",
        "Լեհաստան",
        "Պորտուգալիա",
        "Պուերտո Ռիկո",
        "Կատար",
        "Ռումինիա",
        "Ռուսաստանի Դաշնություն",
        "Ռուանդա",
        "Սուրբ Բարդուղիմեոս",
        "Սուրբ Հելենա",
        "Սենտ Կիտս և Նևիս",
        "Սուրբ Լուչիա",
        "Սուրբ Մարտին",
        "Սեն Պիեռ և Միկելոն",
        "Սենթ Վինսենթ և Գրենադիններ",
        "Սամոա",
        "Սան Մարինո",
        "Սաուդյան Արաբիա",
        "Սենեգալ",
        "Սերբիա",
        "Սեյշելներ",
        "Սիերա Լեոնե",
        "Սինգապուր",
        "Սլովակիա",
        "Սլովենիա",
        "Սողոմոնյան Կղզիներ",
        "Սոմալի",
        "Հարավային Աֆրիկա",
        "Իսպանիա",
        "Շրի Լանկա",
        "Սուդան",
        "Սուրինամ",
        "Սվալբարդ և Յան Մայենյան Կղզիներ",
        "Սվազիլենդ",
        "Շվեդիա",
        "Շվեյցարիա",
        "Սիրիայի Արաբական Հանրապետություն",
        "Թայվան",
        "Տաջիկստան",
        "Տանզանիա",
        "Թաիլանդ",
        "Տոգո",
        "Տոկելաու",
        "Տոնգա",
        "Տրինիդադ և Տոբագո",
        "Թունիս",
        "Թուրքիա",
        "Թուրքմենստան",
        "Տուվալու",
        "Ուգանդա",
        "Ուկրաինա",
        "Արաբական Միացյալ Էմիրություններ",
        "Մեծ Բրիտանիա",
        "Ամերիկայի Միացյալ Նահանգներ",
        "Ուրուգվայ",
        "Ուզբեկստան",
        "Վենեսուելա",
        "Վիետնամ",
        "Ուոլիս և Ֆուտունա",
        "Արևմտյան Սահարա",
        "Եմեն",
        "Զամբիա",
        "Զիմբաբվե",
    )

    # Source: Administrative divisions of Armenia (Wikipedia)
    # https://en.wikipedia.org/wiki/Administrative_divisions_of_Armenia
    states = (
        "Արագածոտն",
        "Արարատ",
        "Արմավիր",
        "Գեղարքունիք",
        "Լոռի",
        "Կոտայք",
        "Շիրակ",
        "Սյունիք",
        "Տավուշ",
        "Վայոց Ձոր",
    )

    states_abbr = (
        "ԱԳ",
        "ԱՐ",
        "ԱՄ",
        "ԳՂ",
        "ԼՌ",
        "ԿՏ",
        "ՇԿ",
        "ՍՅ",
        "ՎՁ",
        "ՏՎ",
    )

    # Source: Postal codes in Armenia (Wikipedia)
    # https://en.wikipedia.org/wiki/Postal_codes_in_Armenia
    states_postcode = {
        "ԱԳ": (200, 599),
        "ԱՐ": (600, 899),
        "ԱՄ": (900, 1199),
        "ԳՂ": (1200, 1699),
        "ԼՌ": (1700, 2199),
        "ԿՏ": (2200, 2599),
        "ՇԿ": (2600, 3199),
        "ՍՅ": (3200, 3599),
        "ՎՁ": (3600, 3899),
        "ՏՎ": (3900, 4299),
    }

    streets = (
        "Ազատության",
        "Արշակունյաց",
        "Արցախի",
        "Գայի",
        "Ծովակալ Իսակովի",
        "Կոմիտասի",
        "Հյուսիսային",
        "Մաշտոցի",
        "Մարշալ Բաղրամյան",
        "Մյասնիկյան",
        "Սայաթ-Նովայի",
        "Տիգրան Մեծի",
        "Աբելյան",
        "Աբովյան",
        "Ագաթանգեղոսի",
        "Ազատամարտիկների",
        "Աթենքի",
        "Աթոյան",
        "Ալեք Մանուկյան",
        "Ալիխանյան",
        "Աղայան",
        "Աղյուսագործների",
        "Ամիրյան",
        "Այասի",
        "Անտառային",
        "Անրի Վեռնոյի",
        "Ավագ Պետրոսյան",
        "Արամ Խաչատրյան",
        "Արամի",
        "Արգիշտիի",
        "Արմենակյան",
        "Բայրոնի",
        "Բարձրաբերդի",
        "Բելինսկու",
        "Բեյրութի",
        "Բուդապեշտի",
        "Բուռնազյան",
        "Բրյուսովի",
        "Գալոյան Եղբայրների",
        "Գարեգին Նժդեհի",
        "Գետառի",
        "Գլինկայի",
        "Գյուլբենկյան",
        "Գրիգոր Լուսավորչի",
        "Գրիգոր Հարությունյան",
        "Գրիգոր Տեր-Գրիգորյան",
        "Գևորգ Էմինի",
        "Գևորգ Հովսեփյան",
        "Գևորգ Քոչարի",
        "Դեղատան",
        "Դերենիկ Դեմիրճյան",
        "Եզնիկ Կողբացու",
        "Եկմալյան",
        "Երվանդ Քոչարի",
        "Զավարյան",
        "Զարոբյան",
        "Զաքյան",
        "Էրեբունու",
        "Թաիրովի",
        "Թամանյան",
        "Թորամանյան",
        "Թումանյան",
        "Իսահակյան",
        "Իսրայելյան",
        "Իտալիայի",
        "Լամբրոնի",
        "Լենինգրադյան",
        "Լեոյի",
        "Լեոնիդ Ազգալդյան",
        "Լեռ Կամսարի",
        "Լիսինյան",
        "Լոմոնոսովի",
        "Լոռիս-Մելիքովի",
        "Լուսինյանց",
        "Խանզադյան",
        "Խանջյան",
        "Ծատուրյան",
        "Ծխախոտագործների",
        "Կալենցի",
        "Կասյան",
        "Կարեն Դեմիրճյան",
        "Կիևյան",
        "Կոնդի",
        "Կորի",
        "Կորյունի",
        "Կուստոյի",
        "Կռիլովի",
        "Հալաբյան",
        "Հակոբ Հակոբյան",
        "Հայրիկ Մուրադյան",
        "Հանրապետության",
        "Հերացու",
        "Հին Երևանցու",
        "Հնդկաստանի",
        "Հովհաննես Կոզեռնի",
        "Հրանտ Շահինյան",
        "Հրաչյա Քոչարի",
        "Ձորափի",
        "Ղազար Փարպեցու",
        "Մայիսյան",
        "Մարկ Գրիգորյան",
        "Մարտի 8-ի",
        "Մելիք-Ադամյան",
        "Միչուրինի",
        "Մհեր Մկրտչյան",
        "Մոնթե Մելքոնյան",
        "Մոսկովյան",
        "Մովսես Խորենացու",
        "Մուրացանի",
        "Նալբանդյան",
        "Նար-Դոսի",
        "Նորքի",
        "Շարա Տալյան",
        "Շարիմանյան",
        "Շուկայի",
        "Ոսկերիչների",
        "Չայկովսկու",
        "Չարենցի",
        "Չեռնիշևսկու",
        "Պարոնյան",
        "Պետրոս Ադամյան",
        "Պուշկինի",
        "Պռոշյան",
        "Պրահայի",
        "Ռոստոմի",
        "Ռոստովյան",
        "Ռուսթավելու",
        "Սասունցի Դավթի",
        "Սարալանջի",
        "Սարմենի",
        "Սարյան",
        "Սեբաստիայի",
        "Սերգեյ Փարաջանովի",
        "Սիլվա Կապուտիկյան",
        "Սիմեոն Երևանցու",
        "Սիսվանի",
        "Սոսեի",
        "Սուվորովի",
        "Սուրբ Հովհաննեսի",
        "Սպենդիարյան",
        "Ստեփան Զորյան",
        "Սևանի",
        "Վազգեն Սարգսյան",
        "Վահրամ Փափազյան",
        "Վաղարշյան",
        "Վարդան Աճեմյան",
        "Վարդանանց",
        "Վերֆելի",
        "Վրացյան",
        "Տարսոնի",
        "Տերյան",
        "Տոլստոյի",
        "Տպագրիչների",
        "Ցախի",
        "Փավստոս Բուզանդի",
        "Քաջազնունու",
        "Քոչինյան",
        "Քրիստափորի",
        "Օստրովսկու",
        "Օրբելի Եղբայրների",
        "Ֆիզկուլտուրնիկների",
        "Ֆիրդուսու",
        "Ֆրիկի",
    )

    # Source: Villages in Armenia (Wikipedia)
    # http://www.armeniapedia.org/wiki/Armenian_Towns_and_Villages
    villages = (
        "Ագարակ",
        "Անտառուտ",
        "Բերքառատ",
        "Գեղաձոր",
        "Գետափ",
        "Զովասար",
        "Լեռնապար",
        "Լուսագյուղ",
        "Կաթնաղբյուր",
        "Կաքավաձոր",
        "Հացաշեն",
        "Նորաշեն",
        "Շենավան",
        "Ոսկեվազ",
        "Ցամաքասար",
        "Այգեզարդ",
        "Բարձրաշեն",
        "Բերքանուշ",
        "Լանջանիստ",
        "Լուսաշող",
        "Ջրաշեն",
        "Քաղցրաշեն",
        "Այգեկ",
        "Առատաշեն",
        "Բամբակաշատ",
        "Գեղակերտ",
        "Լեռնամերձ",
        "Ծաղկալանջ",
        "Հացիկ",
        "Մերձավան",
        "Քարակերտ",
        "Անտառամեջ",
        "Արծվաշեն",
        "Գեղաքար",
        "Զովաբեր",
        "Լանջաղբյուր",
        "Շատջրեք",
        "Այգեհատ",
        "Դարպաս",
        "Լեռնահովիտ",
        "Հարթագյուղ",
        "Պաղաղբյուր",
        "Սարամեջ",
        "Քարաձոր",
        "Զովք",
        "Լեռնանիստ",
        "Մեղրաձոր",
        "Այգաբաց",
        "Թավշուտ",
        "Լանջիկ",
        "Կարմրավան",
        "Հայկասար",
        "Նահապետավան",
        "Վարդաղբյուր",
        "Քարաբերդ",
        "Արծվանիկ",
        "Բարձրավան",
        "Կաղնուտ",
        "Հացավան",
        "Նռնաձոր",
        "Սառնակունք",
        "Աղավնաձոր",
        "Սևաժայռ",
        "Վերնաշեն",
        "Այգեհովիտ",
        "Արծվաբերդ",
        "Բերքաբեր",
        "Գետահովիտ",
        "Ծաղկավան",
        "Հաղթանակ",
        "Ոսկեպար",
        "Սարիգյուղ",
    )

    def city(self) -> str:
        """
        :example: 'Բյուրեղավան'
        """
        return self.random_element(self.cities)

    def city_prefix(self) -> str:
        """
        :example: 'ք.'
        """
        return self.random_element(self.city_prefixes)

    def postcode(self) -> str:
        """
        :example: '3159'
        """
        return "%04d" % self.generator.random.randint(200, 4299)

    def postcode_in_state(self, state_abbr: Optional[str] = None) -> str:
        """
        :example: '4703'
        """
        if state_abbr is None:
            state_abbr = self.random_element(self.states_abbr)

        if state_abbr in self.states_abbr:
            postcode = "%d" % (
                self.generator.random.randint(
                    self.states_postcode[state_abbr][0],
                    self.states_postcode[state_abbr][1],
                )
            )

            if len(postcode) == 3:
                postcode = "0%s" % postcode

            return postcode

        else:
            raise Exception("State Abbreviation not found in list")

    def secondary_address(self) -> str:
        """
        :example: 'բն. 49'
        """
        return self.numerify(self.random_element(self.secondary_address_formats))

    def administrative_unit(self) -> str:
        """
        :example: 'Կոտայք'
        """
        return self.random_element(self.states)

    state = administrative_unit

    def state_abbr(self) -> str:
        """
        :example: 'ՎՁ'
        """
        return self.random_element(self.states_abbr)

    def street(self) -> str:
        """
        :example: 'Ոսկերիչների'
        """
        return self.random_element(self.streets)

    def street_prefix(self) -> str:
        """
        :example: 'փողոց'
        """
        return self.random_element(self.street_prefixes)

    def village(self) -> str:
        """
        :example: 'Ոսկեվազ'
        """
        return self.random_element(self.villages)

    def village_prefix(self) -> str:
        """
        :example: 'գ.'
        """
        return self.random_element(self.village_prefixes)
