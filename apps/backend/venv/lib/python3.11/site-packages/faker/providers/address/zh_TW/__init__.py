from .. import Provider as AddressProvider


class Provider(AddressProvider):
    city_formats = ("{{city_name}}", "{{city_name}}{{city_name_suffix}}")
    building_number_formats = ("%號", "%#號", "%##號")
    postcode_formats = ("%####", "%##")
    section_formats = ("", "", "", "", "%段")
    street_address_formats = ("{{street_name}}{{street_name_suffix}}{{section_number}}{{building_number}}",)
    address_formats = ("{{postcode}} {{city}}{{street_address}}{{secondary_address}}",)
    secondary_address_formats = ("#樓", "之#")

    street_names = (
        "中正",
        "中山",
        "民生",
        "中華",
        "和平",
        "中興",
        "仁愛",
        "復興",
        "民族",
        "民權",
        "忠孝",
        "信義",
        "成功",
        "新興",
        "新生",
        "動物園",
        "淡水",
        "新生",
        "文化",
        "大同",
        "三民",
        "新生",
        "光復",
        "自強",
        "光明",
        "公園",
        "文山",
        "松山",
        "新店",
        "建國",
        "西門",
        "古亭",
        "迴龍",
        "中山",
        "新莊",
        "蘆洲",
        "永安",
        "四維",
        "大橋頭",
        "府中",
        "福德",
        "大同",
        "文昌",
        "土城",
        "博愛",
        "象山",
        "光華",
        "太平",
        "水源",
        "莒光",
        "廣慈",
        "大仁",
        "中央",
        "大智",
        "林森",
        "長春",
        "南",
        "劍南",
        "大坪",
        "國凱" "八德",
        "天母",
        "東興",
        "勝利",
        "頂福州",
        "東湖",
        "大勇",
        "民有",
        "自由",
        "長安",
        "明德",
        "大安",
        "龍山寺",
        "德",
        "忠義",
        "中和",
        "自由",
        "新埔",
        "永和",
        "延平",
        "正義",
        "五福",
        "華興",
        "育英",
        "平和",
        "福安",
        "小碧潭",
        "永寧",
        "育英",
        "興",
        "自立",
        "民享",
        "昆陽",
        "民治",
        "關渡",
        "學府",
        "奇岩",
        "紅樹林",
        "和街",
        "民富",
        "關渡",
        "北投",
        "石牌",
        "芝山",
        "景美",
        "士林",
        "劍潭",
        "雙連",
        "新北投",
        "萬隆",
    )

    street_suffixes = ("路", "街", "巷")

    cities = (
        "基隆",
        "台北",
        "新北",
        "桃園",
        "新竹",
        "新竹",
        "苗栗",
        "台中",
        "彰化",
        "南投",
        "雲林",
        "嘉義",
        "桃園",
        "台南",
        "高雄",
        "屏東",
        "台東",
        "花蓮",
        "宜蘭",
        "澎湖",
        "金門",
        "連江",
        "太保",
        "朴子",
        "馬公",
        "頭份",
        "臺東",
        "斗六",
        "員林",
        "竹北",
        "平鎮",
        "臺中",
        "八德",
        "板橋",
        "大里",
        "鳳山",
        "豐原",
        "蘆洲",
        "蘆竹",
        "三重",
        "樹林",
        "太平",
        "新營",
        "新營",
        "汐止",
        "楊梅",
        "永和",
        "永康",
        "中和",
        "中壢",
        "阿里山",
        "白沙",
        "褒忠",
        "北斗",
        "北竿",
        "北港",
        "卑南",
        "草屯",
        "梅山",
        "牡丹",
        "橫山",
        "光復",
        "關山",
        "古坑",
        "竹田",
    )

    city_suffixes = ("市", "縣")

    # from
    countries = (
        "阿爾巴尼亞",
        "剛果共和國",
        "阿爾及利亞",
        "丹麥",
        "安哥拉",
        "多明尼加",
        "安圭拉",
        "多米尼克",
        "阿根廷",
        "厄瓜多爾",
        "亞美尼亞",
        "埃及",
        "阿路巴",
        "薩爾瓦多",
        "澳大利亞",
        "厄利垂亞",
        "奧地利",
        "愛沙尼亞",
        "亞塞拜然",
        "衣索匹亞",
        "巴哈馬",
        "斐濟",
        "巴林",
        "芬蘭",
        "孟加拉",
        "法屬玻里尼西亞",
        "法國",
        "巴貝多",
        "加彭",
        "白俄羅斯",
        "喬治亞",
        "比利時",
        "德國",
        "貝里斯",
        "迦納",
        "貝南",
        "直布羅陀",
        "百慕達",
        "英國",
        "不丹",
        "希臘",
        "玻利維亞",
        "格瑞那達",
        "波希尼亞及赫塞哥維那",
        "瓜地馬拉",
        "波札那",
        "幾內亞",
        "巴西",
        "蓋亞那",
        "汶萊",
        "海地",
        "保加利亞",
        "宏都拉斯",
        "布吉納法索",
        "香港",
        "蒲隆地",
        "匈牙利",
        "柬埔寨",
        "冰島",
        "喀麥隆",
        "印度",
        "加拿大",
        "印尼",
        "維德角島",
        "依朗",
        "開曼群島",
        "伊拉克",
        "中非共和國",
        "愛爾蘭",
        "查德",
        "以色列",
        "智利",
        "義大利",
        "中國大陸",
        "牙買加",
        "哥倫比亞",
        "日本",
        "剛果",
        "約旦",
        "科克群島",
        "肯亞",
        "哥斯大黎加",
        "韓國",
        "象牙海岸",
        "科威特",
        "克羅埃西亞",
        "寮國",
        "塞浦路斯",
        "拉脫維亞",
        "捷克",
        "賴索托",
        "盧森堡",
        "聖露西亞",
        "澳門",
        "聖文森及格瑞那丁",
        "北馬其頓",
        "聖多美及普林西比",
        "馬達加斯加",
        "沙烏地阿拉伯",
        "馬拉威",
        "塞內加爾",
        "馬來西亞",
        "塞席爾",
        "馬爾地夫",
        "獅子山",
        "馬利",
        "新加坡",
        "馬爾他",
        "斯洛伐克",
        "模里西斯",
        "斯洛維尼亞",
        "茅利塔尼亞",
        "索羅門群島",
        "墨西哥",
        "索馬利亞",
        "摩爾多瓦",
        "南非",
        "蒙古",
        "西班牙",
        "摩洛哥",
        "斯里蘭卡",
        "緬甸",
        "蘇丹",
        "納米比亞",
        "蘇利南",
        "諾魯",
        "史瓦濟蘭",
        "尼泊爾",
        "瑞典",
        "荷蘭",
        "瑞士",
        "新喀里多尼亞",
        "敘利亞",
        "紐西蘭",
        "坦尚尼亞",
        "尼日",
        "泰國",
        "奈及利亞",
        "多哥",
        "挪威",
        "千里達及托貝哥",
        "阿曼",
        "突尼西亞",
        "巴基斯坦",
        "土耳其",
        "巴拿馬",
        "烏干達",
        "巴布亞紐幾內亞",
        "烏克蘭",
        "巴拉圭",
        "阿拉伯聯合大公國",
        "秘魯",
        "美國",
        "菲律賓",
        "烏拉圭",
        "波蘭",
        "委內瑞拉",
        "葡萄牙",
        "越南",
        "卡達",
        "西薩摩亞",
        "羅馬尼亞",
        "葉門",
        "俄羅斯",
        "尚比亞",
        "盧安達",
        "辛巴威",
        "聖克里斯多福及尼維斯",
    )

    def secondary_address(self) -> str:
        return self.numerify(self.random_element(self.secondary_address_formats))

    def building_number(self) -> str:
        return self.numerify(self.random_element(self.building_number_formats))

    def street_name(self) -> str:
        return self.random_element(self.street_names)

    def street_name_suffix(self) -> str:
        return self.random_element(self.street_suffixes)

    def city_name(self) -> str:
        return self.random_element(self.cities)

    def city_name_suffix(self) -> str:
        return self.random_element(self.city_suffixes)

    def section_number(self) -> str:
        return self.numerify(self.random_element(self.section_formats))
