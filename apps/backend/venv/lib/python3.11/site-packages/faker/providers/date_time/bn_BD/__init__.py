from faker.typing import Country

from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "রবিবার",
        "1": "সোমবার",
        "2": "মঙ্গলবার",
        "3": "বুধবার",
        "4": "বৃহস্পতিবার",
        "5": "শুক<PERSON>রবার",
        "6": "শনিবার",
    }

    MONTH_NAMES = {
        "01": "জানুয়ারি",
        "02": "ফেব্রুয়ারি",
        "03": "মার্চ",
        "04": "এপ্রিল",
        "05": "মে",
        "06": "জুন",
        "07": "জুলাই",
        "08": "আগস্ট",
        "09": "সেপ্টেম্বর",
        "10": "অক্টোবর",
        "11": "নভেম্বর",
        "12": "ডিসেম্বর",
    }

    countries = [
        Country(
            timezones=["ইউরোপ/অ্যান্ডোরা"],
            alpha_2_code="AD",
            alpha_3_code="এবং",
            continent="ইউরোপ",
            name="অ্যান্ডোরা",
            capital="অ্যান্ডোরা লা ভেলা",
        ),
        Country(
            timezones=["এশিয়া/কাবুল"],
            alpha_2_code="AF",
            alpha_3_code="AFG",
            continent="এশিয়া",
            name="আফগানিস্তান",
            capital="কাবুল",
        ),
        Country(
            timezones=["আমেরিকা/অ্যান্টিগা"],
            alpha_2_code="AG",
            alpha_3_code="ATG",
            continent="উত্তর আমেরিকা",
            name="অ্যান্টিগা এবং বারবুডা",
            capital="সেন্ট জনস",
        ),
        Country(
            timezones=["ইউরোপ/তিরানে"],
            alpha_2_code="AL",
            alpha_3_code="ALB",
            continent="ইউরোপ",
            name="আলবেনিয়া",
            capital="তিরানা",
        ),
        Country(
            timezones=["এশিয়া/ইয়েরেভান"],
            alpha_2_code="AM",
            alpha_3_code="ARM",
            continent="এশিয়া",
            name="আর্মেনিয়া",
            capital="ইয়েরেভান",
        ),
        Country(
            timezones=["আফ্রিকা/লুয়ান্ডা"],
            alpha_2_code="AO",
            alpha_3_code="আগে",
            continent="আফ্রিকা",
            name="অ্যাঙ্গোলা",
            capital="লুয়ান্ডা",
        ),
        Country(
            timezones=[
                "আমেরিকা/আর্জেন্টিনা/বুয়েনস_আয়ার্স",
                "আমেরিকা/আর্জেন্টিনা/কর্ডোবা",
                "আমেরিকা/আর্জেন্টিনা/জুজুয়",
                "আমেরিকা/আর্জেন্টিনা/টুকুমান",
                "আমেরিকা/আর্জেন্টিনা/কাটামার্কা",
                "আমেরিকা/আর্জেন্টিনা/লা_রিওজা",
                "আমেরিকা/আর্জেন্টিনা/সান_জুয়ান",
                "আমেরিকা/আর্জেন্টিনা/মেন্ডোজা",
                "আমেরিকা/আর্জেন্টিনা/রিও_গ্যালেগোস",
                "আমেরিকা/আর্জেন্টিনা/উশুইয়া",
            ],
            alpha_2_code="AR",
            alpha_3_code="ARG",
            continent="দক্ষিণ আমেরিকা",
            name="আর্জেন্টিনা",
            capital="বুয়েনস আইরেস",
        ),
        Country(
            timezones=["ইউরোপ/ভিয়েনা"],
            alpha_2_code="AT",
            alpha_3_code="AUT",
            continent="ইউরোপ",
            name="অস্ট্রিয়া",
            capital="ভিয়েনা",
        ),
        Country(
            timezones=[
                "অস্ট্রেলিয়া/লর্ড_হাউ",
                "অস্ট্রেলিয়া/হোবার্ট",
                "অস্ট্রেলিয়া/কারি",
                "অস্ট্রেলিয়া/মেলবোর্ন",
                "অস্ট্রেলিয়া/সিডনি",
                "অস্ট্রেলিয়া/ব্রোকেন_হিল",
                "অস্ট্রেলিয়া/ব্রিসবেন",
                "অস্ট্রেলিয়া/লিন্ডেম্যান",
                "অস্ট্রেলিয়া/অ্যাডিলেড",
                "অস্ট্রেলিয়া/ডারউইন",
                "অস্ট্রেলিয়া/পার্থ",
            ],
            alpha_2_code="AU",
            alpha_3_code="AUS",
            continent="ওশেনিয়া",
            name="অস্ট্রেলিয়া",
            capital="ক্যানবেরা",
        ),
        Country(
            timezones=["এশিয়া/বাকু"],
            alpha_2_code="AZ",
            alpha_3_code="AZE",
            continent="এশিয়া",
            name="আজারবাইজান",
            capital="বাকু",
        ),
        Country(
            timezones=["আমেরিকা/বার্বাডোস"],
            alpha_2_code="BB",
            alpha_3_code="BRB",
            continent="উত্তর আমেরিকা",
            name="বার্বাডোস",
            capital="ব্রিজটাউন",
        ),
        Country(
            timezones=["এশিয়া/ঢাকা"],
            alpha_2_code="বিডি",
            alpha_3_code="BGD",
            continent="এশিয়া",
            name="বাংলাদেশ",
            capital="ঢাকা",
        ),
        Country(
            timezones=["ইউরোপ/ব্রাসেলস"],
            alpha_2_code="BE",
            alpha_3_code="BEL",
            continent="ইউরোপ",
            name="বেলজিয়াম",
            capital="ব্রাসেলস",
        ),
        Country(
            timezones=["আফ্রিকা/ওগাডুগু"],
            alpha_2_code="BF",
            alpha_3_code="BFA",
            continent="আফ্রিকা",
            name="বুর্কিনা ফাসো",
            capital="ওগাডুগউ",
        ),
        Country(
            timezones=["ইউরোপ/সোফিয়া"],
            alpha_2_code="বিজি",
            alpha_3_code="BGR",
            continent="ইউরোপ",
            name="বুলগেরিয়া",
            capital="সোফিয়া",
        ),
        Country(
            timezones=["এশিয়া/বাহরাইন"],
            alpha_2_code="BH",
            alpha_3_code="BHR",
            continent="এশিয়া",
            name="বাহরাইন",
            capital="মাnameা",
        ),
        Country(
            timezones=["আফ্রিকা/বুজুম্বুরা"],
            alpha_2_code="BI",
            alpha_3_code="BDI",
            continent="আফ্রিকা",
            name="বুরুন্ডি",
            capital="বুজুম্বুরা",
        ),
        Country(
            timezones=["আফ্রিকা/পোর্টো-নোভো"],
            alpha_2_code="BJ",
            alpha_3_code="BEN",
            continent="আফ্রিকা",
            name="বেনিন",
            capital="পোর্টো-নভো",
        ),
        Country(
            timezones=["এশিয়া/ব্রুনাই"],
            alpha_2_code="BN",
            alpha_3_code="BRN",
            continent="এশিয়া",
            name="ব্রুনাই দারুসসালাম",
            capital="বন্দর সেরি বেগাওয়ান",
        ),
        Country(
            timezones=["আমেরিকা/লা_পাজ"],
            alpha_2_code="BO",
            alpha_3_code="BOL",
            continent="দক্ষিণ আমেরিকা",
            name="বলিভিয়া",
            capital="সুক্রে",
        ),
        Country(
            timezones=[
                "আমেরিকা/নরোনহা",
                "আমেরিকা/বেলেম",
                "আমেরিকা/ফর্তালেজা",
                "আমেরিকা/রেসিফ",
                "আমেরিকা/আরাগুয়েনা",
                "আমেরিকা/ম্যাসিও",
                "আমেরিকা/বাহিয়া",
                "আমেরিকা/সাও_পাওলো",
                "আমেরিকা/ক্যাম্পো_গ্রান্ডে",
                "আমেরিকা/কুয়াবা",
                "আমেরিকা/পোর্টো_ভেলহো",
                "আমেরিকা/বোয়া_ভিস্তা",
                "আমেরিকা/মানাস",
                "আমেরিকা/ইরুনেপে",
                "আমেরিকা/রিও_ব্র্যাঙ্কো",
            ],
            alpha_2_code="BR",
            alpha_3_code="BRA",
            continent="দক্ষিণ আমেরিকা",
            name="ব্রাজিল",
            capital="ব্রাসেলস",
        ),
        Country(
            timezones=["আমেরিকা/নাসাউ"],
            alpha_2_code="BS",
            alpha_3_code="BHS",
            continent="উত্তর আমেরিকা",
            name="বাহামাস",
            capital="নাসাউ",
        ),
        Country(
            timezones=["এশিয়া/থিম্পু"],
            alpha_2_code="BT",
            alpha_3_code="BTN",
            continent="এশিয়া",
            name="ভুটান",
            capital="থিম্পু",
        ),
        Country(
            timezones=["আফ্রিকা/গ্যাবোরোন"],
            alpha_2_code="BW",
            alpha_3_code="BWA",
            continent="আফ্রিকা",
            name="বতসোয়ানা",
            capital="গ্যাবরোন",
        ),
        Country(
            timezones=["ইউরোপ/মিনস্ক"],
            alpha_2_code="দ্বারা",
            alpha_3_code="BLR",
            continent="ইউরোপ",
            name="বেলারুশ",
            capital="মিনস্ক",
        ),
        Country(
            timezones=["আমেরিকা/বেলিজ"],
            alpha_2_code="BZ",
            alpha_3_code="BLZ",
            continent="উত্তর আমেরিকা",
            name="বেলিজ",
            capital="বেলমোপান",
        ),
        Country(
            timezones=[
                "আমেরিকা/সেন্ট জনস",
                "আমেরিকা/হ্যালিফ্যাক্স",
                "আমেরিকা/গ্লেস_বে",
                "আমেরিকা/মঙ্কটন",
                "আমেরিকা/গোজ_বে",
                "আমেরিকা/ব্ল্যাঙ্ক-সাবলন",
                "আমেরিকা/মন্ট্রিল",
                "আমেরিকা/টরন্টো",
                "আমেরিকা/নিপিগন",
                "আমেরিকা/থান্ডার_বে",
                "আমেরিকা/পাংনিরতুং",
                "আমেরিকা/ইকালুইট",
                "আমেরিকা/আতিকোকান",
                "আমেরিকা/র‍্যাঙ্কিন_ইনলেট",
                "আমেরিকা/উইনিপেগ",
                "আমেরিকা/বৃষ্টি_নদী",
                "আমেরিকা/কেমব্রিজ_বে",
                "আমেরিকা/রেজিনা",
                "আমেরিকা/সুইফট_কারেন্ট",
                "আমেরিকা/এডমন্টন",
                "আমেরিকা/ইয়েলোনাইফ",
                "আমেরিকা/ইনুভিক",
                "আমেরিকা/ডসন_ক্রিক",
                "আমেরিকা/ভ্যাঙ্কুভার",
                "আমেরিকা/হোয়াইটহরস",
                "আমেরিকা/ডসন",
            ],
            alpha_2_code="CA",
            alpha_3_code="CAN",
            continent="উত্তর আমেরিকা",
            name="কানাডা",
            capital="অটোয়া",
        ),
        Country(
            timezones=["আফ্রিকা/কিনশাসা", "আফ্রিকা/লুবুম্বাশি"],
            alpha_2_code="CD",
            alpha_3_code="COD",
            continent="আফ্রিকা",
            name="কঙ্গো গণতান্ত্রিক প্রজাতন্ত্র",
            capital="কিনশাসা",
        ),
        Country(
            timezones=["আফ্রিকা/ব্রাজাভিল"],
            alpha_2_code="CG",
            alpha_3_code="COG",
            continent="আফ্রিকা",
            name="কঙ্গো প্রজাতন্ত্র",
            capital="ব্রাজাভিল",
        ),
        Country(
            timezones=["আফ্রিকা/আবিজান"],
            alpha_2_code="CI",
            alpha_3_code="CIV",
            continent="আফ্রিকা",
            name="C\xc3\xb4te d'Ivoire",
            capital="ইয়ামুসুক্রো",
        ),
        Country(
            timezones=["আমেরিকা/সান্টিয়াগো", "প্যাসিফিক/ইস্টার"],
            alpha_2_code="CL",
            alpha_3_code="CHL",
            continent="দক্ষিণ আমেরিকা",
            name="চিলি",
            capital="সান্তিয়াগো",
        ),
        Country(
            timezones=["আফ্রিকা/ডুয়ালা"],
            alpha_2_code="CM",
            alpha_3_code="CMR",
            continent="আফ্রিকা",
            name="ক্যামেরুন",
            capital="ইয়াউন্ড",
        ),
        Country(
            timezones=[
                "এশিয়া/সাংহাই",
                "এশিয়া/হারবিন",
                "এশিয়া/চংকিং",
                "এশিয়া/উরুমকি",
                "এশিয়া/কাশগর",
            ],
            alpha_2_code="CN",
            alpha_3_code="CHN",
            continent="এশিয়া",
            name="গণপ্রজাতন্ত্রী চীন",
            capital="বেইজিং",
        ),
        Country(
            timezones=["আমেরিকা/বোগোটা"],
            alpha_2_code="CO",
            alpha_3_code="COL",
            continent="দক্ষিণ আমেরিকা",
            name="কলম্বিয়া",
            capital="বোগোট\xc3\xa1",
        ),
        Country(
            timezones=["আমেরিকা/কোস্টা_রিকা"],
            alpha_2_code="CR",
            alpha_3_code="CRI",
            continent="উত্তর আমেরিকা",
            name="কোস্টারিকা",
            capital="সান জোস\xc3\xa9",
        ),
        Country(
            timezones=["আমেরিকা/হাভানা"],
            alpha_2_code="CU",
            alpha_3_code="CUB",
            continent="উত্তর আমেরিকা",
            name="কিউবা",
            capital="হাভানা",
        ),
        Country(
            timezones=["আটলান্টিক/কেপ_ভার্দে"],
            alpha_2_code="সিভি",
            alpha_3_code="CPV",
            continent="আফ্রিকা",
            name="কেপ ভার্দে",
            capital="প্রাইয়া",
        ),
        Country(
            timezones=["এশিয়া/নিকোসিয়া"],
            alpha_2_code="CY",
            alpha_3_code="CYP",
            continent="এশিয়া",
            name="সাইপ্রাস",
            capital="নিকোসিয়া",
        ),
        Country(
            timezones=["ইউরোপ/প্রাগ"],
            alpha_2_code="CZ",
            alpha_3_code="CZE",
            continent="ইউরোপ",
            name="চেক প্রজাতন্ত্র",
            capital="প্রাগ",
        ),
        Country(
            timezones=["ইউরোপ/বার্লিন"],
            alpha_2_code="DE",
            alpha_3_code="DEU",
            continent="ইউরোপ",
            name="জার্মানি",
            capital="বার্লিন",
        ),
        Country(
            timezones=["আফ্রিকা/জিবুতি"],
            alpha_2_code="ডিজে",
            alpha_3_code="DJI",
            continent="আফ্রিকা",
            name="জিবুতি",
            capital="জিবুতি শহর",
        ),
        Country(
            timezones=["ইউরোপ/কোপেনহেগেন"],
            alpha_2_code="DK",
            alpha_3_code="DNK",
            continent="ইউরোপ",
            name="ডেনমার্ক",
            capital="কোপেনহেগেন",
        ),
        Country(
            timezones=["আমেরিকা/ডোমিনিকা"],
            alpha_2_code="DM",
            alpha_3_code="DMA",
            continent="উত্তর আমেরিকা",
            name="ডোমিনিকা",
            capital="রোজাও",
        ),
        Country(
            timezones=["আমেরিকা/সান্টো_ডোমিঙ্গো"],
            alpha_2_code="করুন",
            alpha_3_code="DOM",
            continent="উত্তর আমেরিকা",
            name="ডোমিনিকান রিপাবলিক",
            capital="সান্তো ডোমিঙ্গো",
        ),
        Country(
            timezones=["আমেরিকা/গুয়াকিল", "প্যাসিফিক/গালাপাগোস"],
            alpha_2_code="EC",
            alpha_3_code="ECU",
            continent="দক্ষিণ আমেরিকা",
            name="ইকুয়েডর",
            capital="কুইটো",
        ),
        Country(
            timezones=["ইউরোপ/টালিন"],
            alpha_2_code="EE",
            alpha_3_code="EST",
            continent="ইউরোপ",
            name="এস্তোনিয়া",
            capital="টালিন",
        ),
        Country(
            timezones=["আফ্রিকা/কায়রো"],
            alpha_2_code="EG",
            alpha_3_code="EGY",
            continent="আফ্রিকা",
            name="মিশর",
            capital="কায়রো",
        ),
        Country(
            timezones=["আফ্রিকা/আসমেরা"],
            alpha_2_code="ER",
            alpha_3_code="ERI",
            continent="আফ্রিকা",
            name="ইরিত্রিয়া",
            capital="আসমারা",
        ),
        Country(
            timezones=["আফ্রিকা/আদিস_আবাবা"],
            alpha_2_code="ET",
            alpha_3_code="ETH",
            continent="আফ্রিকা",
            name="ইথিওপিয়া",
            capital="আদিস আবাবা",
        ),
        Country(
            timezones=["ইউরোপ/হেলসিঙ্কি"],
            alpha_2_code="FI",
            alpha_3_code="FIN",
            continent="ইউরোপ",
            name="ফিনল্যান্ড",
            capital="হেলসিঙ্কি",
        ),
        Country(
            timezones=["প্যাসিফিক/ফিজি"],
            alpha_2_code="FJ",
            alpha_3_code="FJI",
            continent="ওশেনিয়া",
            name="ফিজি",
            capital="সুভা",
        ),
        Country(
            timezones=["ইউরোপ/প্যারিস"],
            alpha_2_code="FR",
            alpha_3_code="FRA",
            continent="ইউরোপ",
            name="ফ্রান্স",
            capital="প্যারিস",
        ),
        Country(
            timezones=["আফ্রিকা/লিব্রেভিল"],
            alpha_2_code="GA",
            alpha_3_code="GAB",
            continent="আফ্রিকা",
            name="গ্যাবন",
            capital="লিব্রেভিল",
        ),
        Country(
            timezones=["এশিয়া/টিবিলিসি"],
            alpha_2_code="GE",
            alpha_3_code="জিও",
            continent="এশিয়া",
            name="জর্জিয়া",
            capital="তিবিলিসি",
        ),
        Country(
            timezones=["আফ্রিকা/আকরা"],
            alpha_2_code="GH",
            alpha_3_code="GHA",
            continent="আফ্রিকা",
            name="ঘানা",
            capital="আকরা",
        ),
        Country(
            timezones=["আফ্রিকা/বানজুল"],
            alpha_2_code="GM",
            alpha_3_code="GMB",
            continent="আফ্রিকা",
            name="গাম্বিয়া",
            capital="বানজুল",
        ),
        Country(
            timezones=["আফ্রিকা/কোনাক্রি"],
            alpha_2_code="GN",
            alpha_3_code="GIN",
            continent="আফ্রিকা",
            name="গিনি",
            capital="কোনাক্রি",
        ),
        Country(
            timezones=["ইউরোপ/এথেন্স"],
            alpha_2_code="GR",
            alpha_3_code="GRC",
            continent="ইউরোপ",
            name="গ্রীস",
            capital="এথেন্স",
        ),
        Country(
            timezones=["আমেরিকা/গুয়েতেমালা"],
            alpha_2_code="GT",
            alpha_3_code="GTM",
            continent="উত্তর আমেরিকা",
            name="গুয়েতেমালা",
            capital="গুয়েতেমালা সিটি",
        ),
        Country(
            timezones=["আমেরিকা/গুয়েতেমালা"],
            alpha_2_code="HT",
            alpha_3_code="HTI",
            continent="উত্তর আমেরিকা",
            name="হাইতি",
            capital="পোর্ট-অ-প্রিন্স",
        ),
        Country(
            timezones=["আফ্রিকা/বিসাউ"],
            alpha_2_code="GW",
            alpha_3_code="GNB",
            continent="আফ্রিকা",
            name="গিনি-বিসাউ",
            capital="বিসাউ",
        ),
        Country(
            timezones=["আমেরিকা/গিয়ানা"],
            alpha_2_code="GY",
            alpha_3_code="লোক",
            continent="দক্ষিণ আমেরিকা",
            name="গিয়ানা",
            capital="জর্জটাউন",
        ),
        Country(
            timezones=["আমেরিকা/টেগুসিগালপা"],
            alpha_2_code="HN",
            alpha_3_code="HND",
            continent="উত্তর আমেরিকা",
            name="হন্ডুরাস",
            capital="টেগুসিগালপা",
        ),
        Country(
            timezones=["ইউরোপ/বুদাপেস্ট"],
            alpha_2_code="HU",
            alpha_3_code="HUN",
            continent="ইউরোপ",
            name="হাঙ্গেরি",
            capital="বুদাপেস্ট",
        ),
        Country(
            timezones=[
                "এশিয়া/জাকার্তা",
                "এশিয়া/পন্টিয়ানাক",
                "এশিয়া/মাকাসার",
                "এশিয়া/জয়াপুরা",
            ],
            alpha_2_code="আইডি",
            alpha_3_code="IDN",
            continent="এশিয়া",
            name="ইন্দোনেশিয়া",
            capital="জাকার্তা",
        ),
        Country(
            timezones=["ইউরোপ/ডাবলিন"],
            alpha_2_code="IE",
            alpha_3_code="IRL",
            continent="ইউরোপ",
            name="আয়ারল্যান্ড প্রজাতন্ত্র",
            capital="ডাবলিন",
        ),
        Country(
            timezones=["এশিয়া/জেরুজালেম"],
            alpha_2_code="IL",
            alpha_3_code="ISR",
            continent="এশিয়া",
            name="ইসরায়েল",
            capital="জেরুজালেম",
        ),
        Country(
            timezones=["এশিয়া/কলকাতা"],
            alpha_2_code="IN",
            alpha_3_code="IND",
            continent="এশিয়া",
            name="ভারত",
            capital="নয়া দিল্লী",
        ),
        Country(
            timezones=["এশিয়া/বাগদাদ"],
            alpha_2_code="IQ",
            alpha_3_code="IRQ",
            continent="এশিয়া",
            name="ইরাক",
            capital="বাগদাদ",
        ),
        Country(
            timezones=["এশিয়া/তেহরান"],
            alpha_2_code="IR",
            alpha_3_code="IRN",
            continent="এশিয়া",
            name="ইরান",
            capital="তেহরান",
        ),
        Country(
            timezones=["আটলান্টিক/রেকজাভিক"],
            alpha_2_code="IS",
            alpha_3_code="ISL",
            continent="ইউরোপ",
            name="আইসল্যান্ড",
            capital="রেকজাভিক",
        ),
        Country(
            timezones=["ইউরোপ/রোম"],
            alpha_2_code="IT",
            alpha_3_code="ITA",
            continent="ইউরোপ",
            name="ইতালি",
            capital="রোম",
        ),
        Country(
            timezones=["আমেরিকা/জ্যামাইকা"],
            alpha_2_code="JM",
            alpha_3_code="JAM",
            continent="উত্তর আমেরিকা",
            name="জ্যামাইকা",
            capital="কিংসটন",
        ),
        Country(
            timezones=["এশিয়া/আম্মান"],
            alpha_2_code="JO",
            alpha_3_code="JOR",
            continent="এশিয়া",
            name="জর্ডান",
            capital="আম্মান",
        ),
        Country(
            timezones=["এশিয়া/টোকিও"],
            alpha_2_code="JP",
            alpha_3_code="JPN",
            continent="এশিয়া",
            name="জাপান",
            capital="টোকিও",
        ),
        Country(
            timezones=["আফ্রিকা/নাইরোবি"],
            alpha_2_code="KE",
            alpha_3_code="KEN",
            continent="আফ্রিকা",
            name="কেনিয়া",
            capital="নাইরোবি",
        ),
        Country(
            timezones=["এশিয়া/বিশকেক"],
            alpha_2_code="কেজি",
            alpha_3_code="KGZ",
            continent="এশিয়া",
            name="কিরগিজস্তান",
            capital="বিশকেক",
        ),
        Country(
            timezones=["প্যাসিফিক/তারাওয়া", "প্যাসিফিক/এন্ডারবেরি", "প্যাসিফিক/কিরিটিমাতি"],
            alpha_2_code="KI",
            alpha_3_code="KIR",
            continent="ওশেনিয়া",
            name="কিরিবাতি",
            capital="তারাওয়া",
        ),
        Country(
            timezones=["এশিয়া/পিয়ংইয়ং"],
            alpha_2_code="KP",
            alpha_3_code="PRK",
            continent="এশিয়া",
            name="উত্তর কোরিয়া",
            capital="পিয়ংইয়ং",
        ),
        Country(
            timezones=["এশিয়া/সিউল"],
            alpha_2_code="KR",
            alpha_3_code="KOR",
            continent="এশিয়া",
            name="দক্ষিণ কোরিয়া",
            capital="সিউল",
        ),
        Country(
            timezones=["এশিয়া/কুয়েত"],
            alpha_2_code="কিলোওয়াট",
            alpha_3_code="KWT",
            continent="এশিয়া",
            name="কুয়েত",
            capital="কুয়েত সিটি",
        ),
        Country(
            timezones=["এশিয়া/বৈরুত"],
            alpha_2_code="LB",
            alpha_3_code="LBN",
            continent="এশিয়া",
            name="লেবানন",
            capital="বৈরুত",
        ),
        Country(
            timezones=["ইউরোপ/ভাদুজ"],
            alpha_2_code="LI",
            alpha_3_code="মিথ্যা",
            continent="ইউরোপ",
            name="লিচেনস্টাইন",
            capital="ভাদুজ",
        ),
        Country(
            timezones=["আফ্রিকা/মনরোভিয়া"],
            alpha_2_code="LR",
            alpha_3_code="LBR",
            continent="আফ্রিকা",
            name="লাইবেরিয়া",
            capital="মনরোভিয়া",
        ),
        Country(
            timezones=["আফ্রিকা/মাসেরু"],
            alpha_2_code="LS",
            alpha_3_code="LSO",
            continent="আফ্রিকা",
            name="লেসোথো",
            capital="মাসেরু",
        ),
        Country(
            timezones=["ইউরোপ/ভিলনিয়াস"],
            alpha_2_code="LT",
            alpha_3_code="LTU",
            continent="ইউরোপ",
            name="লিথুয়ানিয়া",
            capital="ভিলনিয়াস",
        ),
        Country(
            timezones=["ইউরোপ/লাক্সেমবার্গ"],
            alpha_2_code="LU",
            alpha_3_code="LUX",
            continent="ইউরোপ",
            name="লাক্সেমবার্গ",
            capital="লাক্সেমবার্গ সিটি",
        ),
        Country(
            timezones=["ইউরোপ/রিগা"],
            alpha_2_code="LV",
            alpha_3_code="LVA",
            continent="ইউরোপ",
            name="লাটভিয়া",
            capital="রিগা",
        ),
        Country(
            timezones=["আফ্রিকা/ত্রিপোলি"],
            alpha_2_code="LY",
            alpha_3_code="LBY",
            continent="আফ্রিকা",
            name="লিবিয়া",
            capital="ত্রিপোলি",
        ),
        Country(
            timezones=["ভারতীয়/আন্তানানারিভো"],
            alpha_2_code="MG",
            alpha_3_code="MDG",
            continent="আফ্রিকা",
            name="মাদাগাস্কার",
            capital="আন্তানানারিভো",
        ),
        Country(
            timezones=["প্যাসিফিক/মাজুরো", "প্যাসিফিক/কোয়াজালেইন"],
            alpha_2_code="MH",
            alpha_3_code="MHL",
            continent="ওশেনিয়া",
            name="মার্শাল দ্বীপপুঞ্জ",
            capital="মাজুরো",
        ),
        Country(
            timezones=["ইউরোপ/স্কোপজে"],
            alpha_2_code="MK",
            alpha_3_code="MKD",
            continent="ইউরোপ",
            name="ম্যাসিডোনিয়া",
            capital="স্কোপজে",
        ),
        Country(
            timezones=["আফ্রিকা/বামাকো"],
            alpha_2_code="ML",
            alpha_3_code="MLI",
            continent="আফ্রিকা",
            name="মালি",
            capital="বামাকো",
        ),
        Country(
            timezones=["এশিয়া/রেঙ্গুন"],
            alpha_2_code="MM",
            alpha_3_code="MMR",
            continent="এশিয়া",
            name="মিয়ানমার",
            capital="নায়প্যিদা",
        ),
        Country(
            timezones=["এশিয়া/উলানবাতার", "এশিয়া/হোভড", "এশিয়া/চোইবালসান"],
            alpha_2_code="MN",
            alpha_3_code="MNG",
            continent="এশিয়া",
            name="মঙ্গোলিয়া",
            capital="উলানবাতার",
        ),
        Country(
            timezones=["আফ্রিকা/নোয়াকচট"],
            alpha_2_code="MR",
            alpha_3_code="MRT",
            continent="আফ্রিকা",
            name="মৌরিতানিয়া",
            capital="নুয়াকচট",
        ),
        Country(
            timezones=["ইউরোপ/মাল্টা"],
            alpha_2_code="MT",
            alpha_3_code="MLT",
            continent="ইউরোপ",
            name="মাল্টা",
            capital="ভ্যালেটা",
        ),
        Country(
            timezones=["ভারতীয়/মরিশাস"],
            alpha_2_code="MU",
            alpha_3_code="MUS",
            continent="আফ্রিকা",
            name="মরিশাস",
            capital="পোর্ট লুইস",
        ),
        Country(
            timezones=["ভারতীয়/মালদ্বীপ"],
            alpha_2_code="MV",
            alpha_3_code="MDV",
            continent="এশিয়া",
            name="মালদ্বীপ",
            capital="মাল\xc3\xa9",
        ),
        Country(
            timezones=["আফ্রিকা/ব্লান্টিয়ার"],
            alpha_2_code="মেগাওয়াট",
            alpha_3_code="MWI",
            continent="আফ্রিকা",
            name="মালাউই",
            capital="লিলংওয়ে",
        ),
        Country(
            timezones=[
                "আমেরিকা/মেক্সিকো_সিটি",
                "আমেরিকা/কানকুন",
                "আমেরিকা/মেরিডা",
                "আমেরিকা/মন্টেরে",
                "আমেরিকা/মাজাতলান",
                "আমেরিকা/চিহুয়াহুয়া",
                "আমেরিকা/হার্মোসিলো",
                "আমেরিকা/টিজুয়ানা",
            ],
            alpha_2_code="MX",
            alpha_3_code="MEX",
            continent="উত্তর আমেরিকা",
            name="মেক্সিকো",
            capital="মেক্সিকো সিটি",
        ),
        Country(
            timezones=["এশিয়া/কুয়ালা_লামপুর", "এশিয়া/কুচিং"],
            alpha_2_code="আমার",
            alpha_3_code="MYS",
            continent="এশিয়া",
            name="মালয়েশিয়া",
            capital="কুয়ালালামপুর",
        ),
        Country(
            timezones=["আফ্রিকা/মাপুটো"],
            alpha_2_code="MZ",
            alpha_3_code="MOZ",
            continent="আফ্রিকা",
            name="মোজাম্বিক",
            capital="মাপুতো",
        ),
        Country(
            timezones=["আফ্রিকা/উইন্ডহোক"],
            alpha_2_code="NA",
            alpha_3_code="NAM",
            continent="আফ্রিকা",
            name="nameিবিয়া",
            capital="উইন্ডহোক",
        ),
        Country(
            timezones=["আফ্রিকা/নিয়ামে"],
            alpha_2_code="NE",
            alpha_3_code="NER",
            continent="আফ্রিকা",
            name="নাইজার",
            capital="নিয়ামে",
        ),
        Country(
            timezones=["আফ্রিকা/লাগোস"],
            alpha_2_code="NG",
            alpha_3_code="NGA",
            continent="আফ্রিকা",
            name="নাইজেরিয়া",
            capital="আবুজা",
        ),
        Country(
            timezones=["আমেরিকা/মানাগুয়া"],
            alpha_2_code="NI",
            alpha_3_code="NIC",
            continent="উত্তর আমেরিকা",
            name="নিকারাগুয়া",
            capital="মানাগুয়া",
        ),
        Country(
            timezones=["ইউরোপ/আমস্টারডাম"],
            alpha_2_code="NL",
            alpha_3_code="NLD",
            continent="ইউরোপ",
            name="নেদারল্যান্ডের রাজ্য",
            capital="আমস্টারডাম",
        ),
        Country(
            timezones=["ইউরোপ/অসলো"],
            alpha_2_code="না",
            alpha_3_code="NOR",
            continent="ইউরোপ",
            name="নরওয়ে",
            capital="অসলো",
        ),
        Country(
            timezones=["এশিয়া/কাটমান্ডু"],
            alpha_2_code="NP",
            alpha_3_code="NPL",
            continent="এশিয়া",
            name="নেপাল",
            capital="কাঠমান্ডু",
        ),
        Country(
            timezones=["প্যাসিফিক/নাউরু"],
            alpha_2_code="NR",
            alpha_3_code="NRU",
            continent="ওশেনিয়া",
            name="নাউরু",
            capital="ইয়েরেন",
        ),
        Country(
            timezones=["প্যাসিফিক/অকল্যান্ড", "প্যাসিফিক/চ্যাথাম"],
            alpha_2_code="NZ",
            alpha_3_code="NZL",
            continent="ওশেনিয়া",
            name="নিউজিল্যান্ড",
            capital="ওয়েলিংটন",
        ),
        Country(
            timezones=["এশিয়া/মাস্কাট"],
            alpha_2_code="OM",
            alpha_3_code="OMN",
            continent="এশিয়া",
            name="ওমান",
            capital="মাস্কাট",
        ),
        Country(
            timezones=["আমেরিকা/পাnameা"],
            alpha_2_code="PA",
            alpha_3_code="PAN",
            continent="উত্তর আমেরিকা",
            name="পাnameা",
            capital="পাnameা সিটি",
        ),
        Country(
            timezones=["আমেরিকা/লিমা"],
            alpha_2_code="PE",
            alpha_3_code="PER",
            continent="দক্ষিণ আমেরিকা",
            name="পেরু",
            capital="লিমা",
        ),
        Country(
            timezones=["প্যাসিফিক/পোর্ট_মোরেসবি"],
            alpha_2_code="PG",
            alpha_3_code="PNG",
            continent="ওশেনিয়া",
            name="পাপুয়া নিউ গিনি",
            capital="পোর্ট মোরসবি",
        ),
        Country(
            timezones=["এশিয়া/ম্যানিলা"],
            alpha_2_code="PH",
            alpha_3_code="PHL",
            continent="এশিয়া",
            name="ফিলিপাইন",
            capital="ম্যানিলা",
        ),
        Country(
            timezones=["এশিয়া/করাচি"],
            alpha_2_code="PK",
            alpha_3_code="PAK",
            continent="এশিয়া",
            name="পাকিস্তান",
            capital="ইসলামাবাদ",
        ),
        Country(
            timezones=["ইউরোপ/ওয়ারশ"],
            alpha_2_code="PL",
            alpha_3_code="POL",
            continent="ইউরোপ",
            name="পোল্যান্ড",
            capital="ওয়ারশ",
        ),
        Country(
            timezones=["ইউরোপ/লিসবন", "আটলান্টিক/মাদেইরা", "আটলান্টিক/আজোরস"],
            alpha_2_code="PT",
            alpha_3_code="PRT",
            continent="ইউরোপ",
            name="পর্তুগাল",
            capital="লিসবন",
        ),
        Country(
            timezones=["প্যাসিফিক/পালাউ"],
            alpha_2_code="PW",
            alpha_3_code="PLW",
            continent="ওশেনিয়া",
            name="পালাউ",
            capital="এনগেরুলমুদ",
        ),
        Country(
            timezones=["আমেরিকা/আসুনসিয়ন"],
            alpha_2_code="PY",
            alpha_3_code="PRY",
            continent="দক্ষিণ আমেরিকা",
            name="প্যারাগুয়ে",
            capital="আসুন্সি\xc3\xb3n",
        ),
        Country(
            timezones=["এশিয়া/কাতার"],
            alpha_2_code="QA",
            alpha_3_code="QAT",
            continent="এশিয়া",
            name="কাতার",
            capital="দোহা",
        ),
        Country(
            timezones=["ইউরোপ/বুখারেস্ট"],
            alpha_2_code="RO",
            alpha_3_code="ROU",
            continent="ইউরোপ",
            name="রোমানিয়া",
            capital="বুখারেস্ট",
        ),
        Country(
            timezones=[
                "ইউরোপ/ক্যালিনিনগ্রাদ",
                "ইউরোপ/মস্কো",
                "ইউরোপ/ভলগোগ্রাদ",
                "ইউরোপ/সামারা",
                "এশিয়া/ইয়েকাটেরিনবার্গ",
                "এশিয়া/ওমস্ক",
                "এশিয়া/নভোসিবিরস্ক",
                "এশিয়া/ক্রাসনোয়ারস্ক",
                "এশিয়া/ইরকুটস্ক",
                "এশিয়া/ইয়াকুটস্ক",
                "এশিয়া/ভ্লাদিভোস্টক",
                "এশিয়া/সাখালিন",
                "এশিয়া/মাগাদান",
                "এশিয়া/কামচাটকা",
                "এশিয়া/আনাডার",
            ],
            alpha_2_code="RU",
            alpha_3_code="RUS",
            continent="ইউরোপ",
            name="রাশিয়া",
            capital="মস্কো",
        ),
        Country(
            timezones=["আফ্রিকা/কিগালি"],
            alpha_2_code="RW",
            alpha_3_code="RWA",
            continent="আফ্রিকা",
            name="রুয়ান্ডা",
            capital="কিগালি",
        ),
        Country(
            timezones=["এশিয়া/রিয়াদ"],
            alpha_2_code="SA",
            alpha_3_code="SAU",
            continent="এশিয়া",
            name="সৌদি আরব",
            capital="রিয়াদ",
        ),
        Country(
            timezones=["প্যাসিফিক/গুয়াডালকানাল"],
            alpha_2_code="SB",
            alpha_3_code="SLB",
            continent="ওশেনিয়া",
            name="সলোমন দ্বীপপুঞ্জ",
            capital="হোনিয়ারা",
        ),
        Country(
            timezones=["ভারতীয়/মাহে"],
            alpha_2_code="SC",
            alpha_3_code="SYC",
            continent="আফ্রিকা",
            name="সেশেলস",
            capital="ভিক্টোরিয়া",
        ),
        Country(
            timezones=["আফ্রিকা/খার্তুম"],
            alpha_2_code="SD",
            alpha_3_code="SDN",
            continent="আফ্রিকা",
            name="সুদান",
            capital="খার্তুম",
        ),
        Country(
            timezones=["ইউরোপ/স্টকহোম"],
            alpha_2_code="SE",
            alpha_3_code="SWE",
            continent="ইউরোপ",
            name="সুইডেন",
            capital="স্টকহোম",
        ),
        Country(
            timezones=["এশিয়া/সিঙ্গাপুর"],
            alpha_2_code="SG",
            alpha_3_code="SGP",
            continent="এশিয়া",
            name="সিঙ্গাপুর",
            capital="সিঙ্গাপুর",
        ),
        Country(
            timezones=["ইউরোপ/লুব্লজানা"],
            alpha_2_code="SI",
            alpha_3_code="SVN",
            continent="ইউরোপ",
            name="স্লোভেনিয়া",
            capital="লুব্লজানা",
        ),
        Country(
            timezones=["ইউরোপ/ব্র্যাটিস্লাভা"],
            alpha_2_code="SK",
            alpha_3_code="SVK",
            continent="ইউরোপ",
            name="স্লোভাকিয়া",
            capital="ব্রাটিস্লাভা",
        ),
        Country(
            timezones=["আফ্রিকা/ফ্রিটাউন"],
            alpha_2_code="SL",
            alpha_3_code="SLE",
            continent="আফ্রিকা",
            name="সিয়েরা লিওন",
            capital="ফ্রিটাউন",
        ),
        Country(
            timezones=["ইউরোপ/সান_মারিনো"],
            alpha_2_code="SM",
            alpha_3_code="SMR",
            continent="ইউরোপ",
            name="সান মারিনো",
            capital="সান মারিনো",
        ),
        Country(
            timezones=["আফ্রিকা/ডাকার"],
            alpha_2_code="SN",
            alpha_3_code="SEN",
            continent="আফ্রিকা",
            name="সেনেগাল",
            capital="ডাকার",
        ),
        Country(
            timezones=["আফ্রিকা/মোগাদিশু"],
            alpha_2_code="SO",
            alpha_3_code="SOM",
            continent="আফ্রিকা",
            name="সোমালিয়া",
            capital="মোগাদিশু",
        ),
        Country(
            timezones=["আমেরিকা/পারামারিবো"],
            alpha_2_code="SR",
            alpha_3_code="SUR",
            continent="দক্ষিণ আমেরিকা",
            name="সুরিname",
            capital="পারমারিবো",
        ),
        Country(
            timezones=["আফ্রিকা/সাও_টোম"],
            alpha_2_code="ST",
            alpha_3_code="STP",
            continent="আফ্রিকা",
            name="S\xc3\xa3o Tom\xc3\xa9 এবং Pr\xc3\xadncipe",
            capital="S\xc3\xa3o টম\xc3\xa9",
        ),
        Country(
            timezones=["এশিয়া/দামাস্কাস"],
            alpha_2_code="SY",
            alpha_3_code="SYR",
            continent="এশিয়া",
            name="সিরিয়া",
            capital="দামাস্কাস",
        ),
        Country(
            timezones=["আফ্রিকা/লোম"],
            alpha_2_code="TG",
            alpha_3_code="TGO",
            continent="আফ্রিকা",
            name="টোগো",
            capital="Lom\xc3\xa9",
        ),
        Country(
            timezones=["এশিয়া/ব্যাংকক"],
            alpha_2_code="TH",
            alpha_3_code="THA",
            continent="এশিয়া",
            name="থাইল্যান্ড",
            capital="ব্যাংকক",
        ),
        Country(
            timezones=["এশিয়া/দুশানবে"],
            alpha_2_code="TJ",
            alpha_3_code="TJK",
            continent="এশিয়া",
            name="তাজিকিস্তান",
            capital="দুশানবে",
        ),
        Country(
            timezones=["এশিয়া/আশগাবাত"],
            alpha_2_code="TM",
            alpha_3_code="TKM",
            continent="এশিয়া",
            name="তুর্কমেনিস্তান",
            capital="আশগাবাত",
        ),
        Country(
            timezones=["আফ্রিকা/টিউনিস"],
            alpha_2_code="TN",
            alpha_3_code="TUN",
            continent="আফ্রিকা",
            name="তিউনিসিয়া",
            capital="তিউনিস",
        ),
        Country(
            timezones=["প্যাসিফিক/টোঙ্গাটাপু"],
            alpha_2_code="TO",
            alpha_3_code="TON",
            continent="ওশেনিয়া",
            name="টোঙ্গা",
            capital="নুকু\xca\xbbalofa",
        ),
        Country(
            timezones=["ইউরোপ/ইস্তানবুল"],
            alpha_2_code="TR",
            alpha_3_code="TUR",
            continent="এশিয়া",
            name="তুরস্ক",
            capital="আঙ্কারা",
        ),
        Country(
            timezones=["আমেরিকা/পোর্ট_অফ_স্পেন"],
            alpha_2_code="TT",
            alpha_3_code="TTO",
            continent="উত্তর আমেরিকা",
            name="ত্রিনিদাদ ও টোবাগো",
            capital="স্পেন বন্দর",
        ),
        Country(
            timezones=["প্যাসিফিক/ফুনাফুটি"],
            alpha_2_code="টিভি",
            alpha_3_code="TUV",
            continent="ওশেনিয়া",
            name="টুভালু",
            capital="ফুনাফুটি",
        ),
        Country(
            timezones=["আফ্রিকা/দার_এস_সালাম"],
            alpha_2_code="TZ",
            alpha_3_code="TZA",
            continent="আফ্রিকা",
            name="তানজানিয়া",
            capital="ডোডোমা",
        ),
        Country(
            timezones=[
                "ইউরোপ/কিয়েভ",
                "ইউরোপ/উজগোরড",
                "ইউরোপ/জাপোরোজি",
                "ইউরোপ/সিমফেরোপল",
            ],
            alpha_2_code="UA",
            alpha_3_code="UKR",
            continent="ইউরোপ",
            name="ইউক্রেন",
            capital="কিয়েভ",
        ),
        Country(
            timezones=["আফ্রিকা/কাম্পালা"],
            alpha_2_code="UG",
            alpha_3_code="UGA",
            continent="আফ্রিকা",
            name="উগান্ডা",
            capital="কাম্পালা",
        ),
        Country(
            timezones=[
                "আমেরিকা/নিউইয়র্ক",
                "আমেরিকা/ডেট্রয়েট",
                "আমেরিকা/কেনটাকি/লুইসভিল",
                "আমেরিকা/কেনটাকি/মন্টিসেলো",
                "আমেরিকা/ইন্ডিয়ানা/ইন্ডিয়ানাপোলিস",
                "আমেরিকা/ইন্ডিয়ানা/মারেঙ্গো",
                "আমেরিকা/ইন্ডিয়ানা/নক্স",
                "আমেরিকা/ইন্ডিয়ানা/ভেভে",
                "আমেরিকা/শিকাগো",
                "আমেরিকা/ইন্ডিয়ানা/ভিনসেনেস",
                "আমেরিকা/ইন্ডিয়ানা/পিটার্সবার্গ",
                "আমেরিকা/মেনোমিনী",
                "আমেরিকা/উত্তর_ডাকোটা/সেন্টার",
                "আমেরিকা/উত্তর_ডাকোটা/নিউ_সালেম",
                "আমেরিকা/ডেনভার",
                "আমেরিকা/বোইস",
                "আমেরিকা/শিপ্রক",
                "আমেরিকা/ফিনিক্স",
                "আমেরিকা/লস_এঞ্জেলেস",
                "আমেরিকা/অ্যাঙ্কোরেজ",
                "আমেরিকা/জুনেউ",
                "আমেরিকা/ইয়াকুটাত",
                "আমেরিকা/name",
                "আমেরিকা/আডাক",
                "প্যাসিফিক/হনোলুলু",
            ],
            alpha_2_code="মার্কিন যুক্তরাষ্ট্র",
            alpha_3_code="USA",
            continent="উত্তর আমেরিকা",
            name="মার্কিন যুক্তরাষ্ট্র",
            capital="ওয়াশিংটন, ডিসি",
        ),
        Country(
            timezones=["আমেরিকা/মন্টেভিডিও"],
            alpha_2_code="UY",
            alpha_3_code="URY",
            continent="দক্ষিণ আমেরিকা",
            name="উরুগুয়ে",
            capital="মন্টেভিডিও",
        ),
        Country(
            timezones=["এশিয়া/সমরকন্দ", "এশিয়া/তাসখন্দ"],
            alpha_2_code="UZ",
            alpha_3_code="UZB",
            continent="এশিয়া",
            name="উজবেকিস্তান",
            capital="তাসখন্দ",
        ),
        Country(
            timezones=["ইউরোপ/ভ্যাটিকান"],
            alpha_2_code="VA",
            alpha_3_code="ভ্যাট",
            continent="ইউরোপ",
            name="ভ্যাটিকান সিটি",
            capital="ভ্যাটিকান সিটি",
        ),
        Country(
            timezones=["আমেরিকা/কারাকাস"],
            alpha_2_code="VE",
            alpha_3_code="VEN",
            continent="দক্ষিণ আমেরিকা",
            name="ভেনিজুয়েলা",
            capital="কারাকাস",
        ),
        Country(
            timezones=["এশিয়া/সাইগন"],
            alpha_2_code="VN",
            alpha_3_code="VNM",
            continent="এশিয়া",
            name="ভিয়েতname",
            capital="হানয়",
        ),
        Country(
            timezones=["প্যাসিফিক/ইফেট"],
            alpha_2_code="VU",
            alpha_3_code="VUT",
            continent="ওশেনিয়া",
            name="ভানুয়াতু",
            capital="পোর্ট ভিলা",
        ),
        Country(
            timezones=["এশিয়া/এডেন"],
            alpha_2_code="YE",
            alpha_3_code="YEM",
            continent="এশিয়া",
            name="ইয়েমেন",
            capital="সানা",
        ),
        Country(
            timezones=["আফ্রিকা/লুসাকা"],
            alpha_2_code="ZM",
            alpha_3_code="ZMB",
            continent="আফ্রিকা",
            name="জাম্বিয়া",
            capital="লুসাকা",
        ),
        Country(
            timezones=["আফ্রিকা/হারারে"],
            alpha_2_code="ZW",
            alpha_3_code="ZWE",
            continent="আফ্রিকা",
            name="জিম্বাবুয়ে",
            capital="হারারে",
        ),
        Country(
            timezones=["আফ্রিকা/আলজিয়ার্স"],
            alpha_2_code="DZ",
            alpha_3_code="DZA",
            continent="আফ্রিকা",
            name="আলজেরিয়া",
            capital="আলজিয়ার্স",
        ),
        Country(
            timezones=["ইউরোপ/সারাজেভো"],
            alpha_2_code="BA",
            alpha_3_code="BIH",
            continent="ইউরোপ",
            name="বসনিয়া ও হার্জেগোভিনা",
            capital="সারায়েভো",
        ),
        Country(
            timezones=["এশিয়া/ফনম_পেন"],
            alpha_2_code="KH",
            alpha_3_code="KHM",
            continent="এশিয়া",
            name="কম্বোডিয়া",
            capital="নম পেন",
        ),
        Country(
            timezones=["আফ্রিকা/বাঙ্গুই"],
            alpha_2_code="CF",
            alpha_3_code="CAF",
            continent="আফ্রিকা",
            name="মধ্য আফ্রিকান প্রজাতন্ত্র",
            capital="বাঙ্গুই",
        ),
        Country(
            timezones=["আফ্রিকা/এনডজামেনা"],
            alpha_2_code="TD",
            alpha_3_code="TCD",
            continent="আফ্রিকা",
            name="চাদ",
            capital="এন'জামেনা",
        ),
        Country(
            timezones=["ভারতীয়/কোমোরো"],
            alpha_2_code="KM",
            alpha_3_code="COM",
            continent="আফ্রিকা",
            name="কোমোরোস",
            capital="মোরোনি",
        ),
        Country(
            timezones=["ইউরোপ/জাগরেব"],
            alpha_2_code="HR",
            alpha_3_code="HRV",
            continent="ইউরোপ",
            name="ক্রোয়েশিয়া",
            capital="জাগরেব",
        ),
        Country(
            timezones=["এশিয়া/দিলি"],
            alpha_2_code="TL",
            alpha_3_code="TLS",
            continent="এশিয়া",
            name="পূর্ব তিমুর",
            capital="দিলি",
        ),
        Country(
            timezones=["আমেরিকা/এল_সালভাদর"],
            alpha_2_code="SV",
            alpha_3_code="SLV",
            continent="উত্তর আমেরিকা",
            name="এল সালভাদর",
            capital="সান সালভাদর",
        ),
        Country(
            timezones=["আফ্রিকা/মালাবো"],
            alpha_2_code="GQ",
            alpha_3_code="GNQ",
            continent="আফ্রিকা",
            name="নিরক্ষীয় গিনি",
            capital="মালাবো",
        ),
        Country(
            timezones=["আমেরিকা/গ্রেনাডা"],
            alpha_2_code="GD",
            alpha_3_code="GRD",
            continent="উত্তর আমেরিকা",
            name="গ্রেনাডা",
            capital="সেন্ট জর্জস",
        ),
        Country(
            timezones=[
                "এশিয়া/আলমাটি",
                "এশিয়া/কিউজিলর্ডা",
                "এশিয়া/আকতোব",
                "এশিয়া/আকতাউ",
                "এশিয়া/ওরাল",
            ],
            alpha_2_code="KZ",
            alpha_3_code="KAZ",
            continent="এশিয়া",
            name="কাজাখস্তান",
            capital="আস্তানা",
        ),
        Country(
            timezones=["এশিয়া/ভিয়েনতিয়েন"],
            alpha_2_code="LA",
            alpha_3_code="LAO",
            continent="এশিয়া",
            name="লাওস",
            capital="ভিয়েনতিয়েন",
        ),
        Country(
            timezones=["প্যাসিফিক/ট্রুক", "প্যাসিফিক/পোনাপে", "প্যাসিফিক/কোসরা"],
            alpha_2_code="FM",
            alpha_3_code="FSM",
            continent="ওশেনিয়া",
            name="মাইক্রোনেশিয়ার ফেডারেটেড স্টেটস",
            capital="পালকির",
        ),
        Country(
            timezones=["ইউরোপ/চিসিনাউ"],
            alpha_2_code="MD",
            alpha_3_code="MDA",
            continent="ইউরোপ",
            name="মোল্দোভা",
            capital="চি\xc5\x9fin\xc4\x83u",
        ),
        Country(
            timezones=["ইউরোপ/মোনাকো"],
            alpha_2_code="MC",
            alpha_3_code="MCO",
            continent="ইউরোপ",
            name="মোনাকো",
            capital="মোনাকো",
        ),
        Country(
            timezones=["ইউরোপ/পডগোরিকা"],
            alpha_2_code="ME",
            alpha_3_code="MNE",
            continent="ইউরোপ",
            name="মন্টিনিগ্রো",
            capital="পডগোরিকা",
        ),
        Country(
            timezones=["আফ্রিকা/ক্যাসাব্লাঙ্কা"],
            alpha_2_code="MA",
            alpha_3_code="MAR",
            continent="আফ্রিকা",
            name="মরক্কো",
            capital="রাবাত",
        ),
        Country(
            timezones=["আমেরিকা/সেন্ট_কিটস"],
            alpha_2_code="KN",
            alpha_3_code="KNA",
            continent="উত্তর আমেরিকা",
            name="সেন্ট কিটস অ্যান্ড নেভিস",
            capital="ব্যাসেটেরে",
        ),
        Country(
            timezones=["আমেরিকা/সেন্ট_লুসিয়া"],
            alpha_2_code="LC",
            alpha_3_code="LCA",
            continent="উত্তর আমেরিকা",
            name="সেন্ট লুসিয়া",
            capital="ক্যাস্ট্রিজ",
        ),
        Country(
            timezones=["America/St_Vincent"],
            alpha_2_code="ভিসি",
            alpha_3_code="VCT",
            continent="উত্তর আমেরিকা",
            name="সেন্ট ভিনসেন্ট এবং গ্রেনাডাইনস",
            capital="কিংসটাউন",
        ),
        Country(
            timezones=["প্যাসিফিক/অপিয়া"],
            alpha_2_code="WS",
            alpha_3_code="WSM",
            continent="ওশেনিয়া",
            name="সামোয়া",
            capital="আপিয়া",
        ),
        Country(
            timezones=["ইউরোপ/বেলগ্রেড"],
            alpha_2_code="RS",
            alpha_3_code="SRB",
            continent="ইউরোপ",
            name="সার্বিয়া",
            capital="বেলগ্রেড",
        ),
        Country(
            timezones=["আফ্রিকা/জোহানেসবার্গ"],
            alpha_2_code="ZA",
            alpha_3_code="ZAF",
            continent="আফ্রিকা",
            name="দক্ষিণ আফ্রিকা",
            capital="প্রিটোরিয়া",
        ),
        Country(
            timezones=["ইউরোপ/মাদ্রিদ", "আফ্রিকা/সেউটা", "আটলান্টিক/ক্যানারি"],
            alpha_2_code="ES",
            alpha_3_code="ESP",
            continent="ইউরোপ",
            name="স্পেন",
            capital="মাদ্রিদ",
        ),
        Country(
            timezones=["এশিয়া/কলম্বো"],
            alpha_2_code="LK",
            alpha_3_code="LKA",
            continent="এশিয়া",
            name="শ্রীলঙ্কা",
            capital="শ্রী জয়বর্ধনেপুরা কোট্টে",
        ),
        Country(
            timezones=["আফ্রিকা/এমবাবেন"],
            alpha_2_code="SZ",
            alpha_3_code="SWZ",
            continent="আফ্রিকা",
            name="সোয়াজিল্যান্ড",
            capital="এমবাবেন",
        ),
        Country(
            timezones=["ইউরোপ/জুরিখ"],
            alpha_2_code="CH",
            alpha_3_code="CHE",
            continent="ইউরোপ",
            name="সুইজারল্যান্ড",
            capital="বার্ন",
        ),
        Country(
            timezones=["এশিয়া/দুবাই"],
            alpha_2_code="AE",
            alpha_3_code="ARE",
            continent="এশিয়া",
            name="সংযুক্ত আরব আমিরাত",
            capital="আবুধাবি",
        ),
        Country(
            timezones=["ইউরোপ/লন্ডন"],
            alpha_2_code="GB",
            alpha_3_code="GBR",
            continent="ইউরোপ",
            name="যুক্তরাজ্য",
            capital="লন্ডন",
        ),
        Country(
            timezones=["এশিয়া/তাইপেই"],
            alpha_2_code="TW",
            alpha_3_code="TWN",
            continent="এশিয়া",
            name="তাইওয়ান",
            capital="তাইপেই",
        ),
        Country(
            timezones=["এশিয়া/গাজা", "এশিয়া/হেব্রন"],
            alpha_2_code="PS",
            alpha_3_code="PSE",
            continent="এশিয়া",
            name="ফিলিস্তিন",
            capital="রামাল্লা",
        ),
    ]

    def day_of_week(self) -> str:
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        return self.MONTH_NAMES[month]
