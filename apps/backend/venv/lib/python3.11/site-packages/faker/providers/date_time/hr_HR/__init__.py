from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    def day_of_week(self) -> str:
        day = self.date("%w")
        DAY_NAMES = {
            "0": "Ned<PERSON><PERSON><PERSON>",
            "1": "Ponedjeljak",
            "2": "Utorak",
            "3": "<PERSON><PERSON><PERSON>",
            "4": "Četvrtak",
            "5": "<PERSON>ak",
            "6": "<PERSON><PERSON>",
        }
        return DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        MONTH_NAMES = {
            "01": "Siječanj",
            "02": "Veljača",
            "03": "<PERSON><PERSON><PERSON><PERSON>",
            "04": "Travanj",
            "05": "Svibanj",
            "06": "<PERSON>panj",
            "07": "<PERSON>panj",
            "08": "<PERSON>lov<PERSON>",
            "09": "Rujan",
            "10": "Listopad",
            "11": "<PERSON><PERSON><PERSON>",
            "12": "<PERSON>sin<PERSON>",
        }
        return MONTH_NAMES[month]
