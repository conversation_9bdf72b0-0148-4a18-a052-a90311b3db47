from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "<PERSON><PERSON>",
        "1": "<PERSON><PERSON><PERSON>",
        "2": "<PERSON><PERSON>",
        "3": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "4": "<PERSON><PERSON><PERSON><PERSON>",
        "5": "<PERSON><PERSON>",
        "6": "<PERSON><PERSON><PERSON><PERSON>",
    }

    MONTH_NAMES = {
        "01": "<PERSON>ca<PERSON>",
        "02": "<PERSON><PERSON><PERSON>",
        "03": "Mart",
        "04": "<PERSON><PERSON>",
        "05": "<PERSON><PERSON><PERSON>",
        "06": "Haziran",
        "07": "Temmuz",
        "08": "A<PERSON>ustos",
        "09": "<PERSON><PERSON><PERSON><PERSON>",
        "10": "<PERSON><PERSON>",
        "11": "<PERSON><PERSON><PERSON><PERSON>",
        "12": "<PERSON><PERSON><PERSON><PERSON>",
    }

    def day_of_week(self):
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self):
        month = self.month()
        return self.MONTH_NAMES[month]
