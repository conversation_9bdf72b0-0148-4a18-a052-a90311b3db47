from .. import Provider as InternetProvider


class Provider(InternetProvider):
    replacements = (
        ("س", "s"),
        ("ق", "q"),
        ("ب", "b"),
        ("خ", "x"),
        ("ش", "$"),
        ("َ", "a"),
        ("ئ", "}"),
        ("إ", "<"),
        ("ل", "l"),
        ("ٰ", "`"),
        ("ف", "f"),
        ("و", "w"),
        ("ض", "D"),
        ("ي", "y"),
        ("ُ", "u"),
        ("ة", "p"),
        ("ظ", "Z"),
        ("ث", "v"),
        ("ـ", "_"),
        ("ج", "j"),
        ("د", "d"),
        ("ح", "H"),
        ("ا", "A"),
        ("أ", ">"),
        ("ر", "r"),
        ("ى", "Y"),
        ("ذ", "*"),
        ("ْ", "o"),
        ("ن", "n"),
        ("ّ", "~"),
        ("ك", "k"),
        ("ء", "'"),
        ("ط", "T"),
        ("ت", "t"),
        ("ه", "h"),
        ("ً", "F"),
        ("ؤ", "&"),
        ("ٍ", "K"),
        ("ِ", "i"),
        ("ص", "S"),
        ("ٱ", "{"),
        ("ٌ", "N"),
        ("م", "m"),
        ("ز", "z"),
        ("ع", "E"),
        ("آ", "|"),
        ("غ", "g"),
    )
