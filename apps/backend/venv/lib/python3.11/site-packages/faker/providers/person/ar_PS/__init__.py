from ..ar_AA import Provider as ArabicPersonProvider


class Provider(ArabicPersonProvider):
    last_names = (
        "أبو اسنينة",
        "أبو شقدم",
        "أبو شلبك",
        "أبو غليون",
        "أبو قمر",
        "أستيتية",
        "الأدغم",
        "الإغباري",
        "البرغوثي",
        "التركمان",
        "التميمي",
        "الجنيدي",
        "الحسيني",
        "الحنبلي",
        "الخازن",
        "الخماش",
        "الخياط",
        "الزيتاوي",
        "الزيدانية",
        "السكاكيني",
        "الصالحي",
        "النشاشيبي",
        "النعنيش",
        "بدر",
        "ترابين",
        "جرار",
        "جزار",
        "حمامي",
        "حوسة",
        "خوري",
        "دغمش",
        "دلاشة",
        "شاهين",
        "صليبا",
        "طوقان",
        "فطاير",
        "قرادة",
        "كسواني",
        "مرمش",
        "مهيار",
        "نسيبة",
        "هاشم",
    )
