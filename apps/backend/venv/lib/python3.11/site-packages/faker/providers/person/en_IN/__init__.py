from .. import Provider as PersonProvider


class Provider(PersonProvider):

    formats_male = ("{{first_name_male}} {{last_name}}",)
    formats_female = ("{{first_name_female}} {{last_name}}",)
    formats = ("{{first_name}} {{last_name}}",)

    # https://www.in.pampers.com/pregnancy/baby-names/article/indian-baby-boys-names
    first_names_male = (
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "Dominic",
        "Ekalinga",
        "Ekansh",
        "Ekapad",
        "Ekaraj",
        "Ekavir",
        "Ekbal",
        "Elijah",
        "Ethan",
        "Falan",
        "Faqid",
        "Faraj",
        "Faras",
        "Farhan",
        "Fariq",
        "Faris",
        "Finn",
        "Fitan",
        "Fiyaz",
        "Frado",
        "Frederick",
        "Gabriel",
        "Gagan",
        "Gaurang",
        "Gaurav",
        "Gautam",
        "Gavin",
        "George",
        "Girik",
        "Girindra",
        "Girish",
        "Gopal",
        "Gunbir",
        "Guneet",
        "Hardik",
        "Harish",
        "Harrison",
        "Harsh",
        "Harshil",
        "Hemang",
        "Henry",
        "Hitesh",
        "Hredhaan",
        "Hritik",
        "Ikbal",
        "Imaran",
        "Indrajit",
        "Isaac",
        "Isaiah",
        "Ishaan",
        "Ishwar",
        "Jack",
        "Jackson",
        "Jacob",
        "Jagat",
        "Jagdish",
        "Jai",
        "Jairaj",
        "Jason",
        "Jatin",
        "Jeet",
        "Jeremiah",
        "Jonathan",
        "Joshua",
        "Kabir",
        "Kai",
        "Kalpit",
        "Karan",
        "Kevin",
        "Kiaan",
        "Krish",
        "Krishna",
        "Laban",
        "Laksh",
        "Lakshit",
        "Liam",
        "Logan",
        "Lohit",
        "Lucky",
        "Luke",
        "Maanas",
        "Maanav",
        "Madhav",
        "Manan",
        "Manbir",
        "Manthan",
        "Mason",
        "Matthew",
        "Max",
        "Michael",
        "Mitesh",
        "Mohammed",
        "Nachiket",
        "Naksh",
        "Nakul",
        "Nathan",
        "Nathaniel",
        "Naveen",
        "Neel",
        "Nicholas",
        "Nihal",
        "Nitesh",
        "Noah",
        "Ojas",
        "Oliver",
        "Om",
        "Omkaar",
        "Onkar",
        "Onveer",
        "Orinder",
        "Oscar",
        "Owen",
        "Parth",
        "Patrick",
        "Peter",
        "Pranav",
        "Praneel",
        "Pranit",
        "Pratyush",
        "Qabil",
        "Qadim",
        "Qarin",
        "Qasim",
        "Quincy",
        "Rachit",
        "Raghav",
        "Ranbir",
        "Ranveer",
        "Rayaan",
        "Rehaan",
        "Reyansh",
        "Rishi",
        "Robert",
        "Rohan",
        "Ronith",
        "Rudra",
        "Rushil",
        "Ryan",
        "Sai",
        "Saksham",
        "Samaksh",
        "Samar",
        "Samarth",
        "Samesh",
        "Samuel",
        "Sarthak",
        "Sathvik",
        "Shaurya",
        "Shivansh",
        "Siddharth",
        "Simon",
        "Tanay",
        "Tanish",
        "Tanveer",
        "Tarak",
        "Teerth",
        "Tejas",
        "Theodore",
        "Thomas",
        "Timothy",
        "Tristan",
        "Udant",
        "Udarsh",
        "Umang",
        "Upkaar",
        "Utkarsh",
        "Vedant",
        "Veer",
        "Victor",
        "Vihaan",
        "Vincent",
        "Viraj",
        "Vivaan",
        "Wahab",
        "Warinder",
        "Warjas",
        "Wazir",
        "William",
        "Wriddhish",
        "Wridesh",
        "Wyatt",
        "Xavier",
        "Yagnesh",
        "Yash",
        "Yatan",
        "Yatin",
        "Yug",
        "Yuvraj",
        "Zaid",
        "Zashil",
        "Zayan",
        "Zayyan",
        "Zehaan",
    )

    # https://www.pampers.com/en-us/pregnancy/baby-names/article/indian-girl-names
    first_names_female = (
        "Aachal",
        "Aadhya",
        "Aahana",
        "Aarini",
        "Aarna",
        "Aashi",
        "Abha",
        "Advika",
        "Adweta",
        "Adya",
        "Aishani",
        "Alka",
        "Amaira",
        "Amara",
        "Amrita",
        "Amruta",
        "Anamika",
        "Anika",
        "Anita",
        "Anjali",
        "Anusha",
        "Anvi",
        "Anya",
        "Aradhana",
        "Arunima",
        "Arya",
        "Ati",
        "Avni",
        "Baghyawati",
        "Barkha",
        "Bhanumati",
        "Bhavani",
        "Bhavika",
        "Bhavini",
        "Bhavna",
        "Bhavya",
        "Bimala",
        "Bina",
        "Bishakha",
        "Brinda",
        "Chaaya",
        "Chaitaly",
        "Chakrika",
        "Chaman",
        "Chameli",
        "Chanchal",
        "Chandani",
        "Charita",
        "Charvi",
        "Chasmum",
        "Chavvi",
        "Daksha",
        "Dalaja",
        "Damini",
        "Damyanti",
        "Darika",
        "Dayamai",
        "Dayita",
        "Deepa",
        "Devika",
        "Dhriti",
        "Dipta",
        "Divya",
        "Diya",
        "Edhitha",
        "Eesha",
        "Eiravati",
        "Ekaja",
        "Ekani",
        "Ekanta",
        "Ekantika",
        "Ekiya",
        "Ekta",
        "Eshana",
        "Eta",
        "Falak",
        "Falguni",
        "Forum",
        "Ganga",
        "Garima",
        "Gaurangi",
        "Gauri",
        "Gaurika",
        "Gautami",
        "Gayathri",
        "Geetika",
        "Hamsini",
        "Harinakshi",
        "Harini",
        "Harita",
        "Hema",
        "Hemal",
        "Hemangini",
        "Hemani",
        "Hiral",
        "Idika",
        "Ijaya",
        "Ikshita",
        "Inaya",
        "Indali",
        "Indira",
        "Ira",
        "Irya",
        "Isha",
        "Ishani",
        "Ishanvi",
        "Ishita",
        "Jagrati",
        "Jagvi",
        "Jalsa",
        "Janaki",
        "Janani",
        "Januja",
        "Janya",
        "Jasmit",
        "Jeevika",
        "Jhalak",
        "Jyoti",
        "Kala",
        "Kamala",
        "Kamya",
        "Kashish",
        "Kashvi",
        "Kavya",
        "Keya",
        "Krisha",
        "Krishna",
        "Kritika",
        "Ladli",
        "Lajita",
        "Lakshmi",
        "Lavanya",
        "Leela",
        "Leena",
        "Lekha",
        "Libni",
        "Lila",
        "Lipika",
        "Lopa",
        "Madhavi",
        "Mahika",
        "Manya",
        "Maya",
        "Meera",
        "Megha",
        "Meghana",
        "Mekhala",
        "Mitali",
        "Mohini",
        "Mugdha",
        "Nandini",
        "Neelima",
        "Neha",
        "Netra",
        "Nidhi",
        "Nidra",
        "Niharika",
        "Nikita",
        "Nilima",
        "Nimrat",
        "Nirja",
        "Nisha",
        "Nitara",
        "Odika",
        "Oeshi",
        "Ojasvi",
        "Omaja",
        "Omisha",
        "Omya",
        "Oni",
        "Osha",
        "Oviya",
        "Pahal",
        "Pallavi",
        "Panini",
        "Pavani",
        "Pooja",
        "Prisha",
        "Priya",
        "Pushti",
        "Qushi",
        "Raagini",
        "Rachana",
        "Rachita",
        "Radha",
        "Radhika",
        "Rajata",
        "Rajeshri",
        "Raksha",
        "Reva",
        "Ria",
        "Ridhi",
        "Riya",
        "Saanvi",
        "Sachi",
        "Sai",
        "Sanaya",
        "Sanya",
        "Sara",
        "Saumya",
        "Shivani",
        "Shravya",
        "Siya",
        "Sneha",
        "Sudiksha",
        "Suhani",
        "Tamanna",
        "Tanmayi",
        "Tanvi",
        "Tara",
        "Tripti",
        "Triveni",
        "Triya",
        "Turvi",
        "Ubika",
        "Ucchal",
        "Udyati",
        "Unnati",
        "Unni",
        "Upadhriti",
        "Upasna",
        "Upma",
        "Urishilla",
        "Urmi",
        "Urvashi",
        "Urvi",
        "Vaishnavi",
        "Vamakshi",
        "Vansha",
        "Vanya",
        "Varenya",
        "Varsha",
        "Vasana",
        "Vasatika",
        "Vasudha",
        "Veda",
        "Vedhika",
        "Vedika",
        "Vidhi",
        "Vinaya",
        "Vrinda",
        "Vrishti",
        "Vritti",
        "Vyanjana",
        "Waida",
        "Wakeeta",
        "Warda",
        "Warhi",
        "Watika",
        "Widisha",
        "Wishi",
        "Xalak",
        "Xiti",
        "Yachana",
        "Yadavi",
        "Yahvi",
        "Yamini",
        "Yashasvi",
        "Yashawini",
        "Yashica",
        "Yashoda",
        "Yashodhara",
        "Yashvi",
        "Yasti",
        "Yauvani",
        "Yochana",
        "Yoshita",
        "Yutika",
        "Zaitra",
        "Zansi",
        "Zarna",
        "Zilmil",
        "Zinal",
    )

    first_names = first_names_male + first_names_female

    last_names = (
        "Acharya",
        "Agarwal",
        "Agate",
        "Aggarwal",
        "Agrawal",
        "Ahluwalia",
        "Ahuja",
        "Amble",
        "Anand",
        "Andra",
        "Anne",
        "Apte",
        "Arora",
        "Arya",
        "Atwal",
        "Aurora",
        "Babu",
        "Badal",
        "Badami",
        "Bahl",
        "Bahri",
        "Bail",
        "Bains",
        "Bajaj",
        "Bajwa",
        "Bakshi",
        "Bal",
        "Bala",
        "Bala",
        "Balakrishnan",
        "Balan",
        "Balasubramanian",
        "Balay",
        "Bali",
        "Bandi",
        "Banerjee",
        "Banik",
        "Bansal",
        "Barad",
        "Barad",
        "Baral",
        "Baria",
        "Barman",
        "Basak",
        "Bassi",
        "Basu",
        "Bath",
        "Batra",
        "Batta",
        "Bava",
        "Bawa",
        "Bedi",
        "Behl",
        "Ben",
        "Bera",
        "Bhagat",
        "Bhakta",
        "Bhalla",
        "Bhandari",
        "Bhardwaj",
        "Bhargava",
        "Bhasin",
        "Bhat",
        "Bhatia",
        "Bhatnagar",
        "Bhatt",
        "Bhattacharyya",
        "Bhatti",
        "Bhavsar",
        "Bir",
        "Biswas",
        "Boase",
        "Bobal",
        "Bora",
        "Bora",
        "Borah",
        "Borde",
        "Borra",
        "Bose",
        "Brahmbhatt",
        "Brar",
        "Buch",
        "Buch",
        "Bumb",
        "Butala",
        "Chacko",
        "Chad",
        "Chada",
        "Chadha",
        "Chahal",
        "Chakrabarti",
        "Chakraborty",
        "Chana",
        "Chand",
        "Chanda",
        "Chander",
        "Chandra",
        "Chandran",
        "Char",
        "Chatterjee",
        "Chaudhari",
        "Chaudhary",
        "Chaudhry",
        "Chaudhuri",
        "Chaudry",
        "Chauhan",
        "Chawla",
        "Cheema",
        "Cherian",
        "Chhabra",
        "Chokshi",
        "Chopra",
        "Choudhary",
        "Choudhry",
        "Choudhury",
        "Chowdhury",
        "Comar",
        "Contractor",
        "D’Alia",
        "Dada",
        "Dalal",
        "Dani",
        "Dar",
        "Dara",
        "Dara",
        "Das",
        "Dasgupta",
        "Dash",
        "Dass",
        "Date",
        "Datta",
        "Dave",
        "Dayal",
        "De",
        "Deep",
        "Deo",
        "Deol",
        "Desai",
        "Deshmukh",
        "Deshpande",
        "Devan",
        "Devi",
        "Dewan",
        "Dey",
        "Dhaliwal",
        "Dhar",
        "Dhar",
        "Dhawan",
        "Dhillon",
        "Dhingra",
        "Din",
        "Divan",
        "Dixit",
        "Doctor",
        "Dora",
        "Doshi",
        "Dua",
        "Dube",
        "Dubey",
        "Dugal",
        "Dugar",
        "Dugar",
        "Dutt",
        "Dutta",
        "Dyal",
        "Edwin",
        "Gaba",
        "Gade",
        "Gala",
        "Gandhi",
        "Ganesan",
        "Ganesh",
        "Ganguly",
        "Gara",
        "Garde",
        "Garg",
        "Gera",
        "Ghose",
        "Ghosh",
        "Gill",
        "Goda",
        "Goel",
        "Gokhale",
        "Gola",
        "Gole",
        "Golla",
        "Gopal",
        "Goswami",
        "Gour",
        "Goyal",
        "Grewal",
        "Grover",
        "Guha",
        "Gulati",
        "Gupta",
        "Halder",
        "Handa",
        "Hans",
        "Hari",
        "Hayer",
        "Hayre",
        "Hegde",
        "Hora",
        "Issac",
        "Iyengar",
        "Iyer",
        "Jaggi",
        "Jain",
        "Jani",
        "Jayaraman",
        "Jha",
        "Jhaveri",
        "Johal",
        "Joshi",
        "Kadakia",
        "Kade",
        "Kakar",
        "Kala",
        "Kala",
        "Kala",
        "Kale",
        "Kalita",
        "Kalla",
        "Kamdar",
        "Kanda",
        "Kannan",
        "Kant",
        "Kapadia",
        "Kapoor",
        "Kapur",
        "Kar",
        "Kara",
        "Karan",
        "Kari",
        "Karnik",
        "Karpe",
        "Kashyap",
        "Kata",
        "Kaul",
        "Kaur",
        "Keer",
        "Keer",
        "Khalsa",
        "Khanna",
        "Khare",
        "Khatri",
        "Khosla",
        "Khurana",
        "Kibe",
        "Kohli",
        "Konda",
        "Korpal",
        "Koshy",
        "Kota",
        "Kothari",
        "Krish",
        "Krishna",
        "Krishnamurthy",
        "Krishnan",
        "Kulkarni",
        "Kumar",
        "Kumer",
        "Kunda",
        "Kurian",
        "Kuruvilla",
        "Lad",
        "Lad",
        "Lal",
        "Lala",
        "Lall",
        "Lalla",
        "Lanka",
        "Lata",
        "Loke",
        "Loyal",
        "Luthra",
        "Madan",
        "Madan",
        "Magar",
        "Mahajan",
        "Mahal",
        "Maharaj",
        "Majumdar",
        "Malhotra",
        "Mall",
        "Mallick",
        "Mammen",
        "Mand",
        "Manda",
        "Mandal",
        "Mander",
        "Mane",
        "Mangal",
        "Mangat",
        "Mani",
        "Mani",
        "Mann",
        "Mannan",
        "Manne",
        "Master",
        "Memon",
        "Menon",
        "Merchant",
        "Minhas",
        "Mishra",
        "Misra",
        "Mistry",
        "Mital",
        "Mitra",
        "Mittal",
        "Mitter",
        "Modi",
        "Mody",
        "Mohan",
        "Mohanty",
        "Morar",
        "More",
        "Mukherjee",
        "Mukhopadhyay",
        "Muni",
        "Munshi",
        "Murthy",
        "Murty",
        "Mutti",
        "Nadig",
        "Nadkarni",
        "Nagar",
        "Nagarajan",
        "Nagi",
        "Nagy",
        "Naidu",
        "Naik",
        "Nair",
        "Nanda",
        "Narain",
        "Narang",
        "Narasimhan",
        "Narayan",
        "Narayanan",
        "Narula",
        "Natarajan",
        "Nath",
        "Natt",
        "Nayak",
        "Nayar",
        "Nazareth",
        "Nigam",
        "Nori",
        "Oak",
        "Om",
        "Oommen",
        "Oza",
        "Padmanabhan",
        "Pai",
        "Pal",
        "Palan",
        "Pall",
        "Palla",
        "Palla",
        "Panchal",
        "Pandey",
        "Pandit",
        "Pandya",
        "Pant",
        "Parekh",
        "Parikh",
        "Parmar",
        "Parmer",
        "Parsa",
        "Patel",
        "Pathak",
        "Patil",
        "Patla",
        "Patla",
        "Pau",
        "Peri",
        "Pillai",
        "Pillay",
        "Pingle",
        "Prabhakar",
        "Prabhu",
        "Pradhan",
        "Prakash",
        "Prasad",
        "Prashad",
        "Puri",
        "Purohit",
        "Radhakrishnan",
        "Raghavan",
        "Rai",
        "Raj",
        "Raja",
        "Rajagopal",
        "Rajagopalan",
        "Rajan",
        "Raju",
        "Ram",
        "Rama",
        "Ramachandran",
        "Ramakrishnan",
        "Raman",
        "Ramanathan",
        "Ramaswamy",
        "Ramesh",
        "Rana",
        "Randhawa",
        "Ranganathan",
        "Rao",
        "Rastogi",
        "Ratta",
        "Rattan",
        "Ratti",
        "Rau",
        "Raval",
        "Ravel",
        "Ravi",
        "Ray",
        "Reddy",
        "Rege",
        "Rout",
        "Roy",
        "Sabharwal",
        "Sachar",
        "Sachdev",
        "Sachdeva",
        "Sagar",
        "Saha",
        "Sahni",
        "Sahota",
        "Saini",
        "Salvi",
        "Sama",
        "Sami",
        "Sampath",
        "Samra",
        "Sandal",
        "Sandhu",
        "Sane",
        "Sangha",
        "Sanghvi",
        "Sani",
        "Sankar",
        "Sankaran",
        "Sant",
        "Saraf",
        "Saran",
        "Sarin",
        "Sarkar",
        "Sarma",
        "Sarna",
        "Sarraf",
        "Sastry",
        "Sathe",
        "Savant",
        "Sawhney",
        "Saxena",
        "Sehgal",
        "Sekhon",
        "Sem",
        "Sen",
        "Sengupta",
        "Seshadri",
        "Seth",
        "Sethi",
        "Setty",
        "Sha",
        "Shah",
        "Shan",
        "Shankar",
        "Shanker",
        "Sharaf",
        "Sharma",
        "Shenoy",
        "Shere",
        "Sheth",
        "Shetty",
        "Shroff",
        "Shukla",
        "Sibal",
        "Sidhu",
        "Singh",
        "Singhal",
        "Sinha",
        "Sodhi",
        "Solanki",
        "Som",
        "Soman",
        "Soni",
        "Sood",
        "Sridhar",
        "Srinivas",
        "Srinivasan",
        "Srivastava",
        "Subramaniam",
        "Subramanian",
        "Sule",
        "Sundaram",
        "Sunder",
        "Sur",
        "Sura",
        "Suresh",
        "Suri",
        "Swaminathan",
        "Swamy",
        "Tailor",
        "Tak",
        "Talwar",
        "Tandon",
        "Taneja",
        "Tank",
        "Tara",
        "Tata",
        "Tella",
        "Thaker",
        "Thakkar",
        "Thakur",
        "Thaman",
        "Tiwari",
        "Toor",
        "Tripathi",
        "Trivedi",
        "Upadhyay",
        "Uppal",
        "Vaidya",
        "Vala",
        "Varghese",
        "Varkey",
        "Varma",
        "Varty",
        "Varughese",
        "Vasa",
        "Venkataraman",
        "Venkatesh",
        "Verma",
        "Vig",
        "Virk",
        "Viswanathan",
        "Vohra",
        "Vora",
        "Vyas",
        "Wable",
        "Wadhwa",
        "Wagle",
        "Wali",
        "Wali",
        "Walia",
        "Walla",
        "Warrior",
        "Wason",
        "Yadav",
        "Yogi",
        "Yohannan",
        "Zacharia",
        "Zachariah",
    )
