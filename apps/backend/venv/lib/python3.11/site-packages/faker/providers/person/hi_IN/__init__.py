from .. import Provider as <PERSON><PERSON><PERSON>ider


class Provider(PersonProvider):

    formats_male = (
        "{{first_name_male}} {{last_name}}",
        "{{prefix_male}} {{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{last_name}}{{suffix}}",
        "{{prefix}} {{first_name_male}} {{last_name}}",
    )
    formats_female = (
        "{{first_name_female}} {{last_name}}",
        "{{prefix_female}} {{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{last_name}}{{suffix}}",
        "{{prefix}} {{first_name_female}} {{last_name}}",
    )

    formats = (
        "{{first_name}} {{last_name}}",
        "{{prefix}} {{first_name}} {{last_name}}",
        "{{first_name}} {{last_name}}{{suffix}}",
    )

    # http://www.20000-names.com/male_hindi_names.htm
    first_names_male = (
        "अभय",
        "आदित्य",
        "अजित",
        "आकाङ्क्षा",
        "अकबर",
        "अखिल",
        "अमर",
        "अमित",
        "अमृत",
        "आनन्द",
        "अनन्‍त",
        "अनिल",
        "अनिरुद्ध",
        "अनिश",
        "अंकुर",
        "अनुज",
        "अनुपम",
        "अरविन्द",
        "अर्जुन",
        "अरुण",
        "अरुणा",
        "असीम",
        "अशोक",
        "बल",
        "बलदेव",
        "बलराम",
        "भारत",
        "ब्रह्मा",
        "बृजेश",
        "चण्ड",
        "चन्दना",
        "चन्द्रकान्त",
        "दामोदर",
        "दर्शन",
        "दयाराम",
        "देवदान",
        "दीपक",
        "देवदान",
        "देवदास",
        "देवराज",
        "धनञ्जय",
        "धवल",
        "दिलीप",
        "दिनेश",
        "दीपक",
        "दिलीप",
        "गणेश",
        "गौतम",
        "गोपाल",
        "गोतम",
        "गोविंदा",
        "गुलज़ार",
        "हनुमान्",
        "हरेन्द्र",
        "हरि",
        "हरीश",
        "हर्श",
        "हर्शद",
        "हर्शल",
        "इला",
        "इन्द्र",
        "इन्द्रजित",
        "ईश",
        "जगन्नाथ",
        "जगदीश",
        "जगजीत",
        "जयदेव",
        "ज़स्विन्देर्",
        "जय",
        "जयन्त",
        "जयेन्द्र",
        "जितेन्द्र",
        "जौहर",
        "ज्योतिष",
        "कैलाश",
        "कालिदास",
        "काम",
        "कमल",
        "कम्बोज",
        "कपिल",
        "कर्ण",
        "ख़ान",
        "किरण",
        "कशोर",
        "कृष्ण",
        "कुमार",
        "कुणाल",
        "लक्ष्मण",
        "लाल",
        "ललित",
        "लोचन",
        "माधव",
        "मधुकर",
        "महात्मा",
        "महावीर",
        "महेन्द्रा",
        "मानदीप",
        "मनीश",
        "मणि",
        "मणीन्द्र",
        "मनीश",
        "मञ्जुनाथ",
        "मोहन",
        "मुकेश",
        "नंद",
        "नारायण",
        "नरेन्द्र",
        "नवीन",
        "निखिल",
        "नीरव",
        "िनशा",
        "ओम",
        "पद्म",
        "पल्लव",
        "पीताम्बर",
        "प्रभाकर",
        "प्रभात",
        "प्रभु",
        "प्रबोध",
        "प्रदीप",
        "प्रकाश",
        "प्रमोद",
        "प्रणव",
        "प्रणय",
        "प्रसाद",
        "प्रसन्न",
        "प्रताप",
        "प्रेम",
        "पुरुषोत्तम",
        "रघु",
        "राहुल",
        "राज",
        "राजन",
        "रजनीकांत",
        "राजीव",
        "राजेन्द्र",
        "राजेश",
        "राजीव",
        "राकेश",
        "राम",
        "रामचन्द्र",
        "रामकृष्ण",
        "रञ्जित",
        "रतन",
        "रत्नम",
        "रावण",
        "रवि",
        "ऋषि",
        "रोहन",
        "सचिन",
        "संदीप",
        "शनि",
        "संजय",
        "संजित",
        "संजीव",
        "शंकर",
        "सरल",
        "सतीश",
        "सवितृ",
        "शेखर",
        "सेठ",
        "शनि",
        "शंकर",
        "शङ्कर",
        "शंतनु",
        "शर्म",
        "शशि",
        "शेखर",
        "शेष",
        "शिव",
        "श्रेष्ठ",
        "श्रीपति",
        "श्याम",
        "श्यामल",
        "सिद्धार्थ",
        "सिकन्दर",
        "सोहेल",
        "सुभाष",
        "सुदर्शन",
        "सुधीर",
        "सुमन",
        "सुमन्त्र",
        "सुन्दर",
        "सुनील",
        "सुरज",
        "सुरेन्द्र",
        "सुरेश",
        "सूर्य",
        "सुशील",
        "स्वपन",
        "स्वप्निल",
        "स्वर्ण",
        "उत्तम",
        "वसन्त",
        "वासिष्ठ",
        "भरत",
        "विजय",
        "विजया",
        "विक्रम",
        "विमल",
        "विनय",
        "विपिन",
        "विपुल",
        "विशाल",
        "विष्णु",
        "विवेक",
        "यश",
    )

    # http://www.20000-names.com/female_hindi_names.htm
    first_names_female = (
        "आभा",
        "अभिलाषा",
        "अदिती",
        "ऐश्वर्या",
        "आकाङ्क्षा",
        "अमला",
        "अमिता",
        "अमृता",
        "आनन्दा",
        "अनिला",
        "अणिमा",
        "अंकिता",
        "अनुष्का",
        "अनुजा",
        "अर्चना",
        "अरुंधती",
        "आशा",
        "अवनी",
        "अवन्ती",
        "बल",
        "भरत",
        "चण्डा",
        "चन्दना",
        "चन्द्रकान्ता",
        "चेतना",
        "दमयंती",
        "दर्शना",
        "दीपाली",
        "दीप्ति",
        "देवी",
        "दीपाली",
        "दीप्ति",
        "दिव्या",
        "दुर्गा",
        "एषा",
        "गौहर",
        "गौरी",
        "गीता",
        "गोपीनाथ",
        "गुलज़ार",
        "इला",
        "इन्दिरा",
        "इन्द्रजित",
        "इन्दु",
        "ज़स्विन्देर्",
        "जया",
        "जयन्ती",
        "ज्योत्सना",
        "ज्योत्स्ना",
        "कैलाश",
        "कला",
        "काली",
        "कल्पना",
        "कमला",
        "कान्ता",
        "कान्ती",
        "करिश्मा",
        "काशी",
        "कौशल्या",
        "िकशोरी",
        "क्षितिज",
        "कुमारी",
        "कुंती",
        "लक्ष्मी",
        "लता",
        "लावण्या",
        "लक्ष्मी",
        "लीला",
        "लीलावती",
        "लीला",
        "लीला",
        "लीलावती",
        "लीना",
        "माधवी",
        "मधु",
        "मधुर",
        "माला",
        "मालती",
        "मनीषा",
        "मञ्जु",
        "मञ्जुला",
        "मञ्जूषा",
        "माया",
        "मीरा",
        "मोहना",
        "मोहिनी",
        "मुक्ता",
        "नेहा",
        "निखिला",
        "निशा",
        "नित्य",
        "पद्म",
        "पद्मावती",
        "पद्मिनी",
        "पार्वती",
        "परवीन",
        "पूर्णिमा",
        "प्रतिभा",
        "प्रतिमा",
        "प्रेमा",
        "प्रिया",
        "पूर्णिमा",
        "पुष्पा",
        "रचना",
        "राधा",
        "रजनी",
        "राज्य",
        "रानी",
        "रश्मी",
        "रति",
        "रत्न",
        "रेशमी",
        "रीतिका",
        "रिया",
        "रोहना",
        "रुक्मिणी",
        "रुपिन्द्र",
        "संजना",
        "सरला",
        "सरस्वती",
        "सारिका",
        "सती",
        "सावित्री",
        "सीमा",
        "सीता",
        "शक्ति",
        "शकुन्तला",
        "शान्ता",
        "शान्ती",
        "शर्मिला",
        "शशी",
        "शीला",
        "शिवाली",
        "शोभा",
        "श्यामा",
        "श्यामला",
        "सीमा",
        "सीता",
        "सितारा",
        "सोनल",
        "श्री",
        "सुदर्शना",
        "सुलभा",
        "सुमना",
        "सुमती",
        "सुनीता",
        "सुनीती",
        "सुशीला",
        "स्वर्ण",
        "तारा",
        "तृष्णा",
        "उमा",
        "उषा",
        "वसन्ता",
        "विद्या",
        "विजया",
        "विमला",
    )

    first_names = first_names_male + first_names_female

    # https://blogs.transparent.com/hindi/common-surnames-in-india/
    last_names = (
        # Common Surnames in North India (Delhi, Haryana, Punjab,etc)
        "शर्मा",
        "भट",
        "वर्मा",
        "कुमार",
        "गुप्ता",
        "मल्होत्रा",
        "भटनागर",
        "सक्सेना",
        "कपूर",
        "सिंह",
        "महरा",
        "चोपरा",
        "सरीन",
        "मालिक",
        "सैनी",
        "जैन",
        "कौल",
        "खत्री",
        "गोयल",
        "तिवारी",
        "भरद्वाज",
        "चोपरा",
        "प्रसाद",
        "आचार्य",
        "अगरवाल",
        "अहलूवालिया",
        "टंडन",
        "आहूजा",
        "अरोरा",
        # Common Surnames in East India: (Bengal, Orrisa, etc.)
        "चटर्जी",
        "चतुर्वेदी",
        "सेन",
        "बोस",
        "सेनगुप्ता",
        "दास",
        "दासगुप्ता",
        "मुख़र्जी",
        "दुत्ता",
        "बनर्जी",
        "चक्रवर्ती",
        "भट्टाचार्य",
        "घोष",
        "मित्रा",
        "गुहा",
        "सरकार",
        "साहा",
        "रॉय",
        "चोधरी",
        "रॉय चौधरी",
        "मजूमदार",
        "मंडल",
        "मैती",
        "कलिता",
        "हजारिका",
        "नाथ",
        "बुरुाह",
        "थापा",
        "गुरुंग",
        "राय",
        "प्रधान",
        "तमांग",
        "छेत्री",
        # Common Surnames in South India (Karnataka, Tamil Nadu, Kerala, etc.)
        "नायर",
        "मेनन",
        "पिल्लई",
        "वेंकटएसन",
        "बलासुब्रमानियम",
        "राव",
        "जयरामन",
        "सुब्रमण्यम",
        "रंगन",
        "रंगराजन",
        "नारायण",
        "रेड्डी",
        # Common Surnames in Central India (Bihar/ Uttar Pradesh, Madhya Pradesh, etc)
        "सिंह",
        "द्विवेदी",
        "मिश्रा",
        "त्रिवेदी",
        "झा",
        "शुक्ला",
        "यादव",
        "सिन्हा",
        "पाण्डेय",
        "झादव",
        "जेटली",
        "चौहान",
        "जोशी",
        "मिस्त्री",
        "खान",
        "श्रीवास्तव",
        # Common Surnames in West India (Maharashtra, Gujarat, Goa etc)
        "शाह",
        "देशपांडे",
        "गावडे",
        "कदम",
        "ताम्बे",
        "मेहता",
        "पटेल",
        "पाटिल",
        "पवार",
        "चवन",
        "डी’सोउज़ा",
        "लोबो",
        "रोद्रिगुएस",
        "डी’कोस्टा",
    )

    prefixes_male = ("श्री", "श्रीमान")

    prefixes_female = ("श्री", "श्रीमती")

    prefixes = (
        "माननीय",
        "आदरसूचक",
        "सम्मानसूचक",
        "संमानित",
        "आदरवाचक",
        "सम्मानात्मक",
    )

    suffixes = ("जी",)
