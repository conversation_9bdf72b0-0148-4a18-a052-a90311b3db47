Autor/Author: <PERSON><PERSON><PERSON> <<EMAIL>>

pt-BR: Este Divisor de Sílabas está em desenvolvimento por Raimundo Moura. Ele está
licenciado sob os termos da Licença Pública Geral Menor versão 3 (LGPLv3), como
publicado pela Free Software Foundation e pela Mozilla Public License como publicada
pela Mozilla Foundation. Os créditos estão disponíveis em
http://pt-br.libreoffice.org/projetos/projeto-vero-verificador-ortografico/
e você pode encontrar novas versões em
http://extensions.libreoffice.org

Copyright (C) 2008 - 2013 por/by Raimundo Santos Moura <<EMAIL>>

========================================================================================
APRESENTAÇÃO
========================================================================================

O Projeto DivSilab do LibreOffice é um projeto
colaborativo desenvolvido pela comunidade Brasileira.
A relação completa dos colaboradores deste projeto está em:
http://pt-br.libreoffice.org/projetos/projeto-vero-verificador-ortografico/

O DivSilab é uma ferramenta para realizar a translineação de textos.
Ele divide a palavra silabicamente, ficando parte no final da linha e o restante no início
da linha inferior.
======================
SOBRE ESTA ATUALIZAÇÃO
======================
Versão 1.0.8 DivSilab
Retirada de duplicadas:
1n2â
1s2â
1t2â
1x2â
1z2â
Inclusão de:
1s2õ      (pro.vi.sões)
Colaboração Adriano Konzen.

Inclusão/alteração de:
2e4as.
2e4os.      (alterado de 2eos.)
1n2ú        (alterado de 1n3ú)
1b2â        re.tum.bân.cia
1c2â        al.cân.ta.ra
1d2â        a.bun.dân.cia
1f2â        al.fân.de.ga
1g2â        a.mal.gâ.mi.co
1j2ô        em.jô.o
1m2â        ger.mâ.ni.co
1p2â        cam.pâ.nu.la
1p2è        am.pè.re
1r2â        ce.râ.mi.ca
1v2â        gal.vâ.ni.co
1x2ú        lu.xú.ria
Colaboração Adriano Konzen.

Inclusão de: 
e1â         pre.âm.bu.lo - o.ce.â.ni.co
h2â         bri.lhâ.ncia
i1â         di.â.me.tro
1j2â        ca.jâ.nea
a3ô         fa.ra.ô.ni.co
o3ô         al.co.ô.me.tro
u3ô         va.cu.ô.me.tro

Para corrigir separação de psi inclusão de: 
a3p2s2i1c
o2p3s2i1c
a3p2s2i1q
o2p3s2i1q
á2p3s2i
é2p3s2i
í2p3s2i
ó2p3s2i
2p1s2i
.p2s2i


â1n2a
â2n1c
â2n1d
â1n2e
â2n1f
â2n1g
â1n2h
â1n2i
â2n1j
â2n1n
â1n2o
â2n1q
â2n1r
â2n1s2i
â2n1s2e
â2n4s1c
â2n4s1f
â2n1t
â1n2u
â2n1v
â2n1z

Inclusão de regras para palavras acentuadas terminadas em io e ia 

2á1b2i4a
2á1c2i4a
2á1d2i4a
2á1f2i4a
2á1g2i4a
2á1l2i4a
2á1p2i4a
2á1r2i4a
2á1s2i4a
2á1t2i4a
2á1v2i4a
2á1x2i4a
2á1z2i4a
2â1m2i4a
2â1n2i4a
2é1b2i4a
2é1c2i4a
2é1d2i4a
2é1f2i4a
2é1g2i4a
2é1l2i4a
2é1n2i4a
2é1p2i4a
2é1r2i4a
2é1s2i4a
2é1t2i4a
2é1v2i4a
2é1x2i4a
2é1y2i4a
2é1z2i4a
2ê1m2i4a
2ê1n2i4a
2í1b2i4a
2í1c2i4a
2í1d2i4a
2í1f2i4a
2í1g2i4a
2í1j2i4a
2í1l2i4a
2í1m2i4a
2í1n2i4a
2í1p2i4a
2í1r2i4a
2í1s2i4a
2í1t2i4a
2í1v2i4a
2í1x2i4a
2í1z2i4a
2ó1b2i4a
2ó1c2i4a
2ó1d2i4a
2ó1f2i4a
2ó1g2i4a
2ó1j2i4a
2ó1l2i4a
2ó1p2i4a
2ó1r2i4a
2ó1s2i4a
2ó1t2i4a
2ó1v2i4a
2ó1x2i4a
2ó1z2i4a
2ô1d2i4a
2ô1m2i4a
2ô1n2i4a
2ú1b2i4a
2ú1c2i4a
2ú1d2i4a
2ú1f2i4a
2ú1g2i4a
2ú1l2i4a
2ú1m2i4a
2ú1n2i4a
2ú1p2i4a
2ú1r2i4a
2ú1s2i4a
2ú1v2i4a
2ú1x2i4a
2ú1z2i4a
2á1b2i4o
2á1c2i4o
2á1d2i4o
2á1f2i4o
2á1g2i4o
2á1l2i4o
2á1n2i4o
2á1p2i4o
2á1r2i4o
2á1s2i4o
2á1t2i4o
2á1v2i4o
2á1x2i4o
2á1z2i4o
2â1m2i4o
2â1n2i4o
2é1b2i4o
2é1c2i4o
2é1d2i4o
2é1f2i4o
2é1g2i4o
2é1l2i4o
2é1n2i4o
2é1p2i4o
2é1r2i4o
2é1s2i4o
2é1t2i4o
2é1v2i4o
2é1x2i4o
2é1z2i4o
2ê1m2i4o
2ê1n2i4o
2ê1s2i4o
2í1b2i4o
2í1c2i4o
2í1d2i4o
2í1f2i4o
2í1g2i4o
2í1l2i4o
2í1m2i4o
2í1n2i4o
2í1p2i4o
2í1r2i4o
2í1s2i4o
2í1t2i4o
2í1v2i4o
2í1z2i4o
2ó1b2i4o
2ó1c2i4o
2ó1d2i4o
2ó1f2i4o
2ó1g2i4o
2ó1l2i4o
2ó1m2i4o
2ó1p2i4o
2ó1r2i4o
2ó1s2i4o
2ó1t2i4o
2ó1v2i4o
2ó1x2i4o
2ó1z2i4o
2ô1d2i4o
2ô1m2i4o
2ô1n2i4o
2ú1b2i4o
2ú1c2i4o
2ú1d2i4o
2ú1f2i4o
2ú1g2i4o
2ú1j2i4o
2ú1l2i4o
2ú1m2i4o
2ú1n2i4o
2ú1p2i4o
2ú1r2i4o
2ú1s2i4o
2ú1t2i4o
2ú1v2i4o
2ú1z2i4o
2á4i1b2i4a
2â4m1b2i4a
2é4b1b2i4a
2é4r1b2i4a
2é4s1b2i4a
2í4l1b2i4a
2í4m1b2i4a
2í4r1b2i4a
2ó4b1b2i4a
2ô4m1b2i4a
2ó4s1b2i4a
2ú4m1b2i4a
2á4l1c2i4a
2â4n1c2i4a
2á4r1c2i4a
2á4u1c2i4a
2ê4n1c2i4a
2é4p1c2i4a
2é4r1c2i4a
2é4s1c2i4a
2ê4u1c2i4a
2í4l1c2i4a
2í4n1c2i4a
2í4r1c2i4a
2í4s1c2i4a
2ó4l1c2i4a
2ô4n1c2i4a
2ó4r1c2i4a
2ó4s1c2i4a
2ú4n1c2i4a
2ú4p1c2i4a
2ú4r1c2i4a
2á4b1d2i4a
2á4g1d2i4a
2á4l1d2i4a
2â4n1d2i4a
2á4r1d2i4a
2á4u1d2i4a
2ê4i1d2i4a
2é4l1d2i4a
2ê4n1d2i4a
2é4r1d2i4a
2í4l1d2i4a
2í4n1d2i4a
2ó4l1d2i4a
2ô4l1d2i4a
2ô4n1d2i4a
2ó4r1d2i4a
2ú4l1d2i4a
2ú4n1d2i4a
2ú4r1d2i4a
2â4n1f2i4a
2á4r1f2i4a
2é4l1f2i4a
2ó4l1f2i4a
2ô4n1f2i4a
2ó4r1f2i4a
2ú4l1f2i4a
2á4i1g2i4a
2á4l1g2i4a
2â4n1g2i4a
2á4r1g2i4a
2é4r1g2i4a
2í4n1g2i4a
2ô4n1g2i4a
2ó4r1g2i4a
2ú4n1g2i4a
2ú4r1g2i4a
2á1c4h2i4a
2á1l4h2i4a
2â1n4h2i4a
2é1c4h2i4a
2é4t1h2i4a
2í4g1h2i4a
2ô1n4h2i4a
2ú1c4h2i4a
2á1c4l2i4a
2á1g4l2i4a
2á4l1l2i4a
2á4r1l2i4a
2á4u1l2i4a
2é4l1l2i4a
2é4s1l2i4a
2é1t4l2i4a
2ê4u1l2i4a
2í1b4l2i4a
2í1c4l2i4a
2ó1g4l2i4a
2ó1p4l2i4a
2ô4u1l2i4a
2á4l1m2i4a
2á4r1m2i4a
2á4s1m2i4a
2á4u1m2i4a
2ê4i1m2i4a
2é4l1m2i4a
2é4r1m2i4a
2é4s1m2i4a
2ê4s1m2i4a
2í4m1m2i4a
2í4s1m2i4a
2ô4l1m2i4a
2ó4r1m2i4a
2ó4s1m2i4a
2á4f1n2i4a
2á4g1n2i4a
2â4g1n2i4a
2â4i1n2i4a
2â4m1n2i4a
2â4n1n2i4a
2á4u1n2i4a
2é4g1n2i4a
2ê4i1n2i4a
2é4r1n2i4a
2í4g1n2i4a
2í4m1n2i4a
2í4r1n2i4a
2ó4c1n2i4a
2ó4d1n2i4a
2ó4l1n2i4a
2ó4r1n2i4a
2ó4t1n2i4a
2ú4r1n2i4a
2á4l1p2i4a
2â4m1p2i4a
2á4p1p2i4a
2á4r1p2i4a
2é4r1p2i4a
2í4m1p2i4a
2í4p1p2i4a
2ó4r1p2i4a
2ó4s1p2i4a
2ú4l1p2i4a
2ú4p1p2i4a
2á1b4r2i4a
2á1c4r2i4a
2á1d4r2i4a
2á1f4r2i4a
2á1g4r2i4a
2â4n1r2i4a
2á4r1r2i4a
2á1t4r2i4a
2á4u1r2i4a
2é1b4r2i4a
2é1d4r2i4a
2é1f4r2i4a
2é4r1r2i4a
2é1t4r2i4a
2ê4u1r2i4a
2í1c4r2i4a
2í1d4r2i4a
2í1p4r2i4a
2í1t4r2i4a
2ó1d4r2i4a
2ó1f4r2i4a
2ô4i1r2i4a
2ó1p4r2i4a
2ó4r1r2i4a
2ó1t4r2i4a
2ô4u1r2i4a
2ú1g4r2i4a
2ú4r1r2i4a
2ú1t4r2i4a
2á4i1s2i4a
2â4n1s2i4a
2á4p1s2i4a
2á4r1s2i4a
2á4s1s2i4a
2á4t1s2i4a
2á4u1s2i4a
2é4l1s2i4a
2ê4n1s2i4a
2é4p1s2i4a
2é4r1s2i4a
2é4s1s2i4a
2é4t1s2i4a
2ê4y1s2i4a
2í4n1s2i4a
2í4p1s2i4a
2í4s1s2i4a
2í4t1s2i4a
2ô4n1s2i4a
2ó4p1s2i4a
2ó4r1s2i4a
2ó4s1s2i4a
2ô4u1s2i4a
2ú4c1s2i4a
2ú4n1s2i4a
2ú4s1s2i4a
2á4c1t2i4a
2á4l1t2i4a
2â4n1t2i4a
2á4p1t2i4a
2á4r1t2i4a
2á4s1t2i4a
2á4t1t2i4a
2é4c1t2i4a
2é4d1t2i4a
2ê4n1t2i4a
2é4r1t2i4a
2é4s1t2i4a
2é4t1t2i4a
2í4c1t2i4a
2í4d1t2i4a
2í4f1t2i4a
2í4n1t2i4a
2í4p1t2i4a
2í4s1t2i4a
2ô4n1t2i4a
2ó4r1t2i4a
2ó4s1t2i4a
2ó4t1t2i4a
2ú4r1t2i4a
2ú4s1t2i4a
2á1g4u2i4a
2á1q4u2i4a
2é1g4u2i4a
2é1q4u2i4a
2í1q4u2i4a
2ó1q4u2i4a
2ú1q4u2i4a
2á4l1v2i4a
2á4u1v2i4a
2é4r1v2i4a
2í4l1v2i4a
2ó4r1v2i4a
2á4r1x2i4a
2á4u1x2i4a
2â4u1x2i4a
2ê4i1x2i4a
2á4r1z2i4a
2ê4n1z2i4a
2é4t1z2i4a
2í4t1z2i4a
2ó4l1z2i4a
2ô4l1z2i4a
2ô4n1z2i4a
2ó4t1z2i4a
2ó4z1z2i4a
2â4m1b2i4o
2á4r1b2i4o
2é4r1b2i4o
2é4s1b2i4o
2í4m1b2i4o
2ô4m1b2i4o
2ó4r1b2i4o
2ú4m1b2i4o
2ú4r1b2i4o
2á4l1c2i4o
2â4n1c2i4o
2á4r1c2i4o
2á4s1c2i4o
2á4u1c2i4o
2é4l1c2i4o
2ê4n1c2i4o
2é4r1c2i4o
2é4s1c2i4o
2ê4u1c2i4o
2í4n1c2i4o
2í4p1c2i4o
2í4r1c2i4o
2í4s1c2i4o
2ô4n1c2i4o
2ó4r1c2i4o
2ó4s1c2i4o
2ú4l1c2i4o
2ú4n1c2i4o
2ú4r1c2i4o
2ú4s1c2i4o
2á4b1d2i4o
2â4n1d2i4o
2á4r1d2i4o
2á4u1d2i4o
2ê4i1d2i4o
2ê4n1d2i4o
2í4l1d2i4o
2í4n1d2i4o
2ô4n1d2i4o
2ó4r1d2i4o
2ú4n1d2i4o
2ú4r1d2i4o
2á4r1f2i4o
2é4l1f2i4o
2ê4n1f2i4o
2í4l1f2i4o
2ú4n1f2i4o
2á4l1g2i4o
2â4n1g2i4o
2á4r1g2i4o
2á4s1g2i4o
2é4l1g2i4o
2é4r1g2i4o
2í4n1g2i4o
2ô4n1g2i4o
2ó4r1g2i4o
2ú4r1g2i4o
2á1c4h2i4o
2ó1c4h2i4o
2á1b4l2i4o
2á1c4l2i4o
2á4u1l2i4o
2ê4i1l2i4o
2í1b4l2i4o
2í1c4l2i4o
2í1g4l2i4o
2ó1c4l2i4o
2ó1g4l2i4o
2ó1p4l2i4o
2ú1c4l2i4o
2á4d1m2i4o
2á4g1m2i4o
2á4i1m2i4o
2á4j1m2i4o
2á4l1m2i4o
2á4s1m2i4o
2á4t1m2i4o
2é4l1m2i4o
2é4r1m2i4o
2é4s1m2i4o
2í4s1m2i4o
2ó4c1m2i4o
2ó4l1m2i4o
2ó4r1m2i4o
2ó4s1m2i4o
2á4c1n2i4o
2á4f1n2i4o
2á4g1n2i4o
2â4m1n2i4o
2á4r1n2i4o
2á4u1n2i4o
2é4g1n2i4o
2ê4i1n2i4o
2ê4m1n2i4o
2é4r1n2i4o
2í4c1n2i4o
2í4f1n2i4o
2í4g1n2i4o
2í4m1n2i4o
2í4r1n2i4o
2í4s1n2i4o
2í4t1n2i4o
2ó4d1n2i4o
2ó4g1n2i4o
2ó4r1n2i4o
2ó4s1n2i4o
2ú4b1n2i4o
2ú4r1n2i4o
2á4l1p2i4o
2â4m1p2i4o
2á4r1p2i4o
2á4s1p2i4o
2á4u1p2i4o
2é4s1p2i4o
2í4m1p2i4o
2ó4l1p2i4o
2ó4r1p2i4o
2ó4s1p2i4o
2ú4l1p2i4o
2á4b1r2i4o
2á1c4r2i4o
2á1d4r2i4o
2á1f4r2i4o
2á1g4r2i4o
2á4r1r2i4o
2á4s1r2i4o
2á1t4r2i4o
2á4u1r2i4o
2é1b4r2i4o
2é1d4r2i4o
2é1g4r2i4o
2ê1n4r2i4o
2é1t4r2i4o
2ê4u1r2i4o
2í1b4r2i4o
2í1d4r2i4o
2í1p4r2i4o
2í1t4r2i4o
2ó1b4r2i4o
2ó1c4r2i4o
2ó1f4r2i4o
2ó4h1r2i4o
2ó1p4r2i4o
2ó4r1r2i4o
2ó1t4r2i4o
2ú1b4r2i4o
2ú4h1r2i4o
2ú4r1r2i4o
2ú1t4r2i4o
2á4i1s2i4o
2á4r1s2i4o
2á4s1s2i4o
2á4u1s2i4o
2ê4n1s2i4o
2é4p1s2i4o
2é4r1s2i4o
2é4s1s2i4o
2í4s1s2i4o
2ô4n1s2i4o
2ó4p1s2i4o
2ó4s1s2i4o
2ú4r1s2i4o
2ú4s1s2i4o
2á4c1t2i4o
2â4n1t2i4o
2á4r1t2i4o
2á4s1t2i4o
2é4l1t2i4o
2ê4n1t2i4o
2é4r1t2i4o
2é4s1t2i4o
2í4c1t2i4o
2í4n1t2i4o
2í4p1t2i4o
2í4r1t2i4o
2í4s1t2i4o
2ó4c1t2i4o
2ó4l1t2i4o
2ô4n1t2i4o
2ó4p1t2i4o
2ó4s1t2i4o
2ú4r1t2i4o
2ú4s1t2i4o
2á1q4u2i4o
2é1q4u2i4o
2í1q4u2i4o
2ó1q4u2i4o
2á4l1v2i4o
2â4n1v2i4o
2é4l1v2i4o
2é4r1v2i4o
2í4l1v2i4o
2í4n1v2i4o
2ó4b1v2i4o
2ú4l1v2i4o
2é4l1z2i4o



Versão 1.0.7 DivSilab

Correção da quebra de sílabas das palavras com 'guin', como: 'seguintes' ERRADO(se-gu-in-tes)
Colaboração Flávio Cardoso;
inclusão das regras:
1g2u4i3n2a
1g2u4i3n2á
1g2u4i2n1c
1g2u4i2n1d
1g2u4i3n2e
1g2u4i3n2é
1g2u4i2n1g
1g2u4i3n2h
1g2u4i3n2i
1g2u4i3n2í
1g2u4i2n1j
1g2u4i3n2o
1g2u4i3n2ó
1g2u4i2n2s
1g2u4i2n1t
1g2u4i3n2u
1g2u4i2n1x


Versão 1.0.6 DivSilab

Correção da quebra de sílabas das palavras com 'br', como: 'vibração' ERRADO(vib-ra-ção)
Colaboração Leandro Dutra;
Correção da quebra de sílabas das palavras com 'psic', como: 'neuropsicologia' ERRADO(neu-rop-si-co-lo-gia)
Colaboração Raimundo;
Correção da quebra de sílabas das palavras com 'uin', como: 'contribuinte' ERRADO(con-tri-buin-te)
Colaboração João Paulo Vinha Bittar;



Versão 1.0.5 DivSilab

Correção da quebra de sílabas das palavras com 'gn', como: 'incógnito' ERRADO(in-có-g-ni-to)

Exclusão dos códigos
ó2s3t2
2g1g2
2g3s2
2g1t2
2m1m2
1n2ã
2n3c42
2n3d2
2n3t2
2p1p2
2t3g2
2d1g2
2d1d2
2d1t2
ó1g2a
ó1g2e
ó1g2i
ó1g2l2
ó2g3n2
ó1g2o
ó1g2r2
ó2g1u
o1g2a
o1g2á
o1g2â
o1g2ã
o2g1b2
o2g1c2
o2g1d2
o1g2e
o1g2é
o1g2ê
o2g1g2
o1g2i
o1g2í
o1g4l2
o2g1m2
o2g1n2
o1g2o
o1g2ó
o1g2ô
o1g4r2
o2g1s2
o2g1t2
o1g2u
o1g2ú
i1g2a
i1g2á
i1g2â
i1g2ã
i2g1b2
i2g1d2
i1g2e
i1g2é
i1g2ê
i2g1g2
i2g1h2
i1g2i
i1g2í
i1g4l2
i2g1m2
i2g1n2
i1g2o
i1g2ó
i1g2ô
i1g2õ
i2g1p2
i1g4r2
i2g1s2
i2g1t2
i1g2u
i1g2ú
i2g1v2
i1g2y
í1g2a
í2g1d2
í1g2e
í2g1h2
í1g2i
í1g4l2
í2g1m2
í2g1n2
í1g2o
í1g4r2
í1g2u
u1g2a
u1g2á
u1g2â
u1g2ã
u2g1b2
u2g1d2
u1g2e
u1g2é
u1g2ê
u2g1g2
u2g1h2
u1g2i
u1g2í
u1g4l2
u2g1m2
u2g1n2
u1g2o
u1g2ó
u1g2ô
u1g4r2
u2g1t2
u1g2u
u1g2ú
ú1g2a
ú2g1b2
ú1g2e
ú1g2i
ú1g2o
ú1g4r2
ú1g2u

=========================================================
Versão 1.0.4 DivSilab

Sílabas 'a1rô' estão duplicadas com 'a1rô'. Ignorada!
Sílabas '2a4y3i1' estão duplicadas com '2a4y3i'. Usada '2a4y3i1'!
Sílabas '2a4y3l' estão duplicadas com '2a4yl3'. Concatenada '2a4y3l3'!
Sílabas '1co3ê' estão duplicadas com '1co3ê'. Ignorada!
Sílabas '1co3ê' estão duplicadas com '1co3ê'. Ignorada!
Sílabas '1co3ê' estão duplicadas com '1co3ê'. Ignorada!
Sílabas 'e3r2o1m2a' estão duplicadas com 'e3r2o1m2a'. Ignorada!
Sílabas 'e3r2o1m2e' estão duplicadas com 'e3r2o1m2e'. Ignorada!
Sílabas 'e3r2o1m2i' estão duplicadas com 'e3r2o1m2i'. Ignorada!
Sílabas 'e3r4o2m1n2' estão duplicadas com 'e3r2o2m1n2'. Usada 'e3r4o2m1n2'!
Sílabas 'e3r2o1m2o' estão duplicadas com 'e3r2o1m2o'. Ignorada!
Sílabas 'e3r2o1m2u' estão duplicadas com 'e3r2o1m2u'. Ignorada!
Sílabas 'e3r2o1m2á' estão duplicadas com 'e3r2o1m2á'. Ignorada!
Sílabas 'e3r2o1m2â' estão duplicadas com 'e3r2o1m2â'. Ignorada!
Sílabas 'e3r2o1m2é' estão duplicadas com 'e3r2o1m2é'. Ignorada!
Sílabas 'e3r2o1m2í' estão duplicadas com 'e3r2o1m2í'. Ignorada!
Sílabas 'e3r2o1m2ó' estão duplicadas com 'e3r2o1m2ó'. Ignorada!
Sílabas 'e3r2o1m2ô' estão duplicadas com 'e3r2o1m2ô'. Ignorada!
Sílabas '1lo3w' estão duplicadas com 'l2ow1'. Concatenada '1l2o3w1'!
Sílabas 'o3g2' estão duplicadas com 'o3g2'. Ignorada!
Sílabas 'r2a2i4s.' estão duplicadas com 'r2a2i4s.'. Ignorada!
Sílabas 'r2a3i2s.' estão duplicadas com 'r2a2i4s.'. Concatenada 'r2a3i4s.'!
Sílabas '1rais.' estão duplicadas com 'r2a2i4s.'. Concatenada '1r2a3i4s.'!
Sílabas 'r2a3y' estão duplicadas com 'r2a2y3'. Concatenada 'r2a3y3'!
Sílabas '1ra3ó' estão duplicadas com 'r2a3ó'. Concatenada '1r2a3ó'!
Sílabas '1s2e' estão duplicadas com '1s2e'. Ignorada!
Colaboração Adriano Konzen

Correção da quebra de sílabas das palavras 'envolvidos' (en-vo-lvi-dos)
substituição  de 'o1l2v' por 'o2l1v'
Colaboração João Paulo Vinha Bittar

Inclusão dos códigos
t3g
1t4h2
1t2i
1t2í
1t2l4
2t3m4
1t3n2
1t2o
1t2ó
1t2ô
1t2õ4
1t4r2
2t3s4
2t3t4
1t2u
1t2ú
2t3z
u2a
g4u6á
u1á
q4u6á
u1ã
g4u6ã
q4u6ã
u1b2á
u1b2a
u2b1z2
u2bl
u1b2â
u2b1n2
u2b1t2
u2b1m2
u2b1j2
u2b1f2
u1b2ú
u2b1v2
u2b1p2
u2br
u1b2u
u1b2i
u1b2é
u1b2e
u2b1d2
u1b2ô
u1b2ã
u1b2ó
u2b1g2
u2b1c2
u2b1s2
u2b1q4
u1b2í
u1b2o
ú3b2
.s2u4b3s4i6s1
.s2u4b3s4e6r1
.s2u4b3s2e1
.s2u4b3l2i5n4h2
1s2u4b4s3t2
.s2u4b5r2
.s2u4b3n2
.s2u4b3s2i1
.s2u4b4s3t2
.s2u4b4s3c2
.s2u4b3s2í1
.s2u4b3m2
.s2u4b3l2i5m2i3n2
.s2u4b3l2o5
.s2u4b3p2
u1c2á
u1c2o
u1c2í
u1c4h2
u1c2ú
u1c2u
u1c2ô
u1c2e
u1c2ê
u1c2ã
u2c1n2
u1c2é
u2c1ç2
u1c2a
u2c1z2
u1c2y
u2c1m2
u1c4r2
u2c1s2
u1c2ó
u1c2i
u2c1t2
u2c1c2
u1c2â
u1c4l2
ú3c2
u3d2
ú3d2
u2e
u1é
q4u6é
g4u6é
u1ê
g4u6ê
q4u6ê
u3f2
u3g2
ú1g
u2i
u1í
g4u6í
q4u6í
u3j2
u1l2o
u2l1ç2
u2l1d2
u1l2õ4
u1l2ô
u2l1c2
u1l2a
u2l1b2
u2l1p2
u2l1g2
u2l1t2
u1l2e
u2l1q4
u1l2u
u2l1f2
u2l1m2
u2l1l2
u2l1z2
u1l2ó
u1l2ã
u1l2â
u2l1s2
u1l2á
u2l1x2
u2l1n2
u1l4h2
u2l1v2
u1l2í
u2l1r2
u1l2ú
u1l2é
u1l2ê
u1l2i
ú2l1m2
ú2l1v2
ú1l2u
ú1l2e
ú2l1f2
ú1l4h2
ú2l1p2
ú2l1t2
ú1l2a
ú2l1g2
ú1l2o
ú1l2i
ú2l1d2
ú2l1b2
ú2l1c2
u2m1m2
u1m2é
u2m1t2
u1m2í
u2m1z2
u2m1n2
u1m2a
u1m2â
u1m2i
u2m1h2
u1m2o
u1m2ó
u1m2ã
u1m2e
u2m1l2
u1m2á
u2m1b2
u2m1p2
u1m2ô
u1m2ú
u1m2ê
u1m2u
ú2m1n2
ú1m2e
ú1m2a
ú2m1p2
ú1m2i
ú1m2u
ú2m1b2
u2n4s1v2
u2n1l2
u2n1x2
u2n1z2
u2n4s1p2
u2n1f2
u2n1t2
u2n1v2
u2n4s1f2
u2n4s1t2
u2n4s.
u2n4s1d2
u2n1r2
u2n1g2
u2n1s2é
u2n1b2
u2n1ç2
u2n1d2
u2n1s2i
u2n1s2o
u2n1j2
u2n1k2
u2n1n2
u1n2í
u1n2ó
u1n2ú
u1n2i
u1n2é
u1n2e
u1n2ã
u1n2â
u1n2á
u2n1q4
u1n2a
u2n4s1c2
u1n2o
u1n2ô
u1n4h2
u2n1s2e
u1n2u
u2n1c2
ú1n2i
ú2n1q4
ú1n2e
ú1n4h2
ú1n2o
ú2n1d2
ú2n1z2
ú1n2a
ú2n1v2
ú2n1s2i
ú2n1g2
ú2n3s.
ú2n1c2
ú2n1j2
ú2n1f2
ú1n2u
u2o
2u1ó3
u3p2
ú3p2
u3q4
u2r1z2
u2r1j2
u2r1t2
u2r3r2
u1r2ú
u2r1c2
u2r1s2
u2r1b2
u1r2i
u2r1f2
u1r2a
u1r2ô
u2r1d2
u1r2õ4
u1r2í
u1r2e
u2r1n2
u1r2ó
u1r2á
u1r2â
u1r2o
u2r1h2
u1r2é
u1r2ê
u1r2ã
u2r1g2
u2r1q4
u2r1m2
u2r1ç2
u2r1p2
u2r1k2
u1r2y
u2r1v2
u1r2u
u2r1l2
ú3r2
u1s2u
u2s1z2
u2s1q4
u2s1b2
u1s4h2
u2s3s2
u2s1t2
u2s1j2
u2s1n2
u2s1m2
u2s1v2
u2s1g2
u2s1c2
u1s2i
u2s1l2
u1s2o
u1s2á
u1s2ó
u1s2ú
u2s1f2
u2s1d2
u1s2a
u1s2é
u1s2í
u1s2ê
u1s2â
u1s2ã
u1s2e
u1s2ô
u2s1p2
u2s1r2
ú2s1p2
ú2s3s2
ú2s1m2
ú2s1q4
ú1s2a
ú2s1t2
ú2s1n2
ú1s2o
ú1s2e
ú1s2i
ú2s1c2
u3t2
ú3t2
u3u
u3v2
ú3v2
u3x2
u1z2a
u1z2ã
u1z2o
u1z2ú
u1z2â
u1z2e
u1z2í
u2z1m2
u2z1b2
u1z2á
u2z1z2
u1z2ô
u1z2ê
u2z1l2
u1z2i
u1z2u
u1z2ó
u1z2é
1v2a
1v2á
1v2ã
1v2e
1v2é
1v2ê
1v2i
1v2í
1v2o
1v2ó
1v2ô
1võe3z
1vões
1v4r2
1v2u
w2a
1x2a
1x2á
1x2ã
2x3c4
1x2e
1x2é
1x2ê
1x2i
1x2í
1x2o
1x2ó
1x2ô
2x3p4
x1s
2x3t4
1x2u
y2a
1ye
.yer1
1z2a
1z2á
1z2ã
1z2e
1z2é
1z2ê
1z2i
z4í
1z2o
1z2ó
1z2ô
1z2u
z1z
e1õ2e3z


==========

Versão 1.0.3 DivSilab. Nela foram implementados os seguintes recursos:

.Inclusão de regras para o par 'eg'. Corrigindo translineação de 'segmento'.
.Inclusão de regras para o par 'ég'.
.Inclusão de regras para o par 'eb'.
.Inclusão de regras para o par 'éb'.
.Inclusão de regras para o par 'ed'.
.Inclusão de regras para o par 'éd'.
.Inclusão de regras para o par 'ef'.
.Inclusão de regras para o par 'éf'.
.Inclusão de regras para os pares: ab, áb, ad, ád, af, áf, ag, ág, ah,aj,ak, at e át.
.Correção de regras para o par 'ob'.Corrigindo translineação de 'problema'.

=======================
Versões Anteriores

Versão 1.0.2 DivSilab

Nesta atualização foi implementado o DivSilab - Divisor de Sílabas do LibreOffice.

Desenvolvido com base no léxico do VERO, através de análise combinatória,
extraíndo-se os casos reais e descartando-se as condições inexistentes.

O DivSilab é um arquivo texto. Seu desenvolvimento está baseado no algorítmo de Frank M. Liang.
O algorítimo de Liang usa valores entre 0 e 9 exclui-se o valor 0 (zero), uma vez que está
implícito. Valores ímpares indicam possíveis pontos divisão silábica, e os valores pares
(incluindo 0) indicam pontos que não devem ter divisão. Números mais altos indicam uma maior
magnitude do "melhor" para os números ímpares, e pares uma maior amplitude do "pior".
O ponto (.) indica a extremidade de uma palavra. À esquerda início e à direita término.

Exemplo:
Examinemos a palavra a baixo.

  Silábicas

Extraindo-se do arquivo somente as regras envolvidas para a formação desta palavra vamos encontrar:

  s2i
  i3l2á
  l4á
  á1b2
  3b2i
  i1c4
  3c2a
  2s.

Compondo teremos:

obs.: mude a fonte para Courier New para visualizar melhor o exemplo.

s i l á b i c a s
s2i
    l4á
  i3l2á
    l4á
      á1b2
       3b2i
          i1c4
           3c2a
               2s.
------------------
s2i3l4á3b2i3c4a2s   <--- Resultado
s i-l á-b i-c a s --> si-lá-bi-cas

Obs.: ao resultado é agregado o maior peso de cada coluna.



Esquema de Liang
--------------------------------------------------
Descrição         Peso       Entrada Liang
--------------------------------------------------
Melhor que abaixo   5             9
Melhor que abaixo   4             7
Melhor que abaixo   3             5
Melhor que abaixo   2             3
Admissível          1             1
Evitar              0             0
Pior que acima     -1             2
Pior que acima     -2             4
Pior que acima     -3             6
Pior que acima     -4             8
--------------------------------------------------

No nosso trabalho usamos até o nível 6.
Foram desenvolvidos três pequenos aplicativos. Um para gerar combinações ' ab','ac' ,'ad'
... 'ich', 'ras', etc, etc.
O outro para conferir a existência destas combinações no léxico do VERO, extraindo somente
as válidas e o número de ocorrências de cada uma.
E um terceiro para extrair as regras envolvidas na composição de uma determinada palavra.
Usamos uma planilha no Calc para ordenar e configurar as regras, começando pelas de maior
incidência. As combinações óbvias como 'ca', 'ba', 'pr', dr, bl...
foram rapidamente configuradas ...'c2a', 'b2a', 'p4r', 'd4r', 'b4l' ...
Para os mais complexos, extraímos do Vero todas as palavras contempladas com a combinação
pesquisada, e criamos as regras. Exemplo 'ic'
mICAreta --> i3c2a
frICCionar --> i2c3c4
mICRo  --> i2c3r4
...
