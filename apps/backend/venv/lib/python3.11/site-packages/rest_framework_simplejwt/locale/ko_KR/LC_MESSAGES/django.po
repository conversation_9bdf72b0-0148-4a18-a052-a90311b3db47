# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-05 06:48+0900\n"
"Last-Translator: 양영광 <<EMAIL>>\n"
"Language: ko_KR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "인증 헤더에는 공백으로 구분 된 두 개의 값이 포함되어야 합니다"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "이 토큰은 모든 타입의 토큰에 대해 유효하지 않습니다"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "토큰에 사용자 식별자가 포함되어 있지 않습니다"

#: authentication.py:132
msgid "User not found"
msgstr "찾을 수 없는 사용자"

#: authentication.py:135
msgid "User is inactive"
msgstr "비활성화된 사용자"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:69
msgid "Unrecognized algorithm type '{}'"
msgstr "알 수 없는 알고리즘 유형 '{}'"

#: backends.py:75
msgid "You must have cryptography installed to use {}."
msgstr "{}를 사용하려면 암호화가 설치되어 있어야 합니다."

#: backends.py:90
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"알 수 없는 타입 '{}', 'leeway' 값은 반드시 int, float 또는 timedelta 타입이어"
"야 합니다."

#: backends.py:104 backends.py:154 exceptions.py:47 tokens.py:58
msgid "Token is invalid or expired"
msgstr "유효하지 않거나 만료된 토큰"

#: backends.py:152
msgid "Invalid algorithm specified"
msgstr "잘못된 알고리즘이 지정되었습니다"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "지정된 자격 증명에 해당하는 활성화된 사용자를 찾을 수 없습니다"

#: settings.py:73
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr "'{}' 설정이 제거되었습니다. 사용 가능한 설정은 '{}'을 참조하십시오."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "사용자"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "생성 시간"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "만료 시간"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "토큰 블랙리스트"

#: tokens.py:44
msgid "Cannot create token with no type or lifetime"
msgstr "타입 또는 수명이 없는 토큰을 생성할 수 없습니다"

#: tokens.py:116
msgid "Token has no id"
msgstr "토큰에 식별자가 주어지지 않음"

#: tokens.py:128
msgid "Token has no type"
msgstr "토큰 타입이 주어지지 않음"

#: tokens.py:131
msgid "Token has wrong type"
msgstr "잘못된 토큰 타입"

#: tokens.py:190
msgid "Token has no '{}' claim"
msgstr "토큰에 '{}' 클레임이 없음"

#: tokens.py:195
msgid "Token '{}' claim has expired"
msgstr "토큰 '{}' 클레임이 만료되었습니다"

#: tokens.py:257
msgid "Token is blacklisted"
msgstr "블랙리스트에 추가된 토큰"
