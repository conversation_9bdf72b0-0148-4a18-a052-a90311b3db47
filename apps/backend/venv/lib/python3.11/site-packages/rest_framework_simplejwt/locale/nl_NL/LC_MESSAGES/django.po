# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-17 11:06+0200\n"
"Last-Translator: rene <<EMAIL>>\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Authorisatie header moet twee waarden bevatten, gescheiden door een spatie"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Het token is voor geen enkel token-type geldig"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token bevat geen herkenbare gebruikersidentificatie"

#: authentication.py:132
msgid "User not found"
msgstr "Gebruiker niet gevonden"

#: authentication.py:135
msgid "User is inactive"
msgstr "Gebruiker is inactief"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:69
msgid "Unrecognized algorithm type '{}'"
msgstr "Niet herkend algoritme type '{}"

#: backends.py:75
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:90
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:104 backends.py:154 exceptions.py:47 tokens.py:58
msgid "Token is invalid or expired"
msgstr "Token is niet geldig of verlopen"

#: backends.py:152
msgid "Invalid algorithm specified"
msgstr ""

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Geen actief account gevonden voor deze gegevens"

#: settings.py:73
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"De '{}' instelling bestaat niet meer. Zie '{}' for beschikbareinstellingen."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "gebruiker"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "aangemaakt op"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "verloopt op"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: tokens.py:44
msgid "Cannot create token with no type or lifetime"
msgstr "Kan geen token maken zonder type of levensduur"

#: tokens.py:116
msgid "Token has no id"
msgstr "Token heeft geen id"

#: tokens.py:128
msgid "Token has no type"
msgstr "Token heeft geen type"

#: tokens.py:131
msgid "Token has wrong type"
msgstr "Token heeft het verkeerde type"

#: tokens.py:190
msgid "Token has no '{}' claim"
msgstr "Token heeft geen '{}' recht"

#: tokens.py:195
msgid "Token '{}' claim has expired"
msgstr "Token '{}' recht is verlopen"

#: tokens.py:257
msgid "Token is blacklisted"
msgstr "Token is ge-blacklist"
