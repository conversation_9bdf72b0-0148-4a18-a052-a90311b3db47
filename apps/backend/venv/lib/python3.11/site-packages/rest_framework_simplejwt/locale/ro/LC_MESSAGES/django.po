# This file is distributed under the same license as the PACKAGE package.
# FIRST AUTHOR <PERSON> <danie<PERSON><PERSON><PERSON><EMAIL>>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-13 10:45+0100\n"
"Last-Translator: <PERSON> <daniel<PERSON><EMAIL>>\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Header-ul(antetul) de autorizare trebuie să conțină două valori separate "
"prin spațiu"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Tokenul dat nu este valid pentru niciun tip de token"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Tokenul nu conține date de identificare a utilizatorului"

#: authentication.py:132
msgid "User not found"
msgstr "Utilizatorul nu a fost găsit"

#: authentication.py:135
msgid "User is inactive"
msgstr "Utilizatorul este inactiv"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:69
msgid "Unrecognized algorithm type '{}'"
msgstr "Tipul de algoritm '{}' nu este recunoscut"

#: backends.py:75
msgid "You must have cryptography installed to use {}."
msgstr "Trebuie să aveți instalată criptografia pentru a utiliza {}."

#: backends.py:90
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"Tipul '{}' nu este recunoscut, 'leeway' trebuie să fie de tip int, float sau "
"timedelta."

#: backends.py:104 backends.py:154 exceptions.py:47 tokens.py:58
msgid "Token is invalid or expired"
msgstr "Token nu este valid sau a expirat"

#: backends.py:152
msgid "Invalid algorithm specified"
msgstr "Algoritm nevalid specificat"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Nu a fost găsit cont activ cu aceste date de autentificare"

#: settings.py:73
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Setarea '{}' a fost ștearsă. Referați la '{}' pentru setări disponibile."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "utilizator"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "creat la"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "expiră la"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Listă de token-uri blocate"

#: tokens.py:44
msgid "Cannot create token with no type or lifetime"
msgstr "Nu se poate crea token fără tip sau durată de viață"

#: tokens.py:116
msgid "Token has no id"
msgstr "Tokenul nu are id"

#: tokens.py:128
msgid "Token has no type"
msgstr "Tokenul nu are tip"

#: tokens.py:131
msgid "Token has wrong type"
msgstr "Tokenul are tipul greșit"

#: tokens.py:190
msgid "Token has no '{}' claim"
msgstr "Tokenul nu are reclamația '{}'"

#: tokens.py:195
msgid "Token '{}' claim has expired"
msgstr "Reclamația tokenului '{}' a expirat"

#: tokens.py:257
msgid "Token is blacklisted"
msgstr "Tokenul este în listă de tokenuri blocate"
