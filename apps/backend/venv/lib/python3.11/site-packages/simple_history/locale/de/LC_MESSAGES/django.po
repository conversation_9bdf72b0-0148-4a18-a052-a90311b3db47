# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: django-simple-history\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-11-21 15:58+0100\n"
"PO-Revision-Date: 2018-11-21 16:31+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
#: simple_history/admin.py:78
#, python-format
msgid "Change history: %s"
msgstr "Änderungshistorie: %s"

#: simple_history/admin.py:97
#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr "%(name)s \"%(obj)s\" wurde erfolgreich geändert."

#: simple_history/admin.py:103
msgid "You may edit it again below"
msgstr "Sie können es unten wieder bearbeiten"

#: simple_history/admin.py:162
#, python-format
msgid "Revert %s"
msgstr "%s wiederherstellen"

#: simple_history/models.py:314
msgid "Created"
msgstr "Erstellt"

#: simple_history/models.py:314
msgid "Changed"
msgstr "Geändert"

#: simple_history/models.py:314
msgid "Deleted"
msgstr "Gelöscht"

#: simple_history/templates/simple_history/_object_history_list.html:9
msgid "Object"
msgstr "Objekt"

#: simple_history/templates/simple_history/_object_history_list.html:13
msgid "Date/time"
msgstr "Datum/Uhrzeit"

#: simple_history/templates/simple_history/_object_history_list.html:14
msgid "Comment"
msgstr "Kommentar"

#: simple_history/templates/simple_history/_object_history_list.html:15
msgid "Changed by"
msgstr "Geändert von"

#: simple_history/templates/simple_history/_object_history_list.html:16
msgid "Change reason"
msgstr "Änderungsgrund"

#: simple_history/templates/simple_history/_object_history_list.html:37
msgid "None"
msgstr "Keine/r"

#: simple_history/templates/simple_history/object_history.html:11
msgid ""
"Choose a date from the list below to revert to a previous version of this "
"object."
msgstr ""
"Wählen Sie eine Version des Objektes aus der untenstehenden Liste, um diese "
"wiederherzustellen."

#: simple_history/templates/simple_history/object_history.html:17
msgid "This object doesn't have a change history."
msgstr "Dieses Objekt hat keine Änderungshistorie."

#: simple_history/templates/simple_history/object_history_form.html:7
msgid "Home"
msgstr "Start"

#: simple_history/templates/simple_history/object_history_form.html:11
msgid "History"
msgstr "Änderungshistorie"

#: simple_history/templates/simple_history/object_history_form.html:12
#, python-format
msgid "Revert %(verbose_name)s"
msgstr "%(verbose_name)s wiederherstellen"

#: simple_history/templates/simple_history/object_history_form.html:21
msgid ""
"Press the 'Revert' button below to revert to this version of the object."
msgstr ""
"Klicken Sie unten auf 'Wiederherstellen', um diese Version des Objektes "
"wiederherzustellen."

#: simple_history/templates/simple_history/object_history_form.html:21
msgid "Or press the 'Change History' button to edit the history."
msgstr "Oder wählen Sie 'Historie ändern', um diese zu bearbeiten."

#: simple_history/templates/simple_history/submit_line.html:3
msgid "Revert"
msgstr "Wiederherstellen"

#: simple_history/templates/simple_history/submit_line.html:4
msgid "Change History"
msgstr "Historie ändern"
