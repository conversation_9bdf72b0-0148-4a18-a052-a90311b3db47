# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-simple-history\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-10 16:47+0300\n"
"PO-Revision-Date: 2021-10-14 14:05+0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ru_RU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);\n"
"X-Generator: Poedit 3.0\n"

#: admin.py:77
#, python-format
msgid "Change history: %s"
msgstr "История изменений: %s"

#: admin.py:96
#, python-format
msgid "The %(name)s \"%(obj)s\" was changed successfully."
msgstr "%(name)s \"%(obj)s\" было успешно изменено."

#: admin.py:102
msgid "You may edit it again below"
msgstr "Вы можете отредактировать его снова ниже"

#: admin.py:160
#, python-format
msgid "Revert %s"
msgstr "Восстановить %s"

#: models.py:304
msgid "Created"
msgstr "Создано"

#: models.py:305
msgid "Changed"
msgstr "Изменено"

#: models.py:306
msgid "Deleted"
msgstr "Удалено"

#: templates/simple_history/_object_history_list.html:9
msgid "Object"
msgstr "Объект"

#: templates/simple_history/_object_history_list.html:13
msgid "Date/time"
msgstr "Дата/время"

#: templates/simple_history/_object_history_list.html:14
msgid "Comment"
msgstr "Комментарий"

#: templates/simple_history/_object_history_list.html:15
msgid "Changed by"
msgstr "Изменено"

#: templates/simple_history/_object_history_list.html:36
msgid "None"
msgstr "None"

#: templates/simple_history/object_history.html:11
msgid ""
"Choose a date from the list below to revert to a previous version of this "
"object."
msgstr ""
"Выберите дату из списка ниже, чтобы вернуться к предыдущей версии этого "
"объекта."

#: templates/simple_history/object_history.html:17
msgid "This object doesn't have a change history."
msgstr "Этот объект не имеет истории изменений."

#: templates/simple_history/object_history_form.html:7
msgid "Home"
msgstr "Главная"

#: templates/simple_history/object_history_form.html:11
msgid "History"
msgstr "История"

#: templates/simple_history/object_history_form.html:12
#, python-format
msgid "Revert %(verbose_name)s"
msgstr "Восстановить %(verbose_name)s"

#: templates/simple_history/object_history_form.html:21
msgid ""
"Press the 'Revert' button below to revert to this version of the object."
msgstr ""
"Нажмите кнопку 'Восстановить' ниже, чтобы вернуться к этой версии объекта."

#: templates/simple_history/object_history_form.html:21
msgid "Or press the 'Change History' button to edit the history."
msgstr "Или нажмите кнопку 'Изменить запись', чтобы изменить историю."

#: templates/simple_history/submit_line.html:3
msgid "Revert"
msgstr "Восстановить"

#: templates/simple_history/submit_line.html:4
msgid "Change History"
msgstr "Изменить запись"

#: templates/simple_history/_object_history_list.html:16
msgid "Change reason"
msgstr "Причина изменения"
