# Generated by Django 2.1 on 2018-10-19 21:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import simple_history.models

from .. import models as my_models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("migration_test_app", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalModelWithCustomAttrForeignKey",
            fields=[
                (
                    "id",
                    models.IntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                ("history_date", models.DateTimeField()),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "what_i_mean",
                    my_models.CustomAttrNameForeignKey(
                        attr_name="custom_attr_name",
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="migration_test_app.WhatIMean",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical model with custom attr foreign key",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="ModelWithCustomAttrForeignKey",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "what_i_mean",
                    my_models.CustomAttrNameForeignKey(
                        attr_name="custom_attr_name",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="migration_test_app.WhatIMean",
                    ),
                ),
            ],
        ),
    ]
