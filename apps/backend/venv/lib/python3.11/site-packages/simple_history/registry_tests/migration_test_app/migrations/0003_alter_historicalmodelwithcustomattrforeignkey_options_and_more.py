# Generated by Django 4.0.dev20210802171549 on 2021-08-11 11:05

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        (
            "migration_test_app",
            "0002_historicalmodelwithcustomattrforeignkey_modelwithcustomattrforeignkey",
        ),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalmodelwithcustomattrforeignkey",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical model with custom attr foreign key",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalyar",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical yar",
            },
        ),
    ]
