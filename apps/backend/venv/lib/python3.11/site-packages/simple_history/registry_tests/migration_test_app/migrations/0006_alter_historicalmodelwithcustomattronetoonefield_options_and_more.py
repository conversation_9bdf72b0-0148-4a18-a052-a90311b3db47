# Generated by Django 4.1.dev20211006030854 on 2021-10-07 13:51

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "migration_test_app",
            "0005_historicalmodelwithcustomattronetoonefield_modelwithcustomattronetoonefield",
        ),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalmodelwithcustomattronetoonefield",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical model with custom attr one to one field",
            },
        ),
        migrations.AlterField(
            model_name="historicalmodelwithcustomattronetoonefield",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
    ]
