# Generated by Django 4.0.1 on 2022-01-28 11:26

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        (
            "migration_test_app",
            "0006_alter_historicalmodelwithcustomattronetoonefield_options_and_more",
        ),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicalmodelwithcustomattrforeignkey",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical model with custom attr foreign key",
                "verbose_name_plural": "historical model with custom attr foreign keys",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalmodelwithcustomattronetoonefield",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical model with custom attr one to one field",
                "verbose_name_plural": "historical model with custom attr one to one fields",
            },
        ),
        migrations.AlterModelOptions(
            name="historicalyar",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical yar",
                "verbose_name_plural": "historical yars",
            },
        ),
    ]
