{% load i18n %}
{% load url from simple_history_compat %}
{% load admin_urls %}
{% load getattribute from getattributes %}

<table id="change-history" class="table table-bordered table-striped">
  <thead>
    <tr>
      <th scope="col">{% trans 'Object' %}</th>
      {% for column in history_list_display %}
        <th scope="col">{% trans column %}</th>
      {% endfor %}
      <th scope="col">{% trans 'Date/time' %}</th>
      <th scope="col">{% trans 'Comment' %}</th>
      <th scope="col">{% trans 'Changed by' %}</th>
      <th scope="col">{% trans 'Change reason' %}</th>
    </tr>
  </thead>
  <tbody>
    {% for action in action_list %}
      <tr>
        <td><a href="{% url opts|admin_urlname:'simple_history' object.pk action.pk %}">{{ action.history_object }}</a></td>
        {% for column in history_list_display %}
        <td scope="col">{{ action|getattribute:column }}</th>
        {% endfor %}
        <td>{{ action.history_date }}</td>
        <td>{{ action.get_history_type_display }}</td>
        <td>
          {% if action.history_user %}
            {% url admin_user_view action.history_user_id as admin_user_url %}
            {% if admin_user_url %}
              <a href="{{ admin_user_url }}">{{ action.history_user }}</a>
            {% else %}
              {{ action.history_user }}
            {% endif %}
          {% else %}
            {% trans "None" %}
          {% endif %}
        </td>
        <td>
            {{ action.history_change_reason }}
        </td>
      </tr>
    {% endfor %}
  </tbody>
</table>
