{% extends "admin/change_form.html" %}
{% load i18n %}
{% load url from simple_history_compat %}

{% block breadcrumbs %}
  <div class="breadcrumbs">
    <a href="{% url "admin:index" %}">{% trans "Home" %}</a> &rsaquo;
    <a href="{% url "admin:app_list" app_label %}">{{app_label|capfirst|escape}}</a> &rsaquo;
    <a href="{{changelist_url}}">{{opts.verbose_name_plural|capfirst}}</a> &rsaquo;
    <a href="{{change_url}}">{{original|truncatewords:"18"}}</a> &rsaquo;
    <a href="../">{% trans "History" %}</a> &rsaquo;
    {% if revert_disabled %}{% blocktrans with original_opts.verbose_name as verbose_name %}View {{verbose_name}}{% endblocktrans %}{% else %}{% blocktrans with original_opts.verbose_name as verbose_name %}Revert {{verbose_name}}{% endblocktrans %}{% endif %}
  </div>
{% endblock %}

{% block submit_buttons_top %}
  {% include "simple_history/submit_line.html" %}
{% endblock %}

{% block submit_buttons_bottom %}
  {% include "simple_history/submit_line.html" %}
{% endblock %}

{% block form_top %}
<p>{% if not revert_disabled %}{% blocktrans %}Press the 'Revert' button below to revert to this version of the object.{% endblocktrans %}{% endif %}{% if change_history %}{% blocktrans %}Press the 'Change History' button below to edit the history.{% endblocktrans %}{% endif %}</p>
{% endblock %}
