'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/lib/stores/auth-store';
import { authAPI } from '@/lib/api';

export function AuthDebug() {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  const [tokens, setTokens] = useState<{access: string | null, refresh: string | null}>({
    access: null,
    refresh: null
  });

  useEffect(() => {
    // Check tokens in localStorage
    const checkTokens = () => {
      const access = localStorage.getItem('access_token');
      const refresh = localStorage.getItem('refresh_token');
      setTokens({ access, refresh });
    };

    checkTokens();
    
    // Check every second for changes
    const interval = setInterval(checkTokens, 1000);
    return () => clearInterval(interval);
  }, []);

  const testLogin = async () => {
    try {
      console.log('Testing login...');
      await useAuthStore.getState().login({
        username: 'founder',
        password: 'demo123'
      });
      console.log('Login successful');
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const testProfile = async () => {
    try {
      console.log('Testing profile fetch...');
      const profile = await authAPI.getProfile();
      console.log('Profile:', profile);
    } catch (error) {
      console.error('Profile fetch failed:', error);
    }
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-sm">
      <h3 className="font-bold mb-2">Auth Debug</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Auth State:</strong>
          <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
          <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
          <div>User: {user ? user.username : 'None'}</div>
        </div>
        
        <div>
          <strong>Tokens:</strong>
          <div>Access: {tokens.access ? `${tokens.access.substring(0, 20)}...` : 'None'}</div>
          <div>Refresh: {tokens.refresh ? `${tokens.refresh.substring(0, 20)}...` : 'None'}</div>
        </div>
        
        <div className="flex gap-2 mt-3">
          <button 
            onClick={testLogin}
            className="px-2 py-1 bg-blue-500 text-white rounded text-xs"
          >
            Test Login
          </button>
          <button 
            onClick={testProfile}
            className="px-2 py-1 bg-green-500 text-white rounded text-xs"
          >
            Test Profile
          </button>
        </div>
      </div>
    </div>
  );
}
