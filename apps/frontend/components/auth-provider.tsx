'use client';

import { useEffect } from 'react';
import { useAuthStore, initializeAuth } from '@/lib/stores/auth-store';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    // Hydrate the store on client side
    useAuthStore.persist.rehydrate();

    // Initialize auth state
    const initAuth = async () => {
      try {
        await initializeAuth();
      } catch (error) {
        console.error('Auth initialization failed:', error);
      }
    };

    initAuth();
  }, []); // Remove isAuthenticated dependency to avoid loops

  return <>{children}</>;
}
