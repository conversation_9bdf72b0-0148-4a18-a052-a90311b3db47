version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mtbrmg_postgres
    environment:
      POSTGRES_DB: mtbrmg_erp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mtbrmg_network

  # Redis for caching and Celery
  redis:
    image: redis:7-alpine
    container_name: mtbrmg_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - mtbrmg_network

  # Django Backend
  backend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: mtbrmg_backend
    env_file:
      - ../apps/backend/.env.docker
    volumes:
      - ../apps/backend:/app
      - backend_static:/app/staticfiles
      - backend_media:/app/media
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/admin/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mtbrmg_network

  # Next.js Frontend
  frontend:
    build:
      context: ..
      dockerfile: docker/Dockerfile.frontend
    container_name: mtbrmg_frontend
    env_file:
      - ../apps/frontend/.env.docker
    volumes:
      - ../apps/frontend:/app/apps/frontend
      - /app/apps/frontend/node_modules
      - /app/apps/frontend/.next
    ports:
      - "3001:3001"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - mtbrmg_network

  # Celery Worker (for background tasks)
  celery:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: mtbrmg_celery
    command: celery -A mtbrmg_erp worker --loglevel=info
    env_file:
      - ../apps/backend/.env.docker
    volumes:
      - ../apps/backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mtbrmg_network

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile.backend
    container_name: mtbrmg_celery_beat
    command: celery -A mtbrmg_erp beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    env_file:
      - ../apps/backend/.env.docker
    volumes:
      - ../apps/backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - mtbrmg_network

volumes:
  postgres_data:
  redis_data:
  backend_static:
  backend_media:

networks:
  mtbrmg_network:
    driver: bridge
