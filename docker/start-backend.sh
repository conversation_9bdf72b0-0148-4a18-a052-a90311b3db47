#!/bin/bash

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
while ! pg_isready -h postgres -p 5432 -U postgres; do
    echo "PostgreSQL is not ready yet. Waiting..."
    sleep 2
done
echo "PostgreSQL is ready!"

# Wait for <PERSON><PERSON> to be ready
echo "Waiting for <PERSON><PERSON> to be ready..."
while ! redis-cli -h redis ping; do
    echo "Redis is not ready yet. Waiting..."
    sleep 2
done
echo "Redis is ready!"

# Run database migrations
echo "Running database migrations..."
python manage.py makemigrations
python manage.py migrate

# Create founder user and sample data
echo "Creating founder user and sample data..."
python manage.py create_founder

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput --clear

# Start the Django development server
echo "Starting Django development server..."
python manage.py runserver 0.0.0.0:8000
