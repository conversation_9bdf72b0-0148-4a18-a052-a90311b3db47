import _Object$setPrototypeOf from "core-js-pure/features/object/set-prototype-of.js";
import _bindInstanceProperty from "core-js-pure/features/instance/bind.js";
import _Object$getPrototypeOf from "core-js-pure/features/object/get-prototype-of.js";
function _getPrototypeOf(t) {
  var _context;
  return _getPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$getPrototypeOf).call(_context) : function (t) {
    return t.__proto__ || _Object$getPrototypeOf(t);
  }, _getPrototypeOf(t);
}
export { _getPrototypeOf as default };