var _unshiftInstanceProperty = require("core-js-pure/features/instance/unshift.js");
function _regeneratorKeys(e) {
  var n = Object(e),
    r = [];
  for (var t in n) _unshiftInstanceProperty(r).call(r, t);
  return function e() {
    for (; r.length;) if ((t = r.pop()) in n) return e.value = t, e.done = !1, e;
    return e.done = !0, e;
  };
}
module.exports = _regeneratorKeys, module.exports.__esModule = true, module.exports["default"] = module.exports;