var _sliceInstanceProperty = require("core-js-pure/features/instance/slice.js");
var _Array$from = require("core-js-pure/features/array/from.js");
var arrayLikeToArray = require("./arrayLikeToArray.js");
function _unsupportedIterableToArray(r, a) {
  if (r) {
    var _context;
    if ("string" == typeof r) return arrayLikeToArray(r, a);
    var t = _sliceInstanceProperty(_context = {}.toString.call(r)).call(_context, 8, -1);
    return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? _Array$from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;
  }
}
module.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports["default"] = module.exports;