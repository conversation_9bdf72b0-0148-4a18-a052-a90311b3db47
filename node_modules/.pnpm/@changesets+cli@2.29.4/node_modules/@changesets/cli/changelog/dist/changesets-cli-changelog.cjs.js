'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var changelogGit = require('@changesets/changelog-git');

function _interopDefault (e) { return e && e.__esModule ? e : { 'default': e }; }

var changelogGit__default = /*#__PURE__*/_interopDefault(changelogGit);



Object.defineProperty(exports, 'default', {
	enumerable: true,
	get: function () { return changelogGit__default["default"]; }
});
