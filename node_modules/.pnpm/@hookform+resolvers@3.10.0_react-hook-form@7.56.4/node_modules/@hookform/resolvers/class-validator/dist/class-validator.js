var r=require("@hookform/resolvers"),e=require("class-transformer"),t=require("class-validator"),a=function r(e,t,a,s){return void 0===a&&(a={}),void 0===s&&(s=""),e.reduce(function(e,a){var i=s?s+"."+a.property:a.property;if(a.constraints){var o=Object.keys(a.constraints)[0];e[i]={type:o,message:a.constraints[o]};var n=e[i];t&&n&&Object.assign(n,{types:a.constraints})}return a.children&&a.children.length&&r(a.children,t,e,i),e},a)};exports.classValidatorResolver=function(s,i,o){return void 0===i&&(i={}),void 0===o&&(o={}),function(n,l,c){try{var v=i.validator,d=e.plainToClass(s,n,i.transformer);return Promise.resolve(("sync"===o.mode?t.validateSync:t.validate)(d,v)).then(function(e){return e.length?{values:{},errors:r.toNestErrors(a(e,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)}:(c.shouldUseNativeValidation&&r.validateFieldsNatively({},c),{values:o.rawValues?n:d,errors:{}})})}catch(r){return Promise.reject(r)}}};
//# sourceMappingURL=class-validator.js.map
