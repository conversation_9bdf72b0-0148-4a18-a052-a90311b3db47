{"version": 3, "file": "computed-types.modern.mjs", "sources": ["../src/computed-types.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type { ValidationError } from 'computed-types';\nimport type { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nconst isValidationError = (error: any): error is ValidationError =>\n  error.errors != null;\n\nconst parseErrorSchema = (computedTypesError: ValidationError) => {\n  const parsedErrors: FieldErrors = {};\n  return (computedTypesError.errors || []).reduce((acc, error) => {\n    acc[error.path.join('.')] = {\n      type: error.error.name,\n      message: error.error.message,\n    };\n\n    return acc;\n  }, parsedErrors);\n};\n\nexport const computedTypesResolver: Resolver =\n  (schema) => async (values, _, options) => {\n    try {\n      const data = await schema(values);\n\n      options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n      return {\n        errors: {},\n        values: data,\n      };\n    } catch (error: any) {\n      if (isValidationError(error)) {\n        return {\n          values: {},\n          errors: toNestErrors(parseErrorSchema(error), options),\n        };\n      }\n\n      throw error;\n    }\n  };\n"], "names": ["computedTypesResolver", "schema", "async", "values", "_", "options", "data", "shouldUseNativeValidation", "validateFieldsNatively", "errors", "error", "isValidationError", "toNestErrors", "computedTypesError", "reduce", "acc", "path", "join", "type", "name", "message"], "mappings": "+EAKA,MAeaA,EACVC,GAAWC,MAAOC,EAAQC,EAAGC,KAC5B,IACE,MAAMC,QAAaL,EAAOE,GAI1B,OAFAE,EAAQE,2BAA6BC,EAAuB,CAAE,EAAEH,GAEzD,CACLI,OAAQ,CAAA,EACRN,OAAQG,EAEZ,CAAE,MAAOI,GACP,GA3BqBA,IACT,MAAhBA,EAAMD,OA0BEE,CAAkBD,GACpB,MAAO,CACLP,OAAQ,CAAA,EACRM,OAAQG,GA3BQC,EA2BsBH,GAzBtCG,EAAmBJ,QAAU,IAAIK,OAAO,CAACC,EAAKL,KACpDK,EAAIL,EAAMM,KAAKC,KAAK,MAAQ,CAC1BC,KAAMR,EAAMA,MAAMS,KAClBC,QAASV,EAAMA,MAAMU,SAGhBL,GAPyB,KA0BoBV,IAIlD,MAAMK,CACR,CAhCsBG,KAgCtB"}