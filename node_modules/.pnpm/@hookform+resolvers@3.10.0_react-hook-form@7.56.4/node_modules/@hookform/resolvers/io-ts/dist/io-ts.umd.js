!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("@hookform/resolvers"),require("fp-ts/Either"),require("fp-ts/function"),require("fp-ts/Option"),require("fp-ts/ReadonlyArray"),require("fp-ts/ReadonlyRecord"),require("fp-ts/Semigroup"),require("io-ts")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","fp-ts/Either","fp-ts/function","fp-ts/Option","fp-ts/ReadonlyArray","fp-ts/ReadonlyRecord","fp-ts/Semigroup","io-ts"],n):n((e||self).hookformResolversIoTs={},e.hookformResolvers,e.Either,e._function,e.Option,e.<PERSON>,e.<PERSON>,e.Semigroup,e.ioTs)}(this,function(e,n,t,r,o,i,u,a,f){function s(e){if(e&&e.__esModule)return e;var n=Object.create(null);return e&&Object.keys(e).forEach(function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}}),n.default=e,n}var p=/*#__PURE__*/s(t),c=/*#__PURE__*/s(o),l=/*#__PURE__*/s(i),d=/*#__PURE__*/s(u),y=/*#__PURE__*/s(a);function m(){return m=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},m.apply(null,arguments)}var v=function(e){return e.reduce(function(e,n,t){return r.pipe(n,p.fold(function(e){return(t>0?".":"")+e},function(e){return"["+e+"]"}),function(n){return""+e+n})},"")},g=["path"],h=[f.TaggedUnionType,f.UnionType,f.IntersectionType,f.ExactType,f.RefinementType],b=function(e){var n,t=r.pipe(n=e.context,l.filterMapWithIndex(function(e,t){var r=e-1,o=-1===r?void 0:n[r];return void 0===o||h.some(function(e){return o.type instanceof e})?c.none:c.some(t)}),l.map(function(e){return e.key}),l.map(function(e){return r.pipe(e,function(e){return parseInt(e,10)},p.fromPredicate(r.not(Number.isNaN),function(){return e}))}),l.toArray,v);return{message:r.pipe(e.message,p.fromNullable(e.context),p.mapLeft(r.flow(l.last,c.map(function(e){return"expected "+e.type.name+" but got "+JSON.stringify(e.actual)}),c.getOrElseW(function(){return r.absurd("Error context is missing name")}))),p.getOrElseW(r.identity)),type:r.pipe(e.context,l.last,c.map(function(e){return e.type.name}),c.getOrElse(function(){return"unknown"})),path:t}},O=function(e){return r.pipe(e,l.map(function(e){var n;return(n={})[e.path]={type:e.type,message:e.message},n}),function(e){return y.fold({concat:function(e,n){return Object.assign({},n,e)}})({},e)})},R={concat:function(e,n){var t;return m({},n,{types:m({},e.types,(t={},t[e.type]=e.message,t[n.type]=n.message,t))})}},x=function(e){return r.pipe(d.fromFoldableMap(R,l.Foldable)(e,function(e){return[e.path,e]}),d.map(function(e){return function(e,n){if(null==e)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)>=0)continue;t[r]=e[r]}return t}(e,g)}))};e.ioTsResolver=function(e){return function(t,o,i){return r.pipe(t,e.decode,p.mapLeft((u=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,function(e){var n=u?x:O;return r.pipe(e,l.map(b),n)})),p.mapLeft(function(e){return n.toNestErrors(e,i)}),p.fold(function(e){return{values:{},errors:e}},function(e){return i.shouldUseNativeValidation&&n.validateFieldsNatively({},i),{values:e,errors:{}}}));var u}}});
//# sourceMappingURL=io-ts.umd.js.map
