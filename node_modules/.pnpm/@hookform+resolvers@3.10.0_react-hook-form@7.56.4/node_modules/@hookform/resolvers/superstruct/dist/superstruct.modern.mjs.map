{"version": 3, "file": "superstruct.modern.mjs", "sources": ["../src/superstruct.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError } from 'react-hook-form';\n\nimport { StructError, validate } from 'superstruct';\nimport { Resolver } from './types';\n\nconst parseErrorSchema = (error: StructError) =>\n  error.failures().reduce<Record<string, FieldError>>(\n    (previous, error) =>\n      (previous[error.path.join('.')] = {\n        message: error.message,\n        type: error.type,\n      }) && previous,\n    {},\n  );\n\nexport const superstructResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  (values, _, options) => {\n    const result = validate(values, schema, schemaOptions);\n\n    if (result[0]) {\n      return {\n        values: {},\n        errors: toNestErrors(parseErrorSchema(result[0]), options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? values : result[1],\n      errors: {},\n    };\n  };\n"], "names": ["superstructResolver", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "result", "validate", "errors", "toNestErrors", "error", "failures", "reduce", "previous", "path", "join", "message", "type", "shouldUseNativeValidation", "validateFieldsNatively", "raw"], "mappings": "sHAMA,MAUaA,EACXA,CAACC,EAAQC,EAAeC,EAAkB,CAAE,IAC5C,CAACC,EAAQC,EAAGC,KACV,MAAMC,EAASC,EAASJ,EAAQH,EAAQC,GAExC,OAAIK,EAAO,GACF,CACLH,OAAQ,CAAA,EACRK,OAAQC,GAlBUC,EAkBoBJ,EAAO,GAjBnDI,EAAMC,WAAWC,OACf,CAACC,EAAUH,KACRG,EAASH,EAAMI,KAAKC,KAAK,MAAQ,CAChCC,QAASN,EAAMM,QACfC,KAAMP,EAAMO,QACRJ,EACR,CAAA,IAWsDR,KAItDA,EAAQa,2BAA6BC,EAAuB,CAAE,EAAEd,GAEzD,CACLF,OAAQD,EAAgBkB,IAAMjB,EAASG,EAAO,GAC9CE,OAAQ,KA1BYE"}