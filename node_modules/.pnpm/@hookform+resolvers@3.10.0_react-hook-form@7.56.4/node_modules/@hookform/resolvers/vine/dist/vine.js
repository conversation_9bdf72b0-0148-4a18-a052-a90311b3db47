var e=require("@hookform/resolvers"),r=require("@vinejs/vine"),t=require("react-hook-form"),o=function(e,r){for(var o={};e.length;){var s=e[0],i=s.field;if(i in o||(o[i]={message:s.message,type:s.rule}),r){var n=o[i].types,a=n&&n[s.rule];o[i]=t.appendErrors(i,r,o,s.rule,a?[].concat(a,[s.message]):s.message)}e.shift()}return o};exports.vineResolver=function(t,s,i){return void 0===i&&(i={}),function(n,a,u){try{return Promise.resolve(function(r,o){try{var a=Promise.resolve(t.validate(n,s)).then(function(r){return u.shouldUseNativeValidation&&e.validateFieldsNatively({},u),{errors:{},values:i.raw?n:r}})}catch(e){return o(e)}return a&&a.then?a.then(void 0,o):a}(0,function(t){if(t instanceof r.errors.E_VALIDATION_ERROR)return{values:{},errors:e.toNestErrors(o(t.messages,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw t}))}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=vine.js.map
