{"version": 3, "sources": ["../src/FocusGuards.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.outline = 'none';\n  element.style.opacity = '0';\n  element.style.position = 'fixed';\n  element.style.pointerEvents = 'none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n"], "mappings": ";;;AAAA,YAAY,WAAW;AAGvB,IAAI,QAAQ;AAEZ,SAAS,YAAY,OAAY;AAC/B,iBAAe;AACf,SAAO,MAAM;AACf;AAMA,SAAS,iBAAiB;AACxB,EAAM,gBAAU,MAAM;AACpB,UAAM,aAAa,SAAS,iBAAiB,0BAA0B;AACvE,aAAS,KAAK,sBAAsB,cAAc,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACrF,aAAS,KAAK,sBAAsB,aAAa,WAAW,CAAC,KAAK,iBAAiB,CAAC;AACpF;AAEA,WAAO,MAAM;AACX,UAAI,UAAU,GAAG;AACf,iBAAS,iBAAiB,0BAA0B,EAAE,QAAQ,CAAC,SAAS,KAAK,OAAO,CAAC;AAAA,MACvF;AACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,mBAAmB;AAC1B,QAAM,UAAU,SAAS,cAAc,MAAM;AAC7C,UAAQ,aAAa,0BAA0B,EAAE;AACjD,UAAQ,WAAW;AACnB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,UAAU;AACxB,UAAQ,MAAM,WAAW;AACzB,UAAQ,MAAM,gBAAgB;AAC9B,SAAO;AACT;AAEA,IAAM,OAAO;", "names": []}