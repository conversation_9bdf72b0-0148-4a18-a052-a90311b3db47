export { P as AnyDataTag, b5 as CancelOptions, C as CancelledError, T as DataTag, F as DefaultError, b4 as DefaultOptions, aj as DefaultedInfiniteQueryObserverOptions, ah as DefaultedQueryObserverOptions, aO as DefinedInfiniteQueryObserverResult, aF as DefinedQueryObserverResult, D as DehydrateOptions, z as DehydratedState, A as DistributiveOmit, _ as Enabled, am as EnsureInfiniteQueryDataOptions, al as EnsureQueryDataOptions, an as FetchInfiniteQueryOptions, au as FetchNextPageOptions, av as FetchPreviousPageOptions, ak as FetchQueryOptions, ax as FetchStatus, a6 as GetNextPageParamFunction, a5 as GetPreviousPageParamFunction, H as HydrateOptions, V as InferDataFromTag, W as InferErrorFromTag, a7 as InfiniteData, aH as InfiniteQueryObserverBaseResult, aK as InfiniteQueryObserverLoadingErrorResult, aJ as InfiniteQueryObserverLoadingResult, ai as InfiniteQueryObserverOptions, aI as InfiniteQueryObserverPendingResult, aN as InfiniteQueryObserverPlaceholderResult, aL as InfiniteQueryObserverRefetchErrorResult, aP as InfiniteQueryObserverResult, aM as InfiniteQueryObserverSuccessResult, ad as InfiniteQueryPageParamsOptions, a1 as InitialDataFunction, ac as InitialPageParam, as as InvalidateOptions, aq as InvalidateQueryFilters, aY as MutateFunction, aX as MutateOptions, y as Mutation, M as MutationCache, d as MutationCacheNotifyEvent, j as MutationFilters, aU as MutationFunction, aQ as MutationKey, aT as MutationMeta, e as MutationObserver, aZ as MutationObserverBaseResult, b0 as MutationObserverErrorResult, a_ as MutationObserverIdleResult, a$ as MutationObserverLoadingResult, aW as MutationObserverOptions, b2 as MutationObserverResult, b1 as MutationObserverSuccessResult, aV as MutationOptions, aS as MutationScope, x as MutationState, aR as MutationStatus, a9 as NetworkMode, E as NoInfer, N as NonUndefinedGuard, b8 as NotifyEvent, b7 as NotifyEventType, aa as NotifyOnChangeProps, O as OmitKeyof, B as Override, a2 as PlaceholderDataFunction, a3 as QueriesPlaceholderDataFunction, w as Query, Q as QueryCache, a as QueryCacheNotifyEvent, b as QueryClient, b3 as QueryClientConfig, l as QueryFilters, X as QueryFunction, a0 as QueryFunctionContext, G as QueryKey, a4 as QueryKeyHashFunction, a8 as QueryMeta, c as QueryObserver, ay as QueryObserverBaseResult, aB as QueryObserverLoadingErrorResult, aA as QueryObserverLoadingResult, af as QueryObserverOptions, az as QueryObserverPendingResult, aE as QueryObserverPlaceholderResult, aC as QueryObserverRefetchErrorResult, aG as QueryObserverResult, aD as QueryObserverSuccessResult, ab as QueryOptions, $ as QueryPersister, v as QueryState, aw as QueryStatus, ap as RefetchOptions, ar as RefetchQueryFilters, R as Register, at as ResetOptions, ao as ResultOptions, b6 as SetDataOptions, S as SkipToken, Y as StaleTime, Z as StaleTimeFunction, ae as ThrowOnError, L as UnsetMarker, U as Updater, ag as WithRequired, J as dataTagErrorSymbol, I as dataTagSymbol, u as defaultShouldDehydrateMutation, t as defaultShouldDehydrateQuery, p as dehydrate, h as hashKey, q as hydrate, o as isCancelledError, i as isServer, k as keepPreviousData, f as matchMutation, m as matchQuery, n as noop, r as replaceEqualDeep, g as shouldThrowError, s as skipToken, K as unsetMarker } from './hydration-YFGUKxMa.cjs';
export { QueriesObserver, QueriesObserverOptions } from './queriesObserver.cjs';
export { InfiniteQueryObserver } from './infiniteQueryObserver.cjs';
export { defaultScheduler, notifyManager } from './notifyManager.cjs';
export { focusManager } from './focusManager.cjs';
export { onlineManager } from './onlineManager.cjs';
export { streamedQuery as experimental_streamedQuery } from './streamedQuery.cjs';
import './removable.cjs';
import './subscribable.cjs';
