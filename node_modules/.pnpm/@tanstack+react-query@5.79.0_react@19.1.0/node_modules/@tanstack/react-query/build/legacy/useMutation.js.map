{"version": 3, "sources": ["../../src/useMutation.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  MutationObserver,\n  noop,\n  notifyManager,\n  shouldThrowError,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport type { DefaultError, QueryClient } from '@tanstack/query-core'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n  queryClient?: QueryClient,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const client = useQueryClient(queryClient)\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        client,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.throwOnError, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n"], "mappings": ";;;AACA,YAAY,WAAW;AACvB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,sBAAsB;AAUxB,SAAS,YAMd,SACA,aACwD;AACxD,QAAM,SAAS,eAAe,WAAW;AAEzC,QAAM,CAAC,QAAQ,IAAU;AAAA,IACvB,MACE,IAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACJ;AAEA,EAAM,gBAAU,MAAM;AACpB,aAAS,WAAW,OAAO;AAAA,EAC7B,GAAG,CAAC,UAAU,OAAO,CAAC;AAEtB,QAAM,SAAe;AAAA,IACb;AAAA,MACJ,CAAC,kBACC,SAAS,UAAU,cAAc,WAAW,aAAa,CAAC;AAAA,MAC5D,CAAC,QAAQ;AAAA,IACX;AAAA,IACA,MAAM,SAAS,iBAAiB;AAAA,IAChC,MAAM,SAAS,iBAAiB;AAAA,EAClC;AAEA,QAAM,SAAe;AAAA,IAGnB,CAAC,WAAW,kBAAkB;AAC5B,eAAS,OAAO,WAAW,aAAa,EAAE,MAAM,IAAI;AAAA,IACtD;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,MACE,OAAO,SACP,iBAAiB,SAAS,QAAQ,cAAc,CAAC,OAAO,KAAK,CAAC,GAC9D;AACA,UAAM,OAAO;AAAA,EACf;AAEA,SAAO,EAAE,GAAG,QAAQ,QAAQ,aAAa,OAAO,OAAO;AACzD;", "names": []}