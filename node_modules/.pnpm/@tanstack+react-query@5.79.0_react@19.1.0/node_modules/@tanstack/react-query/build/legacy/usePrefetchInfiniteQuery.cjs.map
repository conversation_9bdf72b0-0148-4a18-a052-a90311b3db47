{"version": 3, "sources": ["../../src/usePrefetchInfiniteQuery.tsx"], "sourcesContent": ["import { useQueryClient } from './QueryClientProvider'\nimport type {\n  DefaultError,\n  FetchInfiniteQueryOptions,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\n\nexport function usePrefetchInfiniteQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n  TPageParam = unknown,\n>(\n  options: FetchInfiniteQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryKey,\n    TPageParam\n  >,\n  queryClient?: QueryClient,\n) {\n  const client = useQueryClient(queryClient)\n\n  if (!client.getQueryState(options.queryKey)) {\n    client.prefetchInfiniteQuery(options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAA+B;AAQxB,SAAS,yBAOd,SAOA,aACA;AACA,QAAM,aAAS,2CAAe,WAAW;AAEzC,MAAI,CAAC,OAAO,cAAc,QAAQ,QAAQ,GAAG;AAC3C,WAAO,sBAAsB,OAAO;AAAA,EACtC;AACF;", "names": []}