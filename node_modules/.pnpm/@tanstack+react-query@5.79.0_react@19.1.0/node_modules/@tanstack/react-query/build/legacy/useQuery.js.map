{"version": 3, "sources": ["../../src/useQuery.ts"], "sourcesContent": ["'use client'\nimport { QueryObserver } from '@tanstack/query-core'\nimport { useBaseQuery } from './useBaseQuery'\nimport type {\n  DefaultError,\n  NoInfer,\n  QueryClient,\n  QueryKey,\n} from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport type {\n  DefinedInitialDataOptions,\n  UndefinedInitialDataOptions,\n} from './queryOptions'\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): DefinedUseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  T<PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  queryClient?: QueryClient,\n): UseQueryResult<NoInfer<TData>, TError>\n\nexport function useQuery(options: UseQueryOptions, queryClient?: QueryClient) {\n  return useBaseQuery(options, QueryObserver, queryClient)\n}\n"], "mappings": ";;;AACA,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AA+CtB,SAAS,SAAS,SAA0B,aAA2B;AAC5E,SAAO,aAAa,SAAS,eAAe,WAAW;AACzD;", "names": []}