{"version": 3, "file": "headers.js", "sources": ["../../../src/core/headers.ts"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  HeaderGroup,\n  Table,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nconst debug = 'debugHeaders'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header as Header<TData, TValue>, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      getMemoOptions(table.options, debug, 'getHeaderGroups')\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      getMemoOptions(table.options, debug, 'getCenterHeaderGroups')\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      getMemoOptions(table.options, debug, 'getLeftHeaderGroups')\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      getMemoOptions(table.options, debug, 'getRightHeaderGroups')\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getFooterGroups')\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFooterGroups')\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFooterGroups')\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      getMemoOptions(table.options, debug, 'getRightFooterGroups')\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getFlatHeaders')\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeftFlatHeaders')\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getCenterFlatHeaders')\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getRightFlatHeaders')\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getCenterLeafHeaders')\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getLeftLeafHeaders')\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      getMemoOptions(table.options, debug, 'getRightLeafHeaders')\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      getMemoOptions(table.options, debug, 'getLeafHeaders')\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n"], "names": ["debug", "createHeader", "table", "column", "options", "_options$id", "id", "header", "index", "isPlaceholder", "placeholderId", "depth", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "length", "map", "push", "getContext", "_features", "for<PERSON>ach", "feature", "Headers", "createTable", "getHeaderGroups", "memo", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "leafColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "columnId", "find", "d", "filter", "Boolean", "rightColumns", "centerColumns", "includes", "headerGroups", "buildHeaderGroups", "getMemoOptions", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "getRightHeaderGroups", "_right$map$filter2", "getFooterGroups", "reverse", "getLeftFooterGroups", "getCenterFooterGroups", "getRightFooterGroups", "getFlatHeaders", "headers", "flat", "getLeftFlatHeaders", "getCenterFlatHeaders", "getRightFlatHeaders", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "getLeftLeafHeaders", "_header$subHeaders2", "getRightLeafHeaders", "_header$subHeaders3", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "columns", "Math", "max", "getIsVisible", "_column$columns", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "parent", "undefined", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "_ref", "childColSpan", "childRowSpan", "minChildRowSpan", "min"], "mappings": ";;;;;;;;;;;;;;AAUA,MAAMA,KAAK,GAAG,cAAc,CAAA;AAwM5B;;AAEA,SAASC,YAAYA,CACnBC,KAAmB,EACnBC,MAA6B,EAC7BC,OAMC,EACsB;AAAA,EAAA,IAAAC,WAAA,CAAA;AACvB,EAAA,MAAMC,EAAE,GAAA,CAAAD,WAAA,GAAGD,OAAO,CAACE,EAAE,KAAA,IAAA,GAAAD,WAAA,GAAIF,MAAM,CAACG,EAAE,CAAA;AAElC,EAAA,IAAIC,MAAiC,GAAG;IACtCD,EAAE;IACFH,MAAM;IACNK,KAAK,EAAEJ,OAAO,CAACI,KAAK;AACpBC,IAAAA,aAAa,EAAE,CAAC,CAACL,OAAO,CAACK,aAAa;IACtCC,aAAa,EAAEN,OAAO,CAACM,aAAa;IACpCC,KAAK,EAAEP,OAAO,CAACO,KAAK;AACpBC,IAAAA,UAAU,EAAE,EAAE;AACdC,IAAAA,OAAO,EAAE,CAAC;AACVC,IAAAA,OAAO,EAAE,CAAC;AACVC,IAAAA,WAAW,EAAE,IAAK;IAClBC,cAAc,EAAEA,MAAgC;MAC9C,MAAMC,WAAqC,GAAG,EAAE,CAAA;MAEhD,MAAMC,aAAa,GAAIC,CAAyB,IAAK;QACnD,IAAIA,CAAC,CAACP,UAAU,IAAIO,CAAC,CAACP,UAAU,CAACQ,MAAM,EAAE;AACvCD,UAAAA,CAAC,CAACP,UAAU,CAACS,GAAG,CAACH,aAAa,CAAC,CAAA;AACjC,SAAA;AACAD,QAAAA,WAAW,CAACK,IAAI,CAACH,CAA2B,CAAC,CAAA;OAC9C,CAAA;MAEDD,aAAa,CAACX,MAAM,CAAC,CAAA;AAErB,MAAA,OAAOU,WAAW,CAAA;KACnB;IACDM,UAAU,EAAEA,OAAO;MACjBrB,KAAK;AACLK,MAAAA,MAAM,EAAEA,MAA+B;AACvCJ,MAAAA,MAAAA;KACD,CAAA;GACF,CAAA;AAEDD,EAAAA,KAAK,CAACsB,SAAS,CAACC,OAAO,CAACC,OAAO,IAAI;IACjCA,OAAO,CAACzB,YAAY,IAAA,IAAA,IAApByB,OAAO,CAACzB,YAAY,CAAGM,MAAM,EAA2BL,KAAK,CAAC,CAAA;AAChE,GAAC,CAAC,CAAA;AAEF,EAAA,OAAOK,MAAM,CAAA;AACf,CAAA;AAEO,MAAMoB,OAAqB,GAAG;EACnCC,WAAW,EAA0B1B,KAAmB,IAAW;AACjE;;IAEAA,KAAK,CAAC2B,eAAe,GAAGC,UAAI,CAC1B,MAAM,CACJ5B,KAAK,CAAC6B,aAAa,EAAE,EACrB7B,KAAK,CAAC8B,qBAAqB,EAAE,EAC7B9B,KAAK,CAAC+B,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnCjC,KAAK,CAAC+B,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEC,WAAW,EAAEH,IAAI,EAAEC,KAAK,KAAK;MAAA,IAAAG,gBAAA,EAAAC,iBAAA,CAAA;AACxC,MAAA,MAAMC,WAAW,GAAA,CAAAF,gBAAA,GACfJ,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAd,GAAG,CAACqB,QAAQ,IAAIJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKoC,QAAQ,CAAE,CAAC,CAC3DG,MAAM,CAACC,OAAO,CAAC,KAAAP,IAAAA,GAAAA,gBAAA,GAAI,EAAE,CAAA;AAE1B,MAAA,MAAMQ,YAAY,GAAA,CAAAP,iBAAA,GAChBJ,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDf,GAAG,CAACqB,QAAQ,IAAIJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKoC,QAAQ,CAAE,CAAC,CAC3DG,MAAM,CAACC,OAAO,CAAC,KAAAN,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;AAE1B,MAAA,MAAMQ,aAAa,GAAGV,WAAW,CAACO,MAAM,CACtC1C,MAAM,IAAI,EAACgC,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEc,QAAQ,CAAC9C,MAAM,CAACG,EAAE,CAAC,CAAA,IAAI,EAAC8B,KAAK,YAALA,KAAK,CAAEa,QAAQ,CAAC9C,MAAM,CAACG,EAAE,CAAC,CACrE,CAAC,CAAA;AAED,MAAA,MAAM4C,YAAY,GAAGC,iBAAiB,CACpCd,UAAU,EACV,CAAC,GAAGI,WAAW,EAAE,GAAGO,aAAa,EAAE,GAAGD,YAAY,CAAC,EACnD7C,KACF,CAAC,CAAA;AAED,MAAA,OAAOgD,YAAY,CAAA;KACpB,EACDE,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;IAEDE,KAAK,CAACmD,qBAAqB,GAAGvB,UAAI,CAChC,MAAM,CACJ5B,KAAK,CAAC6B,aAAa,EAAE,EACrB7B,KAAK,CAAC8B,qBAAqB,EAAE,EAC7B9B,KAAK,CAAC+B,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,EACnCjC,KAAK,CAAC+B,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEC,WAAW,EAAEH,IAAI,EAAEC,KAAK,KAAK;AACxCE,MAAAA,WAAW,GAAGA,WAAW,CAACO,MAAM,CAC9B1C,MAAM,IAAI,EAACgC,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEc,QAAQ,CAAC9C,MAAM,CAACG,EAAE,CAAC,CAAA,IAAI,EAAC8B,KAAK,YAALA,KAAK,CAAEa,QAAQ,CAAC9C,MAAM,CAACG,EAAE,CAAC,CACrE,CAAC,CAAA;MACD,OAAO6C,iBAAiB,CAACd,UAAU,EAAEC,WAAW,EAAEpC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACnE,EACDkD,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACoD,mBAAmB,GAAGxB,UAAI,CAC9B,MAAM,CACJ5B,KAAK,CAAC6B,aAAa,EAAE,EACrB7B,KAAK,CAAC8B,qBAAqB,EAAE,EAC7B9B,KAAK,CAAC+B,QAAQ,EAAE,CAACC,aAAa,CAACC,IAAI,CACpC,EACD,CAACE,UAAU,EAAEC,WAAW,EAAEH,IAAI,KAAK;AAAA,MAAA,IAAAoB,iBAAA,CAAA;AACjC,MAAA,MAAMC,kBAAkB,GAAA,CAAAD,iBAAA,GACtBpB,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CACAd,GAAG,CAACqB,QAAQ,IAAIJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKoC,QAAQ,CAAE,CAAC,CAC3DG,MAAM,CAACC,OAAO,CAAC,KAAAS,IAAAA,GAAAA,iBAAA,GAAI,EAAE,CAAA;MAE1B,OAAOJ,iBAAiB,CAACd,UAAU,EAAEmB,kBAAkB,EAAEtD,KAAK,EAAE,MAAM,CAAC,CAAA;KACxE,EACDkD,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACuD,oBAAoB,GAAG3B,UAAI,CAC/B,MAAM,CACJ5B,KAAK,CAAC6B,aAAa,EAAE,EACrB7B,KAAK,CAAC8B,qBAAqB,EAAE,EAC7B9B,KAAK,CAAC+B,QAAQ,EAAE,CAACC,aAAa,CAACE,KAAK,CACrC,EACD,CAACC,UAAU,EAAEC,WAAW,EAAEF,KAAK,KAAK;AAAA,MAAA,IAAAsB,kBAAA,CAAA;AAClC,MAAA,MAAMF,kBAAkB,GAAA,CAAAE,kBAAA,GACtBtB,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CACDf,GAAG,CAACqB,QAAQ,IAAIJ,WAAW,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKoC,QAAQ,CAAE,CAAC,CAC3DG,MAAM,CAACC,OAAO,CAAC,KAAAY,IAAAA,GAAAA,kBAAA,GAAI,EAAE,CAAA;MAE1B,OAAOP,iBAAiB,CAACd,UAAU,EAAEmB,kBAAkB,EAAEtD,KAAK,EAAE,OAAO,CAAC,CAAA;KACzE,EACDkD,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;;AAED;;AAEAE,IAAAA,KAAK,CAACyD,eAAe,GAAG7B,UAAI,CAC1B,MAAM,CAAC5B,KAAK,CAAC2B,eAAe,EAAE,CAAC,EAC/BqB,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACU,OAAO,EAAE,CAAA;KACnC,EACDR,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,iBAAiB,CACxD,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAAC2D,mBAAmB,GAAG/B,UAAI,CAC9B,MAAM,CAAC5B,KAAK,CAACoD,mBAAmB,EAAE,CAAC,EACnCJ,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACU,OAAO,EAAE,CAAA;KACnC,EACDR,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAAC4D,qBAAqB,GAAGhC,UAAI,CAChC,MAAM,CAAC5B,KAAK,CAACmD,qBAAqB,EAAE,CAAC,EACrCH,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACU,OAAO,EAAE,CAAA;KACnC,EACDR,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,uBAAuB,CAC9D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAAC6D,oBAAoB,GAAGjC,UAAI,CAC/B,MAAM,CAAC5B,KAAK,CAACuD,oBAAoB,EAAE,CAAC,EACpCP,YAAY,IAAI;AACd,MAAA,OAAO,CAAC,GAAGA,YAAY,CAAC,CAACU,OAAO,EAAE,CAAA;KACnC,EACDR,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;;AAED;;AAEAE,IAAAA,KAAK,CAAC8D,cAAc,GAAGlC,UAAI,CACzB,MAAM,CAAC5B,KAAK,CAAC2B,eAAe,EAAE,CAAC,EAC/BqB,YAAY,IAAI;AACd,MAAA,OAAOA,YAAY,CAChB7B,GAAG,CAACN,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACkD,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDC,IAAI,EAAE,CAAA;KACV,EACDd,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACiE,kBAAkB,GAAGrC,UAAI,CAC7B,MAAM,CAAC5B,KAAK,CAACoD,mBAAmB,EAAE,CAAC,EACnCnB,IAAI,IAAI;AACN,MAAA,OAAOA,IAAI,CACRd,GAAG,CAACN,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACkD,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDC,IAAI,EAAE,CAAA;KACV,EACDd,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACkE,oBAAoB,GAAGtC,UAAI,CAC/B,MAAM,CAAC5B,KAAK,CAACmD,qBAAqB,EAAE,CAAC,EACrClB,IAAI,IAAI;AACN,MAAA,OAAOA,IAAI,CACRd,GAAG,CAACN,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACkD,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDC,IAAI,EAAE,CAAA;KACV,EACDd,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACmE,mBAAmB,GAAGvC,UAAI,CAC9B,MAAM,CAAC5B,KAAK,CAACuD,oBAAoB,EAAE,CAAC,EACpCtB,IAAI,IAAI;AACN,MAAA,OAAOA,IAAI,CACRd,GAAG,CAACN,WAAW,IAAI;QAClB,OAAOA,WAAW,CAACkD,OAAO,CAAA;AAC5B,OAAC,CAAC,CACDC,IAAI,EAAE,CAAA;KACV,EACDd,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;;AAED;;AAEAE,IAAAA,KAAK,CAACoE,oBAAoB,GAAGxC,UAAI,CAC/B,MAAM,CAAC5B,KAAK,CAACkE,oBAAoB,EAAE,CAAC,EACpCG,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAAC1B,MAAM,CAACtC,MAAM,IAAA;AAAA,QAAA,IAAAiE,kBAAA,CAAA;QAAA,OAAI,EAAA,CAAAA,kBAAA,GAACjE,MAAM,CAACK,UAAU,KAAA,IAAA,IAAjB4D,kBAAA,CAAmBpD,MAAM,CAAA,CAAA;OAAC,CAAA,CAAA;KAChE,EACDgC,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,sBAAsB,CAC7D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACuE,kBAAkB,GAAG3C,UAAI,CAC7B,MAAM,CAAC5B,KAAK,CAACiE,kBAAkB,EAAE,CAAC,EAClCI,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAAC1B,MAAM,CAACtC,MAAM,IAAA;AAAA,QAAA,IAAAmE,mBAAA,CAAA;QAAA,OAAI,EAAA,CAAAA,mBAAA,GAACnE,MAAM,CAACK,UAAU,KAAA,IAAA,IAAjB8D,mBAAA,CAAmBtD,MAAM,CAAA,CAAA;OAAC,CAAA,CAAA;KAChE,EACDgC,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,oBAAoB,CAC3D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACyE,mBAAmB,GAAG7C,UAAI,CAC9B,MAAM,CAAC5B,KAAK,CAACmE,mBAAmB,EAAE,CAAC,EACnCE,WAAW,IAAI;AACb,MAAA,OAAOA,WAAW,CAAC1B,MAAM,CAACtC,MAAM,IAAA;AAAA,QAAA,IAAAqE,mBAAA,CAAA;QAAA,OAAI,EAAA,CAAAA,mBAAA,GAACrE,MAAM,CAACK,UAAU,KAAA,IAAA,IAAjBgE,mBAAA,CAAmBxD,MAAM,CAAA,CAAA;OAAC,CAAA,CAAA;KAChE,EACDgC,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,qBAAqB,CAC5D,CAAC,CAAA;AAEDE,IAAAA,KAAK,CAACc,cAAc,GAAGc,UAAI,CACzB,MAAM,CACJ5B,KAAK,CAACoD,mBAAmB,EAAE,EAC3BpD,KAAK,CAACmD,qBAAqB,EAAE,EAC7BnD,KAAK,CAACuD,oBAAoB,EAAE,CAC7B,EACD,CAACtB,IAAI,EAAE0C,MAAM,EAAEzC,KAAK,KAAK;MAAA,IAAA0C,eAAA,EAAAC,MAAA,EAAAC,iBAAA,EAAAC,QAAA,EAAAC,gBAAA,EAAAC,OAAA,CAAA;AACvB,MAAA,OAAO,CACL,IAAA,CAAAL,eAAA,GAAA,CAAAC,MAAA,GAAI5C,IAAI,CAAC,CAAC,CAAC,KAAP4C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAA,CAASd,OAAO,KAAAa,IAAAA,GAAAA,eAAA,GAAI,EAAE,GAC1B,IAAAE,CAAAA,iBAAA,GAAAC,CAAAA,QAAA,GAAIJ,MAAM,CAAC,CAAC,CAAC,KAATI,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAA,CAAWhB,OAAO,KAAAe,IAAAA,GAAAA,iBAAA,GAAI,EAAE,GAC5B,IAAAE,CAAAA,gBAAA,GAAAC,CAAAA,OAAA,GAAI/C,KAAK,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAR+C,OAAA,CAAUlB,OAAO,KAAA,IAAA,GAAAiB,gBAAA,GAAI,EAAE,EAC5B,CACE7D,GAAG,CAACd,MAAM,IAAI;AACb,QAAA,OAAOA,MAAM,CAACS,cAAc,EAAE,CAAA;AAChC,OAAC,CAAC,CACDkD,IAAI,EAAE,CAAA;KACV,EACDd,oBAAc,CAAClD,KAAK,CAACE,OAAO,EAAEJ,KAAK,EAAE,gBAAgB,CACvD,CAAC,CAAA;AACH,GAAA;AACF,EAAC;AAEM,SAASmD,iBAAiBA,CAC/Bd,UAAoC,EACpC+C,cAAwC,EACxClF,KAAmB,EACnBmF,YAA0C,EAC1C;EAAA,IAAAC,qBAAA,EAAAC,cAAA,CAAA;AACA;AACA;AACA;AACA;AACA;;EAEA,IAAIC,QAAQ,GAAG,CAAC,CAAA;AAEhB,EAAA,MAAMC,YAAY,GAAG,UAACC,OAAiC,EAAE/E,KAAK,EAAS;AAAA,IAAA,IAAdA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,MAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,KAAA;IAChE6E,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAACJ,QAAQ,EAAE7E,KAAK,CAAC,CAAA;AAEpC+E,IAAAA,OAAO,CACJ7C,MAAM,CAAC1C,MAAM,IAAIA,MAAM,CAAC0F,YAAY,EAAE,CAAC,CACvCpE,OAAO,CAACtB,MAAM,IAAI;AAAA,MAAA,IAAA2F,eAAA,CAAA;MACjB,IAAAA,CAAAA,eAAA,GAAI3F,MAAM,CAACuF,OAAO,KAAdI,IAAAA,IAAAA,eAAA,CAAgB1E,MAAM,EAAE;QAC1BqE,YAAY,CAACtF,MAAM,CAACuF,OAAO,EAAE/E,KAAK,GAAG,CAAC,CAAC,CAAA;AACzC,OAAA;KACD,EAAE,CAAC,CAAC,CAAA;GACR,CAAA;EAED8E,YAAY,CAACpD,UAAU,CAAC,CAAA;EAExB,IAAIa,YAAkC,GAAG,EAAE,CAAA;AAE3C,EAAA,MAAM6C,iBAAiB,GAAGA,CACxBC,cAAwC,EACxCrF,KAAa,KACV;AACH;AACA,IAAA,MAAMI,WAA+B,GAAG;MACtCJ,KAAK;AACLL,MAAAA,EAAE,EAAE,CAAC+E,YAAY,EAAE,CAAA,EAAG1E,KAAK,CAAE,CAAA,CAAC,CAACkC,MAAM,CAACC,OAAO,CAAC,CAACmD,IAAI,CAAC,GAAG,CAAC;AACxDhC,MAAAA,OAAO,EAAE,EAAA;KACV,CAAA;;AAED;IACA,MAAMiC,oBAA8C,GAAG,EAAE,CAAA;;AAEzD;AACAF,IAAAA,cAAc,CAACvE,OAAO,CAAC0E,aAAa,IAAI;AACtC;;AAEA,MAAA,MAAMC,yBAAyB,GAAG,CAAC,GAAGF,oBAAoB,CAAC,CAACtC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;MAExE,MAAMyC,YAAY,GAAGF,aAAa,CAAChG,MAAM,CAACQ,KAAK,KAAKI,WAAW,CAACJ,KAAK,CAAA;AAErE,MAAA,IAAIR,MAA8B,CAAA;MAClC,IAAIM,aAAa,GAAG,KAAK,CAAA;AAEzB,MAAA,IAAI4F,YAAY,IAAIF,aAAa,CAAChG,MAAM,CAACmG,MAAM,EAAE;AAC/C;AACAnG,QAAAA,MAAM,GAAGgG,aAAa,CAAChG,MAAM,CAACmG,MAAM,CAAA;AACtC,OAAC,MAAM;AACL;QACAnG,MAAM,GAAGgG,aAAa,CAAChG,MAAM,CAAA;AAC7BM,QAAAA,aAAa,GAAG,IAAI,CAAA;AACtB,OAAA;MAEA,IACE2F,yBAAyB,IACzB,CAAAA,yBAAyB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzBA,yBAAyB,CAAEjG,MAAM,MAAKA,MAAM,EAC5C;AACA;AACAiG,QAAAA,yBAAyB,CAACxF,UAAU,CAACU,IAAI,CAAC6E,aAAa,CAAC,CAAA;AAC1D,OAAC,MAAM;AACL;AACA,QAAA,MAAM5F,MAAM,GAAGN,YAAY,CAACC,KAAK,EAAEC,MAAM,EAAE;UACzCG,EAAE,EAAE,CAAC+E,YAAY,EAAE1E,KAAK,EAAER,MAAM,CAACG,EAAE,EAAE6F,aAAa,IAAA,IAAA,GAAA,KAAA,CAAA,GAAbA,aAAa,CAAE7F,EAAE,CAAC,CACpDuC,MAAM,CAACC,OAAO,CAAC,CACfmD,IAAI,CAAC,GAAG,CAAC;UACZxF,aAAa;UACbC,aAAa,EAAED,aAAa,GACxB,CAAA,EAAGyF,oBAAoB,CAACrD,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACzC,MAAM,KAAKA,MAAM,CAAC,CAACiB,MAAM,CAAA,CAAE,GACjEmF,SAAS;UACb5F,KAAK;UACLH,KAAK,EAAE0F,oBAAoB,CAAC9E,MAAAA;AAC9B,SAAC,CAAC,CAAA;;AAEF;AACAb,QAAAA,MAAM,CAACK,UAAU,CAACU,IAAI,CAAC6E,aAAa,CAAC,CAAA;AACrC;AACA;AACAD,QAAAA,oBAAoB,CAAC5E,IAAI,CAACf,MAAM,CAAC,CAAA;AACnC,OAAA;AAEAQ,MAAAA,WAAW,CAACkD,OAAO,CAAC3C,IAAI,CAAC6E,aAAa,CAAC,CAAA;MACvCA,aAAa,CAACpF,WAAW,GAAGA,WAAW,CAAA;AACzC,KAAC,CAAC,CAAA;AAEFmC,IAAAA,YAAY,CAAC5B,IAAI,CAACP,WAAW,CAAC,CAAA;IAE9B,IAAIJ,KAAK,GAAG,CAAC,EAAE;AACboF,MAAAA,iBAAiB,CAACG,oBAAoB,EAAEvF,KAAK,GAAG,CAAC,CAAC,CAAA;AACpD,KAAA;GACD,CAAA;AAED,EAAA,MAAM6F,aAAa,GAAGpB,cAAc,CAAC/D,GAAG,CAAC,CAAClB,MAAM,EAAEK,KAAK,KACrDP,YAAY,CAACC,KAAK,EAAEC,MAAM,EAAE;AAC1BQ,IAAAA,KAAK,EAAE6E,QAAQ;AACfhF,IAAAA,KAAAA;AACF,GAAC,CACH,CAAC,CAAA;AAEDuF,EAAAA,iBAAiB,CAACS,aAAa,EAAEhB,QAAQ,GAAG,CAAC,CAAC,CAAA;EAE9CtC,YAAY,CAACU,OAAO,EAAE,CAAA;;AAEtB;AACA;AACA;;EAEA,MAAM6C,sBAAsB,GAC1BxC,OAAiC,IACU;AAC3C,IAAA,MAAMyC,eAAe,GAAGzC,OAAO,CAACpB,MAAM,CAACtC,MAAM,IAC3CA,MAAM,CAACJ,MAAM,CAAC0F,YAAY,EAC5B,CAAC,CAAA;AAED,IAAA,OAAOa,eAAe,CAACrF,GAAG,CAACd,MAAM,IAAI;MACnC,IAAIM,OAAO,GAAG,CAAC,CAAA;MACf,IAAIC,OAAO,GAAG,CAAC,CAAA;AACf,MAAA,IAAI6F,aAAa,GAAG,CAAC,CAAC,CAAC,CAAA;MAEvB,IAAIpG,MAAM,CAACK,UAAU,IAAIL,MAAM,CAACK,UAAU,CAACQ,MAAM,EAAE;AACjDuF,QAAAA,aAAa,GAAG,EAAE,CAAA;QAElBF,sBAAsB,CAAClG,MAAM,CAACK,UAAU,CAAC,CAACa,OAAO,CAC/CmF,IAAA,IAAsD;UAAA,IAArD;AAAE/F,YAAAA,OAAO,EAAEgG,YAAY;AAAE/F,YAAAA,OAAO,EAAEgG,YAAAA;AAAa,WAAC,GAAAF,IAAA,CAAA;AAC/C/F,UAAAA,OAAO,IAAIgG,YAAY,CAAA;AACvBF,UAAAA,aAAa,CAACrF,IAAI,CAACwF,YAAY,CAAC,CAAA;AAClC,SACF,CAAC,CAAA;AACH,OAAC,MAAM;AACLjG,QAAAA,OAAO,GAAG,CAAC,CAAA;AACb,OAAA;MAEA,MAAMkG,eAAe,GAAGpB,IAAI,CAACqB,GAAG,CAAC,GAAGL,aAAa,CAAC,CAAA;MAClD7F,OAAO,GAAGA,OAAO,GAAGiG,eAAe,CAAA;MAEnCxG,MAAM,CAACM,OAAO,GAAGA,OAAO,CAAA;MACxBN,MAAM,CAACO,OAAO,GAAGA,OAAO,CAAA;MAExB,OAAO;QAAED,OAAO;AAAEC,QAAAA,OAAAA;OAAS,CAAA;AAC7B,KAAC,CAAC,CAAA;GACH,CAAA;AAED2F,EAAAA,sBAAsB,EAAAnB,qBAAA,GAAA,CAAAC,cAAA,GAACrC,YAAY,CAAC,CAAC,CAAC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAfqC,cAAA,CAAiBtB,OAAO,YAAAqB,qBAAA,GAAI,EAAE,CAAC,CAAA;AAEtD,EAAA,OAAOpC,YAAY,CAAA;AACrB;;;;;"}