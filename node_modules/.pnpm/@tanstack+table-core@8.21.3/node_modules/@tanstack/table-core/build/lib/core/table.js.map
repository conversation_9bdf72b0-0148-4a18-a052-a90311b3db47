{"version": 3, "file": "table.js", "sources": ["../../../src/core/table.ts"], "sourcesContent": ["import { functionalUpdate, getMemoOptions, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n  TableFeature,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnFaceting } from '../features/ColumnFaceting'\nimport { ColumnFiltering } from '../features/ColumnFiltering'\nimport { ColumnGrouping } from '../features/ColumnGrouping'\nimport { ColumnOrdering } from '../features/ColumnOrdering'\nimport { ColumnPinning } from '../features/ColumnPinning'\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { ColumnVisibility } from '../features/ColumnVisibility'\nimport { GlobalFaceting } from '../features/GlobalFaceting'\nimport { GlobalFiltering } from '../features/GlobalFiltering'\nimport { RowExpanding } from '../features/RowExpanding'\nimport { RowPagination } from '../features/RowPagination'\nimport { RowPinning } from '../features/RowPinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { RowSorting } from '../features/RowSorting'\n\nconst builtInFeatures = [\n  Headers,\n  ColumnVisibility,\n  ColumnOrdering,\n  ColumnPinning,\n  ColumnFaceting,\n  ColumnFiltering,\n  GlobalFaceting, //depends on ColumnFaceting\n  GlobalFiltering, //depends on ColumnFiltering\n  RowSorting,\n  ColumnGrouping, //depends on RowSorting\n  RowExpanding,\n  RowPagination,\n  RowPinning,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * An array of extra features that you can add to the table instance.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#_features)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  _features?: TableFeature[]\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output cell debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcells]\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugCells?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    (options.debugAll || options.debugTable)\n  ) {\n    console.info('Creating Table Instance...')\n  }\n\n  const _features = [...builtInFeatures, ...(options._features ?? [])]\n\n  let table = { _features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = (feature.getInitialState?.(initialState) ??\n      initialState) as TableState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      getMemoOptions(options, 'debugColumns', '_getDefaultColumnDef')\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllColumns')\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumns')\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllFlatColumnsById')\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      getMemoOptions(options, 'debugColumns', 'getAllLeafColumns')\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n"], "names": ["builtInFeatures", "Headers", "ColumnVisibility", "ColumnOrdering", "ColumnPinning", "ColumnFaceting", "ColumnFiltering", "GlobalFaceting", "GlobalFiltering", "RowSorting", "ColumnGrouping", "RowExpanding", "RowPagination", "RowPinning", "RowSelection", "ColumnSizing", "createTable", "options", "_options$_features", "_options$initialState", "process", "env", "NODE_ENV", "debugAll", "debugTable", "console", "info", "_features", "table", "defaultOptions", "reduce", "obj", "feature", "Object", "assign", "getDefaultOptions", "mergeOptions", "coreInitialState", "initialState", "for<PERSON>ach", "_feature$getInitialSt", "getInitialState", "queued", "queuedTimeout", "coreInstance", "_queue", "cb", "push", "Promise", "resolve", "then", "length", "shift", "catch", "error", "setTimeout", "reset", "setState", "setOptions", "updater", "newOptions", "functionalUpdate", "getState", "state", "onStateChange", "_getRowId", "row", "index", "parent", "_table$options$getRow", "getRowId", "id", "join", "getCoreRowModel", "_getCoreRowModel", "getRowModel", "getPaginationRowModel", "getRow", "searchAll", "getPrePaginationRowModel", "rowsById", "Error", "_getDefaultColumnDef", "memo", "defaultColumn", "_defaultColumn", "header", "props", "resolvedColumnDef", "column", "columnDef", "accessorKey", "accessorFn", "cell", "_props$renderValue$to", "_props$renderValue", "renderValue", "toString", "getDefaultColumnDef", "getMemoOptions", "_getColumnDefs", "columns", "getAllColumns", "columnDefs", "recurseColumns", "depth", "map", "createColumn", "groupingColumnDef", "getAllFlatColumns", "allColumns", "flatMap", "getFlatColumns", "_getAllFlatColumnsById", "flatColumns", "acc", "getAllLeafColumns", "_getOrderColumnsFn", "orderColumns", "leafColumns", "getLeafColumns", "getColumn", "columnId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,MAAMA,eAAe,GAAG,CACtBC,eAAO,EACPC,iCAAgB,EAChBC,6BAAc,EACdC,2BAAa,EACbC,6BAAc,EACdC,+BAAe,EACfC,6BAAc;AAAE;AAChBC,+BAAe;AAAE;AACjBC,qBAAU,EACVC,6BAAc;AAAE;AAChBC,yBAAY,EACZC,2BAAa,EACbC,qBAAU,EACVC,yBAAY,EACZC,yBAAY,CACJ,CAAA;;AAEV;;AAgOO,SAASC,WAAWA,CACzBC,OAAoC,EACtB;EAAA,IAAAC,kBAAA,EAAAC,qBAAA,CAAA;AACd,EAAA,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KACpCL,OAAO,CAACM,QAAQ,IAAIN,OAAO,CAACO,UAAU,CAAC,EACxC;AACAC,IAAAA,OAAO,CAACC,IAAI,CAAC,4BAA4B,CAAC,CAAA;AAC5C,GAAA;AAEA,EAAA,MAAMC,SAAS,GAAG,CAAC,GAAG3B,eAAe,EAAE,IAAAkB,CAAAA,kBAAA,GAAID,OAAO,CAACU,SAAS,KAAA,IAAA,GAAAT,kBAAA,GAAI,EAAE,EAAE,CAAA;AAEpE,EAAA,IAAIU,KAAK,GAAG;AAAED,IAAAA,SAAAA;GAAsC,CAAA;AAEpD,EAAA,MAAME,cAAc,GAAGD,KAAK,CAACD,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;AAC9D,IAAA,OAAOC,MAAM,CAACC,MAAM,CAACH,GAAG,EAAEC,OAAO,CAACG,iBAAiB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAzBH,OAAO,CAACG,iBAAiB,CAAGP,KAAK,CAAC,CAAC,CAAA;GAC9D,EAAE,EAAE,CAAgC,CAAA;EAErC,MAAMQ,YAAY,GAAInB,OAAoC,IAAK;AAC7D,IAAA,IAAIW,KAAK,CAACX,OAAO,CAACmB,YAAY,EAAE;MAC9B,OAAOR,KAAK,CAACX,OAAO,CAACmB,YAAY,CAACP,cAAc,EAAEZ,OAAO,CAAC,CAAA;AAC5D,KAAA;IAEA,OAAO;AACL,MAAA,GAAGY,cAAc;MACjB,GAAGZ,OAAAA;KACJ,CAAA;GACF,CAAA;EAED,MAAMoB,gBAAgC,GAAG,EAAE,CAAA;AAE3C,EAAA,IAAIC,YAAY,GAAG;AACjB,IAAA,GAAGD,gBAAgB;IACnB,IAAAlB,CAAAA,qBAAA,GAAIF,OAAO,CAACqB,YAAY,KAAAnB,IAAAA,GAAAA,qBAAA,GAAI,EAAE;GACjB,CAAA;AAEfS,EAAAA,KAAK,CAACD,SAAS,CAACY,OAAO,CAACP,OAAO,IAAI;AAAA,IAAA,IAAAQ,qBAAA,CAAA;AACjCF,IAAAA,YAAY,IAAAE,qBAAA,GAAIR,OAAO,CAACS,eAAe,IAAvBT,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAACS,eAAe,CAAGH,YAAY,CAAC,KAAAE,IAAAA,GAAAA,qBAAA,GACrDF,YAA2B,CAAA;AAC/B,GAAC,CAAC,CAAA;EAEF,MAAMI,MAAsB,GAAG,EAAE,CAAA;EACjC,IAAIC,aAAa,GAAG,KAAK,CAAA;AAEzB,EAAA,MAAMC,YAAiC,GAAG;IACxCjB,SAAS;AACTV,IAAAA,OAAO,EAAE;AACP,MAAA,GAAGY,cAAc;MACjB,GAAGZ,OAAAA;KACJ;IACDqB,YAAY;IACZO,MAAM,EAAEC,EAAE,IAAI;AACZJ,MAAAA,MAAM,CAACK,IAAI,CAACD,EAAE,CAAC,CAAA;MAEf,IAAI,CAACH,aAAa,EAAE;AAClBA,QAAAA,aAAa,GAAG,IAAI,CAAA;;AAEpB;AACA;AACAK,QAAAA,OAAO,CAACC,OAAO,EAAE,CACdC,IAAI,CAAC,MAAM;UACV,OAAOR,MAAM,CAACS,MAAM,EAAE;AACpBT,YAAAA,MAAM,CAACU,KAAK,EAAE,EAAG,CAAA;AACnB,WAAA;AACAT,UAAAA,aAAa,GAAG,KAAK,CAAA;SACtB,CAAC,CACDU,KAAK,CAACC,KAAK,IACVC,UAAU,CAAC,MAAM;AACf,UAAA,MAAMD,KAAK,CAAA;AACb,SAAC,CACH,CAAC,CAAA;AACL,OAAA;KACD;IACDE,KAAK,EAAEA,MAAM;AACX5B,MAAAA,KAAK,CAAC6B,QAAQ,CAAC7B,KAAK,CAACU,YAAY,CAAC,CAAA;KACnC;IACDoB,UAAU,EAAEC,OAAO,IAAI;MACrB,MAAMC,UAAU,GAAGC,sBAAgB,CAACF,OAAO,EAAE/B,KAAK,CAACX,OAAO,CAAC,CAAA;AAC3DW,MAAAA,KAAK,CAACX,OAAO,GAAGmB,YAAY,CAACwB,UAAU,CAGtC,CAAA;KACF;IAEDE,QAAQ,EAAEA,MAAM;AACd,MAAA,OAAOlC,KAAK,CAACX,OAAO,CAAC8C,KAAK,CAAA;KAC3B;IAEDN,QAAQ,EAAGE,OAA4B,IAAK;AAC1C/B,MAAAA,KAAK,CAACX,OAAO,CAAC+C,aAAa,IAA3BpC,IAAAA,IAAAA,KAAK,CAACX,OAAO,CAAC+C,aAAa,CAAGL,OAAO,CAAC,CAAA;KACvC;AAEDM,IAAAA,SAAS,EAAEA,CAACC,GAAU,EAAEC,KAAa,EAAEC,MAAmB,KAAA;AAAA,MAAA,IAAAC,qBAAA,CAAA;AAAA,MAAA,OAAA,CAAAA,qBAAA,GACxDzC,KAAK,CAACX,OAAO,CAACqD,QAAQ,IAAtB1C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAACX,OAAO,CAACqD,QAAQ,CAAGJ,GAAG,EAAEC,KAAK,EAAEC,MAAM,CAAC,KAAAC,IAAAA,GAAAA,qBAAA,GAC5C,CAAGD,EAAAA,MAAM,GAAG,CAACA,MAAM,CAACG,EAAE,EAAEJ,KAAK,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC,GAAGL,KAAK,CAAE,CAAA,CAAA;AAAA,KAAA;IAEpDM,eAAe,EAAEA,MAAM;AACrB,MAAA,IAAI,CAAC7C,KAAK,CAAC8C,gBAAgB,EAAE;QAC3B9C,KAAK,CAAC8C,gBAAgB,GAAG9C,KAAK,CAACX,OAAO,CAACwD,eAAe,CAAC7C,KAAK,CAAC,CAAA;AAC/D,OAAA;AAEA,MAAA,OAAOA,KAAK,CAAC8C,gBAAgB,EAAG,CAAA;KACjC;AAED;AACA;;IAEAC,WAAW,EAAEA,MAAM;AACjB,MAAA,OAAO/C,KAAK,CAACgD,qBAAqB,EAAE,CAAA;KACrC;AACD;AACAC,IAAAA,MAAM,EAAEA,CAACN,EAAU,EAAEO,SAAmB,KAAK;MAC3C,IAAIZ,GAAG,GAAG,CACRY,SAAS,GAAGlD,KAAK,CAACmD,wBAAwB,EAAE,GAAGnD,KAAK,CAAC+C,WAAW,EAAE,EAClEK,QAAQ,CAACT,EAAE,CAAC,CAAA;MAEd,IAAI,CAACL,GAAG,EAAE;QACRA,GAAG,GAAGtC,KAAK,CAAC6C,eAAe,EAAE,CAACO,QAAQ,CAACT,EAAE,CAAC,CAAA;QAC1C,IAAI,CAACL,GAAG,EAAE;AACR,UAAA,IAAI9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;AACzC,YAAA,MAAM,IAAI2D,KAAK,CAAC,CAAsCV,mCAAAA,EAAAA,EAAE,EAAE,CAAC,CAAA;AAC7D,WAAA;UACA,MAAM,IAAIU,KAAK,EAAE,CAAA;AACnB,SAAA;AACF,OAAA;AAEA,MAAA,OAAOf,GAAG,CAAA;KACX;AACDgB,IAAAA,oBAAoB,EAAEC,UAAI,CACxB,MAAM,CAACvD,KAAK,CAACX,OAAO,CAACmE,aAAa,CAAC,EACnCA,aAAa,IAAI;AAAA,MAAA,IAAAC,cAAA,CAAA;MACfD,aAAa,GAAA,CAAAC,cAAA,GAAID,aAAa,YAAAC,cAAA,GAAI,EAEjC,CAAA;MAED,OAAO;QACLC,MAAM,EAAEC,KAAK,IAAI;UACf,MAAMC,iBAAiB,GAAGD,KAAK,CAACD,MAAM,CAACG,MAAM,CAC1CC,SAAqC,CAAA;UAExC,IAAIF,iBAAiB,CAACG,WAAW,EAAE;YACjC,OAAOH,iBAAiB,CAACG,WAAW,CAAA;AACtC,WAAA;UAEA,IAAIH,iBAAiB,CAACI,UAAU,EAAE;YAChC,OAAOJ,iBAAiB,CAACjB,EAAE,CAAA;AAC7B,WAAA;AAEA,UAAA,OAAO,IAAI,CAAA;SACZ;AACD;AACAsB,QAAAA,IAAI,EAAEN,KAAK,IAAA;UAAA,IAAAO,qBAAA,EAAAC,kBAAA,CAAA;UAAA,OAAAD,CAAAA,qBAAA,IAAAC,kBAAA,GAAIR,KAAK,CAACS,WAAW,EAAO,KAAxBD,IAAAA,IAAAA,kBAAA,CAA0BE,QAAQ,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlCF,kBAAA,CAA0BE,QAAQ,EAAI,KAAA,IAAA,GAAAH,qBAAA,GAAI,IAAI,CAAA;AAAA,SAAA;QAC7D,GAAGlE,KAAK,CAACD,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAK;AAC1C,UAAA,OAAOC,MAAM,CAACC,MAAM,CAACH,GAAG,EAAEC,OAAO,CAACkE,mBAAmB,oBAA3BlE,OAAO,CAACkE,mBAAmB,EAAI,CAAC,CAAA;SAC3D,EAAE,EAAE,CAAC;QACN,GAAGd,aAAAA;OACJ,CAAA;KACF,EACDe,oBAAc,CAAClF,OAAO,EAAE,cAAc,EAAE,sBAAsB,CAChE,CAAC;AAEDmF,IAAAA,cAAc,EAAEA,MAAMxE,KAAK,CAACX,OAAO,CAACoF,OAAO;AAE3CC,IAAAA,aAAa,EAAEnB,UAAI,CACjB,MAAM,CAACvD,KAAK,CAACwE,cAAc,EAAE,CAAC,EAC9BG,UAAU,IAAI;MACZ,MAAMC,cAAc,GAAG,UACrBD,UAAuC,EACvCnC,MAA+B,EAC/BqC,KAAK,EACwB;AAAA,QAAA,IAD7BA,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,UAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,SAAA;AAET,QAAA,OAAOF,UAAU,CAACG,GAAG,CAAChB,SAAS,IAAI;UACjC,MAAMD,QAAM,GAAGkB,mBAAY,CAAC/E,KAAK,EAAE8D,SAAS,EAAEe,KAAK,EAAErC,MAAM,CAAC,CAAA;UAE5D,MAAMwC,iBAAiB,GAAGlB,SAGzB,CAAA;UAEDD,QAAM,CAACY,OAAO,GAAGO,iBAAiB,CAACP,OAAO,GACtCG,cAAc,CAACI,iBAAiB,CAACP,OAAO,EAAEZ,QAAM,EAAEgB,KAAK,GAAG,CAAC,CAAC,GAC5D,EAAE,CAAA;AAEN,UAAA,OAAOhB,QAAM,CAAA;AACf,SAAC,CAAC,CAAA;OACH,CAAA;MAED,OAAOe,cAAc,CAACD,UAAU,CAAC,CAAA;KAClC,EACDJ,oBAAc,CAAClF,OAAO,EAAE,cAAc,EAAE,eAAe,CACzD,CAAC;AAED4F,IAAAA,iBAAiB,EAAE1B,UAAI,CACrB,MAAM,CAACvD,KAAK,CAAC0E,aAAa,EAAE,CAAC,EAC7BQ,UAAU,IAAI;AACZ,MAAA,OAAOA,UAAU,CAACC,OAAO,CAACtB,MAAM,IAAI;AAClC,QAAA,OAAOA,MAAM,CAACuB,cAAc,EAAE,CAAA;AAChC,OAAC,CAAC,CAAA;KACH,EACDb,oBAAc,CAAClF,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;AAEDgG,IAAAA,sBAAsB,EAAE9B,UAAI,CAC1B,MAAM,CAACvD,KAAK,CAACiF,iBAAiB,EAAE,CAAC,EACjCK,WAAW,IAAI;MACb,OAAOA,WAAW,CAACpF,MAAM,CACvB,CAACqF,GAAG,EAAE1B,MAAM,KAAK;AACf0B,QAAAA,GAAG,CAAC1B,MAAM,CAAClB,EAAE,CAAC,GAAGkB,MAAM,CAAA;AACvB,QAAA,OAAO0B,GAAG,CAAA;OACX,EACD,EACF,CAAC,CAAA;KACF,EACDhB,oBAAc,CAAClF,OAAO,EAAE,cAAc,EAAE,uBAAuB,CACjE,CAAC;IAEDmG,iBAAiB,EAAEjC,UAAI,CACrB,MAAM,CAACvD,KAAK,CAAC0E,aAAa,EAAE,EAAE1E,KAAK,CAACyF,kBAAkB,EAAE,CAAC,EACzD,CAACP,UAAU,EAAEQ,YAAY,KAAK;AAC5B,MAAA,IAAIC,WAAW,GAAGT,UAAU,CAACC,OAAO,CAACtB,MAAM,IAAIA,MAAM,CAAC+B,cAAc,EAAE,CAAC,CAAA;MACvE,OAAOF,YAAY,CAACC,WAAW,CAAC,CAAA;KACjC,EACDpB,oBAAc,CAAClF,OAAO,EAAE,cAAc,EAAE,mBAAmB,CAC7D,CAAC;IAEDwG,SAAS,EAAEC,QAAQ,IAAI;MACrB,MAAMjC,MAAM,GAAG7D,KAAK,CAACqF,sBAAsB,EAAE,CAACS,QAAQ,CAAC,CAAA;MAEvD,IAAItG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACmE,MAAM,EAAE;AACpDhE,QAAAA,OAAO,CAAC6B,KAAK,CAAC,CAA2BoE,wBAAAA,EAAAA,QAAQ,mBAAmB,CAAC,CAAA;AACvE,OAAA;AAEA,MAAA,OAAOjC,MAAM,CAAA;AACf,KAAA;GACD,CAAA;AAEDxD,EAAAA,MAAM,CAACC,MAAM,CAACN,KAAK,EAAEgB,YAAY,CAAC,CAAA;AAElC,EAAA,KAAK,IAAIuB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGvC,KAAK,CAACD,SAAS,CAACwB,MAAM,EAAEgB,KAAK,EAAE,EAAE;AAC3D,IAAA,MAAMnC,OAAO,GAAGJ,KAAK,CAACD,SAAS,CAACwC,KAAK,CAAC,CAAA;IACtCnC,OAAO,IAAA,IAAA,IAAPA,OAAO,CAAEhB,WAAW,IAAA,IAAA,IAApBgB,OAAO,CAAEhB,WAAW,CAAGY,KAAK,CAAC,CAAA;AAC/B,GAAA;AAEA,EAAA,OAAOA,KAAK,CAAA;AACd;;;;"}