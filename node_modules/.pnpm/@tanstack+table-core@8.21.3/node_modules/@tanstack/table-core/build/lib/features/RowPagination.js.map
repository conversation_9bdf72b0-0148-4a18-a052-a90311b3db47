{"version": 3, "file": "RowPagination.js", "sources": ["../../../src/features/RowPagination.ts"], "sourcesContent": ["import {\n  OnChangeFn,\n  Table,\n  RowModel,\n  Updater,\n  <PERSON>Data,\n  TableFeature,\n} from '../types'\nimport {\n  functionalUpdate,\n  getMemoOptions,\n  makeStateUpdater,\n  memo,\n} from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you can supply a total `pageCount` value to the table if you know it (Or supply a `rowCount` and `pageCount` will be calculated). If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n  /**\n   * When manually controlling pagination, you can supply a total `rowCount` value to the table if you know it. The `pageCount` can be calculated from this value and the `pageSize`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#rowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  rowCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns the row count. If manually paginating or controlling the pagination state, this will come directly from the `options.rowCount` table option, otherwise it will be calculated from the table data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getrowcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getRowCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Sets the page index to `0`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#firstpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  firstPage: () => void\n  /**\n   * Sets the page index to the last page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#lastpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  lastPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * @deprecated The page count no longer exists in the pagination state. Just pass as a table option instead.\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const RowPagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    //deprecated\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      getMemoOptions(table.options, 'debugTable', 'getPageOptions')\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.firstPage = () => {\n      return table.setPageIndex(0)\n    }\n\n    table.lastPage = () => {\n      return table.setPageIndex(table.getPageCount() - 1)\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(table.getRowCount() / table.getState().pagination.pageSize)\n      )\n    }\n\n    table.getRowCount = () => {\n      return (\n        table.options.rowCount ?? table.getPrePaginationRowModel().rows.length\n      )\n    }\n  },\n}\n"], "names": ["defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "RowPagination", "getInitialState", "state", "pagination", "getDefaultOptions", "table", "onPaginationChange", "makeStateUpdater", "createTable", "registered", "queued", "_autoResetPageIndex", "_ref", "_table$options$autoRe", "_queue", "options", "autoResetAll", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "updater", "safeUpdater", "old", "newState", "functionalUpdate", "resetPagination", "defaultState", "_table$initialState$p", "initialState", "setPageIndex", "maxPageIndex", "pageCount", "Number", "MAX_SAFE_INTEGER", "Math", "max", "min", "_table$initialState$p2", "_table$initialState", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "floor", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "memo", "getPageCount", "pageOptions", "Array", "fill", "map", "_", "i", "getMemoOptions", "getCanPreviousPage", "getState", "getCanNextPage", "previousPage", "nextPage", "firstPage", "lastPage", "getPrePaginationRowModel", "getExpandedRowModel", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "getRowCount", "_table$options$rowCou", "rowCount", "rows", "length"], "mappings": ";;;;;;;;;;;;;;AAwLA;;AAEA,MAAMA,gBAAgB,GAAG,CAAC,CAAA;AAC1B,MAAMC,eAAe,GAAG,EAAE,CAAA;AAE1B,MAAMC,yBAAyB,GAAGA,OAAwB;AACxDC,EAAAA,SAAS,EAAEH,gBAAgB;AAC3BI,EAAAA,QAAQ,EAAEH,eAAAA;AACZ,CAAC,CAAC,CAAA;AAEK,MAAMI,aAA2B,GAAG;EACzCC,eAAe,EAAGC,KAAK,IAA2B;IAChD,OAAO;AACL,MAAA,GAAGA,KAAK;AACRC,MAAAA,UAAU,EAAE;QACV,GAAGN,yBAAyB,EAAE;AAC9B,QAAA,IAAGK,KAAK,IAAA,IAAA,GAAA,KAAA,CAAA,GAALA,KAAK,CAAEC,UAAU;AACtB,OAAA;KACD,CAAA;GACF;EAEDC,iBAAiB,EACfC,KAAmB,IACU;IAC7B,OAAO;AACLC,MAAAA,kBAAkB,EAAEC,sBAAgB,CAAC,YAAY,EAAEF,KAAK,CAAA;KACzD,CAAA;GACF;EAEDG,WAAW,EAA0BH,KAAmB,IAAW;IACjE,IAAII,UAAU,GAAG,KAAK,CAAA;IACtB,IAAIC,MAAM,GAAG,KAAK,CAAA;IAElBL,KAAK,CAACM,mBAAmB,GAAG,MAAM;MAAA,IAAAC,IAAA,EAAAC,qBAAA,CAAA;MAChC,IAAI,CAACJ,UAAU,EAAE;QACfJ,KAAK,CAACS,MAAM,CAAC,MAAM;AACjBL,UAAAA,UAAU,GAAG,IAAI,CAAA;AACnB,SAAC,CAAC,CAAA;AACF,QAAA,OAAA;AACF,OAAA;MAEA,IAAAG,CAAAA,IAAA,GAAAC,CAAAA,qBAAA,GACER,KAAK,CAACU,OAAO,CAACC,YAAY,KAAAH,IAAAA,GAAAA,qBAAA,GAC1BR,KAAK,CAACU,OAAO,CAACE,kBAAkB,KAAA,IAAA,GAAAL,IAAA,GAChC,CAACP,KAAK,CAACU,OAAO,CAACG,gBAAgB,EAC/B;AACA,QAAA,IAAIR,MAAM,EAAE,OAAA;AACZA,QAAAA,MAAM,GAAG,IAAI,CAAA;QACbL,KAAK,CAACS,MAAM,CAAC,MAAM;UACjBT,KAAK,CAACc,cAAc,EAAE,CAAA;AACtBT,UAAAA,MAAM,GAAG,KAAK,CAAA;AAChB,SAAC,CAAC,CAAA;AACJ,OAAA;KACD,CAAA;AACDL,IAAAA,KAAK,CAACe,aAAa,GAAGC,OAAO,IAAI;MAC/B,MAAMC,WAAqC,GAAGC,GAAG,IAAI;AACnD,QAAA,IAAIC,QAAQ,GAAGC,sBAAgB,CAACJ,OAAO,EAAEE,GAAG,CAAC,CAAA;AAE7C,QAAA,OAAOC,QAAQ,CAAA;OAChB,CAAA;AAED,MAAA,OAAOnB,KAAK,CAACU,OAAO,CAACT,kBAAkB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAhCD,KAAK,CAACU,OAAO,CAACT,kBAAkB,CAAGgB,WAAW,CAAC,CAAA;KACvD,CAAA;AACDjB,IAAAA,KAAK,CAACqB,eAAe,GAAGC,YAAY,IAAI;AAAA,MAAA,IAAAC,qBAAA,CAAA;MACtCvB,KAAK,CAACe,aAAa,CACjBO,YAAY,GACR9B,yBAAyB,EAAE,GAAA,CAAA+B,qBAAA,GAC3BvB,KAAK,CAACwB,YAAY,CAAC1B,UAAU,KAAA,IAAA,GAAAyB,qBAAA,GAAI/B,yBAAyB,EAChE,CAAC,CAAA;KACF,CAAA;AACDQ,IAAAA,KAAK,CAACyB,YAAY,GAAGT,OAAO,IAAI;AAC9BhB,MAAAA,KAAK,CAACe,aAAa,CAACG,GAAG,IAAI;QACzB,IAAIzB,SAAS,GAAG2B,sBAAgB,CAACJ,OAAO,EAAEE,GAAG,CAACzB,SAAS,CAAC,CAAA;AAExD,QAAA,MAAMiC,YAAY,GAChB,OAAO1B,KAAK,CAACU,OAAO,CAACiB,SAAS,KAAK,WAAW,IAC9C3B,KAAK,CAACU,OAAO,CAACiB,SAAS,KAAK,CAAC,CAAC,GAC1BC,MAAM,CAACC,gBAAgB,GACvB7B,KAAK,CAACU,OAAO,CAACiB,SAAS,GAAG,CAAC,CAAA;AAEjClC,QAAAA,SAAS,GAAGqC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACvC,SAAS,EAAEiC,YAAY,CAAC,CAAC,CAAA;QAE1D,OAAO;AACL,UAAA,GAAGR,GAAG;AACNzB,UAAAA,SAAAA;SACD,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;AACDO,IAAAA,KAAK,CAACc,cAAc,GAAGQ,YAAY,IAAI;MAAA,IAAAW,sBAAA,EAAAC,mBAAA,CAAA;AACrClC,MAAAA,KAAK,CAACyB,YAAY,CAChBH,YAAY,GACRhC,gBAAgB,GAAA2C,CAAAA,sBAAA,GAAAC,CAAAA,mBAAA,GAChBlC,KAAK,CAACwB,YAAY,KAAAU,IAAAA,IAAAA,CAAAA,mBAAA,GAAlBA,mBAAA,CAAoBpC,UAAU,KAA9BoC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,mBAAA,CAAgCzC,SAAS,KAAAwC,IAAAA,GAAAA,sBAAA,GAAI3C,gBACnD,CAAC,CAAA;KACF,CAAA;AACDU,IAAAA,KAAK,CAACmC,aAAa,GAAGb,YAAY,IAAI;MAAA,IAAAc,sBAAA,EAAAC,oBAAA,CAAA;AACpCrC,MAAAA,KAAK,CAACsC,WAAW,CACfhB,YAAY,GACR/B,eAAe,GAAA6C,CAAAA,sBAAA,GAAAC,CAAAA,oBAAA,GACfrC,KAAK,CAACwB,YAAY,KAAAa,IAAAA,IAAAA,CAAAA,oBAAA,GAAlBA,oBAAA,CAAoBvC,UAAU,KAA9BuC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAAgC3C,QAAQ,KAAA0C,IAAAA,GAAAA,sBAAA,GAAI7C,eAClD,CAAC,CAAA;KACF,CAAA;AACDS,IAAAA,KAAK,CAACsC,WAAW,GAAGtB,OAAO,IAAI;AAC7BhB,MAAAA,KAAK,CAACe,aAAa,CAACG,GAAG,IAAI;AACzB,QAAA,MAAMxB,QAAQ,GAAGoC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,sBAAgB,CAACJ,OAAO,EAAEE,GAAG,CAACxB,QAAQ,CAAC,CAAC,CAAA;QACrE,MAAM6C,WAAW,GAAGrB,GAAG,CAACxB,QAAQ,GAAGwB,GAAG,CAACzB,SAAU,CAAA;QACjD,MAAMA,SAAS,GAAGqC,IAAI,CAACU,KAAK,CAACD,WAAW,GAAG7C,QAAQ,CAAC,CAAA;QAEpD,OAAO;AACL,UAAA,GAAGwB,GAAG;UACNzB,SAAS;AACTC,UAAAA,QAAAA;SACD,CAAA;AACH,OAAC,CAAC,CAAA;KACH,CAAA;AACD;IACAM,KAAK,CAACyC,YAAY,GAAGzB,OAAO,IAC1BhB,KAAK,CAACe,aAAa,CAACG,GAAG,IAAI;AAAA,MAAA,IAAAwB,qBAAA,CAAA;AACzB,MAAA,IAAIC,YAAY,GAAGvB,sBAAgB,CACjCJ,OAAO,EAAA,CAAA0B,qBAAA,GACP1C,KAAK,CAACU,OAAO,CAACiB,SAAS,KAAA,IAAA,GAAAe,qBAAA,GAAI,CAAC,CAC9B,CAAC,CAAA;AAED,MAAA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;QACpCA,YAAY,GAAGb,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEY,YAAY,CAAC,CAAA;AAC3C,OAAA;MAEA,OAAO;AACL,QAAA,GAAGzB,GAAG;AACNS,QAAAA,SAAS,EAAEgB,YAAAA;OACZ,CAAA;AACH,KAAC,CAAC,CAAA;AAEJ3C,IAAAA,KAAK,CAAC4C,cAAc,GAAGC,UAAI,CACzB,MAAM,CAAC7C,KAAK,CAAC8C,YAAY,EAAE,CAAC,EAC5BnB,SAAS,IAAI;MACX,IAAIoB,WAAqB,GAAG,EAAE,CAAA;AAC9B,MAAA,IAAIpB,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;QAC9BoB,WAAW,GAAG,CAAC,GAAG,IAAIC,KAAK,CAACrB,SAAS,CAAC,CAAC,CAACsB,IAAI,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC,CAAA;AACrE,OAAA;AACA,MAAA,OAAOL,WAAW,CAAA;KACnB,EACDM,oBAAc,CAACrD,KAAK,CAACU,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAC9D,CAAC,CAAA;AAEDV,IAAAA,KAAK,CAACsD,kBAAkB,GAAG,MAAMtD,KAAK,CAACuD,QAAQ,EAAE,CAACzD,UAAU,CAACL,SAAS,GAAG,CAAC,CAAA;IAE1EO,KAAK,CAACwD,cAAc,GAAG,MAAM;MAC3B,MAAM;AAAE/D,QAAAA,SAAAA;AAAU,OAAC,GAAGO,KAAK,CAACuD,QAAQ,EAAE,CAACzD,UAAU,CAAA;AAEjD,MAAA,MAAM6B,SAAS,GAAG3B,KAAK,CAAC8C,YAAY,EAAE,CAAA;AAEtC,MAAA,IAAInB,SAAS,KAAK,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;MAEA,IAAIA,SAAS,KAAK,CAAC,EAAE;AACnB,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AAEA,MAAA,OAAOlC,SAAS,GAAGkC,SAAS,GAAG,CAAC,CAAA;KACjC,CAAA;IAED3B,KAAK,CAACyD,YAAY,GAAG,MAAM;MACzB,OAAOzD,KAAK,CAACyB,YAAY,CAACP,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,CAAA;KAC1C,CAAA;IAEDlB,KAAK,CAAC0D,QAAQ,GAAG,MAAM;AACrB,MAAA,OAAO1D,KAAK,CAACyB,YAAY,CAACP,GAAG,IAAI;QAC/B,OAAOA,GAAG,GAAG,CAAC,CAAA;AAChB,OAAC,CAAC,CAAA;KACH,CAAA;IAEDlB,KAAK,CAAC2D,SAAS,GAAG,MAAM;AACtB,MAAA,OAAO3D,KAAK,CAACyB,YAAY,CAAC,CAAC,CAAC,CAAA;KAC7B,CAAA;IAEDzB,KAAK,CAAC4D,QAAQ,GAAG,MAAM;MACrB,OAAO5D,KAAK,CAACyB,YAAY,CAACzB,KAAK,CAAC8C,YAAY,EAAE,GAAG,CAAC,CAAC,CAAA;KACpD,CAAA;IAED9C,KAAK,CAAC6D,wBAAwB,GAAG,MAAM7D,KAAK,CAAC8D,mBAAmB,EAAE,CAAA;IAClE9D,KAAK,CAAC+D,qBAAqB,GAAG,MAAM;MAClC,IACE,CAAC/D,KAAK,CAACgE,sBAAsB,IAC7BhE,KAAK,CAACU,OAAO,CAACqD,qBAAqB,EACnC;QACA/D,KAAK,CAACgE,sBAAsB,GAC1BhE,KAAK,CAACU,OAAO,CAACqD,qBAAqB,CAAC/D,KAAK,CAAC,CAAA;AAC9C,OAAA;MAEA,IAAIA,KAAK,CAACU,OAAO,CAACG,gBAAgB,IAAI,CAACb,KAAK,CAACgE,sBAAsB,EAAE;AACnE,QAAA,OAAOhE,KAAK,CAAC6D,wBAAwB,EAAE,CAAA;AACzC,OAAA;AAEA,MAAA,OAAO7D,KAAK,CAACgE,sBAAsB,EAAE,CAAA;KACtC,CAAA;IAEDhE,KAAK,CAAC8C,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAmB,sBAAA,CAAA;AACzB,MAAA,OAAA,CAAAA,sBAAA,GACEjE,KAAK,CAACU,OAAO,CAACiB,SAAS,KAAA,IAAA,GAAAsC,sBAAA,GACvBnC,IAAI,CAACoC,IAAI,CAAClE,KAAK,CAACmE,WAAW,EAAE,GAAGnE,KAAK,CAACuD,QAAQ,EAAE,CAACzD,UAAU,CAACJ,QAAQ,CAAC,CAAA;KAExE,CAAA;IAEDM,KAAK,CAACmE,WAAW,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,CAAA;AACxB,MAAA,OAAA,CAAAA,qBAAA,GACEpE,KAAK,CAACU,OAAO,CAAC2D,QAAQ,KAAAD,IAAAA,GAAAA,qBAAA,GAAIpE,KAAK,CAAC6D,wBAAwB,EAAE,CAACS,IAAI,CAACC,MAAM,CAAA;KAEzE,CAAA;AACH,GAAA;AACF;;;;"}