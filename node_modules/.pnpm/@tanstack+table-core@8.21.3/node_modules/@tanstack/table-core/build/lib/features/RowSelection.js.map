{"version": 3, "file": "RowSelection.js", "sources": ["../../../src/features/RowSelection.ts"], "sourcesContent": ["import {\n  OnChangeFn,\n  Table,\n  Row,\n  <PERSON>Model,\n  Updater,\n  RowData,\n  TableFeature,\n} from '../types'\nimport { getMemoOptions, makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getSelectedRowModel')\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFilteredSelectedRowModel')\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getGroupedSelectedRowModel')\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n"], "names": ["RowSelection", "getInitialState", "state", "rowSelection", "getDefaultOptions", "table", "onRowSelectionChange", "makeStateUpdater", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "createTable", "setRowSelection", "updater", "options", "resetRowSelection", "defaultState", "_table$initialState$r", "initialState", "toggleAllRowsSelected", "value", "old", "getIsAllRowsSelected", "preGroupedFlatRows", "getPreGroupedRowModel", "flatRows", "for<PERSON>ach", "row", "getCanSelect", "id", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "getRowModel", "rows", "mutateRowIsSelected", "getPreSelectedRowModel", "getCoreRowModel", "getSelectedRowModel", "memo", "getState", "rowModel", "Object", "keys", "length", "rowsById", "selectRowsFn", "getMemoOptions", "getFilteredSelectedRowModel", "getFilteredRowModel", "getGroupedSelectedRowModel", "getSortedRowModel", "isAllRowsSelected", "Boolean", "some", "paginationFlatRows", "getPaginationRowModel", "filter", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "d", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "e", "target", "checked", "getToggleAllPageRowsSelectedHandler", "createRow", "toggleSelected", "opts", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "_table$options$enable", "getCanSelectSubRows", "_table$options$enable2", "getCanMultiSelect", "_table$options$enable3", "getToggleSelectedHandler", "canSelect", "_target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_row$subRows", "getRow", "key", "subRows", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "depth", "map", "_row$subRows2", "push", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected"], "mappings": ";;;;;;;;;;;;;;AAoMA;;AAEO,MAAMA,YAA0B,GAAG;EACxCC,eAAe,EAAGC,KAAK,IAA6B;IAClD,OAAO;MACLC,YAAY,EAAE,EAAE;MAChB,GAAGD,KAAAA;KACJ,CAAA;GACF;EAEDE,iBAAiB,EACfC,KAAmB,IACY;IAC/B,OAAO;AACLC,MAAAA,oBAAoB,EAAEC,sBAAgB,CAAC,cAAc,EAAEF,KAAK,CAAC;AAC7DG,MAAAA,kBAAkB,EAAE,IAAI;AACxBC,MAAAA,uBAAuB,EAAE,IAAI;AAC7BC,MAAAA,qBAAqB,EAAE,IAAA;AACvB;AACA;AACA;KACD,CAAA;GACF;EAEDC,WAAW,EAA0BN,KAAmB,IAAW;AACjEA,IAAAA,KAAK,CAACO,eAAe,GAAGC,OAAO,IAC7BR,KAAK,CAACS,OAAO,CAACR,oBAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAlCD,KAAK,CAACS,OAAO,CAACR,oBAAoB,CAAGO,OAAO,CAAC,CAAA;IAC/CR,KAAK,CAACU,iBAAiB,GAAGC,YAAY,IAAA;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAAA,OACpCZ,KAAK,CAACO,eAAe,CACnBI,YAAY,GAAG,EAAE,GAAAC,CAAAA,qBAAA,GAAGZ,KAAK,CAACa,YAAY,CAACf,YAAY,YAAAc,qBAAA,GAAI,EACzD,CAAC,CAAA;AAAA,KAAA,CAAA;AACHZ,IAAAA,KAAK,CAACc,qBAAqB,GAAGC,KAAK,IAAI;AACrCf,MAAAA,KAAK,CAACO,eAAe,CAACS,GAAG,IAAI;AAC3BD,QAAAA,KAAK,GACH,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAACf,KAAK,CAACiB,oBAAoB,EAAE,CAAA;AAEtE,QAAA,MAAMnB,YAAY,GAAG;UAAE,GAAGkB,GAAAA;SAAK,CAAA;QAE/B,MAAME,kBAAkB,GAAGlB,KAAK,CAACmB,qBAAqB,EAAE,CAACC,QAAQ,CAAA;;AAEjE;AACA;AACA,QAAA,IAAIL,KAAK,EAAE;AACTG,UAAAA,kBAAkB,CAACG,OAAO,CAACC,GAAG,IAAI;AAChC,YAAA,IAAI,CAACA,GAAG,CAACC,YAAY,EAAE,EAAE;AACvB,cAAA,OAAA;AACF,aAAA;AACAzB,YAAAA,YAAY,CAACwB,GAAG,CAACE,EAAE,CAAC,GAAG,IAAI,CAAA;AAC7B,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM;AACLN,UAAAA,kBAAkB,CAACG,OAAO,CAACC,GAAG,IAAI;AAChC,YAAA,OAAOxB,YAAY,CAACwB,GAAG,CAACE,EAAE,CAAC,CAAA;AAC7B,WAAC,CAAC,CAAA;AACJ,SAAA;AAEA,QAAA,OAAO1B,YAAY,CAAA;AACrB,OAAC,CAAC,CAAA;KACH,CAAA;IACDE,KAAK,CAACyB,yBAAyB,GAAGV,KAAK,IACrCf,KAAK,CAACO,eAAe,CAACS,GAAG,IAAI;AAC3B,MAAA,MAAMU,aAAa,GACjB,OAAOX,KAAK,KAAK,WAAW,GACxBA,KAAK,GACL,CAACf,KAAK,CAAC2B,wBAAwB,EAAE,CAAA;AAEvC,MAAA,MAAM7B,YAA+B,GAAG;QAAE,GAAGkB,GAAAA;OAAK,CAAA;MAElDhB,KAAK,CAAC4B,WAAW,EAAE,CAACC,IAAI,CAACR,OAAO,CAACC,GAAG,IAAI;AACtCQ,QAAAA,mBAAmB,CAAChC,YAAY,EAAEwB,GAAG,CAACE,EAAE,EAAEE,aAAa,EAAE,IAAI,EAAE1B,KAAK,CAAC,CAAA;AACvE,OAAC,CAAC,CAAA;AAEF,MAAA,OAAOF,YAAY,CAAA;AACrB,KAAC,CAAC,CAAA;;AAEJ;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;IACAE,KAAK,CAAC+B,sBAAsB,GAAG,MAAM/B,KAAK,CAACgC,eAAe,EAAE,CAAA;IAC5DhC,KAAK,CAACiC,mBAAmB,GAAGC,UAAI,CAC9B,MAAM,CAAClC,KAAK,CAACmC,QAAQ,EAAE,CAACrC,YAAY,EAAEE,KAAK,CAACgC,eAAe,EAAE,CAAC,EAC9D,CAAClC,YAAY,EAAEsC,QAAQ,KAAK;MAC1B,IAAI,CAACC,MAAM,CAACC,IAAI,CAACxC,YAAY,CAAC,CAACyC,MAAM,EAAE;QACrC,OAAO;AACLV,UAAAA,IAAI,EAAE,EAAE;AACRT,UAAAA,QAAQ,EAAE,EAAE;AACZoB,UAAAA,QAAQ,EAAE,EAAC;SACZ,CAAA;AACH,OAAA;AAEA,MAAA,OAAOC,YAAY,CAACzC,KAAK,EAAEoC,QAAQ,CAAC,CAAA;KACrC,EACDM,oBAAc,CAAC1C,KAAK,CAACS,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;IAEDT,KAAK,CAAC2C,2BAA2B,GAAGT,UAAI,CACtC,MAAM,CAAClC,KAAK,CAACmC,QAAQ,EAAE,CAACrC,YAAY,EAAEE,KAAK,CAAC4C,mBAAmB,EAAE,CAAC,EAClE,CAAC9C,YAAY,EAAEsC,QAAQ,KAAK;MAC1B,IAAI,CAACC,MAAM,CAACC,IAAI,CAACxC,YAAY,CAAC,CAACyC,MAAM,EAAE;QACrC,OAAO;AACLV,UAAAA,IAAI,EAAE,EAAE;AACRT,UAAAA,QAAQ,EAAE,EAAE;AACZoB,UAAAA,QAAQ,EAAE,EAAC;SACZ,CAAA;AACH,OAAA;AAEA,MAAA,OAAOC,YAAY,CAACzC,KAAK,EAAEoC,QAAQ,CAAC,CAAA;KACrC,EACDM,oBAAc,CAAC1C,KAAK,CAACS,OAAO,EAAE,YAAY,EAAE,6BAA6B,CAC3E,CAAC,CAAA;IAEDT,KAAK,CAAC6C,0BAA0B,GAAGX,UAAI,CACrC,MAAM,CAAClC,KAAK,CAACmC,QAAQ,EAAE,CAACrC,YAAY,EAAEE,KAAK,CAAC8C,iBAAiB,EAAE,CAAC,EAChE,CAAChD,YAAY,EAAEsC,QAAQ,KAAK;MAC1B,IAAI,CAACC,MAAM,CAACC,IAAI,CAACxC,YAAY,CAAC,CAACyC,MAAM,EAAE;QACrC,OAAO;AACLV,UAAAA,IAAI,EAAE,EAAE;AACRT,UAAAA,QAAQ,EAAE,EAAE;AACZoB,UAAAA,QAAQ,EAAE,EAAC;SACZ,CAAA;AACH,OAAA;AAEA,MAAA,OAAOC,YAAY,CAACzC,KAAK,EAAEoC,QAAQ,CAAC,CAAA;KACrC,EACDM,oBAAc,CAAC1C,KAAK,CAACS,OAAO,EAAE,YAAY,EAAE,4BAA4B,CAC1E,CAAC,CAAA;;AAED;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;IAEAT,KAAK,CAACiB,oBAAoB,GAAG,MAAM;MACjC,MAAMC,kBAAkB,GAAGlB,KAAK,CAAC4C,mBAAmB,EAAE,CAACxB,QAAQ,CAAA;MAC/D,MAAM;AAAEtB,QAAAA,YAAAA;AAAa,OAAC,GAAGE,KAAK,CAACmC,QAAQ,EAAE,CAAA;AAEzC,MAAA,IAAIY,iBAAiB,GAAGC,OAAO,CAC7B9B,kBAAkB,CAACqB,MAAM,IAAIF,MAAM,CAACC,IAAI,CAACxC,YAAY,CAAC,CAACyC,MACzD,CAAC,CAAA;AAED,MAAA,IAAIQ,iBAAiB,EAAE;QACrB,IACE7B,kBAAkB,CAAC+B,IAAI,CACrB3B,GAAG,IAAIA,GAAG,CAACC,YAAY,EAAE,IAAI,CAACzB,YAAY,CAACwB,GAAG,CAACE,EAAE,CACnD,CAAC,EACD;AACAuB,UAAAA,iBAAiB,GAAG,KAAK,CAAA;AAC3B,SAAA;AACF,OAAA;AAEA,MAAA,OAAOA,iBAAiB,CAAA;KACzB,CAAA;IAED/C,KAAK,CAAC2B,wBAAwB,GAAG,MAAM;AACrC,MAAA,MAAMuB,kBAAkB,GAAGlD,KAAK,CAC7BmD,qBAAqB,EAAE,CACvB/B,QAAQ,CAACgC,MAAM,CAAC9B,GAAG,IAAIA,GAAG,CAACC,YAAY,EAAE,CAAC,CAAA;MAC7C,MAAM;AAAEzB,QAAAA,YAAAA;AAAa,OAAC,GAAGE,KAAK,CAACmC,QAAQ,EAAE,CAAA;AAEzC,MAAA,IAAIkB,qBAAqB,GAAG,CAAC,CAACH,kBAAkB,CAACX,MAAM,CAAA;AAEvD,MAAA,IACEc,qBAAqB,IACrBH,kBAAkB,CAACD,IAAI,CAAC3B,GAAG,IAAI,CAACxB,YAAY,CAACwB,GAAG,CAACE,EAAE,CAAC,CAAC,EACrD;AACA6B,QAAAA,qBAAqB,GAAG,KAAK,CAAA;AAC/B,OAAA;AAEA,MAAA,OAAOA,qBAAqB,CAAA;KAC7B,CAAA;IAEDrD,KAAK,CAACsD,qBAAqB,GAAG,MAAM;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAClC,MAAMC,aAAa,GAAGnB,MAAM,CAACC,IAAI,CAAAiB,CAAAA,qBAAA,GAC/BvD,KAAK,CAACmC,QAAQ,EAAE,CAACrC,YAAY,KAAAyD,IAAAA,GAAAA,qBAAA,GAAI,EACnC,CAAC,CAAChB,MAAM,CAAA;AACR,MAAA,OACEiB,aAAa,GAAG,CAAC,IACjBA,aAAa,GAAGxD,KAAK,CAAC4C,mBAAmB,EAAE,CAACxB,QAAQ,CAACmB,MAAM,CAAA;KAE9D,CAAA;IAEDvC,KAAK,CAACyD,yBAAyB,GAAG,MAAM;MACtC,MAAMP,kBAAkB,GAAGlD,KAAK,CAACmD,qBAAqB,EAAE,CAAC/B,QAAQ,CAAA;AACjE,MAAA,OAAOpB,KAAK,CAAC2B,wBAAwB,EAAE,GACnC,KAAK,GACLuB,kBAAkB,CACfE,MAAM,CAAC9B,GAAG,IAAIA,GAAG,CAACC,YAAY,EAAE,CAAC,CACjC0B,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACC,aAAa,EAAE,IAAID,CAAC,CAACE,iBAAiB,EAAE,CAAC,CAAA;KAC7D,CAAA;IAED5D,KAAK,CAAC6D,+BAA+B,GAAG,MAAM;AAC5C,MAAA,OAAQC,CAAU,IAAK;QACrB9D,KAAK,CAACc,qBAAqB,CACvBgD,CAAC,CAAgBC,MAAM,CAAsBC,OACjD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;IAEDhE,KAAK,CAACiE,mCAAmC,GAAG,MAAM;AAChD,MAAA,OAAQH,CAAU,IAAK;QACrB9D,KAAK,CAACyB,yBAAyB,CAC3BqC,CAAC,CAAgBC,MAAM,CAAsBC,OACjD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;GACF;AAEDE,EAAAA,SAAS,EAAEA,CACT5C,GAAe,EACftB,KAAmB,KACV;AACTsB,IAAAA,GAAG,CAAC6C,cAAc,GAAG,CAACpD,KAAK,EAAEqD,IAAI,KAAK;AACpC,MAAA,MAAMC,UAAU,GAAG/C,GAAG,CAACqC,aAAa,EAAE,CAAA;AAEtC3D,MAAAA,KAAK,CAACO,eAAe,CAACS,GAAG,IAAI;AAAA,QAAA,IAAAsD,oBAAA,CAAA;QAC3BvD,KAAK,GAAG,OAAOA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,CAACsD,UAAU,CAAA;QAE1D,IAAI/C,GAAG,CAACC,YAAY,EAAE,IAAI8C,UAAU,KAAKtD,KAAK,EAAE;AAC9C,UAAA,OAAOC,GAAG,CAAA;AACZ,SAAA;AAEA,QAAA,MAAMuD,cAAc,GAAG;UAAE,GAAGvD,GAAAA;SAAK,CAAA;QAEjCc,mBAAmB,CACjByC,cAAc,EACdjD,GAAG,CAACE,EAAE,EACNT,KAAK,EAAA,CAAAuD,oBAAA,GACLF,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEI,cAAc,KAAA,IAAA,GAAAF,oBAAA,GAAI,IAAI,EAC5BtE,KACF,CAAC,CAAA;AAED,QAAA,OAAOuE,cAAc,CAAA;AACvB,OAAC,CAAC,CAAA;KACH,CAAA;IACDjD,GAAG,CAACqC,aAAa,GAAG,MAAM;MACxB,MAAM;AAAE7D,QAAAA,YAAAA;AAAa,OAAC,GAAGE,KAAK,CAACmC,QAAQ,EAAE,CAAA;AACzC,MAAA,OAAOsC,aAAa,CAACnD,GAAG,EAAExB,YAAY,CAAC,CAAA;KACxC,CAAA;IAEDwB,GAAG,CAACsC,iBAAiB,GAAG,MAAM;MAC5B,MAAM;AAAE9D,QAAAA,YAAAA;AAAa,OAAC,GAAGE,KAAK,CAACmC,QAAQ,EAAE,CAAA;MACzC,OAAOuC,gBAAgB,CAACpD,GAAG,EAAExB,YAAmB,CAAC,KAAK,MAAM,CAAA;KAC7D,CAAA;IAEDwB,GAAG,CAACqD,uBAAuB,GAAG,MAAM;MAClC,MAAM;AAAE7E,QAAAA,YAAAA;AAAa,OAAC,GAAGE,KAAK,CAACmC,QAAQ,EAAE,CAAA;MACzC,OAAOuC,gBAAgB,CAACpD,GAAG,EAAExB,YAAmB,CAAC,KAAK,KAAK,CAAA;KAC5D,CAAA;IAEDwB,GAAG,CAACC,YAAY,GAAG,MAAM;AAAA,MAAA,IAAAqD,qBAAA,CAAA;MACvB,IAAI,OAAO5E,KAAK,CAACS,OAAO,CAACN,kBAAkB,KAAK,UAAU,EAAE;AAC1D,QAAA,OAAOH,KAAK,CAACS,OAAO,CAACN,kBAAkB,CAACmB,GAAG,CAAC,CAAA;AAC9C,OAAA;MAEA,OAAAsD,CAAAA,qBAAA,GAAO5E,KAAK,CAACS,OAAO,CAACN,kBAAkB,KAAA,IAAA,GAAAyE,qBAAA,GAAI,IAAI,CAAA;KAChD,CAAA;IAEDtD,GAAG,CAACuD,mBAAmB,GAAG,MAAM;AAAA,MAAA,IAAAC,sBAAA,CAAA;MAC9B,IAAI,OAAO9E,KAAK,CAACS,OAAO,CAACJ,qBAAqB,KAAK,UAAU,EAAE;AAC7D,QAAA,OAAOL,KAAK,CAACS,OAAO,CAACJ,qBAAqB,CAACiB,GAAG,CAAC,CAAA;AACjD,OAAA;MAEA,OAAAwD,CAAAA,sBAAA,GAAO9E,KAAK,CAACS,OAAO,CAACJ,qBAAqB,KAAA,IAAA,GAAAyE,sBAAA,GAAI,IAAI,CAAA;KACnD,CAAA;IAEDxD,GAAG,CAACyD,iBAAiB,GAAG,MAAM;AAAA,MAAA,IAAAC,sBAAA,CAAA;MAC5B,IAAI,OAAOhF,KAAK,CAACS,OAAO,CAACL,uBAAuB,KAAK,UAAU,EAAE;AAC/D,QAAA,OAAOJ,KAAK,CAACS,OAAO,CAACL,uBAAuB,CAACkB,GAAG,CAAC,CAAA;AACnD,OAAA;MAEA,OAAA0D,CAAAA,sBAAA,GAAOhF,KAAK,CAACS,OAAO,CAACL,uBAAuB,KAAA,IAAA,GAAA4E,sBAAA,GAAI,IAAI,CAAA;KACrD,CAAA;IACD1D,GAAG,CAAC2D,wBAAwB,GAAG,MAAM;AACnC,MAAA,MAAMC,SAAS,GAAG5D,GAAG,CAACC,YAAY,EAAE,CAAA;AAEpC,MAAA,OAAQuC,CAAU,IAAK;AAAA,QAAA,IAAAqB,OAAA,CAAA;QACrB,IAAI,CAACD,SAAS,EAAE,OAAA;AAChB5D,QAAAA,GAAG,CAAC6C,cAAc,CAAAgB,CAAAA,OAAA,GACdrB,CAAC,CAAgBC,MAAM,KAAzBoB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAA,CAAgDnB,OAClD,CAAC,CAAA;OACF,CAAA;KACF,CAAA;AACH,GAAA;AACF,EAAC;AAED,MAAMlC,mBAAmB,GAAGA,CAC1ByC,cAAuC,EACvC/C,EAAU,EACVT,KAAc,EACdqE,eAAwB,EACxBpF,KAAmB,KAChB;AAAA,EAAA,IAAAqF,YAAA,CAAA;EACH,MAAM/D,GAAG,GAAGtB,KAAK,CAACsF,MAAM,CAAC9D,EAAE,EAAE,IAAI,CAAC,CAAA;;AAElC;;AAEA;AACA;AACA;AACA;AACA,EAAA,IAAIT,KAAK,EAAE;AACT,IAAA,IAAI,CAACO,GAAG,CAACyD,iBAAiB,EAAE,EAAE;AAC5B1C,MAAAA,MAAM,CAACC,IAAI,CAACiC,cAAc,CAAC,CAAClD,OAAO,CAACkE,GAAG,IAAI,OAAOhB,cAAc,CAACgB,GAAG,CAAC,CAAC,CAAA;AACxE,KAAA;AACA,IAAA,IAAIjE,GAAG,CAACC,YAAY,EAAE,EAAE;AACtBgD,MAAAA,cAAc,CAAC/C,EAAE,CAAC,GAAG,IAAI,CAAA;AAC3B,KAAA;AACF,GAAC,MAAM;IACL,OAAO+C,cAAc,CAAC/C,EAAE,CAAC,CAAA;AAC3B,GAAA;AACA;;AAEA,EAAA,IAAI4D,eAAe,IAAAC,CAAAA,YAAA,GAAI/D,GAAG,CAACkE,OAAO,KAAA,IAAA,IAAXH,YAAA,CAAa9C,MAAM,IAAIjB,GAAG,CAACuD,mBAAmB,EAAE,EAAE;IACvEvD,GAAG,CAACkE,OAAO,CAACnE,OAAO,CAACC,GAAG,IACrBQ,mBAAmB,CAACyC,cAAc,EAAEjD,GAAG,CAACE,EAAE,EAAET,KAAK,EAAEqE,eAAe,EAAEpF,KAAK,CAC3E,CAAC,CAAA;AACH,GAAA;AACF,CAAC,CAAA;AAEM,SAASyC,YAAYA,CAC1BzC,KAAmB,EACnBoC,QAAyB,EACR;EACjB,MAAMtC,YAAY,GAAGE,KAAK,CAACmC,QAAQ,EAAE,CAACrC,YAAY,CAAA;EAElD,MAAM2F,mBAAiC,GAAG,EAAE,CAAA;EAC5C,MAAMC,mBAA+C,GAAG,EAAE,CAAA;;AAE1D;AACA,EAAA,MAAMC,WAAW,GAAG,UAAC9D,IAAkB,EAAE+D,KAAK,EAAuB;AACnE,IAAA,OAAO/D,IAAI,CACRgE,GAAG,CAACvE,GAAG,IAAI;AAAA,MAAA,IAAAwE,aAAA,CAAA;AACV,MAAA,MAAMzB,UAAU,GAAGI,aAAa,CAACnD,GAAG,EAAExB,YAAY,CAAC,CAAA;AAEnD,MAAA,IAAIuE,UAAU,EAAE;AACdoB,QAAAA,mBAAmB,CAACM,IAAI,CAACzE,GAAG,CAAC,CAAA;AAC7BoE,QAAAA,mBAAmB,CAACpE,GAAG,CAACE,EAAE,CAAC,GAAGF,GAAG,CAAA;AACnC,OAAA;MAEA,IAAAwE,CAAAA,aAAA,GAAIxE,GAAG,CAACkE,OAAO,KAAXM,IAAAA,IAAAA,aAAA,CAAavD,MAAM,EAAE;AACvBjB,QAAAA,GAAG,GAAG;AACJ,UAAA,GAAGA,GAAG;UACNkE,OAAO,EAAEG,WAAW,CAACrE,GAAG,CAACkE,OAAkB,CAAA;SAC5C,CAAA;AACH,OAAA;AAEA,MAAA,IAAInB,UAAU,EAAE;AACd,QAAA,OAAO/C,GAAG,CAAA;AACZ,OAAA;AACF,KAAC,CAAC,CACD8B,MAAM,CAACJ,OAAO,CAAC,CAAA;GACnB,CAAA;EAED,OAAO;AACLnB,IAAAA,IAAI,EAAE8D,WAAW,CAACvD,QAAQ,CAACP,IAAI,CAAC;AAChCT,IAAAA,QAAQ,EAAEqE,mBAAmB;AAC7BjD,IAAAA,QAAQ,EAAEkD,mBAAAA;GACX,CAAA;AACH,CAAA;AAEO,SAASjB,aAAaA,CAC3BnD,GAAe,EACf0E,SAAkC,EACzB;AAAA,EAAA,IAAAC,iBAAA,CAAA;EACT,OAAAA,CAAAA,iBAAA,GAAOD,SAAS,CAAC1E,GAAG,CAACE,EAAE,CAAC,KAAA,IAAA,GAAAyE,iBAAA,GAAI,KAAK,CAAA;AACnC,CAAA;AAEO,SAASvB,gBAAgBA,CAC9BpD,GAAe,EACf0E,SAAkC,EAClChG,KAAmB,EACO;AAAA,EAAA,IAAAkG,aAAA,CAAA;AAC1B,EAAA,IAAI,EAAAA,CAAAA,aAAA,GAAC5E,GAAG,CAACkE,OAAO,KAAXU,IAAAA,IAAAA,aAAA,CAAa3D,MAAM,CAAE,EAAA,OAAO,KAAK,CAAA;EAEtC,IAAI4D,mBAAmB,GAAG,IAAI,CAAA;EAC9B,IAAIC,YAAY,GAAG,KAAK,CAAA;AAExB9E,EAAAA,GAAG,CAACkE,OAAO,CAACnE,OAAO,CAACgF,MAAM,IAAI;AAC5B;AACA,IAAA,IAAID,YAAY,IAAI,CAACD,mBAAmB,EAAE;AACxC,MAAA,OAAA;AACF,KAAA;AAEA,IAAA,IAAIE,MAAM,CAAC9E,YAAY,EAAE,EAAE;AACzB,MAAA,IAAIkD,aAAa,CAAC4B,MAAM,EAAEL,SAAS,CAAC,EAAE;AACpCI,QAAAA,YAAY,GAAG,IAAI,CAAA;AACrB,OAAC,MAAM;AACLD,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAA;AACF,KAAA;;AAEA;IACA,IAAIE,MAAM,CAACb,OAAO,IAAIa,MAAM,CAACb,OAAO,CAACjD,MAAM,EAAE;MAC3C,MAAM+D,sBAAsB,GAAG5B,gBAAgB,CAAC2B,MAAM,EAAEL,SAAgB,CAAC,CAAA;MACzE,IAAIM,sBAAsB,KAAK,KAAK,EAAE;AACpCF,QAAAA,YAAY,GAAG,IAAI,CAAA;AACrB,OAAC,MAAM,IAAIE,sBAAsB,KAAK,MAAM,EAAE;AAC5CF,QAAAA,YAAY,GAAG,IAAI,CAAA;AACnBD,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAC,MAAM;AACLA,QAAAA,mBAAmB,GAAG,KAAK,CAAA;AAC7B,OAAA;AACF,KAAA;AACF,GAAC,CAAC,CAAA;EAEF,OAAOA,mBAAmB,GAAG,KAAK,GAAGC,YAAY,GAAG,MAAM,GAAG,KAAK,CAAA;AACpE;;;;;;;"}