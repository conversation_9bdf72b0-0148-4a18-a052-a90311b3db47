/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */
'use strict';

function safelyAccessDocument(_document) {
  return _document || (typeof document !== 'undefined' ? document : null);
}

exports.safelyAccessDocument = safelyAccessDocument;
//# sourceMappingURL=document.js.map
