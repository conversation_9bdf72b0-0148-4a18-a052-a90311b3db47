{"version": 3, "file": "getCoreRowModel.js", "sources": ["../../../src/utils/getCoreRowModel.ts"], "sourcesContent": ["import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      getMemoOptions(table.options, 'debugTable', 'getRowModel', () =>\n        table._autoResetPageIndex()\n      )\n    )\n}\n"], "names": ["getCoreRowModel", "table", "memo", "options", "data", "rowModel", "rows", "flatRows", "rowsById", "accessRows", "originalRows", "depth", "parentRow", "i", "length", "row", "createRow", "_getRowId", "undefined", "id", "push", "getSubRows", "_row$originalSubRows", "originalSubRows", "subRows", "getMemoOptions", "_autoResetPageIndex"], "mappings": ";;;;;;;;;;;;;;;AAIO,SAASA,eAAeA,GAEJ;AACzB,EAAA,OAAOC,KAAK,IACVC,UAAI,CACF,MAAM,CAACD,KAAK,CAACE,OAAO,CAACC,IAAI,CAAC,EAExBA,IAAI,IAKD;AACH,IAAA,MAAMC,QAAyB,GAAG;AAChCC,MAAAA,IAAI,EAAE,EAAE;AACRC,MAAAA,QAAQ,EAAE,EAAE;AACZC,MAAAA,QAAQ,EAAE,EAAC;KACZ,CAAA;IAED,MAAMC,UAAU,GAAG,UACjBC,YAAqB,EACrBC,KAAK,EACLC,SAAsB,EACL;AAAA,MAAA,IAFjBD,KAAK,KAAA,KAAA,CAAA,EAAA;AAALA,QAAAA,KAAK,GAAG,CAAC,CAAA;AAAA,OAAA;MAGT,MAAML,IAAI,GAAG,EAAkB,CAAA;AAE/B,MAAA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,QAAA,MAAME,KAAG,GAAGC,aAAS,CACnBf,KAAK,EACLA,KAAK,CAACgB,SAAS,CAACP,YAAY,CAACG,CAAC,CAAC,EAAGA,CAAC,EAAED,SAAS,CAAC,EAC/CF,YAAY,CAACG,CAAC,CAAC,EACfA,CAAC,EACDF,KAAK,EACLO,SAAS,EACTN,SAAS,IAAA,IAAA,GAAA,KAAA,CAAA,GAATA,SAAS,CAAEO,EACb,CAAC,CAAA;;AAED;AACAd,QAAAA,QAAQ,CAACE,QAAQ,CAACa,IAAI,CAACL,KAAG,CAAC,CAAA;AAC3B;QACAV,QAAQ,CAACG,QAAQ,CAACO,KAAG,CAACI,EAAE,CAAC,GAAGJ,KAAG,CAAA;AAC/B;AACAT,QAAAA,IAAI,CAACc,IAAI,CAACL,KAAG,CAAC,CAAA;;AAEd;AACA,QAAA,IAAId,KAAK,CAACE,OAAO,CAACkB,UAAU,EAAE;AAAA,UAAA,IAAAC,oBAAA,CAAA;AAC5BP,UAAAA,KAAG,CAACQ,eAAe,GAAGtB,KAAK,CAACE,OAAO,CAACkB,UAAU,CAC5CX,YAAY,CAACG,CAAC,CAAC,EACfA,CACF,CAAC,CAAA;;AAED;UACA,IAAAS,CAAAA,oBAAA,GAAIP,KAAG,CAACQ,eAAe,KAAnBD,IAAAA,IAAAA,oBAAA,CAAqBR,MAAM,EAAE;AAC/BC,YAAAA,KAAG,CAACS,OAAO,GAAGf,UAAU,CAACM,KAAG,CAACQ,eAAe,EAAEZ,KAAK,GAAG,CAAC,EAAEI,KAAG,CAAC,CAAA;AAC/D,WAAA;AACF,SAAA;AACF,OAAA;AAEA,MAAA,OAAOT,IAAI,CAAA;KACZ,CAAA;AAEDD,IAAAA,QAAQ,CAACC,IAAI,GAAGG,UAAU,CAACL,IAAI,CAAC,CAAA;AAEhC,IAAA,OAAOC,QAAQ,CAAA;AACjB,GAAC,EACDoB,oBAAc,CAACxB,KAAK,CAACE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MACzDF,KAAK,CAACyB,mBAAmB,EAC3B,CACF,CAAC,CAAA;AACL;;;;"}