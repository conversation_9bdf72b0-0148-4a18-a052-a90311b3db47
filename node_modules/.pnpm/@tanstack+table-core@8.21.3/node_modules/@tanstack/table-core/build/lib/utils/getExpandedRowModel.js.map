{"version": 3, "file": "getExpandedRowModel.js", "sources": ["../../../src/utils/getExpandedRowModel.ts"], "sourcesContent": ["import { Table, Row, RowModel, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getExpandedRowModel')\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n"], "names": ["getExpandedRowModel", "table", "memo", "getState", "expanded", "getPreExpandedRowModel", "options", "paginateExpandedRows", "rowModel", "rows", "length", "Object", "keys", "expandRows", "getMemoOptions", "expandedRows", "handleRow", "row", "_row$subRows", "push", "subRows", "getIsExpanded", "for<PERSON>ach", "flatRows", "rowsById"], "mappings": ";;;;;;;;;;;;;;AAGO,SAASA,mBAAmBA,GAER;AACzB,EAAA,OAAOC,KAAK,IACVC,UAAI,CACF,MAAM,CACJD,KAAK,CAACE,QAAQ,EAAE,CAACC,QAAQ,EACzBH,KAAK,CAACI,sBAAsB,EAAE,EAC9BJ,KAAK,CAACK,OAAO,CAACC,oBAAoB,CACnC,EACD,CAACH,QAAQ,EAAEI,QAAQ,EAAED,oBAAoB,KAAK;IAC5C,IACE,CAACC,QAAQ,CAACC,IAAI,CAACC,MAAM,IACpBN,QAAQ,KAAK,IAAI,IAAI,CAACO,MAAM,CAACC,IAAI,CAACR,QAAQ,IAARA,IAAAA,GAAAA,QAAQ,GAAI,EAAE,CAAC,CAACM,MAAO,EAC1D;AACA,MAAA,OAAOF,QAAQ,CAAA;AACjB,KAAA;IAEA,IAAI,CAACD,oBAAoB,EAAE;AACzB;AACA,MAAA,OAAOC,QAAQ,CAAA;AACjB,KAAA;IAEA,OAAOK,UAAU,CAACL,QAAQ,CAAC,CAAA;GAC5B,EACDM,oBAAc,CAACb,KAAK,CAACK,OAAO,EAAE,YAAY,EAAE,qBAAqB,CACnE,CAAC,CAAA;AACL,CAAA;AAEO,SAASO,UAAUA,CAAwBL,QAAyB,EAAE;EAC3E,MAAMO,YAA0B,GAAG,EAAE,CAAA;EAErC,MAAMC,SAAS,GAAIC,GAAe,IAAK;AAAA,IAAA,IAAAC,YAAA,CAAA;AACrCH,IAAAA,YAAY,CAACI,IAAI,CAACF,GAAG,CAAC,CAAA;AAEtB,IAAA,IAAI,CAAAC,YAAA,GAAAD,GAAG,CAACG,OAAO,KAAXF,IAAAA,IAAAA,YAAA,CAAaR,MAAM,IAAIO,GAAG,CAACI,aAAa,EAAE,EAAE;AAC9CJ,MAAAA,GAAG,CAACG,OAAO,CAACE,OAAO,CAACN,SAAS,CAAC,CAAA;AAChC,KAAA;GACD,CAAA;AAEDR,EAAAA,QAAQ,CAACC,IAAI,CAACa,OAAO,CAACN,SAAS,CAAC,CAAA;EAEhC,OAAO;AACLP,IAAAA,IAAI,EAAEM,YAAY;IAClBQ,QAAQ,EAAEf,QAAQ,CAACe,QAAQ;IAC3BC,QAAQ,EAAEhB,QAAQ,CAACgB,QAAAA;GACpB,CAAA;AACH;;;;;"}