{"version": 3, "file": "getFacetedMinMaxValues.js", "sources": ["../../../src/utils/getFacetedMinMaxValues.ts"], "sourcesContent": ["import { Table, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const uniqueValues = facetedRowModel.flatRows\n          .flatMap(flatRow => flatRow.getUniqueValues(columnId) ?? [])\n          .map(Number)\n          .filter(value => !Number.isNaN(value))\n\n        if (!uniqueValues.length) return\n\n        let facetedMinValue = uniqueValues[0]!\n        let facetedMaxValue = uniqueValues[uniqueValues.length - 1]!\n\n        for (const value of uniqueValues) {\n          if (value < facetedMinValue) facetedMinValue = value\n          else if (value > facetedMaxValue) facetedMaxValue = value\n        }\n\n        return [facetedMinValue, facetedMaxValue]\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedMinMaxValues')\n    )\n}\n"], "names": ["getFacetedMinMaxValues", "table", "columnId", "memo", "_table$getColumn", "getColumn", "getFacetedRowModel", "facetedRowModel", "undefined", "uniqueValues", "flatRows", "flatMap", "flatRow", "_flatRow$getUniqueVal", "getUniqueValues", "map", "Number", "filter", "value", "isNaN", "length", "facetedMinValue", "facetedMaxValue", "getMemoOptions", "options"], "mappings": ";;;;;;;;;;;;;;AAGO,SAASA,sBAAsBA,GAGE;AACtC,EAAA,OAAO,CAACC,KAAK,EAAEC,QAAQ,KACrBC,UAAI,CACF,MAAA;AAAA,IAAA,IAAAC,gBAAA,CAAA;AAAA,IAAA,OAAM,CAAAA,CAAAA,gBAAA,GAACH,KAAK,CAACI,SAAS,CAACH,QAAQ,CAAC,qBAAzBE,gBAAA,CAA2BE,kBAAkB,EAAE,CAAC,CAAA;AAAA,GAAA,EACvDC,eAAe,IAAI;AACjB,IAAA,IAAI,CAACA,eAAe,EAAE,OAAOC,SAAS,CAAA;IAEtC,MAAMC,YAAY,GAAGF,eAAe,CAACG,QAAQ,CAC1CC,OAAO,CAACC,OAAO,IAAA;AAAA,MAAA,IAAAC,qBAAA,CAAA;MAAA,OAAAA,CAAAA,qBAAA,GAAID,OAAO,CAACE,eAAe,CAACZ,QAAQ,CAAC,KAAA,IAAA,GAAAW,qBAAA,GAAI,EAAE,CAAA;AAAA,KAAA,CAAC,CAC3DE,GAAG,CAACC,MAAM,CAAC,CACXC,MAAM,CAACC,KAAK,IAAI,CAACF,MAAM,CAACG,KAAK,CAACD,KAAK,CAAC,CAAC,CAAA;AAExC,IAAA,IAAI,CAACT,YAAY,CAACW,MAAM,EAAE,OAAA;AAE1B,IAAA,IAAIC,eAAe,GAAGZ,YAAY,CAAC,CAAC,CAAE,CAAA;IACtC,IAAIa,eAAe,GAAGb,YAAY,CAACA,YAAY,CAACW,MAAM,GAAG,CAAC,CAAE,CAAA;AAE5D,IAAA,KAAK,MAAMF,KAAK,IAAIT,YAAY,EAAE;AAChC,MAAA,IAAIS,KAAK,GAAGG,eAAe,EAAEA,eAAe,GAAGH,KAAK,CAC/C,KAAA,IAAIA,KAAK,GAAGI,eAAe,EAAEA,eAAe,GAAGJ,KAAK,CAAA;AAC3D,KAAA;AAEA,IAAA,OAAO,CAACG,eAAe,EAAEC,eAAe,CAAC,CAAA;GAC1C,EACDC,oBAAc,CAACtB,KAAK,CAACuB,OAAO,EAAE,YAAY,EAAE,wBAAwB,CACtE,CAAC,CAAA;AACL;;;;"}