{"version": 3, "file": "getFacetedRowModel.js", "sources": ["../../../src/utils/getFacetedRowModel.ts"], "sourcesContent": ["import { Table, RowModel, Row, RowData } from '../types'\nimport { getMemoOptions, memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      getMemoOptions(table.options, 'debugTable', 'getFacetedRowModel')\n    )\n}\n"], "names": ["getFacetedRowModel", "table", "columnId", "memo", "getPreFilteredRowModel", "getState", "columnFilters", "globalFilter", "getFilteredRowModel", "preRowModel", "rows", "length", "filterableIds", "map", "d", "id", "filter", "undefined", "Boolean", "filterRowsImpl", "row", "i", "filterRows", "getMemoOptions", "options"], "mappings": ";;;;;;;;;;;;;;;AAIO,SAASA,kBAAkBA,GAGP;EACzB,OAAO,CAACC,KAAK,EAAEC,QAAQ,KACrBC,UAAI,CACF,MAAM,CACJF,KAAK,CAACG,sBAAsB,EAAE,EAC9BH,KAAK,CAACI,QAAQ,EAAE,CAACC,aAAa,EAC9BL,KAAK,CAACI,QAAQ,EAAE,CAACE,YAAY,EAC7BN,KAAK,CAACO,mBAAmB,EAAE,CAC5B,EACD,CAACC,WAAW,EAAEH,aAAa,EAAEC,YAAY,KAAK;AAC5C,IAAA,IACE,CAACE,WAAW,CAACC,IAAI,CAACC,MAAM,IACvB,EAACL,aAAa,IAAA,IAAA,IAAbA,aAAa,CAAEK,MAAM,CAAI,IAAA,CAACJ,YAAa,EACzC;AACA,MAAA,OAAOE,WAAW,CAAA;AACpB,KAAA;AAEA,IAAA,MAAMG,aAAa,GAAG,CACpB,GAAGN,aAAa,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,CAAC,CAACC,MAAM,CAACF,CAAC,IAAIA,CAAC,KAAKZ,QAAQ,CAAC,EAC3DK,YAAY,GAAG,YAAY,GAAGU,SAAS,CACxC,CAACD,MAAM,CAACE,OAAO,CAAa,CAAA;IAE7B,MAAMC,cAAc,GAAIC,GAAe,IAAK;AAC1C;AACA,MAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,aAAa,CAACD,MAAM,EAAEU,CAAC,EAAE,EAAE;QAC7C,IAAID,GAAG,CAACd,aAAa,CAACM,aAAa,CAACS,CAAC,CAAC,CAAE,KAAK,KAAK,EAAE;AAClD,UAAA,OAAO,KAAK,CAAA;AACd,SAAA;AACF,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;KACZ,CAAA;IAED,OAAOC,0BAAU,CAACb,WAAW,CAACC,IAAI,EAAES,cAAc,EAAElB,KAAK,CAAC,CAAA;GAC3D,EACDsB,oBAAc,CAACtB,KAAK,CAACuB,OAAO,EAAE,YAAY,EAAE,oBAAoB,CAClE,CAAC,CAAA;AACL;;;;"}