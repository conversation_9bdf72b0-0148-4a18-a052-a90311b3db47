#!/usr/bin/env node
"use strict";var Ev=Object.create;var Zu=Object.defineProperty;var bv=Object.getOwnPropertyDescriptor;var Dv=Object.getOwnPropertyNames;var wv=Object.getPrototypeOf,Av=Object.prototype.hasOwnProperty;var b=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),xv=(t,e)=>{for(var r in e)Zu(t,r,{get:e[r],enumerable:!0})},Sv=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Dv(e))!Av.call(t,i)&&i!==r&&Zu(t,i,{get:()=>e[i],enumerable:!(n=bv(e,i))||n.enumerable});return t};var X=(t,e,r)=>(r=t!=null?Ev(wv(t)):{},Sv(e||!t||!t.__esModule?Zu(r,"default",{value:t,enumerable:!0}):r,t));var gm=b((XM,Jt)=>{function Qu(t){return Jt.exports=Qu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jt.exports.__esModule=!0,Jt.exports.default=Jt.exports,Qu(t)}Jt.exports=Qu,Jt.exports.__esModule=!0,Jt.exports.default=Jt.exports});var Em=b((ZM,Xt)=>{var ym=gm().default;function _m(){"use strict";Xt.exports=_m=function(){return e},Xt.exports.__esModule=!0,Xt.exports.default=Xt.exports;var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(A,F,v){A[F]=v.value},s=typeof Symbol=="function"?Symbol:{},o=s.iterator||"@@iterator",a=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function c(A,F,v){return Object.defineProperty(A,F,{value:v,enumerable:!0,configurable:!0,writable:!0}),A[F]}try{c({},"")}catch{c=function(v,k,$){return v[k]=$}}function l(A,F,v,k){var $=F&&F.prototype instanceof D?F:D,q=Object.create($.prototype),ue=new Qe(k||[]);return i(q,"_invoke",{value:be(A,v,ue)}),q}function f(A,F,v){try{return{type:"normal",arg:A.call(F,v)}}catch(k){return{type:"throw",arg:k}}}e.wrap=l;var h="suspendedStart",m="suspendedYield",_="executing",y="completed",p={};function D(){}function C(){}function N(){}var U={};c(U,o,function(){return this});var M=Object.getPrototypeOf,Y=M&&M(M(S([])));Y&&Y!==r&&n.call(Y,o)&&(U=Y);var G=N.prototype=D.prototype=Object.create(U);function z(A){["next","throw","return"].forEach(function(F){c(A,F,function(v){return this._invoke(F,v)})})}function Q(A,F){function v($,q,ue,Re){var se=f(A[$],A,q);if(se.type!=="throw"){var De=se.arg,V=De.value;return V&&ym(V)=="object"&&n.call(V,"__await")?F.resolve(V.__await).then(function(Ke){v("next",Ke,ue,Re)},function(Ke){v("throw",Ke,ue,Re)}):F.resolve(V).then(function(Ke){De.value=Ke,ue(De)},function(Ke){return v("throw",Ke,ue,Re)})}Re(se.arg)}var k;i(this,"_invoke",{value:function(q,ue){function Re(){return new F(function(se,De){v(q,ue,se,De)})}return k=k?k.then(Re,Re):Re()}})}function be(A,F,v){var k=h;return function($,q){if(k===_)throw new Error("Generator is already running");if(k===y){if($==="throw")throw q;return{value:t,done:!0}}for(v.method=$,v.arg=q;;){var ue=v.delegate;if(ue){var Re=R(ue,v);if(Re){if(Re===p)continue;return Re}}if(v.method==="next")v.sent=v._sent=v.arg;else if(v.method==="throw"){if(k===h)throw k=y,v.arg;v.dispatchException(v.arg)}else v.method==="return"&&v.abrupt("return",v.arg);k=_;var se=f(A,F,v);if(se.type==="normal"){if(k=v.done?y:m,se.arg===p)continue;return{value:se.arg,done:v.done}}se.type==="throw"&&(k=y,v.method="throw",v.arg=se.arg)}}}function R(A,F){var v=F.method,k=A.iterator[v];if(k===t)return F.delegate=null,v==="throw"&&A.iterator.return&&(F.method="return",F.arg=t,R(A,F),F.method==="throw")||v!=="return"&&(F.method="throw",F.arg=new TypeError("The iterator does not provide a '"+v+"' method")),p;var $=f(k,A.iterator,F.arg);if($.type==="throw")return F.method="throw",F.arg=$.arg,F.delegate=null,p;var q=$.arg;return q?q.done?(F[A.resultName]=q.value,F.next=A.nextLoc,F.method!=="return"&&(F.method="next",F.arg=t),F.delegate=null,p):q:(F.method="throw",F.arg=new TypeError("iterator result is not an object"),F.delegate=null,p)}function ae(A){var F={tryLoc:A[0]};1 in A&&(F.catchLoc=A[1]),2 in A&&(F.finallyLoc=A[2],F.afterLoc=A[3]),this.tryEntries.push(F)}function ie(A){var F=A.completion||{};F.type="normal",delete F.arg,A.completion=F}function Qe(A){this.tryEntries=[{tryLoc:"root"}],A.forEach(ae,this),this.reset(!0)}function S(A){if(A||A===""){var F=A[o];if(F)return F.call(A);if(typeof A.next=="function")return A;if(!isNaN(A.length)){var v=-1,k=function $(){for(;++v<A.length;)if(n.call(A,v))return $.value=A[v],$.done=!1,$;return $.value=t,$.done=!0,$};return k.next=k}}throw new TypeError(ym(A)+" is not iterable")}return C.prototype=N,i(G,"constructor",{value:N,configurable:!0}),i(N,"constructor",{value:C,configurable:!0}),C.displayName=c(N,u,"GeneratorFunction"),e.isGeneratorFunction=function(A){var F=typeof A=="function"&&A.constructor;return!!F&&(F===C||(F.displayName||F.name)==="GeneratorFunction")},e.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,N):(A.__proto__=N,c(A,u,"GeneratorFunction")),A.prototype=Object.create(G),A},e.awrap=function(A){return{__await:A}},z(Q.prototype),c(Q.prototype,a,function(){return this}),e.AsyncIterator=Q,e.async=function(A,F,v,k,$){$===void 0&&($=Promise);var q=new Q(l(A,F,v,k),$);return e.isGeneratorFunction(F)?q:q.next().then(function(ue){return ue.done?ue.value:q.next()})},z(G),c(G,u,"Generator"),c(G,o,function(){return this}),c(G,"toString",function(){return"[object Generator]"}),e.keys=function(A){var F=Object(A),v=[];for(var k in F)v.push(k);return v.reverse(),function $(){for(;v.length;){var q=v.pop();if(q in F)return $.value=q,$.done=!1,$}return $.done=!0,$}},e.values=S,Qe.prototype={constructor:Qe,reset:function(F){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(ie),!F)for(var v in this)v.charAt(0)==="t"&&n.call(this,v)&&!isNaN(+v.slice(1))&&(this[v]=t)},stop:function(){this.done=!0;var F=this.tryEntries[0].completion;if(F.type==="throw")throw F.arg;return this.rval},dispatchException:function(F){if(this.done)throw F;var v=this;function k(De,V){return ue.type="throw",ue.arg=F,v.next=De,V&&(v.method="next",v.arg=t),!!V}for(var $=this.tryEntries.length-1;$>=0;--$){var q=this.tryEntries[$],ue=q.completion;if(q.tryLoc==="root")return k("end");if(q.tryLoc<=this.prev){var Re=n.call(q,"catchLoc"),se=n.call(q,"finallyLoc");if(Re&&se){if(this.prev<q.catchLoc)return k(q.catchLoc,!0);if(this.prev<q.finallyLoc)return k(q.finallyLoc)}else if(Re){if(this.prev<q.catchLoc)return k(q.catchLoc,!0)}else{if(!se)throw new Error("try statement without catch or finally");if(this.prev<q.finallyLoc)return k(q.finallyLoc)}}}},abrupt:function(F,v){for(var k=this.tryEntries.length-1;k>=0;--k){var $=this.tryEntries[k];if($.tryLoc<=this.prev&&n.call($,"finallyLoc")&&this.prev<$.finallyLoc){var q=$;break}}q&&(F==="break"||F==="continue")&&q.tryLoc<=v&&v<=q.finallyLoc&&(q=null);var ue=q?q.completion:{};return ue.type=F,ue.arg=v,q?(this.method="next",this.next=q.finallyLoc,p):this.complete(ue)},complete:function(F,v){if(F.type==="throw")throw F.arg;return F.type==="break"||F.type==="continue"?this.next=F.arg:F.type==="return"?(this.rval=this.arg=F.arg,this.method="return",this.next="end"):F.type==="normal"&&v&&(this.next=v),p},finish:function(F){for(var v=this.tryEntries.length-1;v>=0;--v){var k=this.tryEntries[v];if(k.finallyLoc===F)return this.complete(k.completion,k.afterLoc),ie(k),p}},catch:function(F){for(var v=this.tryEntries.length-1;v>=0;--v){var k=this.tryEntries[v];if(k.tryLoc===F){var $=k.completion;if($.type==="throw"){var q=$.arg;ie(k)}return q}}throw new Error("illegal catch attempt")},delegateYield:function(F,v,k){return this.delegate={iterator:S(F),resultName:v,nextLoc:k},this.method==="next"&&(this.arg=t),p}},e}Xt.exports=_m,Xt.exports.__esModule=!0,Xt.exports.default=Xt.exports});var Dm=b((QM,bm)=>{var Ys=Em()();bm.exports=Ys;try{regeneratorRuntime=Ys}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=Ys:Function("r","regeneratorRuntime = r")(Ys)}});var Am=b((Dq,oc)=>{"use strict";var wm=(t,...e)=>new Promise(r=>{r(t(...e))});oc.exports=wm;oc.exports.default=wm});var Sm=b((wq,ac)=>{"use strict";var Cv=Am(),xm=t=>{if(!((Number.isInteger(t)||t===1/0)&&t>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],r=0,n=()=>{r--,e.length>0&&e.shift()()},i=(a,u,...c)=>{r++;let l=Cv(a,...c);u(l),l.then(n,n)},s=(a,u,...c)=>{r<t?i(a,u,...c):e.push(i.bind(null,a,u,...c))},o=(a,...u)=>new Promise(c=>s(a,c,...u));return Object.defineProperties(o,{activeCount:{get:()=>r},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};ac.exports=xm;ac.exports.default=xm});var Rm=b((Aq,uc)=>{"use strict";var Cm=Sm(),Ks=class extends Error{constructor(e){super(),this.value=e}},vv=async(t,e)=>e(await t),Rv=async t=>{let e=await Promise.all(t);if(e[1]===!0)throw new Ks(e[0]);return!1},vm=async(t,e,r)=>{r={concurrency:1/0,preserveOrder:!0,...r};let n=Cm(r.concurrency),i=[...t].map(o=>[o,n(vv,o,e)]),s=Cm(r.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>s(Rv,o)))}catch(o){if(o instanceof Ks)return o.value;throw o}};uc.exports=vm;uc.exports.default=vm});var Bm=b((xq,cc)=>{"use strict";var Fm=require("path"),Js=require("fs"),{promisify:Tm}=require("util"),Fv=Rm(),Tv=Tm(Js.stat),Ov=Tm(Js.lstat),Om={directory:"isDirectory",file:"isFile"};function km({type:t}){if(!(t in Om))throw new Error(`Invalid type specified: ${t}`)}var Pm=(t,e)=>t===void 0||e[Om[t]]();cc.exports=async(t,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},km(e);let r=e.allowSymlinks?Tv:Ov;return Fv(t,async n=>{try{let i=await r(Fm.resolve(e.cwd,n));return Pm(e.type,i)}catch{return!1}},e)};cc.exports.sync=(t,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},km(e);let r=e.allowSymlinks?Js.statSync:Js.lstatSync;for(let n of t)try{let i=r(Fm.resolve(e.cwd,n));if(Pm(e.type,i))return n}catch{}}});var Lm=b((Sq,lc)=>{"use strict";var Im=require("fs"),{promisify:kv}=require("util"),Pv=kv(Im.access);lc.exports=async t=>{try{return await Pv(t),!0}catch{return!1}};lc.exports.sync=t=>{try{return Im.accessSync(t),!0}catch{return!1}}});var Mm=b((Cq,Tn)=>{"use strict";var xr=require("path"),Xs=Bm(),Nm=Lm(),fc=Symbol("findUp.stop");Tn.exports=async(t,e={})=>{let r=xr.resolve(e.cwd||""),{root:n}=xr.parse(r),i=[].concat(t),s=async o=>{if(typeof t!="function")return Xs(i,o);let a=await t(o.cwd);return typeof a=="string"?Xs([a],o):a};for(;;){let o=await s({...e,cwd:r});if(o===fc)return;if(o)return xr.resolve(r,o);if(r===n)return;r=xr.dirname(r)}};Tn.exports.sync=(t,e={})=>{let r=xr.resolve(e.cwd||""),{root:n}=xr.parse(r),i=[].concat(t),s=o=>{if(typeof t!="function")return Xs.sync(i,o);let a=t(o.cwd);return typeof a=="string"?Xs.sync([a],o):a};for(;;){let o=s({...e,cwd:r});if(o===fc)return;if(o)return xr.resolve(r,o);if(r===n)return;r=xr.dirname(r)}};Tn.exports.exists=Nm;Tn.exports.sync.exists=Nm.sync;Tn.exports.stop=fc});var z0=b(In=>{"use strict";Object.defineProperty(In,"__esModule",{value:!0});In.splitWhen=In.flatten=void 0;function _F(t){return t.reduce((e,r)=>[].concat(e,r),[])}In.flatten=_F;function EF(t,e){let r=[[]],n=0;for(let i of t)e(i)?(n++,r[n]=[]):r[n].push(i);return r}In.splitWhen=EF});var V0=b(lo=>{"use strict";Object.defineProperty(lo,"__esModule",{value:!0});lo.isEnoentCodeError=void 0;function bF(t){return t.code==="ENOENT"}lo.isEnoentCodeError=bF});var Y0=b(fo=>{"use strict";Object.defineProperty(fo,"__esModule",{value:!0});fo.createDirentFromStats=void 0;var Ic=class{constructor(e,r){this.name=e,this.isBlockDevice=r.isBlockDevice.bind(r),this.isCharacterDevice=r.isCharacterDevice.bind(r),this.isDirectory=r.isDirectory.bind(r),this.isFIFO=r.isFIFO.bind(r),this.isFile=r.isFile.bind(r),this.isSocket=r.isSocket.bind(r),this.isSymbolicLink=r.isSymbolicLink.bind(r)}};function DF(t,e){return new Ic(t,e)}fo.createDirentFromStats=DF});var K0=b(Lt=>{"use strict";Object.defineProperty(Lt,"__esModule",{value:!0});Lt.removeLeadingDotSegment=Lt.escape=Lt.makeAbsolute=Lt.unixify=void 0;var wF=require("path"),AF=2,xF=/(\\?)([()*?[\]{|}]|^!|[!+@](?=\())/g;function SF(t){return t.replace(/\\/g,"/")}Lt.unixify=SF;function CF(t,e){return wF.resolve(t,e)}Lt.makeAbsolute=CF;function vF(t){return t.replace(xF,"\\$2")}Lt.escape=vF;function RF(t){if(t.charAt(0)==="."){let e=t.charAt(1);if(e==="/"||e==="\\")return t.slice(AF)}return t}Lt.removeLeadingDotSegment=RF});var X0=b((zq,J0)=>{J0.exports=function(e){if(typeof e!="string"||e==="")return!1;for(var r;r=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(r[2])return!0;e=e.slice(r.index+r[0].length)}return!1}});var eg=b((Vq,Q0)=>{var FF=X0(),Z0={"{":"}","(":")","[":"]"},TF=function(t){if(t[0]==="!")return!0;for(var e=0,r=-2,n=-2,i=-2,s=-2,o=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||n!==-1&&t[e]==="["&&t[e+1]!=="]"&&(n<e&&(n=t.indexOf("]",e)),n>e&&(o===-1||o>n||(o=t.indexOf("\\",e),o===-1||o>n)))||i!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(i=t.indexOf("}",e),i>e&&(o=t.indexOf("\\",e),o===-1||o>i))||s!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(s=t.indexOf(")",e),s>e&&(o=t.indexOf("\\",e),o===-1||o>s))||r!==-1&&t[e]==="("&&t[e+1]!=="|"&&(r<e&&(r=t.indexOf("|",e)),r!==-1&&t[r+1]!==")"&&(s=t.indexOf(")",r),s>r&&(o=t.indexOf("\\",r),o===-1||o>s))))return!0;if(t[e]==="\\"){var a=t[e+1];e+=2;var u=Z0[a];if(u){var c=t.indexOf(u,e);c!==-1&&(e=c+1)}if(t[e]==="!")return!0}else e++}return!1},OF=function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var r=t[e+1];e+=2;var n=Z0[r];if(n){var i=t.indexOf(n,e);i!==-1&&(e=i+1)}if(t[e]==="!")return!0}else e++}return!1};Q0.exports=function(e,r){if(typeof e!="string"||e==="")return!1;if(FF(e))return!0;var n=TF;return r&&r.strict===!1&&(n=OF),n(e)}});var rg=b((Yq,tg)=>{"use strict";var kF=eg(),PF=require("path").posix.dirname,BF=require("os").platform()==="win32",Lc="/",IF=/\\/g,LF=/[\{\[].*[\}\]]$/,NF=/(^|[^\\])([\{\[]|\([^\)]+$)/,MF=/\\([\!\*\?\|\[\]\(\)\{\}])/g;tg.exports=function(e,r){var n=Object.assign({flipBackslashes:!0},r);n.flipBackslashes&&BF&&e.indexOf(Lc)<0&&(e=e.replace(IF,Lc)),LF.test(e)&&(e+=Lc),e+="a";do e=PF(e);while(kF(e)||NF.test(e));return e.replace(MF,"$1")}});var ho=b(pt=>{"use strict";pt.isInteger=t=>typeof t=="number"?Number.isInteger(t):typeof t=="string"&&t.trim()!==""?Number.isInteger(Number(t)):!1;pt.find=(t,e)=>t.nodes.find(r=>r.type===e);pt.exceedsLimit=(t,e,r=1,n)=>n===!1||!pt.isInteger(t)||!pt.isInteger(e)?!1:(Number(e)-Number(t))/Number(r)>=n;pt.escapeNode=(t,e=0,r)=>{let n=t.nodes[e];n&&(r&&n.type===r||n.type==="open"||n.type==="close")&&n.escaped!==!0&&(n.value="\\"+n.value,n.escaped=!0)};pt.encloseBrace=t=>t.type!=="brace"||t.commas>>0+t.ranges>>0?!1:(t.invalid=!0,!0);pt.isInvalidBrace=t=>t.type!=="brace"?!1:t.invalid===!0||t.dollar?!0:!(t.commas>>0+t.ranges>>0)||t.open!==!0||t.close!==!0?(t.invalid=!0,!0):!1;pt.isOpenOrClose=t=>t.type==="open"||t.type==="close"?!0:t.open===!0||t.close===!0;pt.reduce=t=>t.reduce((e,r)=>(r.type==="text"&&e.push(r.value),r.type==="range"&&(r.type="text"),e),[]);pt.flatten=(...t)=>{let e=[],r=n=>{for(let i=0;i<n.length;i++){let s=n[i];Array.isArray(s)?r(s,e):s!==void 0&&e.push(s)}return e};return r(t),e}});var po=b((Jq,ig)=>{"use strict";var ng=ho();ig.exports=(t,e={})=>{let r=(n,i={})=>{let s=e.escapeInvalid&&ng.isInvalidBrace(i),o=n.invalid===!0&&e.escapeInvalid===!0,a="";if(n.value)return(s||o)&&ng.isOpenOrClose(n)?"\\"+n.value:n.value;if(n.value)return n.value;if(n.nodes)for(let u of n.nodes)a+=r(u);return a};return r(t)}});var og=b((Xq,sg)=>{"use strict";sg.exports=function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1}});var mg=b((Zq,pg)=>{"use strict";var ag=og(),sn=(t,e,r)=>{if(ag(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(ag(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let n={relaxZeros:!0,...r};typeof n.strictZeros=="boolean"&&(n.relaxZeros=n.strictZeros===!1);let i=String(n.relaxZeros),s=String(n.shorthand),o=String(n.capture),a=String(n.wrap),u=t+":"+e+"="+i+s+o+a;if(sn.cache.hasOwnProperty(u))return sn.cache[u].result;let c=Math.min(t,e),l=Math.max(t,e);if(Math.abs(c-l)===1){let y=t+"|"+e;return n.capture?`(${y})`:n.wrap===!1?y:`(?:${y})`}let f=dg(t)||dg(e),h={min:t,max:e,a:c,b:l},m=[],_=[];if(f&&(h.isPadded=f,h.maxLen=String(h.max).length),c<0){let y=l<0?Math.abs(l):1;_=ug(y,Math.abs(c),h,n),c=h.a=0}return l>=0&&(m=ug(c,l,h,n)),h.negatives=_,h.positives=m,h.result=qF(_,m,n),n.capture===!0?h.result=`(${h.result})`:n.wrap!==!1&&m.length+_.length>1&&(h.result=`(?:${h.result})`),sn.cache[u]=h,h.result};function qF(t,e,r){let n=Nc(t,e,"-",!1,r)||[],i=Nc(e,t,"",!1,r)||[],s=Nc(t,e,"-?",!0,r)||[];return n.concat(s).concat(i).join("|")}function HF(t,e){let r=1,n=1,i=lg(t,r),s=new Set([e]);for(;t<=i&&i<=e;)s.add(i),r+=1,i=lg(t,r);for(i=fg(e+1,n)-1;t<i&&i<=e;)s.add(i),n+=1,i=fg(e+1,n)-1;return s=[...s],s.sort(UF),s}function jF(t,e,r){if(t===e)return{pattern:t,count:[],digits:0};let n=$F(t,e),i=n.length,s="",o=0;for(let a=0;a<i;a++){let[u,c]=n[a];u===c?s+=u:u!=="0"||c!=="9"?s+=GF(u,c,r):o++}return o&&(s+=r.shorthand===!0?"\\d":"[0-9]"),{pattern:s,count:[o],digits:i}}function ug(t,e,r,n){let i=HF(t,e),s=[],o=t,a;for(let u=0;u<i.length;u++){let c=i[u],l=jF(String(o),String(c),n),f="";if(!r.isPadded&&a&&a.pattern===l.pattern){a.count.length>1&&a.count.pop(),a.count.push(l.count[0]),a.string=a.pattern+hg(a.count),o=c+1;continue}r.isPadded&&(f=WF(c,r,n)),l.string=f+l.pattern+hg(l.count),s.push(l),o=c+1,a=l}return s}function Nc(t,e,r,n,i){let s=[];for(let o of t){let{string:a}=o;!n&&!cg(e,"string",a)&&s.push(r+a),n&&cg(e,"string",a)&&s.push(r+a)}return s}function $F(t,e){let r=[];for(let n=0;n<t.length;n++)r.push([t[n],e[n]]);return r}function UF(t,e){return t>e?1:e>t?-1:0}function cg(t,e,r){return t.some(n=>n[e]===r)}function lg(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}function fg(t,e){return t-t%Math.pow(10,e)}function hg(t){let[e=0,r=""]=t;return r||e>1?`{${e+(r?","+r:"")}}`:""}function GF(t,e,r){return`[${t}${e-t===1?"":"-"}${e}]`}function dg(t){return/^-?(0+)\d/.test(t)}function WF(t,e,r){if(!e.isPadded)return t;let n=Math.abs(e.maxLen-String(t).length),i=r.relaxZeros!==!1;switch(n){case 0:return"";case 1:return i?"0?":"0";case 2:return i?"0{0,2}":"00";default:return i?`0{0,${n}}`:`0{${n}}`}}sn.cache={};sn.clearCache=()=>sn.cache={};pg.exports=sn});var Hc=b((Qq,Ag)=>{"use strict";var zF=require("util"),_g=mg(),gg=t=>t!==null&&typeof t=="object"&&!Array.isArray(t),VF=t=>e=>t===!0?Number(e):String(e),Mc=t=>typeof t=="number"||typeof t=="string"&&t!=="",Xi=t=>Number.isInteger(+t),qc=t=>{let e=`${t}`,r=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++r]==="0";);return r>0},YF=(t,e,r)=>typeof t=="string"||typeof e=="string"?!0:r.stringify===!0,KF=(t,e,r)=>{if(e>0){let n=t[0]==="-"?"-":"";n&&(t=t.slice(1)),t=n+t.padStart(n?e-1:e,"0")}return r===!1?String(t):t},yg=(t,e)=>{let r=t[0]==="-"?"-":"";for(r&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return r?"-"+t:t},JF=(t,e)=>{t.negatives.sort((o,a)=>o<a?-1:o>a?1:0),t.positives.sort((o,a)=>o<a?-1:o>a?1:0);let r=e.capture?"":"?:",n="",i="",s;return t.positives.length&&(n=t.positives.join("|")),t.negatives.length&&(i=`-(${r}${t.negatives.join("|")})`),n&&i?s=`${n}|${i}`:s=n||i,e.wrap?`(${r}${s})`:s},Eg=(t,e,r,n)=>{if(r)return _g(t,e,{wrap:!1,...n});let i=String.fromCharCode(t);if(t===e)return i;let s=String.fromCharCode(e);return`[${i}-${s}]`},bg=(t,e,r)=>{if(Array.isArray(t)){let n=r.wrap===!0,i=r.capture?"":"?:";return n?`(${i}${t.join("|")})`:t.join("|")}return _g(t,e,r)},Dg=(...t)=>new RangeError("Invalid range arguments: "+zF.inspect(...t)),wg=(t,e,r)=>{if(r.strictRanges===!0)throw Dg([t,e]);return[]},XF=(t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},ZF=(t,e,r=1,n={})=>{let i=Number(t),s=Number(e);if(!Number.isInteger(i)||!Number.isInteger(s)){if(n.strictRanges===!0)throw Dg([t,e]);return[]}i===0&&(i=0),s===0&&(s=0);let o=i>s,a=String(t),u=String(e),c=String(r);r=Math.max(Math.abs(r),1);let l=qc(a)||qc(u)||qc(c),f=l?Math.max(a.length,u.length,c.length):0,h=l===!1&&YF(t,e,n)===!1,m=n.transform||VF(h);if(n.toRegex&&r===1)return Eg(yg(t,f),yg(e,f),!0,n);let _={negatives:[],positives:[]},y=C=>_[C<0?"negatives":"positives"].push(Math.abs(C)),p=[],D=0;for(;o?i>=s:i<=s;)n.toRegex===!0&&r>1?y(i):p.push(KF(m(i,D),f,h)),i=o?i-r:i+r,D++;return n.toRegex===!0?r>1?JF(_,n):bg(p,null,{wrap:!1,...n}):p},QF=(t,e,r=1,n={})=>{if(!Xi(t)&&t.length>1||!Xi(e)&&e.length>1)return wg(t,e,n);let i=n.transform||(h=>String.fromCharCode(h)),s=`${t}`.charCodeAt(0),o=`${e}`.charCodeAt(0),a=s>o,u=Math.min(s,o),c=Math.max(s,o);if(n.toRegex&&r===1)return Eg(u,c,!1,n);let l=[],f=0;for(;a?s>=o:s<=o;)l.push(i(s,f)),s=a?s-r:s+r,f++;return n.toRegex===!0?bg(l,null,{wrap:!1,options:n}):l},mo=(t,e,r,n={})=>{if(e==null&&Mc(t))return[t];if(!Mc(t)||!Mc(e))return wg(t,e,n);if(typeof r=="function")return mo(t,e,1,{transform:r});if(gg(r))return mo(t,e,0,r);let i={...n};return i.capture===!0&&(i.wrap=!0),r=r||i.step||1,Xi(r)?Xi(t)&&Xi(e)?ZF(t,e,r,i):QF(t,e,Math.max(Math.abs(r),1),i):r!=null&&!gg(r)?XF(r,i):mo(t,e,1,r)};Ag.exports=mo});var Cg=b((e6,Sg)=>{"use strict";var eT=Hc(),xg=ho(),tT=(t,e={})=>{let r=(n,i={})=>{let s=xg.isInvalidBrace(i),o=n.invalid===!0&&e.escapeInvalid===!0,a=s===!0||o===!0,u=e.escapeInvalid===!0?"\\":"",c="";if(n.isOpen===!0||n.isClose===!0)return u+n.value;if(n.type==="open")return a?u+n.value:"(";if(n.type==="close")return a?u+n.value:")";if(n.type==="comma")return n.prev.type==="comma"?"":a?n.value:"|";if(n.value)return n.value;if(n.nodes&&n.ranges>0){let l=xg.reduce(n.nodes),f=eT(...l,{...e,wrap:!1,toRegex:!0});if(f.length!==0)return l.length>1&&f.length>1?`(${f})`:f}if(n.nodes)for(let l of n.nodes)c+=r(l,n);return c};return r(t)};Sg.exports=tT});var Fg=b((t6,Rg)=>{"use strict";var rT=Hc(),vg=po(),Ln=ho(),on=(t="",e="",r=!1)=>{let n=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return r?Ln.flatten(e).map(i=>`{${i}}`):e;for(let i of t)if(Array.isArray(i))for(let s of i)n.push(on(s,e,r));else for(let s of e)r===!0&&typeof s=="string"&&(s=`{${s}}`),n.push(Array.isArray(s)?on(i,s,r):i+s);return Ln.flatten(n)},nT=(t,e={})=>{let r=e.rangeLimit===void 0?1e3:e.rangeLimit,n=(i,s={})=>{i.queue=[];let o=s,a=s.queue;for(;o.type!=="brace"&&o.type!=="root"&&o.parent;)o=o.parent,a=o.queue;if(i.invalid||i.dollar){a.push(on(a.pop(),vg(i,e)));return}if(i.type==="brace"&&i.invalid!==!0&&i.nodes.length===2){a.push(on(a.pop(),["{}"]));return}if(i.nodes&&i.ranges>0){let f=Ln.reduce(i.nodes);if(Ln.exceedsLimit(...f,e.step,r))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let h=rT(...f,e);h.length===0&&(h=vg(i,e)),a.push(on(a.pop(),h)),i.nodes=[];return}let u=Ln.encloseBrace(i),c=i.queue,l=i;for(;l.type!=="brace"&&l.type!=="root"&&l.parent;)l=l.parent,c=l.queue;for(let f=0;f<i.nodes.length;f++){let h=i.nodes[f];if(h.type==="comma"&&i.type==="brace"){f===1&&c.push(""),c.push("");continue}if(h.type==="close"){a.push(on(a.pop(),c,u));continue}if(h.value&&h.type!=="open"){c.push(on(c.pop(),h.value));continue}h.nodes&&n(h,i)}return c};return Ln.flatten(n(t))};Rg.exports=nT});var Og=b((r6,Tg)=>{"use strict";Tg.exports={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"}});var Lg=b((n6,Ig)=>{"use strict";var iT=po(),{MAX_LENGTH:kg,CHAR_BACKSLASH:jc,CHAR_BACKTICK:sT,CHAR_COMMA:oT,CHAR_DOT:aT,CHAR_LEFT_PARENTHESES:uT,CHAR_RIGHT_PARENTHESES:cT,CHAR_LEFT_CURLY_BRACE:lT,CHAR_RIGHT_CURLY_BRACE:fT,CHAR_LEFT_SQUARE_BRACKET:Pg,CHAR_RIGHT_SQUARE_BRACKET:Bg,CHAR_DOUBLE_QUOTE:hT,CHAR_SINGLE_QUOTE:dT,CHAR_NO_BREAK_SPACE:pT,CHAR_ZERO_WIDTH_NOBREAK_SPACE:mT}=Og(),gT=(t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let r=e||{},n=typeof r.maxLength=="number"?Math.min(kg,r.maxLength):kg;if(t.length>n)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${n})`);let i={type:"root",input:t,nodes:[]},s=[i],o=i,a=i,u=0,c=t.length,l=0,f=0,h,m={},_=()=>t[l++],y=p=>{if(p.type==="text"&&a.type==="dot"&&(a.type="text"),a&&a.type==="text"&&p.type==="text"){a.value+=p.value;return}return o.nodes.push(p),p.parent=o,p.prev=a,a=p,p};for(y({type:"bos"});l<c;)if(o=s[s.length-1],h=_(),!(h===mT||h===pT)){if(h===jc){y({type:"text",value:(e.keepEscaping?h:"")+_()});continue}if(h===Bg){y({type:"text",value:"\\"+h});continue}if(h===Pg){u++;let p=!0,D;for(;l<c&&(D=_());){if(h+=D,D===Pg){u++;continue}if(D===jc){h+=_();continue}if(D===Bg&&(u--,u===0))break}y({type:"text",value:h});continue}if(h===uT){o=y({type:"paren",nodes:[]}),s.push(o),y({type:"text",value:h});continue}if(h===cT){if(o.type!=="paren"){y({type:"text",value:h});continue}o=s.pop(),y({type:"text",value:h}),o=s[s.length-1];continue}if(h===hT||h===dT||h===sT){let p=h,D;for(e.keepQuotes!==!0&&(h="");l<c&&(D=_());){if(D===jc){h+=D+_();continue}if(D===p){e.keepQuotes===!0&&(h+=D);break}h+=D}y({type:"text",value:h});continue}if(h===lT){f++;let D={type:"brace",open:!0,close:!1,dollar:a.value&&a.value.slice(-1)==="$"||o.dollar===!0,depth:f,commas:0,ranges:0,nodes:[]};o=y(D),s.push(o),y({type:"open",value:h});continue}if(h===fT){if(o.type!=="brace"){y({type:"text",value:h});continue}let p="close";o=s.pop(),o.close=!0,y({type:p,value:h}),f--,o=s[s.length-1];continue}if(h===oT&&f>0){if(o.ranges>0){o.ranges=0;let p=o.nodes.shift();o.nodes=[p,{type:"text",value:iT(o)}]}y({type:"comma",value:h}),o.commas++;continue}if(h===aT&&f>0&&o.commas===0){let p=o.nodes;if(f===0||p.length===0){y({type:"text",value:h});continue}if(a.type==="dot"){if(o.range=[],a.value+=h,a.type="range",o.nodes.length!==3&&o.nodes.length!==5){o.invalid=!0,o.ranges=0,a.type="text";continue}o.ranges++,o.args=[];continue}if(a.type==="range"){p.pop();let D=p[p.length-1];D.value+=a.value+h,a=D,o.ranges--;continue}y({type:"dot",value:h});continue}y({type:"text",value:h})}do if(o=s.pop(),o.type!=="root"){o.nodes.forEach(C=>{C.nodes||(C.type==="open"&&(C.isOpen=!0),C.type==="close"&&(C.isClose=!0),C.nodes||(C.type="text"),C.invalid=!0)});let p=s[s.length-1],D=p.nodes.indexOf(o);p.nodes.splice(D,1,...o.nodes)}while(s.length>0);return y({type:"eos"}),i};Ig.exports=gT});var qg=b((i6,Mg)=>{"use strict";var Ng=po(),yT=Cg(),_T=Fg(),ET=Lg(),at=(t,e={})=>{let r=[];if(Array.isArray(t))for(let n of t){let i=at.create(n,e);Array.isArray(i)?r.push(...i):r.push(i)}else r=[].concat(at.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(r=[...new Set(r)]),r};at.parse=(t,e={})=>ET(t,e);at.stringify=(t,e={})=>Ng(typeof t=="string"?at.parse(t,e):t,e);at.compile=(t,e={})=>(typeof t=="string"&&(t=at.parse(t,e)),yT(t,e));at.expand=(t,e={})=>{typeof t=="string"&&(t=at.parse(t,e));let r=_T(t,e);return e.noempty===!0&&(r=r.filter(Boolean)),e.nodupes===!0&&(r=[...new Set(r)]),r};at.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?at.compile(t,e):at.expand(t,e);Mg.exports=at});var Zi=b((s6,Gg)=>{"use strict";var bT=require("path"),Nt="\\\\/",Hg=`[^${Nt}]`,nr="\\.",DT="\\+",wT="\\?",go="\\/",AT="(?=.)",jg="[^/]",$c=`(?:${go}|$)`,$g=`(?:^|${go})`,Uc=`${nr}{1,2}${$c}`,xT=`(?!${nr})`,ST=`(?!${$g}${Uc})`,CT=`(?!${nr}{0,1}${$c})`,vT=`(?!${Uc})`,RT=`[^.${go}]`,FT=`${jg}*?`,Ug={DOT_LITERAL:nr,PLUS_LITERAL:DT,QMARK_LITERAL:wT,SLASH_LITERAL:go,ONE_CHAR:AT,QMARK:jg,END_ANCHOR:$c,DOTS_SLASH:Uc,NO_DOT:xT,NO_DOTS:ST,NO_DOT_SLASH:CT,NO_DOTS_SLASH:vT,QMARK_NO_DOT:RT,STAR:FT,START_ANCHOR:$g},TT={...Ug,SLASH_LITERAL:`[${Nt}]`,QMARK:Hg,STAR:`${Hg}*?`,DOTS_SLASH:`${nr}{1,2}(?:[${Nt}]|$)`,NO_DOT:`(?!${nr})`,NO_DOTS:`(?!(?:^|[${Nt}])${nr}{1,2}(?:[${Nt}]|$))`,NO_DOT_SLASH:`(?!${nr}{0,1}(?:[${Nt}]|$))`,NO_DOTS_SLASH:`(?!${nr}{1,2}(?:[${Nt}]|$))`,QMARK_NO_DOT:`[^.${Nt}]`,START_ANCHOR:`(?:^|[${Nt}])`,END_ANCHOR:`(?:[${Nt}]|$)`},OT={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};Gg.exports={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:OT,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:bT.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?TT:Ug}}});var Qi=b(tt=>{"use strict";var kT=require("path"),PT=process.platform==="win32",{REGEX_BACKSLASH:BT,REGEX_REMOVE_BACKSLASH:IT,REGEX_SPECIAL_CHARS:LT,REGEX_SPECIAL_CHARS_GLOBAL:NT}=Zi();tt.isObject=t=>t!==null&&typeof t=="object"&&!Array.isArray(t);tt.hasRegexChars=t=>LT.test(t);tt.isRegexChar=t=>t.length===1&&tt.hasRegexChars(t);tt.escapeRegex=t=>t.replace(NT,"\\$1");tt.toPosixSlashes=t=>t.replace(BT,"/");tt.removeBackslashes=t=>t.replace(IT,e=>e==="\\"?"":e);tt.supportsLookbehinds=()=>{let t=process.version.slice(1).split(".").map(Number);return t.length===3&&t[0]>=9||t[0]===8&&t[1]>=10};tt.isWindows=t=>t&&typeof t.windows=="boolean"?t.windows:PT===!0||kT.sep==="\\";tt.escapeLast=(t,e,r)=>{let n=t.lastIndexOf(e,r);return n===-1?t:t[n-1]==="\\"?tt.escapeLast(t,e,n-1):`${t.slice(0,n)}\\${t.slice(n)}`};tt.removePrefix=(t,e={})=>{let r=t;return r.startsWith("./")&&(r=r.slice(2),e.prefix="./"),r};tt.wrapOutput=(t,e={},r={})=>{let n=r.contains?"":"^",i=r.contains?"":"$",s=`${n}(?:${t})${i}`;return e.negated===!0&&(s=`(?:^(?!${s}).*$)`),s}});var Zg=b((a6,Xg)=>{"use strict";var Wg=Qi(),{CHAR_ASTERISK:Gc,CHAR_AT:MT,CHAR_BACKWARD_SLASH:es,CHAR_COMMA:qT,CHAR_DOT:Wc,CHAR_EXCLAMATION_MARK:zc,CHAR_FORWARD_SLASH:Jg,CHAR_LEFT_CURLY_BRACE:Vc,CHAR_LEFT_PARENTHESES:Yc,CHAR_LEFT_SQUARE_BRACKET:HT,CHAR_PLUS:jT,CHAR_QUESTION_MARK:zg,CHAR_RIGHT_CURLY_BRACE:$T,CHAR_RIGHT_PARENTHESES:Vg,CHAR_RIGHT_SQUARE_BRACKET:UT}=Zi(),Yg=t=>t===Jg||t===es,Kg=t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},GT=(t,e)=>{let r=e||{},n=t.length-1,i=r.parts===!0||r.scanToEnd===!0,s=[],o=[],a=[],u=t,c=-1,l=0,f=0,h=!1,m=!1,_=!1,y=!1,p=!1,D=!1,C=!1,N=!1,U=!1,M=!1,Y=0,G,z,Q={value:"",depth:0,isGlob:!1},be=()=>c>=n,R=()=>u.charCodeAt(c+1),ae=()=>(G=z,u.charCodeAt(++c));for(;c<n;){z=ae();let F;if(z===es){C=Q.backslashes=!0,z=ae(),z===Vc&&(D=!0);continue}if(D===!0||z===Vc){for(Y++;be()!==!0&&(z=ae());){if(z===es){C=Q.backslashes=!0,ae();continue}if(z===Vc){Y++;continue}if(D!==!0&&z===Wc&&(z=ae())===Wc){if(h=Q.isBrace=!0,_=Q.isGlob=!0,M=!0,i===!0)continue;break}if(D!==!0&&z===qT){if(h=Q.isBrace=!0,_=Q.isGlob=!0,M=!0,i===!0)continue;break}if(z===$T&&(Y--,Y===0)){D=!1,h=Q.isBrace=!0,M=!0;break}}if(i===!0)continue;break}if(z===Jg){if(s.push(c),o.push(Q),Q={value:"",depth:0,isGlob:!1},M===!0)continue;if(G===Wc&&c===l+1){l+=2;continue}f=c+1;continue}if(r.noext!==!0&&(z===jT||z===MT||z===Gc||z===zg||z===zc)===!0&&R()===Yc){if(_=Q.isGlob=!0,y=Q.isExtglob=!0,M=!0,z===zc&&c===l&&(U=!0),i===!0){for(;be()!==!0&&(z=ae());){if(z===es){C=Q.backslashes=!0,z=ae();continue}if(z===Vg){_=Q.isGlob=!0,M=!0;break}}continue}break}if(z===Gc){if(G===Gc&&(p=Q.isGlobstar=!0),_=Q.isGlob=!0,M=!0,i===!0)continue;break}if(z===zg){if(_=Q.isGlob=!0,M=!0,i===!0)continue;break}if(z===HT){for(;be()!==!0&&(F=ae());){if(F===es){C=Q.backslashes=!0,ae();continue}if(F===UT){m=Q.isBracket=!0,_=Q.isGlob=!0,M=!0;break}}if(i===!0)continue;break}if(r.nonegate!==!0&&z===zc&&c===l){N=Q.negated=!0,l++;continue}if(r.noparen!==!0&&z===Yc){if(_=Q.isGlob=!0,i===!0){for(;be()!==!0&&(z=ae());){if(z===Yc){C=Q.backslashes=!0,z=ae();continue}if(z===Vg){M=!0;break}}continue}break}if(_===!0){if(M=!0,i===!0)continue;break}}r.noext===!0&&(y=!1,_=!1);let ie=u,Qe="",S="";l>0&&(Qe=u.slice(0,l),u=u.slice(l),f-=l),ie&&_===!0&&f>0?(ie=u.slice(0,f),S=u.slice(f)):_===!0?(ie="",S=u):ie=u,ie&&ie!==""&&ie!=="/"&&ie!==u&&Yg(ie.charCodeAt(ie.length-1))&&(ie=ie.slice(0,-1)),r.unescape===!0&&(S&&(S=Wg.removeBackslashes(S)),ie&&C===!0&&(ie=Wg.removeBackslashes(ie)));let A={prefix:Qe,input:t,start:l,base:ie,glob:S,isBrace:h,isBracket:m,isGlob:_,isExtglob:y,isGlobstar:p,negated:N,negatedExtglob:U};if(r.tokens===!0&&(A.maxDepth=0,Yg(z)||o.push(Q),A.tokens=o),r.parts===!0||r.tokens===!0){let F;for(let v=0;v<s.length;v++){let k=F?F+1:l,$=s[v],q=t.slice(k,$);r.tokens&&(v===0&&l!==0?(o[v].isPrefix=!0,o[v].value=Qe):o[v].value=q,Kg(o[v]),A.maxDepth+=o[v].depth),(v!==0||q!=="")&&a.push(q),F=$}if(F&&F+1<t.length){let v=t.slice(F+1);a.push(v),r.tokens&&(o[o.length-1].value=v,Kg(o[o.length-1]),A.maxDepth+=o[o.length-1].depth)}A.slashes=s,A.parts=a}return A};Xg.exports=GT});var ty=b((u6,ey)=>{"use strict";var yo=Zi(),ut=Qi(),{MAX_LENGTH:_o,POSIX_REGEX_SOURCE:WT,REGEX_NON_SPECIAL_CHARS:zT,REGEX_SPECIAL_CHARS_BACKREF:VT,REPLACEMENTS:Qg}=yo,YT=(t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();let r=`[${t.join("-")}]`;try{new RegExp(r)}catch{return t.map(i=>ut.escapeRegex(i)).join("..")}return r},Nn=(t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,Kc=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=Qg[t]||t;let r={...e},n=typeof r.maxLength=="number"?Math.min(_o,r.maxLength):_o,i=t.length;if(i>n)throw new SyntaxError(`Input length: ${i}, exceeds maximum allowed length: ${n}`);let s={type:"bos",value:"",output:r.prepend||""},o=[s],a=r.capture?"":"?:",u=ut.isWindows(e),c=yo.globChars(u),l=yo.extglobChars(c),{DOT_LITERAL:f,PLUS_LITERAL:h,SLASH_LITERAL:m,ONE_CHAR:_,DOTS_SLASH:y,NO_DOT:p,NO_DOT_SLASH:D,NO_DOTS_SLASH:C,QMARK:N,QMARK_NO_DOT:U,STAR:M,START_ANCHOR:Y}=c,G=I=>`(${a}(?:(?!${Y}${I.dot?y:f}).)*?)`,z=r.dot?"":p,Q=r.dot?N:U,be=r.bash===!0?G(r):M;r.capture&&(be=`(${be})`),typeof r.noext=="boolean"&&(r.noextglob=r.noext);let R={input:t,index:-1,start:0,dot:r.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:o};t=ut.removePrefix(t,R),i=t.length;let ae=[],ie=[],Qe=[],S=s,A,F=()=>R.index===i-1,v=R.peek=(I=1)=>t[R.index+I],k=R.advance=()=>t[++R.index]||"",$=()=>t.slice(R.index+1),q=(I="",le=0)=>{R.consumed+=I,R.index+=le},ue=I=>{R.output+=I.output!=null?I.output:I.value,q(I.value)},Re=()=>{let I=1;for(;v()==="!"&&(v(2)!=="("||v(3)==="?");)k(),R.start++,I++;return I%2===0?!1:(R.negated=!0,R.start++,!0)},se=I=>{R[I]++,Qe.push(I)},De=I=>{R[I]--,Qe.pop()},V=I=>{if(S.type==="globstar"){let le=R.braces>0&&(I.type==="comma"||I.type==="brace"),B=I.extglob===!0||ae.length&&(I.type==="pipe"||I.type==="paren");I.type!=="slash"&&I.type!=="paren"&&!le&&!B&&(R.output=R.output.slice(0,-S.output.length),S.type="star",S.value="*",S.output=be,R.output+=S.output)}if(ae.length&&I.type!=="paren"&&(ae[ae.length-1].inner+=I.value),(I.value||I.output)&&ue(I),S&&S.type==="text"&&I.type==="text"){S.value+=I.value,S.output=(S.output||"")+I.value;return}I.prev=S,o.push(I),S=I},Ke=(I,le)=>{let B={...l[le],conditions:1,inner:""};B.prev=S,B.parens=R.parens,B.output=R.output;let d=(r.capture?"(":"")+B.open;se("parens"),V({type:I,value:le,output:R.output?"":_}),V({type:"paren",extglob:!0,value:k(),output:d}),ae.push(B)},ot=I=>{let le=I.close+(r.capture?")":""),B;if(I.type==="negate"){let d=be;if(I.inner&&I.inner.length>1&&I.inner.includes("/")&&(d=G(r)),(d!==be||F()||/^\)+$/.test($()))&&(le=I.close=`)$))${d}`),I.inner.includes("*")&&(B=$())&&/^\.[^\\/.]+$/.test(B)){let w=Kc(B,{...e,fastpaths:!1}).output;le=I.close=`)${w})${d})`}I.prev.type==="bos"&&(R.negatedExtglob=!0)}V({type:"paren",extglob:!0,value:A,output:le}),De("parens")};if(r.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let I=!1,le=t.replace(VT,(B,d,w,g,O,j)=>g==="\\"?(I=!0,B):g==="?"?d?d+g+(O?N.repeat(O.length):""):j===0?Q+(O?N.repeat(O.length):""):N.repeat(w.length):g==="."?f.repeat(w.length):g==="*"?d?d+g+(O?be:""):be:d?B:`\\${B}`);return I===!0&&(r.unescape===!0?le=le.replace(/\\/g,""):le=le.replace(/\\+/g,B=>B.length%2===0?"\\\\":B?"\\":"")),le===t&&r.contains===!0?(R.output=t,R):(R.output=ut.wrapOutput(le,R,e),R)}for(;!F();){if(A=k(),A==="\0")continue;if(A==="\\"){let B=v();if(B==="/"&&r.bash!==!0||B==="."||B===";")continue;if(!B){A+="\\",V({type:"text",value:A});continue}let d=/^\\+/.exec($()),w=0;if(d&&d[0].length>2&&(w=d[0].length,R.index+=w,w%2!==0&&(A+="\\")),r.unescape===!0?A=k():A+=k(),R.brackets===0){V({type:"text",value:A});continue}}if(R.brackets>0&&(A!=="]"||S.value==="["||S.value==="[^")){if(r.posix!==!1&&A===":"){let B=S.value.slice(1);if(B.includes("[")&&(S.posix=!0,B.includes(":"))){let d=S.value.lastIndexOf("["),w=S.value.slice(0,d),g=S.value.slice(d+2),O=WT[g];if(O){S.value=w+O,R.backtrack=!0,k(),!s.output&&o.indexOf(S)===1&&(s.output=_);continue}}}(A==="["&&v()!==":"||A==="-"&&v()==="]")&&(A=`\\${A}`),A==="]"&&(S.value==="["||S.value==="[^")&&(A=`\\${A}`),r.posix===!0&&A==="!"&&S.value==="["&&(A="^"),S.value+=A,ue({value:A});continue}if(R.quotes===1&&A!=='"'){A=ut.escapeRegex(A),S.value+=A,ue({value:A});continue}if(A==='"'){R.quotes=R.quotes===1?0:1,r.keepQuotes===!0&&V({type:"text",value:A});continue}if(A==="("){se("parens"),V({type:"paren",value:A});continue}if(A===")"){if(R.parens===0&&r.strictBrackets===!0)throw new SyntaxError(Nn("opening","("));let B=ae[ae.length-1];if(B&&R.parens===B.parens+1){ot(ae.pop());continue}V({type:"paren",value:A,output:R.parens?")":"\\)"}),De("parens");continue}if(A==="["){if(r.nobracket===!0||!$().includes("]")){if(r.nobracket!==!0&&r.strictBrackets===!0)throw new SyntaxError(Nn("closing","]"));A=`\\${A}`}else se("brackets");V({type:"bracket",value:A});continue}if(A==="]"){if(r.nobracket===!0||S&&S.type==="bracket"&&S.value.length===1){V({type:"text",value:A,output:`\\${A}`});continue}if(R.brackets===0){if(r.strictBrackets===!0)throw new SyntaxError(Nn("opening","["));V({type:"text",value:A,output:`\\${A}`});continue}De("brackets");let B=S.value.slice(1);if(S.posix!==!0&&B[0]==="^"&&!B.includes("/")&&(A=`/${A}`),S.value+=A,ue({value:A}),r.literalBrackets===!1||ut.hasRegexChars(B))continue;let d=ut.escapeRegex(S.value);if(R.output=R.output.slice(0,-S.value.length),r.literalBrackets===!0){R.output+=d,S.value=d;continue}S.value=`(${a}${d}|${S.value})`,R.output+=S.value;continue}if(A==="{"&&r.nobrace!==!0){se("braces");let B={type:"brace",value:A,output:"(",outputIndex:R.output.length,tokensIndex:R.tokens.length};ie.push(B),V(B);continue}if(A==="}"){let B=ie[ie.length-1];if(r.nobrace===!0||!B){V({type:"text",value:A,output:A});continue}let d=")";if(B.dots===!0){let w=o.slice(),g=[];for(let O=w.length-1;O>=0&&(o.pop(),w[O].type!=="brace");O--)w[O].type!=="dots"&&g.unshift(w[O].value);d=YT(g,r),R.backtrack=!0}if(B.comma!==!0&&B.dots!==!0){let w=R.output.slice(0,B.outputIndex),g=R.tokens.slice(B.tokensIndex);B.value=B.output="\\{",A=d="\\}",R.output=w;for(let O of g)R.output+=O.output||O.value}V({type:"brace",value:A,output:d}),De("braces"),ie.pop();continue}if(A==="|"){ae.length>0&&ae[ae.length-1].conditions++,V({type:"text",value:A});continue}if(A===","){let B=A,d=ie[ie.length-1];d&&Qe[Qe.length-1]==="braces"&&(d.comma=!0,B="|"),V({type:"comma",value:A,output:B});continue}if(A==="/"){if(S.type==="dot"&&R.index===R.start+1){R.start=R.index+1,R.consumed="",R.output="",o.pop(),S=s;continue}V({type:"slash",value:A,output:m});continue}if(A==="."){if(R.braces>0&&S.type==="dot"){S.value==="."&&(S.output=f);let B=ie[ie.length-1];S.type="dots",S.output+=A,S.value+=A,B.dots=!0;continue}if(R.braces+R.parens===0&&S.type!=="bos"&&S.type!=="slash"){V({type:"text",value:A,output:f});continue}V({type:"dot",value:A,output:f});continue}if(A==="?"){if(!(S&&S.value==="(")&&r.noextglob!==!0&&v()==="("&&v(2)!=="?"){Ke("qmark",A);continue}if(S&&S.type==="paren"){let d=v(),w=A;if(d==="<"&&!ut.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(S.value==="("&&!/[!=<:]/.test(d)||d==="<"&&!/<([!=]|\w+>)/.test($()))&&(w=`\\${A}`),V({type:"text",value:A,output:w});continue}if(r.dot!==!0&&(S.type==="slash"||S.type==="bos")){V({type:"qmark",value:A,output:U});continue}V({type:"qmark",value:A,output:N});continue}if(A==="!"){if(r.noextglob!==!0&&v()==="("&&(v(2)!=="?"||!/[!=<:]/.test(v(3)))){Ke("negate",A);continue}if(r.nonegate!==!0&&R.index===0){Re();continue}}if(A==="+"){if(r.noextglob!==!0&&v()==="("&&v(2)!=="?"){Ke("plus",A);continue}if(S&&S.value==="("||r.regex===!1){V({type:"plus",value:A,output:h});continue}if(S&&(S.type==="bracket"||S.type==="paren"||S.type==="brace")||R.parens>0){V({type:"plus",value:A});continue}V({type:"plus",value:h});continue}if(A==="@"){if(r.noextglob!==!0&&v()==="("&&v(2)!=="?"){V({type:"at",extglob:!0,value:A,output:""});continue}V({type:"text",value:A});continue}if(A!=="*"){(A==="$"||A==="^")&&(A=`\\${A}`);let B=zT.exec($());B&&(A+=B[0],R.index+=B[0].length),V({type:"text",value:A});continue}if(S&&(S.type==="globstar"||S.star===!0)){S.type="star",S.star=!0,S.value+=A,S.output=be,R.backtrack=!0,R.globstar=!0,q(A);continue}let I=$();if(r.noextglob!==!0&&/^\([^?]/.test(I)){Ke("star",A);continue}if(S.type==="star"){if(r.noglobstar===!0){q(A);continue}let B=S.prev,d=B.prev,w=B.type==="slash"||B.type==="bos",g=d&&(d.type==="star"||d.type==="globstar");if(r.bash===!0&&(!w||I[0]&&I[0]!=="/")){V({type:"star",value:A,output:""});continue}let O=R.braces>0&&(B.type==="comma"||B.type==="brace"),j=ae.length&&(B.type==="pipe"||B.type==="paren");if(!w&&B.type!=="paren"&&!O&&!j){V({type:"star",value:A,output:""});continue}for(;I.slice(0,3)==="/**";){let H=t[R.index+4];if(H&&H!=="/")break;I=I.slice(3),q("/**",3)}if(B.type==="bos"&&F()){S.type="globstar",S.value+=A,S.output=G(r),R.output=S.output,R.globstar=!0,q(A);continue}if(B.type==="slash"&&B.prev.type!=="bos"&&!g&&F()){R.output=R.output.slice(0,-(B.output+S.output).length),B.output=`(?:${B.output}`,S.type="globstar",S.output=G(r)+(r.strictSlashes?")":"|$)"),S.value+=A,R.globstar=!0,R.output+=B.output+S.output,q(A);continue}if(B.type==="slash"&&B.prev.type!=="bos"&&I[0]==="/"){let H=I[1]!==void 0?"|$":"";R.output=R.output.slice(0,-(B.output+S.output).length),B.output=`(?:${B.output}`,S.type="globstar",S.output=`${G(r)}${m}|${m}${H})`,S.value+=A,R.output+=B.output+S.output,R.globstar=!0,q(A+k()),V({type:"slash",value:"/",output:""});continue}if(B.type==="bos"&&I[0]==="/"){S.type="globstar",S.value+=A,S.output=`(?:^|${m}|${G(r)}${m})`,R.output=S.output,R.globstar=!0,q(A+k()),V({type:"slash",value:"/",output:""});continue}R.output=R.output.slice(0,-S.output.length),S.type="globstar",S.output=G(r),S.value+=A,R.output+=S.output,R.globstar=!0,q(A);continue}let le={type:"star",value:A,output:be};if(r.bash===!0){le.output=".*?",(S.type==="bos"||S.type==="slash")&&(le.output=z+le.output),V(le);continue}if(S&&(S.type==="bracket"||S.type==="paren")&&r.regex===!0){le.output=A,V(le);continue}(R.index===R.start||S.type==="slash"||S.type==="dot")&&(S.type==="dot"?(R.output+=D,S.output+=D):r.dot===!0?(R.output+=C,S.output+=C):(R.output+=z,S.output+=z),v()!=="*"&&(R.output+=_,S.output+=_)),V(le)}for(;R.brackets>0;){if(r.strictBrackets===!0)throw new SyntaxError(Nn("closing","]"));R.output=ut.escapeLast(R.output,"["),De("brackets")}for(;R.parens>0;){if(r.strictBrackets===!0)throw new SyntaxError(Nn("closing",")"));R.output=ut.escapeLast(R.output,"("),De("parens")}for(;R.braces>0;){if(r.strictBrackets===!0)throw new SyntaxError(Nn("closing","}"));R.output=ut.escapeLast(R.output,"{"),De("braces")}if(r.strictSlashes!==!0&&(S.type==="star"||S.type==="bracket")&&V({type:"maybe_slash",value:"",output:`${m}?`}),R.backtrack===!0){R.output="";for(let I of R.tokens)R.output+=I.output!=null?I.output:I.value,I.suffix&&(R.output+=I.suffix)}return R};Kc.fastpaths=(t,e)=>{let r={...e},n=typeof r.maxLength=="number"?Math.min(_o,r.maxLength):_o,i=t.length;if(i>n)throw new SyntaxError(`Input length: ${i}, exceeds maximum allowed length: ${n}`);t=Qg[t]||t;let s=ut.isWindows(e),{DOT_LITERAL:o,SLASH_LITERAL:a,ONE_CHAR:u,DOTS_SLASH:c,NO_DOT:l,NO_DOTS:f,NO_DOTS_SLASH:h,STAR:m,START_ANCHOR:_}=yo.globChars(s),y=r.dot?f:l,p=r.dot?h:l,D=r.capture?"":"?:",C={negated:!1,prefix:""},N=r.bash===!0?".*?":m;r.capture&&(N=`(${N})`);let U=z=>z.noglobstar===!0?N:`(${D}(?:(?!${_}${z.dot?c:o}).)*?)`,M=z=>{switch(z){case"*":return`${y}${u}${N}`;case".*":return`${o}${u}${N}`;case"*.*":return`${y}${N}${o}${u}${N}`;case"*/*":return`${y}${N}${a}${u}${p}${N}`;case"**":return y+U(r);case"**/*":return`(?:${y}${U(r)}${a})?${p}${u}${N}`;case"**/*.*":return`(?:${y}${U(r)}${a})?${p}${N}${o}${u}${N}`;case"**/.*":return`(?:${y}${U(r)}${a})?${o}${u}${N}`;default:{let Q=/^(.*?)\.(\w+)$/.exec(z);if(!Q)return;let be=M(Q[1]);return be?be+o+Q[2]:void 0}}},Y=ut.removePrefix(t,C),G=M(Y);return G&&r.strictSlashes!==!0&&(G+=`${a}?`),G};ey.exports=Kc});var ny=b((c6,ry)=>{"use strict";var KT=require("path"),JT=Zg(),Jc=ty(),Xc=Qi(),XT=Zi(),ZT=t=>t&&typeof t=="object"&&!Array.isArray(t),xe=(t,e,r=!1)=>{if(Array.isArray(t)){let l=t.map(h=>xe(h,e,r));return h=>{for(let m of l){let _=m(h);if(_)return _}return!1}}let n=ZT(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!n)throw new TypeError("Expected pattern to be a non-empty string");let i=e||{},s=Xc.isWindows(e),o=n?xe.compileRe(t,e):xe.makeRe(t,e,!1,!0),a=o.state;delete o.state;let u=()=>!1;if(i.ignore){let l={...e,ignore:null,onMatch:null,onResult:null};u=xe(i.ignore,l,r)}let c=(l,f=!1)=>{let{isMatch:h,match:m,output:_}=xe.test(l,o,e,{glob:t,posix:s}),y={glob:t,state:a,regex:o,posix:s,input:l,output:_,match:m,isMatch:h};return typeof i.onResult=="function"&&i.onResult(y),h===!1?(y.isMatch=!1,f?y:!1):u(l)?(typeof i.onIgnore=="function"&&i.onIgnore(y),y.isMatch=!1,f?y:!1):(typeof i.onMatch=="function"&&i.onMatch(y),f?y:!0)};return r&&(c.state=a),c};xe.test=(t,e,r,{glob:n,posix:i}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};let s=r||{},o=s.format||(i?Xc.toPosixSlashes:null),a=t===n,u=a&&o?o(t):t;return a===!1&&(u=o?o(t):t,a=u===n),(a===!1||s.capture===!0)&&(s.matchBase===!0||s.basename===!0?a=xe.matchBase(t,e,r,i):a=e.exec(u)),{isMatch:!!a,match:a,output:u}};xe.matchBase=(t,e,r,n=Xc.isWindows(r))=>(e instanceof RegExp?e:xe.makeRe(e,r)).test(KT.basename(t));xe.isMatch=(t,e,r)=>xe(e,r)(t);xe.parse=(t,e)=>Array.isArray(t)?t.map(r=>xe.parse(r,e)):Jc(t,{...e,fastpaths:!1});xe.scan=(t,e)=>JT(t,e);xe.compileRe=(t,e,r=!1,n=!1)=>{if(r===!0)return t.output;let i=e||{},s=i.contains?"":"^",o=i.contains?"":"$",a=`${s}(?:${t.output})${o}`;t&&t.negated===!0&&(a=`^(?!${a}).*$`);let u=xe.toRegex(a,e);return n===!0&&(u.state=t),u};xe.makeRe=(t,e={},r=!1,n=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let i={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(i.output=Jc.fastpaths(t,e)),i.output||(i=Jc(t,e)),xe.compileRe(i,e,r,n)};xe.toRegex=(t,e)=>{try{let r=e||{};return new RegExp(t,r.flags||(r.nocase?"i":""))}catch(r){if(e&&e.debug===!0)throw r;return/$^/}};xe.constants=XT;ry.exports=xe});var sy=b((l6,iy)=>{"use strict";iy.exports=ny()});var ly=b((f6,cy)=>{"use strict";var ay=require("util"),uy=qg(),Mt=sy(),Zc=Qi(),oy=t=>t===""||t==="./",ye=(t,e,r)=>{e=[].concat(e),t=[].concat(t);let n=new Set,i=new Set,s=new Set,o=0,a=l=>{s.add(l.output),r&&r.onResult&&r.onResult(l)};for(let l=0;l<e.length;l++){let f=Mt(String(e[l]),{...r,onResult:a},!0),h=f.state.negated||f.state.negatedExtglob;h&&o++;for(let m of t){let _=f(m,!0);(h?!_.isMatch:_.isMatch)&&(h?n.add(_.output):(n.delete(_.output),i.add(_.output)))}}let c=(o===e.length?[...s]:[...i]).filter(l=>!n.has(l));if(r&&c.length===0){if(r.failglob===!0)throw new Error(`No matches found for "${e.join(", ")}"`);if(r.nonull===!0||r.nullglob===!0)return r.unescape?e.map(l=>l.replace(/\\/g,"")):e}return c};ye.match=ye;ye.matcher=(t,e)=>Mt(t,e);ye.isMatch=(t,e,r)=>Mt(e,r)(t);ye.any=ye.isMatch;ye.not=(t,e,r={})=>{e=[].concat(e).map(String);let n=new Set,i=[],s=a=>{r.onResult&&r.onResult(a),i.push(a.output)},o=new Set(ye(t,e,{...r,onResult:s}));for(let a of i)o.has(a)||n.add(a);return[...n]};ye.contains=(t,e,r)=>{if(typeof t!="string")throw new TypeError(`Expected a string: "${ay.inspect(t)}"`);if(Array.isArray(e))return e.some(n=>ye.contains(t,n,r));if(typeof e=="string"){if(oy(t)||oy(e))return!1;if(t.includes(e)||t.startsWith("./")&&t.slice(2).includes(e))return!0}return ye.isMatch(t,e,{...r,contains:!0})};ye.matchKeys=(t,e,r)=>{if(!Zc.isObject(t))throw new TypeError("Expected the first argument to be an object");let n=ye(Object.keys(t),e,r),i={};for(let s of n)i[s]=t[s];return i};ye.some=(t,e,r)=>{let n=[].concat(t);for(let i of[].concat(e)){let s=Mt(String(i),r);if(n.some(o=>s(o)))return!0}return!1};ye.every=(t,e,r)=>{let n=[].concat(t);for(let i of[].concat(e)){let s=Mt(String(i),r);if(!n.every(o=>s(o)))return!1}return!0};ye.all=(t,e,r)=>{if(typeof t!="string")throw new TypeError(`Expected a string: "${ay.inspect(t)}"`);return[].concat(e).every(n=>Mt(n,r)(t))};ye.capture=(t,e,r)=>{let n=Zc.isWindows(r),s=Mt.makeRe(String(t),{...r,capture:!0}).exec(n?Zc.toPosixSlashes(e):e);if(s)return s.slice(1).map(o=>o===void 0?"":o)};ye.makeRe=(...t)=>Mt.makeRe(...t);ye.scan=(...t)=>Mt.scan(...t);ye.parse=(t,e)=>{let r=[];for(let n of[].concat(t||[]))for(let i of uy(String(n),e))r.push(Mt.parse(i,e));return r};ye.braces=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");return e&&e.nobrace===!0||!/\{.*\}/.test(t)?[t]:uy(t,e)};ye.braceExpand=(t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");return ye.braces(t,{...e,expand:!0})};cy.exports=ye});var _y=b(K=>{"use strict";Object.defineProperty(K,"__esModule",{value:!0});K.matchAny=K.convertPatternsToRe=K.makeRe=K.getPatternParts=K.expandBraceExpansion=K.expandPatternsWithBraceExpansion=K.isAffectDepthOfReadingPattern=K.endsWithSlashGlobStar=K.hasGlobStar=K.getBaseDirectory=K.isPatternRelatedToParentDirectory=K.getPatternsOutsideCurrentDirectory=K.getPatternsInsideCurrentDirectory=K.getPositivePatterns=K.getNegativePatterns=K.isPositivePattern=K.isNegativePattern=K.convertToNegativePattern=K.convertToPositivePattern=K.isDynamicPattern=K.isStaticPattern=void 0;var QT=require("path"),eO=rg(),Qc=ly(),fy="**",tO="\\",rO=/[*?]|^!/,nO=/\[[^[]*]/,iO=/(?:^|[^!*+?@])\([^(]*\|[^|]*\)/,sO=/[!*+?@]\([^(]*\)/,oO=/,|\.\./;function hy(t,e={}){return!dy(t,e)}K.isStaticPattern=hy;function dy(t,e={}){return t===""?!1:!!(e.caseSensitiveMatch===!1||t.includes(tO)||rO.test(t)||nO.test(t)||iO.test(t)||e.extglob!==!1&&sO.test(t)||e.braceExpansion!==!1&&aO(t))}K.isDynamicPattern=dy;function aO(t){let e=t.indexOf("{");if(e===-1)return!1;let r=t.indexOf("}",e+1);if(r===-1)return!1;let n=t.slice(e,r);return oO.test(n)}function uO(t){return Eo(t)?t.slice(1):t}K.convertToPositivePattern=uO;function cO(t){return"!"+t}K.convertToNegativePattern=cO;function Eo(t){return t.startsWith("!")&&t[1]!=="("}K.isNegativePattern=Eo;function py(t){return!Eo(t)}K.isPositivePattern=py;function lO(t){return t.filter(Eo)}K.getNegativePatterns=lO;function fO(t){return t.filter(py)}K.getPositivePatterns=fO;function hO(t){return t.filter(e=>!el(e))}K.getPatternsInsideCurrentDirectory=hO;function dO(t){return t.filter(el)}K.getPatternsOutsideCurrentDirectory=dO;function el(t){return t.startsWith("..")||t.startsWith("./..")}K.isPatternRelatedToParentDirectory=el;function pO(t){return eO(t,{flipBackslashes:!1})}K.getBaseDirectory=pO;function mO(t){return t.includes(fy)}K.hasGlobStar=mO;function my(t){return t.endsWith("/"+fy)}K.endsWithSlashGlobStar=my;function gO(t){let e=QT.basename(t);return my(t)||hy(e)}K.isAffectDepthOfReadingPattern=gO;function yO(t){return t.reduce((e,r)=>e.concat(gy(r)),[])}K.expandPatternsWithBraceExpansion=yO;function gy(t){return Qc.braces(t,{expand:!0,nodupes:!0})}K.expandBraceExpansion=gy;function _O(t,e){let{parts:r}=Qc.scan(t,Object.assign(Object.assign({},e),{parts:!0}));return r.length===0&&(r=[t]),r[0].startsWith("/")&&(r[0]=r[0].slice(1),r.unshift("")),r}K.getPatternParts=_O;function yy(t,e){return Qc.makeRe(t,e)}K.makeRe=yy;function EO(t,e){return t.map(r=>yy(r,e))}K.convertPatternsToRe=EO;function bO(t,e){return e.some(r=>r.test(t))}K.matchAny=bO});var wy=b((d6,Dy)=>{"use strict";var DO=require("stream"),Ey=DO.PassThrough,wO=Array.prototype.slice;Dy.exports=AO;function AO(){let t=[],e=wO.call(arguments),r=!1,n=e[e.length-1];n&&!Array.isArray(n)&&n.pipe==null?e.pop():n={};let i=n.end!==!1,s=n.pipeError===!0;n.objectMode==null&&(n.objectMode=!0),n.highWaterMark==null&&(n.highWaterMark=64*1024);let o=Ey(n);function a(){for(let l=0,f=arguments.length;l<f;l++)t.push(by(arguments[l],n));return u(),this}function u(){if(r)return;r=!0;let l=t.shift();if(!l){process.nextTick(c);return}Array.isArray(l)||(l=[l]);let f=l.length+1;function h(){--f>0||(r=!1,u())}function m(_){function y(){_.removeListener("merge2UnpipeEnd",y),_.removeListener("end",y),s&&_.removeListener("error",p),h()}function p(D){o.emit("error",D)}if(_._readableState.endEmitted)return h();_.on("merge2UnpipeEnd",y),_.on("end",y),s&&_.on("error",p),_.pipe(o,{end:!1}),_.resume()}for(let _=0;_<l.length;_++)m(l[_]);h()}function c(){r=!1,o.emit("queueDrain"),i&&o.end()}return o.setMaxListeners(0),o.add=a,o.on("unpipe",function(l){l.emit("merge2UnpipeEnd")}),e.length&&a.apply(null,e),o}function by(t,e){if(Array.isArray(t))for(let r=0,n=t.length;r<n;r++)t[r]=by(t[r],e);else{if(!t._readableState&&t.pipe&&(t=t.pipe(Ey(e))),!t._readableState||!t.pause||!t.pipe)throw new Error("Only readable stream can be merged.");t.pause()}return t}});var xy=b(bo=>{"use strict";Object.defineProperty(bo,"__esModule",{value:!0});bo.merge=void 0;var xO=wy();function SO(t){let e=xO(t);return t.forEach(r=>{r.once("error",n=>e.emit("error",n))}),e.once("close",()=>Ay(t)),e.once("end",()=>Ay(t)),e}bo.merge=SO;function Ay(t){t.forEach(e=>e.emit("close"))}});var Sy=b(Mn=>{"use strict";Object.defineProperty(Mn,"__esModule",{value:!0});Mn.isEmpty=Mn.isString=void 0;function CO(t){return typeof t=="string"}Mn.isString=CO;function vO(t){return t===""}Mn.isEmpty=vO});var ir=b(je=>{"use strict";Object.defineProperty(je,"__esModule",{value:!0});je.string=je.stream=je.pattern=je.path=je.fs=je.errno=je.array=void 0;var RO=z0();je.array=RO;var FO=V0();je.errno=FO;var TO=Y0();je.fs=TO;var OO=K0();je.path=OO;var kO=_y();je.pattern=kO;var PO=xy();je.stream=PO;var BO=Sy();je.string=BO});var Ry=b($e=>{"use strict";Object.defineProperty($e,"__esModule",{value:!0});$e.convertPatternGroupToTask=$e.convertPatternGroupsToTasks=$e.groupPatternsByBaseDirectory=$e.getNegativePatternsAsPositive=$e.getPositivePatterns=$e.convertPatternsToTasks=$e.generate=void 0;var sr=ir();function IO(t,e){let r=Cy(t),n=vy(t,e.ignore),i=r.filter(u=>sr.pattern.isStaticPattern(u,e)),s=r.filter(u=>sr.pattern.isDynamicPattern(u,e)),o=tl(i,n,!1),a=tl(s,n,!0);return o.concat(a)}$e.generate=IO;function tl(t,e,r){let n=[],i=sr.pattern.getPatternsOutsideCurrentDirectory(t),s=sr.pattern.getPatternsInsideCurrentDirectory(t),o=rl(i),a=rl(s);return n.push(...nl(o,e,r)),"."in a?n.push(il(".",s,e,r)):n.push(...nl(a,e,r)),n}$e.convertPatternsToTasks=tl;function Cy(t){return sr.pattern.getPositivePatterns(t)}$e.getPositivePatterns=Cy;function vy(t,e){return sr.pattern.getNegativePatterns(t).concat(e).map(sr.pattern.convertToPositivePattern)}$e.getNegativePatternsAsPositive=vy;function rl(t){let e={};return t.reduce((r,n)=>{let i=sr.pattern.getBaseDirectory(n);return i in r?r[i].push(n):r[i]=[n],r},e)}$e.groupPatternsByBaseDirectory=rl;function nl(t,e,r){return Object.keys(t).map(n=>il(n,t[n],e,r))}$e.convertPatternGroupsToTasks=nl;function il(t,e,r,n){return{dynamic:n,positive:e,negative:r,base:t,patterns:[].concat(e,r.map(sr.pattern.convertToNegativePattern))}}$e.convertPatternGroupToTask=il});var Ty=b(qn=>{"use strict";Object.defineProperty(qn,"__esModule",{value:!0});qn.removeDuplicateSlashes=qn.transform=void 0;var LO=/(?!^)\/{2,}/g;function NO(t){return t.map(e=>Fy(e))}qn.transform=NO;function Fy(t){return t.replace(LO,"/")}qn.removeDuplicateSlashes=Fy});var ky=b(Do=>{"use strict";Object.defineProperty(Do,"__esModule",{value:!0});Do.read=void 0;function MO(t,e,r){e.fs.lstat(t,(n,i)=>{if(n!==null){Oy(r,n);return}if(!i.isSymbolicLink()||!e.followSymbolicLink){sl(r,i);return}e.fs.stat(t,(s,o)=>{if(s!==null){if(e.throwErrorOnBrokenSymbolicLink){Oy(r,s);return}sl(r,i);return}e.markSymbolicLink&&(o.isSymbolicLink=()=>!0),sl(r,o)})})}Do.read=MO;function Oy(t,e){t(e)}function sl(t,e){t(null,e)}});var Py=b(wo=>{"use strict";Object.defineProperty(wo,"__esModule",{value:!0});wo.read=void 0;function qO(t,e){let r=e.fs.lstatSync(t);if(!r.isSymbolicLink()||!e.followSymbolicLink)return r;try{let n=e.fs.statSync(t);return e.markSymbolicLink&&(n.isSymbolicLink=()=>!0),n}catch(n){if(!e.throwErrorOnBrokenSymbolicLink)return r;throw n}}wo.read=qO});var By=b(Rr=>{"use strict";Object.defineProperty(Rr,"__esModule",{value:!0});Rr.createFileSystemAdapter=Rr.FILE_SYSTEM_ADAPTER=void 0;var Ao=require("fs");Rr.FILE_SYSTEM_ADAPTER={lstat:Ao.lstat,stat:Ao.stat,lstatSync:Ao.lstatSync,statSync:Ao.statSync};function HO(t){return t===void 0?Rr.FILE_SYSTEM_ADAPTER:Object.assign(Object.assign({},Rr.FILE_SYSTEM_ADAPTER),t)}Rr.createFileSystemAdapter=HO});var Iy=b(al=>{"use strict";Object.defineProperty(al,"__esModule",{value:!0});var jO=By(),ol=class{constructor(e={}){this._options=e,this.followSymbolicLink=this._getValue(this._options.followSymbolicLink,!0),this.fs=jO.createFileSystemAdapter(this._options.fs),this.markSymbolicLink=this._getValue(this._options.markSymbolicLink,!1),this.throwErrorOnBrokenSymbolicLink=this._getValue(this._options.throwErrorOnBrokenSymbolicLink,!0)}_getValue(e,r){return e!=null?e:r}};al.default=ol});var an=b(Fr=>{"use strict";Object.defineProperty(Fr,"__esModule",{value:!0});Fr.statSync=Fr.stat=Fr.Settings=void 0;var Ly=ky(),$O=Py(),ul=Iy();Fr.Settings=ul.default;function UO(t,e,r){if(typeof e=="function"){Ly.read(t,cl(),e);return}Ly.read(t,cl(e),r)}Fr.stat=UO;function GO(t,e){let r=cl(e);return $O.read(t,r)}Fr.statSync=GO;function cl(t={}){return t instanceof ul.default?t:new ul.default(t)}});var qy=b((x6,My)=>{var Ny;My.exports=typeof queueMicrotask=="function"?queueMicrotask.bind(typeof window!="undefined"?window:global):t=>(Ny||(Ny=Promise.resolve())).then(t).catch(e=>setTimeout(()=>{throw e},0))});var jy=b((S6,Hy)=>{Hy.exports=zO;var WO=qy();function zO(t,e){let r,n,i,s=!0;Array.isArray(t)?(r=[],n=t.length):(i=Object.keys(t),r={},n=i.length);function o(u){function c(){e&&e(u,r),e=null}s?WO(c):c()}function a(u,c,l){r[u]=l,(--n===0||c)&&o(c)}n?i?i.forEach(function(u){t[u](function(c,l){a(u,c,l)})}):t.forEach(function(u,c){u(function(l,f){a(c,l,f)})}):o(null),s=!1}});var ll=b(So=>{"use strict";Object.defineProperty(So,"__esModule",{value:!0});So.IS_SUPPORT_READDIR_WITH_FILE_TYPES=void 0;var xo=process.versions.node.split(".");if(xo[0]===void 0||xo[1]===void 0)throw new Error(`Unexpected behavior. The 'process.versions.node' variable has invalid value: ${process.versions.node}`);var $y=Number.parseInt(xo[0],10),VO=Number.parseInt(xo[1],10),Uy=10,YO=10,KO=$y>Uy,JO=$y===Uy&&VO>=YO;So.IS_SUPPORT_READDIR_WITH_FILE_TYPES=KO||JO});var Gy=b(Co=>{"use strict";Object.defineProperty(Co,"__esModule",{value:!0});Co.createDirentFromStats=void 0;var fl=class{constructor(e,r){this.name=e,this.isBlockDevice=r.isBlockDevice.bind(r),this.isCharacterDevice=r.isCharacterDevice.bind(r),this.isDirectory=r.isDirectory.bind(r),this.isFIFO=r.isFIFO.bind(r),this.isFile=r.isFile.bind(r),this.isSocket=r.isSocket.bind(r),this.isSymbolicLink=r.isSymbolicLink.bind(r)}};function XO(t,e){return new fl(t,e)}Co.createDirentFromStats=XO});var hl=b(vo=>{"use strict";Object.defineProperty(vo,"__esModule",{value:!0});vo.fs=void 0;var ZO=Gy();vo.fs=ZO});var dl=b(Ro=>{"use strict";Object.defineProperty(Ro,"__esModule",{value:!0});Ro.joinPathSegments=void 0;function QO(t,e,r){return t.endsWith(r)?t+e:t+r+e}Ro.joinPathSegments=QO});var Jy=b(Tr=>{"use strict";Object.defineProperty(Tr,"__esModule",{value:!0});Tr.readdir=Tr.readdirWithFileTypes=Tr.read=void 0;var ek=an(),Wy=jy(),tk=ll(),zy=hl(),Vy=dl();function rk(t,e,r){if(!e.stats&&tk.IS_SUPPORT_READDIR_WITH_FILE_TYPES){Yy(t,e,r);return}Ky(t,e,r)}Tr.read=rk;function Yy(t,e,r){e.fs.readdir(t,{withFileTypes:!0},(n,i)=>{if(n!==null){Fo(r,n);return}let s=i.map(a=>({dirent:a,name:a.name,path:Vy.joinPathSegments(t,a.name,e.pathSegmentSeparator)}));if(!e.followSymbolicLinks){pl(r,s);return}let o=s.map(a=>nk(a,e));Wy(o,(a,u)=>{if(a!==null){Fo(r,a);return}pl(r,u)})})}Tr.readdirWithFileTypes=Yy;function nk(t,e){return r=>{if(!t.dirent.isSymbolicLink()){r(null,t);return}e.fs.stat(t.path,(n,i)=>{if(n!==null){if(e.throwErrorOnBrokenSymbolicLink){r(n);return}r(null,t);return}t.dirent=zy.fs.createDirentFromStats(t.name,i),r(null,t)})}}function Ky(t,e,r){e.fs.readdir(t,(n,i)=>{if(n!==null){Fo(r,n);return}let s=i.map(o=>{let a=Vy.joinPathSegments(t,o,e.pathSegmentSeparator);return u=>{ek.stat(a,e.fsStatSettings,(c,l)=>{if(c!==null){u(c);return}let f={name:o,path:a,dirent:zy.fs.createDirentFromStats(o,l)};e.stats&&(f.stats=l),u(null,f)})}});Wy(s,(o,a)=>{if(o!==null){Fo(r,o);return}pl(r,a)})})}Tr.readdir=Ky;function Fo(t,e){t(e)}function pl(t,e){t(null,e)}});var t_=b(Or=>{"use strict";Object.defineProperty(Or,"__esModule",{value:!0});Or.readdir=Or.readdirWithFileTypes=Or.read=void 0;var ik=an(),sk=ll(),Xy=hl(),Zy=dl();function ok(t,e){return!e.stats&&sk.IS_SUPPORT_READDIR_WITH_FILE_TYPES?Qy(t,e):e_(t,e)}Or.read=ok;function Qy(t,e){return e.fs.readdirSync(t,{withFileTypes:!0}).map(n=>{let i={dirent:n,name:n.name,path:Zy.joinPathSegments(t,n.name,e.pathSegmentSeparator)};if(i.dirent.isSymbolicLink()&&e.followSymbolicLinks)try{let s=e.fs.statSync(i.path);i.dirent=Xy.fs.createDirentFromStats(i.name,s)}catch(s){if(e.throwErrorOnBrokenSymbolicLink)throw s}return i})}Or.readdirWithFileTypes=Qy;function e_(t,e){return e.fs.readdirSync(t).map(n=>{let i=Zy.joinPathSegments(t,n,e.pathSegmentSeparator),s=ik.statSync(i,e.fsStatSettings),o={name:n,path:i,dirent:Xy.fs.createDirentFromStats(n,s)};return e.stats&&(o.stats=s),o})}Or.readdir=e_});var r_=b(kr=>{"use strict";Object.defineProperty(kr,"__esModule",{value:!0});kr.createFileSystemAdapter=kr.FILE_SYSTEM_ADAPTER=void 0;var Hn=require("fs");kr.FILE_SYSTEM_ADAPTER={lstat:Hn.lstat,stat:Hn.stat,lstatSync:Hn.lstatSync,statSync:Hn.statSync,readdir:Hn.readdir,readdirSync:Hn.readdirSync};function ak(t){return t===void 0?kr.FILE_SYSTEM_ADAPTER:Object.assign(Object.assign({},kr.FILE_SYSTEM_ADAPTER),t)}kr.createFileSystemAdapter=ak});var n_=b(gl=>{"use strict";Object.defineProperty(gl,"__esModule",{value:!0});var uk=require("path"),ck=an(),lk=r_(),ml=class{constructor(e={}){this._options=e,this.followSymbolicLinks=this._getValue(this._options.followSymbolicLinks,!1),this.fs=lk.createFileSystemAdapter(this._options.fs),this.pathSegmentSeparator=this._getValue(this._options.pathSegmentSeparator,uk.sep),this.stats=this._getValue(this._options.stats,!1),this.throwErrorOnBrokenSymbolicLink=this._getValue(this._options.throwErrorOnBrokenSymbolicLink,!0),this.fsStatSettings=new ck.Settings({followSymbolicLink:this.followSymbolicLinks,fs:this.fs,throwErrorOnBrokenSymbolicLink:this.throwErrorOnBrokenSymbolicLink})}_getValue(e,r){return e!=null?e:r}};gl.default=ml});var To=b(Pr=>{"use strict";Object.defineProperty(Pr,"__esModule",{value:!0});Pr.Settings=Pr.scandirSync=Pr.scandir=void 0;var i_=Jy(),fk=t_(),yl=n_();Pr.Settings=yl.default;function hk(t,e,r){if(typeof e=="function"){i_.read(t,_l(),e);return}i_.read(t,_l(e),r)}Pr.scandir=hk;function dk(t,e){let r=_l(e);return fk.read(t,r)}Pr.scandirSync=dk;function _l(t={}){return t instanceof yl.default?t:new yl.default(t)}});var o_=b((I6,s_)=>{"use strict";function pk(t){var e=new t,r=e;function n(){var s=e;return s.next?e=s.next:(e=new t,r=e),s.next=null,s}function i(s){r.next=s,r=s}return{get:n,release:i}}s_.exports=pk});var u_=b((L6,El)=>{"use strict";var mk=o_();function a_(t,e,r){if(typeof t=="function"&&(r=e,e=t,t=null),r<1)throw new Error("fastqueue concurrency must be greater than 1");var n=mk(gk),i=null,s=null,o=0,a=null,u={push:y,drain:mt,saturated:mt,pause:l,paused:!1,concurrency:r,running:c,resume:m,idle:_,length:f,getQueue:h,unshift:p,empty:mt,kill:C,killAndDrain:N,error:U};return u;function c(){return o}function l(){u.paused=!0}function f(){for(var M=i,Y=0;M;)M=M.next,Y++;return Y}function h(){for(var M=i,Y=[];M;)Y.push(M.value),M=M.next;return Y}function m(){if(u.paused){u.paused=!1;for(var M=0;M<u.concurrency;M++)o++,D()}}function _(){return o===0&&u.length()===0}function y(M,Y){var G=n.get();G.context=t,G.release=D,G.value=M,G.callback=Y||mt,G.errorHandler=a,o===u.concurrency||u.paused?s?(s.next=G,s=G):(i=G,s=G,u.saturated()):(o++,e.call(t,G.value,G.worked))}function p(M,Y){var G=n.get();G.context=t,G.release=D,G.value=M,G.callback=Y||mt,o===u.concurrency||u.paused?i?(G.next=i,i=G):(i=G,s=G,u.saturated()):(o++,e.call(t,G.value,G.worked))}function D(M){M&&n.release(M);var Y=i;Y?u.paused?o--:(s===i&&(s=null),i=Y.next,Y.next=null,e.call(t,Y.value,Y.worked),s===null&&u.empty()):--o===0&&u.drain()}function C(){i=null,s=null,u.drain=mt}function N(){i=null,s=null,u.drain(),u.drain=mt}function U(M){a=M}}function mt(){}function gk(){this.value=null,this.callback=mt,this.next=null,this.release=mt,this.context=null,this.errorHandler=null;var t=this;this.worked=function(r,n){var i=t.callback,s=t.errorHandler,o=t.value;t.value=null,t.callback=mt,t.errorHandler&&s(r,o),i.call(t.context,r,n),t.release(t)}}function yk(t,e,r){typeof t=="function"&&(r=e,e=t,t=null);function n(l,f){e.call(this,l).then(function(h){f(null,h)},f)}var i=a_(t,n,r),s=i.push,o=i.unshift;return i.push=a,i.unshift=u,i.drained=c,i;function a(l){var f=new Promise(function(h,m){s(l,function(_,y){if(_){m(_);return}h(y)})});return f.catch(mt),f}function u(l){var f=new Promise(function(h,m){o(l,function(_,y){if(_){m(_);return}h(y)})});return f.catch(mt),f}function c(){var l=i.drain,f=new Promise(function(h){i.drain=function(){l(),h()}});return f}}El.exports=a_;El.exports.promise=yk});var Oo=b(qt=>{"use strict";Object.defineProperty(qt,"__esModule",{value:!0});qt.joinPathSegments=qt.replacePathSegmentSeparator=qt.isAppliedFilter=qt.isFatalError=void 0;function _k(t,e){return t.errorFilter===null?!0:!t.errorFilter(e)}qt.isFatalError=_k;function Ek(t,e){return t===null||t(e)}qt.isAppliedFilter=Ek;function bk(t,e){return t.split(/[/\\]/).join(e)}qt.replacePathSegmentSeparator=bk;function Dk(t,e,r){return t===""?e:t.endsWith(r)?t+e:t+r+e}qt.joinPathSegments=Dk});var wl=b(Dl=>{"use strict";Object.defineProperty(Dl,"__esModule",{value:!0});var wk=Oo(),bl=class{constructor(e,r){this._root=e,this._settings=r,this._root=wk.replacePathSegmentSeparator(e,r.pathSegmentSeparator)}};Dl.default=bl});var Sl=b(xl=>{"use strict";Object.defineProperty(xl,"__esModule",{value:!0});var Ak=require("events"),xk=To(),Sk=u_(),ko=Oo(),Ck=wl(),Al=class extends Ck.default{constructor(e,r){super(e,r),this._settings=r,this._scandir=xk.scandir,this._emitter=new Ak.EventEmitter,this._queue=Sk(this._worker.bind(this),this._settings.concurrency),this._isFatalError=!1,this._isDestroyed=!1,this._queue.drain=()=>{this._isFatalError||this._emitter.emit("end")}}read(){return this._isFatalError=!1,this._isDestroyed=!1,setImmediate(()=>{this._pushToQueue(this._root,this._settings.basePath)}),this._emitter}get isDestroyed(){return this._isDestroyed}destroy(){if(this._isDestroyed)throw new Error("The reader is already destroyed");this._isDestroyed=!0,this._queue.killAndDrain()}onEntry(e){this._emitter.on("entry",e)}onError(e){this._emitter.once("error",e)}onEnd(e){this._emitter.once("end",e)}_pushToQueue(e,r){let n={directory:e,base:r};this._queue.push(n,i=>{i!==null&&this._handleError(i)})}_worker(e,r){this._scandir(e.directory,this._settings.fsScandirSettings,(n,i)=>{if(n!==null){r(n,void 0);return}for(let s of i)this._handleEntry(s,e.base);r(null,void 0)})}_handleError(e){this._isDestroyed||!ko.isFatalError(this._settings,e)||(this._isFatalError=!0,this._isDestroyed=!0,this._emitter.emit("error",e))}_handleEntry(e,r){if(this._isDestroyed||this._isFatalError)return;let n=e.path;r!==void 0&&(e.path=ko.joinPathSegments(r,e.name,this._settings.pathSegmentSeparator)),ko.isAppliedFilter(this._settings.entryFilter,e)&&this._emitEntry(e),e.dirent.isDirectory()&&ko.isAppliedFilter(this._settings.deepFilter,e)&&this._pushToQueue(n,r===void 0?void 0:e.path)}_emitEntry(e){this._emitter.emit("entry",e)}};xl.default=Al});var c_=b(vl=>{"use strict";Object.defineProperty(vl,"__esModule",{value:!0});var vk=Sl(),Cl=class{constructor(e,r){this._root=e,this._settings=r,this._reader=new vk.default(this._root,this._settings),this._storage=[]}read(e){this._reader.onError(r=>{Rk(e,r)}),this._reader.onEntry(r=>{this._storage.push(r)}),this._reader.onEnd(()=>{Fk(e,this._storage)}),this._reader.read()}};vl.default=Cl;function Rk(t,e){t(e)}function Fk(t,e){t(null,e)}});var l_=b(Fl=>{"use strict";Object.defineProperty(Fl,"__esModule",{value:!0});var Tk=require("stream"),Ok=Sl(),Rl=class{constructor(e,r){this._root=e,this._settings=r,this._reader=new Ok.default(this._root,this._settings),this._stream=new Tk.Readable({objectMode:!0,read:()=>{},destroy:()=>{this._reader.isDestroyed||this._reader.destroy()}})}read(){return this._reader.onError(e=>{this._stream.emit("error",e)}),this._reader.onEntry(e=>{this._stream.push(e)}),this._reader.onEnd(()=>{this._stream.push(null)}),this._reader.read(),this._stream}};Fl.default=Rl});var f_=b(Ol=>{"use strict";Object.defineProperty(Ol,"__esModule",{value:!0});var kk=To(),Po=Oo(),Pk=wl(),Tl=class extends Pk.default{constructor(){super(...arguments),this._scandir=kk.scandirSync,this._storage=[],this._queue=new Set}read(){return this._pushToQueue(this._root,this._settings.basePath),this._handleQueue(),this._storage}_pushToQueue(e,r){this._queue.add({directory:e,base:r})}_handleQueue(){for(let e of this._queue.values())this._handleDirectory(e.directory,e.base)}_handleDirectory(e,r){try{let n=this._scandir(e,this._settings.fsScandirSettings);for(let i of n)this._handleEntry(i,r)}catch(n){this._handleError(n)}}_handleError(e){if(Po.isFatalError(this._settings,e))throw e}_handleEntry(e,r){let n=e.path;r!==void 0&&(e.path=Po.joinPathSegments(r,e.name,this._settings.pathSegmentSeparator)),Po.isAppliedFilter(this._settings.entryFilter,e)&&this._pushToStorage(e),e.dirent.isDirectory()&&Po.isAppliedFilter(this._settings.deepFilter,e)&&this._pushToQueue(n,r===void 0?void 0:e.path)}_pushToStorage(e){this._storage.push(e)}};Ol.default=Tl});var h_=b(Pl=>{"use strict";Object.defineProperty(Pl,"__esModule",{value:!0});var Bk=f_(),kl=class{constructor(e,r){this._root=e,this._settings=r,this._reader=new Bk.default(this._root,this._settings)}read(){return this._reader.read()}};Pl.default=kl});var d_=b(Il=>{"use strict";Object.defineProperty(Il,"__esModule",{value:!0});var Ik=require("path"),Lk=To(),Bl=class{constructor(e={}){this._options=e,this.basePath=this._getValue(this._options.basePath,void 0),this.concurrency=this._getValue(this._options.concurrency,Number.POSITIVE_INFINITY),this.deepFilter=this._getValue(this._options.deepFilter,null),this.entryFilter=this._getValue(this._options.entryFilter,null),this.errorFilter=this._getValue(this._options.errorFilter,null),this.pathSegmentSeparator=this._getValue(this._options.pathSegmentSeparator,Ik.sep),this.fsScandirSettings=new Lk.Settings({followSymbolicLinks:this._options.followSymbolicLinks,fs:this._options.fs,pathSegmentSeparator:this._options.pathSegmentSeparator,stats:this._options.stats,throwErrorOnBrokenSymbolicLink:this._options.throwErrorOnBrokenSymbolicLink})}_getValue(e,r){return e!=null?e:r}};Il.default=Bl});var Io=b(Ht=>{"use strict";Object.defineProperty(Ht,"__esModule",{value:!0});Ht.Settings=Ht.walkStream=Ht.walkSync=Ht.walk=void 0;var p_=c_(),Nk=l_(),Mk=h_(),Ll=d_();Ht.Settings=Ll.default;function qk(t,e,r){if(typeof e=="function"){new p_.default(t,Bo()).read(e);return}new p_.default(t,Bo(e)).read(r)}Ht.walk=qk;function Hk(t,e){let r=Bo(e);return new Mk.default(t,r).read()}Ht.walkSync=Hk;function jk(t,e){let r=Bo(e);return new Nk.default(t,r).read()}Ht.walkStream=jk;function Bo(t={}){return t instanceof Ll.default?t:new Ll.default(t)}});var Lo=b(Ml=>{"use strict";Object.defineProperty(Ml,"__esModule",{value:!0});var $k=require("path"),Uk=an(),m_=ir(),Nl=class{constructor(e){this._settings=e,this._fsStatSettings=new Uk.Settings({followSymbolicLink:this._settings.followSymbolicLinks,fs:this._settings.fs,throwErrorOnBrokenSymbolicLink:this._settings.followSymbolicLinks})}_getFullEntryPath(e){return $k.resolve(this._settings.cwd,e)}_makeEntry(e,r){let n={name:r,path:r,dirent:m_.fs.createDirentFromStats(r,e)};return this._settings.stats&&(n.stats=e),n}_isFatalError(e){return!m_.errno.isEnoentCodeError(e)&&!this._settings.suppressErrors}};Ml.default=Nl});var jl=b(Hl=>{"use strict";Object.defineProperty(Hl,"__esModule",{value:!0});var Gk=require("stream"),Wk=an(),zk=Io(),Vk=Lo(),ql=class extends Vk.default{constructor(){super(...arguments),this._walkStream=zk.walkStream,this._stat=Wk.stat}dynamic(e,r){return this._walkStream(e,r)}static(e,r){let n=e.map(this._getFullEntryPath,this),i=new Gk.PassThrough({objectMode:!0});i._write=(s,o,a)=>this._getEntry(n[s],e[s],r).then(u=>{u!==null&&r.entryFilter(u)&&i.push(u),s===n.length-1&&i.end(),a()}).catch(a);for(let s=0;s<n.length;s++)i.write(s);return i}_getEntry(e,r,n){return this._getStat(e).then(i=>this._makeEntry(i,r)).catch(i=>{if(n.errorFilter(i))return null;throw i})}_getStat(e){return new Promise((r,n)=>{this._stat(e,this._fsStatSettings,(i,s)=>i===null?r(s):n(i))})}};Hl.default=ql});var g_=b(Ul=>{"use strict";Object.defineProperty(Ul,"__esModule",{value:!0});var Yk=Io(),Kk=Lo(),Jk=jl(),$l=class extends Kk.default{constructor(){super(...arguments),this._walkAsync=Yk.walk,this._readerStream=new Jk.default(this._settings)}dynamic(e,r){return new Promise((n,i)=>{this._walkAsync(e,r,(s,o)=>{s===null?n(o):i(s)})})}async static(e,r){let n=[],i=this._readerStream.static(e,r);return new Promise((s,o)=>{i.once("error",o),i.on("data",a=>n.push(a)),i.once("end",()=>s(n))})}};Ul.default=$l});var y_=b(Wl=>{"use strict";Object.defineProperty(Wl,"__esModule",{value:!0});var jn=ir(),Gl=class{constructor(e,r,n){this._patterns=e,this._settings=r,this._micromatchOptions=n,this._storage=[],this._fillStorage()}_fillStorage(){let e=jn.pattern.expandPatternsWithBraceExpansion(this._patterns);for(let r of e){let n=this._getPatternSegments(r),i=this._splitSegmentsIntoSections(n);this._storage.push({complete:i.length<=1,pattern:r,segments:n,sections:i})}}_getPatternSegments(e){return jn.pattern.getPatternParts(e,this._micromatchOptions).map(n=>jn.pattern.isDynamicPattern(n,this._settings)?{dynamic:!0,pattern:n,patternRe:jn.pattern.makeRe(n,this._micromatchOptions)}:{dynamic:!1,pattern:n})}_splitSegmentsIntoSections(e){return jn.array.splitWhen(e,r=>r.dynamic&&jn.pattern.hasGlobStar(r.pattern))}};Wl.default=Gl});var __=b(Vl=>{"use strict";Object.defineProperty(Vl,"__esModule",{value:!0});var Xk=y_(),zl=class extends Xk.default{match(e){let r=e.split("/"),n=r.length,i=this._storage.filter(s=>!s.complete||s.segments.length>n);for(let s of i){let o=s.sections[0];if(!s.complete&&n>o.length||r.every((u,c)=>{let l=s.segments[c];return!!(l.dynamic&&l.patternRe.test(u)||!l.dynamic&&l.pattern===u)}))return!0}return!1}};Vl.default=zl});var E_=b(Kl=>{"use strict";Object.defineProperty(Kl,"__esModule",{value:!0});var No=ir(),Zk=__(),Yl=class{constructor(e,r){this._settings=e,this._micromatchOptions=r}getFilter(e,r,n){let i=this._getMatcher(r),s=this._getNegativePatternsRe(n);return o=>this._filter(e,o,i,s)}_getMatcher(e){return new Zk.default(e,this._settings,this._micromatchOptions)}_getNegativePatternsRe(e){let r=e.filter(No.pattern.isAffectDepthOfReadingPattern);return No.pattern.convertPatternsToRe(r,this._micromatchOptions)}_filter(e,r,n,i){if(this._isSkippedByDeep(e,r.path)||this._isSkippedSymbolicLink(r))return!1;let s=No.path.removeLeadingDotSegment(r.path);return this._isSkippedByPositivePatterns(s,n)?!1:this._isSkippedByNegativePatterns(s,i)}_isSkippedByDeep(e,r){return this._settings.deep===1/0?!1:this._getEntryLevel(e,r)>=this._settings.deep}_getEntryLevel(e,r){let n=r.split("/").length;if(e==="")return n;let i=e.split("/").length;return n-i}_isSkippedSymbolicLink(e){return!this._settings.followSymbolicLinks&&e.dirent.isSymbolicLink()}_isSkippedByPositivePatterns(e,r){return!this._settings.baseNameMatch&&!r.match(e)}_isSkippedByNegativePatterns(e,r){return!No.pattern.matchAny(e,r)}};Kl.default=Yl});var b_=b(Xl=>{"use strict";Object.defineProperty(Xl,"__esModule",{value:!0});var un=ir(),Jl=class{constructor(e,r){this._settings=e,this._micromatchOptions=r,this.index=new Map}getFilter(e,r){let n=un.pattern.convertPatternsToRe(e,this._micromatchOptions),i=un.pattern.convertPatternsToRe(r,this._micromatchOptions);return s=>this._filter(s,n,i)}_filter(e,r,n){if(this._settings.unique&&this._isDuplicateEntry(e)||this._onlyFileFilter(e)||this._onlyDirectoryFilter(e)||this._isSkippedByAbsoluteNegativePatterns(e.path,n))return!1;let i=this._settings.baseNameMatch?e.name:e.path,s=e.dirent.isDirectory(),o=this._isMatchToPatterns(i,r,s)&&!this._isMatchToPatterns(e.path,n,s);return this._settings.unique&&o&&this._createIndexRecord(e),o}_isDuplicateEntry(e){return this.index.has(e.path)}_createIndexRecord(e){this.index.set(e.path,void 0)}_onlyFileFilter(e){return this._settings.onlyFiles&&!e.dirent.isFile()}_onlyDirectoryFilter(e){return this._settings.onlyDirectories&&!e.dirent.isDirectory()}_isSkippedByAbsoluteNegativePatterns(e,r){if(!this._settings.absolute)return!1;let n=un.path.makeAbsolute(this._settings.cwd,e);return un.pattern.matchAny(n,r)}_isMatchToPatterns(e,r,n){let i=un.path.removeLeadingDotSegment(e),s=un.pattern.matchAny(i,r);return!s&&n?un.pattern.matchAny(i+"/",r):s}};Xl.default=Jl});var D_=b(Ql=>{"use strict";Object.defineProperty(Ql,"__esModule",{value:!0});var Qk=ir(),Zl=class{constructor(e){this._settings=e}getFilter(){return e=>this._isNonFatalError(e)}_isNonFatalError(e){return Qk.errno.isEnoentCodeError(e)||this._settings.suppressErrors}};Ql.default=Zl});var A_=b(tf=>{"use strict";Object.defineProperty(tf,"__esModule",{value:!0});var w_=ir(),ef=class{constructor(e){this._settings=e}getTransformer(){return e=>this._transform(e)}_transform(e){let r=e.path;return this._settings.absolute&&(r=w_.path.makeAbsolute(this._settings.cwd,r),r=w_.path.unixify(r)),this._settings.markDirectories&&e.dirent.isDirectory()&&(r+="/"),this._settings.objectMode?Object.assign(Object.assign({},e),{path:r}):r}};tf.default=ef});var Mo=b(nf=>{"use strict";Object.defineProperty(nf,"__esModule",{value:!0});var eP=require("path"),tP=E_(),rP=b_(),nP=D_(),iP=A_(),rf=class{constructor(e){this._settings=e,this.errorFilter=new nP.default(this._settings),this.entryFilter=new rP.default(this._settings,this._getMicromatchOptions()),this.deepFilter=new tP.default(this._settings,this._getMicromatchOptions()),this.entryTransformer=new iP.default(this._settings)}_getRootDirectory(e){return eP.resolve(this._settings.cwd,e.base)}_getReaderOptions(e){let r=e.base==="."?"":e.base;return{basePath:r,pathSegmentSeparator:"/",concurrency:this._settings.concurrency,deepFilter:this.deepFilter.getFilter(r,e.positive,e.negative),entryFilter:this.entryFilter.getFilter(e.positive,e.negative),errorFilter:this.errorFilter.getFilter(),followSymbolicLinks:this._settings.followSymbolicLinks,fs:this._settings.fs,stats:this._settings.stats,throwErrorOnBrokenSymbolicLink:this._settings.throwErrorOnBrokenSymbolicLink,transform:this.entryTransformer.getTransformer()}}_getMicromatchOptions(){return{dot:this._settings.dot,matchBase:this._settings.baseNameMatch,nobrace:!this._settings.braceExpansion,nocase:!this._settings.caseSensitiveMatch,noext:!this._settings.extglob,noglobstar:!this._settings.globstar,posix:!0,strictSlashes:!1}}};nf.default=rf});var x_=b(of=>{"use strict";Object.defineProperty(of,"__esModule",{value:!0});var sP=g_(),oP=Mo(),sf=class extends oP.default{constructor(){super(...arguments),this._reader=new sP.default(this._settings)}async read(e){let r=this._getRootDirectory(e),n=this._getReaderOptions(e);return(await this.api(r,e,n)).map(s=>n.transform(s))}api(e,r,n){return r.dynamic?this._reader.dynamic(e,n):this._reader.static(r.patterns,n)}};of.default=sf});var S_=b(uf=>{"use strict";Object.defineProperty(uf,"__esModule",{value:!0});var aP=require("stream"),uP=jl(),cP=Mo(),af=class extends cP.default{constructor(){super(...arguments),this._reader=new uP.default(this._settings)}read(e){let r=this._getRootDirectory(e),n=this._getReaderOptions(e),i=this.api(r,e,n),s=new aP.Readable({objectMode:!0,read:()=>{}});return i.once("error",o=>s.emit("error",o)).on("data",o=>s.emit("data",n.transform(o))).once("end",()=>s.emit("end")),s.once("close",()=>i.destroy()),s}api(e,r,n){return r.dynamic?this._reader.dynamic(e,n):this._reader.static(r.patterns,n)}};uf.default=af});var C_=b(lf=>{"use strict";Object.defineProperty(lf,"__esModule",{value:!0});var lP=an(),fP=Io(),hP=Lo(),cf=class extends hP.default{constructor(){super(...arguments),this._walkSync=fP.walkSync,this._statSync=lP.statSync}dynamic(e,r){return this._walkSync(e,r)}static(e,r){let n=[];for(let i of e){let s=this._getFullEntryPath(i),o=this._getEntry(s,i,r);o===null||!r.entryFilter(o)||n.push(o)}return n}_getEntry(e,r,n){try{let i=this._getStat(e);return this._makeEntry(i,r)}catch(i){if(n.errorFilter(i))return null;throw i}}_getStat(e){return this._statSync(e,this._fsStatSettings)}};lf.default=cf});var v_=b(hf=>{"use strict";Object.defineProperty(hf,"__esModule",{value:!0});var dP=C_(),pP=Mo(),ff=class extends pP.default{constructor(){super(...arguments),this._reader=new dP.default(this._settings)}read(e){let r=this._getRootDirectory(e),n=this._getReaderOptions(e);return this.api(r,e,n).map(n.transform)}api(e,r,n){return r.dynamic?this._reader.dynamic(e,n):this._reader.static(r.patterns,n)}};hf.default=ff});var R_=b(Un=>{"use strict";Object.defineProperty(Un,"__esModule",{value:!0});Un.DEFAULT_FILE_SYSTEM_ADAPTER=void 0;var $n=require("fs"),mP=require("os"),gP=Math.max(mP.cpus().length,1);Un.DEFAULT_FILE_SYSTEM_ADAPTER={lstat:$n.lstat,lstatSync:$n.lstatSync,stat:$n.stat,statSync:$n.statSync,readdir:$n.readdir,readdirSync:$n.readdirSync};var df=class{constructor(e={}){this._options=e,this.absolute=this._getValue(this._options.absolute,!1),this.baseNameMatch=this._getValue(this._options.baseNameMatch,!1),this.braceExpansion=this._getValue(this._options.braceExpansion,!0),this.caseSensitiveMatch=this._getValue(this._options.caseSensitiveMatch,!0),this.concurrency=this._getValue(this._options.concurrency,gP),this.cwd=this._getValue(this._options.cwd,process.cwd()),this.deep=this._getValue(this._options.deep,1/0),this.dot=this._getValue(this._options.dot,!1),this.extglob=this._getValue(this._options.extglob,!0),this.followSymbolicLinks=this._getValue(this._options.followSymbolicLinks,!0),this.fs=this._getFileSystemMethods(this._options.fs),this.globstar=this._getValue(this._options.globstar,!0),this.ignore=this._getValue(this._options.ignore,[]),this.markDirectories=this._getValue(this._options.markDirectories,!1),this.objectMode=this._getValue(this._options.objectMode,!1),this.onlyDirectories=this._getValue(this._options.onlyDirectories,!1),this.onlyFiles=this._getValue(this._options.onlyFiles,!0),this.stats=this._getValue(this._options.stats,!1),this.suppressErrors=this._getValue(this._options.suppressErrors,!1),this.throwErrorOnBrokenSymbolicLink=this._getValue(this._options.throwErrorOnBrokenSymbolicLink,!1),this.unique=this._getValue(this._options.unique,!0),this.onlyDirectories&&(this.onlyFiles=!1),this.stats&&(this.objectMode=!0)}_getValue(e,r){return e===void 0?r:e}_getFileSystemMethods(e={}){return Object.assign(Object.assign({},Un.DEFAULT_FILE_SYSTEM_ADAPTER),e)}};Un.default=df});var k_=b((a5,O_)=>{"use strict";var F_=Ry(),T_=Ty(),yP=x_(),_P=S_(),EP=v_(),pf=R_(),cn=ir();async function mf(t,e){Gn(t);let r=gf(t,yP.default,e),n=await Promise.all(r);return cn.array.flatten(n)}(function(t){function e(o,a){Gn(o);let u=gf(o,EP.default,a);return cn.array.flatten(u)}t.sync=e;function r(o,a){Gn(o);let u=gf(o,_P.default,a);return cn.stream.merge(u)}t.stream=r;function n(o,a){Gn(o);let u=T_.transform([].concat(o)),c=new pf.default(a);return F_.generate(u,c)}t.generateTasks=n;function i(o,a){Gn(o);let u=new pf.default(a);return cn.pattern.isDynamicPattern(o,u)}t.isDynamicPattern=i;function s(o){return Gn(o),cn.path.escape(o)}t.escapePath=s})(mf||(mf={}));function gf(t,e,r){let n=T_.transform([].concat(t)),i=new pf.default(r),s=F_.generate(n,i),o=new e(i);return s.map(o.read,o)}function Gn(t){if(![].concat(t).every(n=>cn.string.isString(n)&&!cn.string.isEmpty(n)))throw new TypeError("Patterns must be a string (non empty) or an array of strings")}O_.exports=mf});var B_=b((u5,yf)=>{"use strict";var P_=(t,e)=>{for(let r of Reflect.ownKeys(e))Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r));return t};yf.exports=P_;yf.exports.default=P_});var _f=b((c5,Ho)=>{"use strict";var bP=B_(),qo=new WeakMap,I_=(t,e={})=>{if(typeof t!="function")throw new TypeError("Expected a function");let r,n=0,i=t.displayName||t.name||"<anonymous>",s=function(...o){if(qo.set(s,++n),n===1)r=t.apply(this,o),t=null;else if(e.throw===!0)throw new Error(`Function \`${i}\` can only be called once`);return r};return bP(s,t),qo.set(s,n),s};Ho.exports=I_;Ho.exports.default=I_;Ho.exports.callCount=t=>{if(!qo.has(t))throw new Error(`The given function \`${t.name}\` is not wrapped by the \`onetime\` package`);return qo.get(t)}});var L_=b((l5,jo)=>{jo.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&jo.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&jo.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var Df=b((f5,Vn)=>{var _e=global.process,ln=function(t){return t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function"};ln(_e)?(N_=require("assert"),Wn=L_(),M_=/^win/i.test(_e.platform),ts=require("events"),typeof ts!="function"&&(ts=ts.EventEmitter),_e.__signal_exit_emitter__?ke=_e.__signal_exit_emitter__:(ke=_e.__signal_exit_emitter__=new ts,ke.count=0,ke.emitted={}),ke.infinite||(ke.setMaxListeners(1/0),ke.infinite=!0),Vn.exports=function(t,e){if(!ln(global.process))return function(){};N_.equal(typeof t,"function","a callback must be provided for exit handler"),zn===!1&&Ef();var r="exit";e&&e.alwaysLast&&(r="afterexit");var n=function(){ke.removeListener(r,t),ke.listeners("exit").length===0&&ke.listeners("afterexit").length===0&&$o()};return ke.on(r,t),n},$o=function(){!zn||!ln(global.process)||(zn=!1,Wn.forEach(function(e){try{_e.removeListener(e,Uo[e])}catch{}}),_e.emit=Go,_e.reallyExit=bf,ke.count-=1)},Vn.exports.unload=$o,fn=function(e,r,n){ke.emitted[e]||(ke.emitted[e]=!0,ke.emit(e,r,n))},Uo={},Wn.forEach(function(t){Uo[t]=function(){if(ln(global.process)){var r=_e.listeners(t);r.length===ke.count&&($o(),fn("exit",null,t),fn("afterexit",null,t),M_&&t==="SIGHUP"&&(t="SIGINT"),_e.kill(_e.pid,t))}}}),Vn.exports.signals=function(){return Wn},zn=!1,Ef=function(){zn||!ln(global.process)||(zn=!0,ke.count+=1,Wn=Wn.filter(function(e){try{return _e.on(e,Uo[e]),!0}catch{return!1}}),_e.emit=H_,_e.reallyExit=q_)},Vn.exports.load=Ef,bf=_e.reallyExit,q_=function(e){ln(global.process)&&(_e.exitCode=e||0,fn("exit",_e.exitCode,null),fn("afterexit",_e.exitCode,null),bf.call(_e,_e.exitCode))},Go=_e.emit,H_=function(e,r){if(e==="exit"&&ln(global.process)){r!==void 0&&(_e.exitCode=r);var n=Go.apply(this,arguments);return fn("exit",_e.exitCode,null),fn("afterexit",_e.exitCode,null),n}else return Go.apply(this,arguments)}):Vn.exports=function(){return function(){}};var N_,Wn,M_,ts,ke,$o,fn,Uo,zn,Ef,bf,q_,Go,H_});var $_=b((h5,j_)=>{"use strict";var DP=_f(),wP=Df();j_.exports=DP(()=>{wP(()=>{process.stderr.write("\x1B[?25h")},{alwaysLast:!0})})});var U_=b(Yn=>{"use strict";var AP=$_(),Wo=!1;Yn.show=(t=process.stderr)=>{t.isTTY&&(Wo=!1,t.write("\x1B[?25h"))};Yn.hide=(t=process.stderr)=>{t.isTTY&&(AP(),Wo=!0,t.write("\x1B[?25l"))};Yn.toggle=(t,e)=>{t!==void 0&&(Wo=t),Wo?Yn.show(e):Yn.hide(e)}});var G_=b((p5,xP)=>{xP.exports={dots:{interval:80,frames:["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"]},dots2:{interval:80,frames:["\u28FE","\u28FD","\u28FB","\u28BF","\u287F","\u28DF","\u28EF","\u28F7"]},dots3:{interval:80,frames:["\u280B","\u2819","\u281A","\u281E","\u2816","\u2826","\u2834","\u2832","\u2833","\u2813"]},dots4:{interval:80,frames:["\u2804","\u2806","\u2807","\u280B","\u2819","\u2838","\u2830","\u2820","\u2830","\u2838","\u2819","\u280B","\u2807","\u2806"]},dots5:{interval:80,frames:["\u280B","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B"]},dots6:{interval:80,frames:["\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2834","\u2832","\u2812","\u2802","\u2802","\u2812","\u281A","\u2819","\u2809","\u2801"]},dots7:{interval:80,frames:["\u2808","\u2809","\u280B","\u2813","\u2812","\u2810","\u2810","\u2812","\u2816","\u2826","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808"]},dots8:{interval:80,frames:["\u2801","\u2801","\u2809","\u2819","\u281A","\u2812","\u2802","\u2802","\u2812","\u2832","\u2834","\u2824","\u2804","\u2804","\u2824","\u2820","\u2820","\u2824","\u2826","\u2816","\u2812","\u2810","\u2810","\u2812","\u2813","\u280B","\u2809","\u2808","\u2808"]},dots9:{interval:80,frames:["\u28B9","\u28BA","\u28BC","\u28F8","\u28C7","\u2867","\u2857","\u284F"]},dots10:{interval:80,frames:["\u2884","\u2882","\u2881","\u2841","\u2848","\u2850","\u2860"]},dots11:{interval:100,frames:["\u2801","\u2802","\u2804","\u2840","\u2880","\u2820","\u2810","\u2808"]},dots12:{interval:80,frames:["\u2880\u2800","\u2840\u2800","\u2804\u2800","\u2882\u2800","\u2842\u2800","\u2805\u2800","\u2883\u2800","\u2843\u2800","\u280D\u2800","\u288B\u2800","\u284B\u2800","\u280D\u2801","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2888\u2829","\u2840\u2899","\u2804\u2859","\u2882\u2829","\u2842\u2898","\u2805\u2858","\u2883\u2828","\u2843\u2890","\u280D\u2850","\u288B\u2820","\u284B\u2880","\u280D\u2841","\u288B\u2801","\u284B\u2801","\u280D\u2809","\u280B\u2809","\u280B\u2809","\u2809\u2819","\u2809\u2819","\u2809\u2829","\u2808\u2899","\u2808\u2859","\u2808\u2829","\u2800\u2899","\u2800\u2859","\u2800\u2829","\u2800\u2898","\u2800\u2858","\u2800\u2828","\u2800\u2890","\u2800\u2850","\u2800\u2820","\u2800\u2880","\u2800\u2840"]},dots13:{interval:80,frames:["\u28FC","\u28F9","\u28BB","\u283F","\u285F","\u28CF","\u28E7","\u28F6"]},dots8Bit:{interval:80,frames:["\u2800","\u2801","\u2802","\u2803","\u2804","\u2805","\u2806","\u2807","\u2840","\u2841","\u2842","\u2843","\u2844","\u2845","\u2846","\u2847","\u2808","\u2809","\u280A","\u280B","\u280C","\u280D","\u280E","\u280F","\u2848","\u2849","\u284A","\u284B","\u284C","\u284D","\u284E","\u284F","\u2810","\u2811","\u2812","\u2813","\u2814","\u2815","\u2816","\u2817","\u2850","\u2851","\u2852","\u2853","\u2854","\u2855","\u2856","\u2857","\u2818","\u2819","\u281A","\u281B","\u281C","\u281D","\u281E","\u281F","\u2858","\u2859","\u285A","\u285B","\u285C","\u285D","\u285E","\u285F","\u2820","\u2821","\u2822","\u2823","\u2824","\u2825","\u2826","\u2827","\u2860","\u2861","\u2862","\u2863","\u2864","\u2865","\u2866","\u2867","\u2828","\u2829","\u282A","\u282B","\u282C","\u282D","\u282E","\u282F","\u2868","\u2869","\u286A","\u286B","\u286C","\u286D","\u286E","\u286F","\u2830","\u2831","\u2832","\u2833","\u2834","\u2835","\u2836","\u2837","\u2870","\u2871","\u2872","\u2873","\u2874","\u2875","\u2876","\u2877","\u2838","\u2839","\u283A","\u283B","\u283C","\u283D","\u283E","\u283F","\u2878","\u2879","\u287A","\u287B","\u287C","\u287D","\u287E","\u287F","\u2880","\u2881","\u2882","\u2883","\u2884","\u2885","\u2886","\u2887","\u28C0","\u28C1","\u28C2","\u28C3","\u28C4","\u28C5","\u28C6","\u28C7","\u2888","\u2889","\u288A","\u288B","\u288C","\u288D","\u288E","\u288F","\u28C8","\u28C9","\u28CA","\u28CB","\u28CC","\u28CD","\u28CE","\u28CF","\u2890","\u2891","\u2892","\u2893","\u2894","\u2895","\u2896","\u2897","\u28D0","\u28D1","\u28D2","\u28D3","\u28D4","\u28D5","\u28D6","\u28D7","\u2898","\u2899","\u289A","\u289B","\u289C","\u289D","\u289E","\u289F","\u28D8","\u28D9","\u28DA","\u28DB","\u28DC","\u28DD","\u28DE","\u28DF","\u28A0","\u28A1","\u28A2","\u28A3","\u28A4","\u28A5","\u28A6","\u28A7","\u28E0","\u28E1","\u28E2","\u28E3","\u28E4","\u28E5","\u28E6","\u28E7","\u28A8","\u28A9","\u28AA","\u28AB","\u28AC","\u28AD","\u28AE","\u28AF","\u28E8","\u28E9","\u28EA","\u28EB","\u28EC","\u28ED","\u28EE","\u28EF","\u28B0","\u28B1","\u28B2","\u28B3","\u28B4","\u28B5","\u28B6","\u28B7","\u28F0","\u28F1","\u28F2","\u28F3","\u28F4","\u28F5","\u28F6","\u28F7","\u28B8","\u28B9","\u28BA","\u28BB","\u28BC","\u28BD","\u28BE","\u28BF","\u28F8","\u28F9","\u28FA","\u28FB","\u28FC","\u28FD","\u28FE","\u28FF"]},sand:{interval:80,frames:["\u2801","\u2802","\u2804","\u2840","\u2848","\u2850","\u2860","\u28C0","\u28C1","\u28C2","\u28C4","\u28CC","\u28D4","\u28E4","\u28E5","\u28E6","\u28EE","\u28F6","\u28F7","\u28FF","\u287F","\u283F","\u289F","\u281F","\u285B","\u281B","\u282B","\u288B","\u280B","\u280D","\u2849","\u2809","\u2811","\u2821","\u2881"]},line:{interval:130,frames:["-","\\","|","/"]},line2:{interval:100,frames:["\u2802","-","\u2013","\u2014","\u2013","-"]},pipe:{interval:100,frames:["\u2524","\u2518","\u2534","\u2514","\u251C","\u250C","\u252C","\u2510"]},simpleDots:{interval:400,frames:[".  ",".. ","...","   "]},simpleDotsScrolling:{interval:200,frames:[".  ",".. ","..."," ..","  .","   "]},star:{interval:70,frames:["\u2736","\u2738","\u2739","\u273A","\u2739","\u2737"]},star2:{interval:80,frames:["+","x","*"]},flip:{interval:70,frames:["_","_","_","-","`","`","'","\xB4","-","_","_","_"]},hamburger:{interval:100,frames:["\u2631","\u2632","\u2634"]},growVertical:{interval:120,frames:["\u2581","\u2583","\u2584","\u2585","\u2586","\u2587","\u2586","\u2585","\u2584","\u2583"]},growHorizontal:{interval:120,frames:["\u258F","\u258E","\u258D","\u258C","\u258B","\u258A","\u2589","\u258A","\u258B","\u258C","\u258D","\u258E"]},balloon:{interval:140,frames:[" ",".","o","O","@","*"," "]},balloon2:{interval:120,frames:[".","o","O","\xB0","O","o","."]},noise:{interval:100,frames:["\u2593","\u2592","\u2591"]},bounce:{interval:120,frames:["\u2801","\u2802","\u2804","\u2802"]},boxBounce:{interval:120,frames:["\u2596","\u2598","\u259D","\u2597"]},boxBounce2:{interval:100,frames:["\u258C","\u2580","\u2590","\u2584"]},triangle:{interval:50,frames:["\u25E2","\u25E3","\u25E4","\u25E5"]},arc:{interval:100,frames:["\u25DC","\u25E0","\u25DD","\u25DE","\u25E1","\u25DF"]},circle:{interval:120,frames:["\u25E1","\u2299","\u25E0"]},squareCorners:{interval:180,frames:["\u25F0","\u25F3","\u25F2","\u25F1"]},circleQuarters:{interval:120,frames:["\u25F4","\u25F7","\u25F6","\u25F5"]},circleHalves:{interval:50,frames:["\u25D0","\u25D3","\u25D1","\u25D2"]},squish:{interval:100,frames:["\u256B","\u256A"]},toggle:{interval:250,frames:["\u22B6","\u22B7"]},toggle2:{interval:80,frames:["\u25AB","\u25AA"]},toggle3:{interval:120,frames:["\u25A1","\u25A0"]},toggle4:{interval:100,frames:["\u25A0","\u25A1","\u25AA","\u25AB"]},toggle5:{interval:100,frames:["\u25AE","\u25AF"]},toggle6:{interval:300,frames:["\u101D","\u1040"]},toggle7:{interval:80,frames:["\u29BE","\u29BF"]},toggle8:{interval:100,frames:["\u25CD","\u25CC"]},toggle9:{interval:100,frames:["\u25C9","\u25CE"]},toggle10:{interval:100,frames:["\u3282","\u3280","\u3281"]},toggle11:{interval:50,frames:["\u29C7","\u29C6"]},toggle12:{interval:120,frames:["\u2617","\u2616"]},toggle13:{interval:80,frames:["=","*","-"]},arrow:{interval:100,frames:["\u2190","\u2196","\u2191","\u2197","\u2192","\u2198","\u2193","\u2199"]},arrow2:{interval:80,frames:["\u2B06\uFE0F ","\u2197\uFE0F ","\u27A1\uFE0F ","\u2198\uFE0F ","\u2B07\uFE0F ","\u2199\uFE0F ","\u2B05\uFE0F ","\u2196\uFE0F "]},arrow3:{interval:120,frames:["\u25B9\u25B9\u25B9\u25B9\u25B9","\u25B8\u25B9\u25B9\u25B9\u25B9","\u25B9\u25B8\u25B9\u25B9\u25B9","\u25B9\u25B9\u25B8\u25B9\u25B9","\u25B9\u25B9\u25B9\u25B8\u25B9","\u25B9\u25B9\u25B9\u25B9\u25B8"]},bouncingBar:{interval:80,frames:["[    ]","[=   ]","[==  ]","[=== ]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},bouncingBall:{interval:80,frames:["( \u25CF    )","(  \u25CF   )","(   \u25CF  )","(    \u25CF )","(     \u25CF)","(    \u25CF )","(   \u25CF  )","(  \u25CF   )","( \u25CF    )","(\u25CF     )"]},smiley:{interval:200,frames:["\u{1F604} ","\u{1F61D} "]},monkey:{interval:300,frames:["\u{1F648} ","\u{1F648} ","\u{1F649} ","\u{1F64A} "]},hearts:{interval:100,frames:["\u{1F49B} ","\u{1F499} ","\u{1F49C} ","\u{1F49A} ","\u2764\uFE0F "]},clock:{interval:100,frames:["\u{1F55B} ","\u{1F550} ","\u{1F551} ","\u{1F552} ","\u{1F553} ","\u{1F554} ","\u{1F555} ","\u{1F556} ","\u{1F557} ","\u{1F558} ","\u{1F559} ","\u{1F55A} "]},earth:{interval:180,frames:["\u{1F30D} ","\u{1F30E} ","\u{1F30F} "]},material:{interval:17,frames:["\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2588","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581","\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581\u2581"]},moon:{interval:80,frames:["\u{1F311} ","\u{1F312} ","\u{1F313} ","\u{1F314} ","\u{1F315} ","\u{1F316} ","\u{1F317} ","\u{1F318} "]},runner:{interval:140,frames:["\u{1F6B6} ","\u{1F3C3} "]},pong:{interval:80,frames:["\u2590\u2802       \u258C","\u2590\u2808       \u258C","\u2590 \u2802      \u258C","\u2590 \u2820      \u258C","\u2590  \u2840     \u258C","\u2590  \u2820     \u258C","\u2590   \u2802    \u258C","\u2590   \u2808    \u258C","\u2590    \u2802   \u258C","\u2590    \u2820   \u258C","\u2590     \u2840  \u258C","\u2590     \u2820  \u258C","\u2590      \u2802 \u258C","\u2590      \u2808 \u258C","\u2590       \u2802\u258C","\u2590       \u2820\u258C","\u2590       \u2840\u258C","\u2590      \u2820 \u258C","\u2590      \u2802 \u258C","\u2590     \u2808  \u258C","\u2590     \u2802  \u258C","\u2590    \u2820   \u258C","\u2590    \u2840   \u258C","\u2590   \u2820    \u258C","\u2590   \u2802    \u258C","\u2590  \u2808     \u258C","\u2590  \u2802     \u258C","\u2590 \u2820      \u258C","\u2590 \u2840      \u258C","\u2590\u2820       \u258C"]},shark:{interval:120,frames:["\u2590|\\____________\u258C","\u2590_|\\___________\u258C","\u2590__|\\__________\u258C","\u2590___|\\_________\u258C","\u2590____|\\________\u258C","\u2590_____|\\_______\u258C","\u2590______|\\______\u258C","\u2590_______|\\_____\u258C","\u2590________|\\____\u258C","\u2590_________|\\___\u258C","\u2590__________|\\__\u258C","\u2590___________|\\_\u258C","\u2590____________|\\\u258C","\u2590____________/|\u258C","\u2590___________/|_\u258C","\u2590__________/|__\u258C","\u2590_________/|___\u258C","\u2590________/|____\u258C","\u2590_______/|_____\u258C","\u2590______/|______\u258C","\u2590_____/|_______\u258C","\u2590____/|________\u258C","\u2590___/|_________\u258C","\u2590__/|__________\u258C","\u2590_/|___________\u258C","\u2590/|____________\u258C"]},dqpb:{interval:100,frames:["d","q","p","b"]},weather:{interval:100,frames:["\u2600\uFE0F ","\u2600\uFE0F ","\u2600\uFE0F ","\u{1F324} ","\u26C5\uFE0F ","\u{1F325} ","\u2601\uFE0F ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u26C8 ","\u{1F328} ","\u{1F327} ","\u{1F328} ","\u2601\uFE0F ","\u{1F325} ","\u26C5\uFE0F ","\u{1F324} ","\u2600\uFE0F ","\u2600\uFE0F "]},christmas:{interval:400,frames:["\u{1F332}","\u{1F384}"]},grenade:{interval:80,frames:["\u060C  ","\u2032  "," \xB4 "," \u203E ","  \u2E0C","  \u2E0A","  |","  \u204E","  \u2055"," \u0DF4 ","  \u2053","   ","   ","   "]},point:{interval:125,frames:["\u2219\u2219\u2219","\u25CF\u2219\u2219","\u2219\u25CF\u2219","\u2219\u2219\u25CF","\u2219\u2219\u2219"]},layer:{interval:150,frames:["-","=","\u2261"]},betaWave:{interval:80,frames:["\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1\u03B2","\u03B2\u03B2\u03B2\u03B2\u03B2\u03B2\u03C1"]},fingerDance:{interval:160,frames:["\u{1F918} ","\u{1F91F} ","\u{1F596} ","\u270B ","\u{1F91A} ","\u{1F446} "]},fistBump:{interval:80,frames:["\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u{1F91C}\u3000\u3000\u3000\u3000\u{1F91B} ","\u3000\u{1F91C}\u3000\u3000\u{1F91B}\u3000 ","\u3000\u3000\u{1F91C}\u{1F91B}\u3000\u3000 ","\u3000\u{1F91C}\u2728\u{1F91B}\u3000\u3000 ","\u{1F91C}\u3000\u2728\u3000\u{1F91B}\u3000 "]},soccerHeader:{interval:80,frames:[" \u{1F9D1}\u26BD\uFE0F       \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}       \u26BD\uFE0F\u{1F9D1}  ","\u{1F9D1}      \u26BD\uFE0F  \u{1F9D1} ","\u{1F9D1}     \u26BD\uFE0F   \u{1F9D1} ","\u{1F9D1}    \u26BD\uFE0F    \u{1F9D1} ","\u{1F9D1}   \u26BD\uFE0F     \u{1F9D1} ","\u{1F9D1}  \u26BD\uFE0F      \u{1F9D1} "]},mindblown:{interval:160,frames:["\u{1F610} ","\u{1F610} ","\u{1F62E} ","\u{1F62E} ","\u{1F626} ","\u{1F626} ","\u{1F627} ","\u{1F627} ","\u{1F92F} ","\u{1F4A5} ","\u2728 ","\u3000 ","\u3000 ","\u3000 "]},speaker:{interval:160,frames:["\u{1F508} ","\u{1F509} ","\u{1F50A} ","\u{1F509} "]},orangePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} "]},bluePulse:{interval:100,frames:["\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},orangeBluePulse:{interval:100,frames:["\u{1F538} ","\u{1F536} ","\u{1F7E0} ","\u{1F7E0} ","\u{1F536} ","\u{1F539} ","\u{1F537} ","\u{1F535} ","\u{1F535} ","\u{1F537} "]},timeTravel:{interval:100,frames:["\u{1F55B} ","\u{1F55A} ","\u{1F559} ","\u{1F558} ","\u{1F557} ","\u{1F556} ","\u{1F555} ","\u{1F554} ","\u{1F553} ","\u{1F552} ","\u{1F551} ","\u{1F550} "]},aesthetic:{interval:80,frames:["\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B1","\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0\u25B0","\u25B0\u25B1\u25B1\u25B1\u25B1\u25B1\u25B1"]}}});var V_=b((m5,z_)=>{"use strict";var zo=Object.assign({},G_()),W_=Object.keys(zo);Object.defineProperty(zo,"random",{get(){let t=Math.floor(Math.random()*W_.length),e=W_[t];return zo[e]}});z_.exports=zo});var K_=b((g5,Y_)=>{"use strict";var Br=require("chalk"),SP=process.platform!=="win32"||process.env.CI||process.env.TERM==="xterm-256color",CP={info:Br.blue("\u2139"),success:Br.green("\u2714"),warning:Br.yellow("\u26A0"),error:Br.red("\u2716")},vP={info:Br.blue("i"),success:Br.green("\u221A"),warning:Br.yellow("\u203C"),error:Br.red("\xD7")};Y_.exports=SP?CP:vP});var X_=b((y5,J_)=>{"use strict";J_.exports=({onlyFirst:t=!1}={})=>{let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}});var Q_=b((_5,Z_)=>{"use strict";var RP=X_();Z_.exports=t=>typeof t=="string"?t.replace(RP(),""):t});var eE=b((E5,Vo)=>{var FP=function(){"use strict";function t(o,a,u,c){var l;typeof a=="object"&&(u=a.depth,c=a.prototype,l=a.filter,a=a.circular);var f=[],h=[],m=typeof Buffer!="undefined";typeof a=="undefined"&&(a=!0),typeof u=="undefined"&&(u=1/0);function _(y,p){if(y===null)return null;if(p==0)return y;var D,C;if(typeof y!="object")return y;if(t.__isArray(y))D=[];else if(t.__isRegExp(y))D=new RegExp(y.source,s(y)),y.lastIndex&&(D.lastIndex=y.lastIndex);else if(t.__isDate(y))D=new Date(y.getTime());else{if(m&&Buffer.isBuffer(y))return Buffer.allocUnsafe?D=Buffer.allocUnsafe(y.length):D=new Buffer(y.length),y.copy(D),D;typeof c=="undefined"?(C=Object.getPrototypeOf(y),D=Object.create(C)):(D=Object.create(c),C=c)}if(a){var N=f.indexOf(y);if(N!=-1)return h[N];f.push(y),h.push(D)}for(var U in y){var M;C&&(M=Object.getOwnPropertyDescriptor(C,U)),!(M&&M.set==null)&&(D[U]=_(y[U],p-1))}return D}return _(o,u)}t.clonePrototype=function(a){if(a===null)return null;var u=function(){};return u.prototype=a,new u};function e(o){return Object.prototype.toString.call(o)}t.__objToStr=e;function r(o){return typeof o=="object"&&e(o)==="[object Date]"}t.__isDate=r;function n(o){return typeof o=="object"&&e(o)==="[object Array]"}t.__isArray=n;function i(o){return typeof o=="object"&&e(o)==="[object RegExp]"}t.__isRegExp=i;function s(o){var a="";return o.global&&(a+="g"),o.ignoreCase&&(a+="i"),o.multiline&&(a+="m"),a}return t.__getRegExpFlags=s,t}();typeof Vo=="object"&&Vo.exports&&(Vo.exports=FP)});var rE=b((b5,tE)=>{var TP=eE();tE.exports=function(t,e){return t=t||{},Object.keys(e).forEach(function(r){typeof t[r]=="undefined"&&(t[r]=TP(e[r]))}),t}});var iE=b((D5,nE)=>{nE.exports=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531],[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]]});var uE=b((w5,wf)=>{"use strict";var OP=rE(),rs=iE(),oE={nul:0,control:0};wf.exports=function(e){return aE(e,oE)};wf.exports.config=function(t){return t=OP(t||{},oE),function(r){return aE(r,t)}};function aE(t,e){if(typeof t!="string")return sE(t,e);for(var r=0,n=0;n<t.length;n++){var i=sE(t.charCodeAt(n),e);if(i<0)return-1;r+=i}return r}function sE(t,e){return t===0?e.nul:t<32||t>=127&&t<160?e.control:kP(t)?0:1+(t>=4352&&(t<=4447||t==9001||t==9002||t>=11904&&t<=42191&&t!=12351||t>=44032&&t<=55203||t>=63744&&t<=64255||t>=65040&&t<=65049||t>=65072&&t<=65135||t>=65280&&t<=65376||t>=65504&&t<=65510||t>=131072&&t<=196605||t>=196608&&t<=262141))}function kP(t){var e=0,r=rs.length-1,n;if(t<rs[0][0]||t>rs[r][1])return!1;for(;r>=e;)if(n=Math.floor((e+r)/2),t>rs[n][1])e=n+1;else if(t<rs[n][0])r=n-1;else return!0;return!1}});var lE=b((A5,cE)=>{"use strict";cE.exports=({stream:t=process.stdout}={})=>!!(t&&t.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))});var hE=b((x5,fE)=>{var Af=require("stream");fE.exports=Pe;function Pe(t){Af.apply(this),t=t||{},this.writable=this.readable=!0,this.muted=!1,this.on("pipe",this._onpipe),this.replace=t.replace,this._prompt=t.prompt||null,this._hadControl=!1}Pe.prototype=Object.create(Af.prototype);Object.defineProperty(Pe.prototype,"constructor",{value:Pe,enumerable:!1});Pe.prototype.mute=function(){this.muted=!0};Pe.prototype.unmute=function(){this.muted=!1};Object.defineProperty(Pe.prototype,"_onpipe",{value:PP,enumerable:!1,writable:!0,configurable:!0});function PP(t){this._src=t}Object.defineProperty(Pe.prototype,"isTTY",{get:BP,set:IP,enumerable:!0,configurable:!0});function BP(){return this._dest?this._dest.isTTY:this._src?this._src.isTTY:!1}function IP(t){Object.defineProperty(this,"isTTY",{value:t,enumerable:!0,writable:!0,configurable:!0})}Object.defineProperty(Pe.prototype,"rows",{get:function(){return this._dest?this._dest.rows:this._src?this._src.rows:void 0},enumerable:!0,configurable:!0});Object.defineProperty(Pe.prototype,"columns",{get:function(){return this._dest?this._dest.columns:this._src?this._src.columns:void 0},enumerable:!0,configurable:!0});Pe.prototype.pipe=function(t,e){return this._dest=t,Af.prototype.pipe.call(this,t,e)};Pe.prototype.pause=function(){if(this._src)return this._src.pause()};Pe.prototype.resume=function(){if(this._src)return this._src.resume()};Pe.prototype.write=function(t){if(this.muted){if(!this.replace)return!0;if(t.match(/^\u001b/))return t.indexOf(this._prompt)===0&&(t=t.substr(this._prompt.length),t=t.replace(/./g,this.replace),t=this._prompt+t),this._hadControl=!0,this.emit("data",t);this._prompt&&this._hadControl&&t.indexOf(this._prompt)===0&&(this._hadControl=!1,this.emit("data",this._prompt),t=t.substr(this._prompt.length)),t=t.toString().replace(/./g,this.replace)}this.emit("data",t)};Pe.prototype.end=function(t){this.muted&&(t&&this.replace?t=t.toString().replace(/./g,this.replace):t=null),t&&this.emit("data",t),this.emit("end")};function xf(t){return function(){var e=this._dest,r=this._src;e&&e[t]&&e[t].apply(e,arguments),r&&r[t]&&r[t].apply(r,arguments)}}Pe.prototype.destroy=xf("destroy");Pe.prototype.destroySoon=xf("destroySoon");Pe.prototype.close=xf("close")});var pE=b((S5,vf)=>{"use strict";var LP=require("readline"),NP=require("chalk"),dE=U_(),Yo=V_(),Ko=K_(),MP=Q_(),qP=uE(),HP=lE(),jP=hE(),Sf=Symbol("text"),Jo=Symbol("prefixText"),$P=3,Cf=class{constructor(){this.requests=0,this.mutedStream=new jP,this.mutedStream.pipe(process.stdout),this.mutedStream.mute();let e=this;this.ourEmit=function(r,n,...i){let{stdin:s}=process;if(e.requests>0||s.emit===e.ourEmit){if(r==="keypress")return;r==="data"&&n.includes($P)&&process.emit("SIGINT"),Reflect.apply(e.oldEmit,this,[r,n,...i])}else Reflect.apply(process.stdin.emit,this,[r,n,...i])}}start(){this.requests++,this.requests===1&&this.realStart()}stop(){if(this.requests<=0)throw new Error("`stop` called more times than `start`");this.requests--,this.requests===0&&this.realStop()}realStart(){process.platform!=="win32"&&(this.rl=LP.createInterface({input:process.stdin,output:this.mutedStream}),this.rl.on("SIGINT",()=>{process.listenerCount("SIGINT")===0?process.emit("SIGINT"):(this.rl.close(),process.kill(process.pid,"SIGINT"))}))}realStop(){process.platform!=="win32"&&(this.rl.close(),this.rl=void 0)}},Xo,Zo=class{constructor(e){Xo||(Xo=new Cf),typeof e=="string"&&(e={text:e}),this.options={text:"",color:"cyan",stream:process.stderr,discardStdin:!0,...e},this.spinner=this.options.spinner,this.color=this.options.color,this.hideCursor=this.options.hideCursor!==!1,this.interval=this.options.interval||this.spinner.interval||100,this.stream=this.options.stream,this.id=void 0,this.isEnabled=typeof this.options.isEnabled=="boolean"?this.options.isEnabled:HP({stream:this.stream}),this.text=this.options.text,this.prefixText=this.options.prefixText,this.linesToClear=0,this.indent=this.options.indent,this.discardStdin=this.options.discardStdin,this.isDiscardingStdin=!1}get indent(){return this._indent}set indent(e=0){if(!(e>=0&&Number.isInteger(e)))throw new Error("The `indent` option must be an integer from 0 and up");this._indent=e}_updateInterval(e){e!==void 0&&(this.interval=e)}get spinner(){return this._spinner}set spinner(e){if(this.frameIndex=0,typeof e=="object"){if(e.frames===void 0)throw new Error("The given spinner must have a `frames` property");this._spinner=e}else if(process.platform==="win32")this._spinner=Yo.line;else if(e===void 0)this._spinner=Yo.dots;else if(Yo[e])this._spinner=Yo[e];else throw new Error(`There is no built-in spinner named '${e}'. See https://github.com/sindresorhus/cli-spinners/blob/master/spinners.json for a full list.`);this._updateInterval(this._spinner.interval)}get text(){return this[Sf]}get prefixText(){return this[Jo]}get isSpinning(){return this.id!==void 0}updateLineCount(){let e=this.stream.columns||80,r=typeof this[Jo]=="string"?this[Jo]+"-":"";this.lineCount=MP(r+"--"+this[Sf]).split(`
`).reduce((n,i)=>n+Math.max(1,Math.ceil(qP(i)/e)),0)}set text(e){this[Sf]=e,this.updateLineCount()}set prefixText(e){this[Jo]=e,this.updateLineCount()}frame(){let{frames:e}=this.spinner,r=e[this.frameIndex];this.color&&(r=NP[this.color](r)),this.frameIndex=++this.frameIndex%e.length;let n=typeof this.prefixText=="string"&&this.prefixText!==""?this.prefixText+" ":"",i=typeof this.text=="string"?" "+this.text:"";return n+r+i}clear(){if(!this.isEnabled||!this.stream.isTTY)return this;for(let e=0;e<this.linesToClear;e++)e>0&&this.stream.moveCursor(0,-1),this.stream.clearLine(),this.stream.cursorTo(this.indent);return this.linesToClear=0,this}render(){return this.clear(),this.stream.write(this.frame()),this.linesToClear=this.lineCount,this}start(e){return e&&(this.text=e),this.isEnabled?this.isSpinning?this:(this.hideCursor&&dE.hide(this.stream),this.discardStdin&&process.stdin.isTTY&&(this.isDiscardingStdin=!0,Xo.start()),this.render(),this.id=setInterval(this.render.bind(this),this.interval),this):(this.text&&this.stream.write(`- ${this.text}
`),this)}stop(){return this.isEnabled?(clearInterval(this.id),this.id=void 0,this.frameIndex=0,this.clear(),this.hideCursor&&dE.show(this.stream),this.discardStdin&&process.stdin.isTTY&&this.isDiscardingStdin&&(Xo.stop(),this.isDiscardingStdin=!1),this):this}succeed(e){return this.stopAndPersist({symbol:Ko.success,text:e})}fail(e){return this.stopAndPersist({symbol:Ko.error,text:e})}warn(e){return this.stopAndPersist({symbol:Ko.warning,text:e})}info(e){return this.stopAndPersist({symbol:Ko.info,text:e})}stopAndPersist(e={}){let r=e.prefixText||this.prefixText,n=typeof r=="string"&&r!==""?r+" ":"",i=e.text||this.text,s=typeof i=="string"?" "+i:"";return this.stop(),this.stream.write(`${n}${e.symbol||" "}${s}
`),this}},UP=function(t){return new Zo(t)};vf.exports=UP;vf.exports.promise=(t,e)=>{if(typeof t.then!="function")throw new TypeError("Parameter `action` must be a Promise");let r=new Zo(e);return r.start(),(async()=>{try{await t,r.succeed()}catch{r.fail()}})(),r}});var mE=b((C5,Qo)=>{(function(t){var e=/^\s+/,r=/\s+$/,n=0,i=t.round,s=t.min,o=t.max,a=t.random;function u(d,w){if(d=d||"",w=w||{},d instanceof u)return d;if(!(this instanceof u))return new u(d,w);var g=c(d);this._originalInput=d,this._r=g.r,this._g=g.g,this._b=g.b,this._a=g.a,this._roundA=i(100*this._a)/100,this._format=w.format||g.format,this._gradientType=w.gradientType,this._r<1&&(this._r=i(this._r)),this._g<1&&(this._g=i(this._g)),this._b<1&&(this._b=i(this._b)),this._ok=g.ok,this._tc_id=n++}u.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var d=this.toRgb();return(d.r*299+d.g*587+d.b*114)/1e3},getLuminance:function(){var d=this.toRgb(),w,g,O,j,H,oe;return w=d.r/255,g=d.g/255,O=d.b/255,w<=.03928?j=w/12.92:j=t.pow((w+.055)/1.055,2.4),g<=.03928?H=g/12.92:H=t.pow((g+.055)/1.055,2.4),O<=.03928?oe=O/12.92:oe=t.pow((O+.055)/1.055,2.4),.2126*j+.7152*H+.0722*oe},setAlpha:function(d){return this._a=v(d),this._roundA=i(100*this._a)/100,this},toHsv:function(){var d=m(this._r,this._g,this._b);return{h:d.h*360,s:d.s,v:d.v,a:this._a}},toHsvString:function(){var d=m(this._r,this._g,this._b),w=i(d.h*360),g=i(d.s*100),O=i(d.v*100);return this._a==1?"hsv("+w+", "+g+"%, "+O+"%)":"hsva("+w+", "+g+"%, "+O+"%, "+this._roundA+")"},toHsl:function(){var d=f(this._r,this._g,this._b);return{h:d.h*360,s:d.s,l:d.l,a:this._a}},toHslString:function(){var d=f(this._r,this._g,this._b),w=i(d.h*360),g=i(d.s*100),O=i(d.l*100);return this._a==1?"hsl("+w+", "+g+"%, "+O+"%)":"hsla("+w+", "+g+"%, "+O+"%, "+this._roundA+")"},toHex:function(d){return y(this._r,this._g,this._b,d)},toHexString:function(d){return"#"+this.toHex(d)},toHex8:function(d){return p(this._r,this._g,this._b,this._a,d)},toHex8String:function(d){return"#"+this.toHex8(d)},toRgb:function(){return{r:i(this._r),g:i(this._g),b:i(this._b),a:this._a}},toRgbString:function(){return this._a==1?"rgb("+i(this._r)+", "+i(this._g)+", "+i(this._b)+")":"rgba("+i(this._r)+", "+i(this._g)+", "+i(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:i(k(this._r,255)*100)+"%",g:i(k(this._g,255)*100)+"%",b:i(k(this._b,255)*100)+"%",a:this._a}},toPercentageRgbString:function(){return this._a==1?"rgb("+i(k(this._r,255)*100)+"%, "+i(k(this._g,255)*100)+"%, "+i(k(this._b,255)*100)+"%)":"rgba("+i(k(this._r,255)*100)+"%, "+i(k(this._g,255)*100)+"%, "+i(k(this._b,255)*100)+"%, "+this._roundA+")"},toName:function(){return this._a===0?"transparent":this._a<1?!1:A[y(this._r,this._g,this._b,!0)]||!1},toFilter:function(d){var w="#"+D(this._r,this._g,this._b,this._a),g=w,O=this._gradientType?"GradientType = 1, ":"";if(d){var j=u(d);g="#"+D(j._r,j._g,j._b,j._a)}return"progid:DXImageTransform.Microsoft.gradient("+O+"startColorstr="+w+",endColorstr="+g+")"},toString:function(d){var w=!!d;d=d||this._format;var g=!1,O=this._a<1&&this._a>=0,j=!w&&O&&(d==="hex"||d==="hex6"||d==="hex3"||d==="hex4"||d==="hex8"||d==="name");return j?d==="name"&&this._a===0?this.toName():this.toRgbString():(d==="rgb"&&(g=this.toRgbString()),d==="prgb"&&(g=this.toPercentageRgbString()),(d==="hex"||d==="hex6")&&(g=this.toHexString()),d==="hex3"&&(g=this.toHexString(!0)),d==="hex4"&&(g=this.toHex8String(!0)),d==="hex8"&&(g=this.toHex8String()),d==="name"&&(g=this.toName()),d==="hsl"&&(g=this.toHslString()),d==="hsv"&&(g=this.toHsvString()),g||this.toHexString())},clone:function(){return u(this.toString())},_applyModification:function(d,w){var g=d.apply(null,[this].concat([].slice.call(w)));return this._r=g._r,this._g=g._g,this._b=g._b,this.setAlpha(g._a),this},lighten:function(){return this._applyModification(M,arguments)},brighten:function(){return this._applyModification(Y,arguments)},darken:function(){return this._applyModification(G,arguments)},desaturate:function(){return this._applyModification(C,arguments)},saturate:function(){return this._applyModification(N,arguments)},greyscale:function(){return this._applyModification(U,arguments)},spin:function(){return this._applyModification(z,arguments)},_applyCombination:function(d,w){return d.apply(null,[this].concat([].slice.call(w)))},analogous:function(){return this._applyCombination(ie,arguments)},complement:function(){return this._applyCombination(Q,arguments)},monochromatic:function(){return this._applyCombination(Qe,arguments)},splitcomplement:function(){return this._applyCombination(ae,arguments)},triad:function(){return this._applyCombination(be,arguments)},tetrad:function(){return this._applyCombination(R,arguments)}},u.fromRatio=function(d,w){if(typeof d=="object"){var g={};for(var O in d)d.hasOwnProperty(O)&&(O==="a"?g[O]=d[O]:g[O]=De(d[O]));d=g}return u(d,w)};function c(d){var w={r:0,g:0,b:0},g=1,O=null,j=null,H=null,oe=!1,Ee=!1;return typeof d=="string"&&(d=le(d)),typeof d=="object"&&(I(d.r)&&I(d.g)&&I(d.b)?(w=l(d.r,d.g,d.b),oe=!0,Ee=String(d.r).substr(-1)==="%"?"prgb":"rgb"):I(d.h)&&I(d.s)&&I(d.v)?(O=De(d.s),j=De(d.v),w=_(d.h,O,j),oe=!0,Ee="hsv"):I(d.h)&&I(d.s)&&I(d.l)&&(O=De(d.s),H=De(d.l),w=h(d.h,O,H),oe=!0,Ee="hsl"),d.hasOwnProperty("a")&&(g=d.a)),g=v(g),{ok:oe,format:d.format||Ee,r:s(255,o(w.r,0)),g:s(255,o(w.g,0)),b:s(255,o(w.b,0)),a:g}}function l(d,w,g){return{r:k(d,255)*255,g:k(w,255)*255,b:k(g,255)*255}}function f(d,w,g){d=k(d,255),w=k(w,255),g=k(g,255);var O=o(d,w,g),j=s(d,w,g),H,oe,Ee=(O+j)/2;if(O==j)H=oe=0;else{var we=O-j;switch(oe=Ee>.5?we/(2-O-j):we/(O+j),O){case d:H=(w-g)/we+(w<g?6:0);break;case w:H=(g-d)/we+2;break;case g:H=(d-w)/we+4;break}H/=6}return{h:H,s:oe,l:Ee}}function h(d,w,g){var O,j,H;d=k(d,360),w=k(w,100),g=k(g,100);function oe(dt,Mi,Ft){return Ft<0&&(Ft+=1),Ft>1&&(Ft-=1),Ft<1/6?dt+(Mi-dt)*6*Ft:Ft<1/2?Mi:Ft<2/3?dt+(Mi-dt)*(2/3-Ft)*6:dt}if(w===0)O=j=H=g;else{var Ee=g<.5?g*(1+w):g+w-g*w,we=2*g-Ee;O=oe(we,Ee,d+1/3),j=oe(we,Ee,d),H=oe(we,Ee,d-1/3)}return{r:O*255,g:j*255,b:H*255}}function m(d,w,g){d=k(d,255),w=k(w,255),g=k(g,255);var O=o(d,w,g),j=s(d,w,g),H,oe,Ee=O,we=O-j;if(oe=O===0?0:we/O,O==j)H=0;else{switch(O){case d:H=(w-g)/we+(w<g?6:0);break;case w:H=(g-d)/we+2;break;case g:H=(d-w)/we+4;break}H/=6}return{h:H,s:oe,v:Ee}}function _(d,w,g){d=k(d,360)*6,w=k(w,100),g=k(g,100);var O=t.floor(d),j=d-O,H=g*(1-w),oe=g*(1-j*w),Ee=g*(1-(1-j)*w),we=O%6,dt=[g,oe,H,H,Ee,g][we],Mi=[Ee,g,g,oe,H,H][we],Ft=[H,H,Ee,g,g,oe][we];return{r:dt*255,g:Mi*255,b:Ft*255}}function y(d,w,g,O){var j=[se(i(d).toString(16)),se(i(w).toString(16)),se(i(g).toString(16))];return O&&j[0].charAt(0)==j[0].charAt(1)&&j[1].charAt(0)==j[1].charAt(1)&&j[2].charAt(0)==j[2].charAt(1)?j[0].charAt(0)+j[1].charAt(0)+j[2].charAt(0):j.join("")}function p(d,w,g,O,j){var H=[se(i(d).toString(16)),se(i(w).toString(16)),se(i(g).toString(16)),se(V(O))];return j&&H[0].charAt(0)==H[0].charAt(1)&&H[1].charAt(0)==H[1].charAt(1)&&H[2].charAt(0)==H[2].charAt(1)&&H[3].charAt(0)==H[3].charAt(1)?H[0].charAt(0)+H[1].charAt(0)+H[2].charAt(0)+H[3].charAt(0):H.join("")}function D(d,w,g,O){var j=[se(V(O)),se(i(d).toString(16)),se(i(w).toString(16)),se(i(g).toString(16))];return j.join("")}u.equals=function(d,w){return!d||!w?!1:u(d).toRgbString()==u(w).toRgbString()},u.random=function(){return u.fromRatio({r:a(),g:a(),b:a()})};function C(d,w){w=w===0?0:w||10;var g=u(d).toHsl();return g.s-=w/100,g.s=$(g.s),u(g)}function N(d,w){w=w===0?0:w||10;var g=u(d).toHsl();return g.s+=w/100,g.s=$(g.s),u(g)}function U(d){return u(d).desaturate(100)}function M(d,w){w=w===0?0:w||10;var g=u(d).toHsl();return g.l+=w/100,g.l=$(g.l),u(g)}function Y(d,w){w=w===0?0:w||10;var g=u(d).toRgb();return g.r=o(0,s(255,g.r-i(255*-(w/100)))),g.g=o(0,s(255,g.g-i(255*-(w/100)))),g.b=o(0,s(255,g.b-i(255*-(w/100)))),u(g)}function G(d,w){w=w===0?0:w||10;var g=u(d).toHsl();return g.l-=w/100,g.l=$(g.l),u(g)}function z(d,w){var g=u(d).toHsl(),O=(g.h+w)%360;return g.h=O<0?360+O:O,u(g)}function Q(d){var w=u(d).toHsl();return w.h=(w.h+180)%360,u(w)}function be(d){var w=u(d).toHsl(),g=w.h;return[u(d),u({h:(g+120)%360,s:w.s,l:w.l}),u({h:(g+240)%360,s:w.s,l:w.l})]}function R(d){var w=u(d).toHsl(),g=w.h;return[u(d),u({h:(g+90)%360,s:w.s,l:w.l}),u({h:(g+180)%360,s:w.s,l:w.l}),u({h:(g+270)%360,s:w.s,l:w.l})]}function ae(d){var w=u(d).toHsl(),g=w.h;return[u(d),u({h:(g+72)%360,s:w.s,l:w.l}),u({h:(g+216)%360,s:w.s,l:w.l})]}function ie(d,w,g){w=w||6,g=g||30;var O=u(d).toHsl(),j=360/g,H=[u(d)];for(O.h=(O.h-(j*w>>1)+720)%360;--w;)O.h=(O.h+j)%360,H.push(u(O));return H}function Qe(d,w){w=w||6;for(var g=u(d).toHsv(),O=g.h,j=g.s,H=g.v,oe=[],Ee=1/w;w--;)oe.push(u({h:O,s:j,v:H})),H=(H+Ee)%1;return oe}u.mix=function(d,w,g){g=g===0?0:g||50;var O=u(d).toRgb(),j=u(w).toRgb(),H=g/100,oe={r:(j.r-O.r)*H+O.r,g:(j.g-O.g)*H+O.g,b:(j.b-O.b)*H+O.b,a:(j.a-O.a)*H+O.a};return u(oe)},u.readability=function(d,w){var g=u(d),O=u(w);return(t.max(g.getLuminance(),O.getLuminance())+.05)/(t.min(g.getLuminance(),O.getLuminance())+.05)},u.isReadable=function(d,w,g){var O=u.readability(d,w),j,H;switch(H=!1,j=B(g),j.level+j.size){case"AAsmall":case"AAAlarge":H=O>=4.5;break;case"AAlarge":H=O>=3;break;case"AAAsmall":H=O>=7;break}return H},u.mostReadable=function(d,w,g){var O=null,j=0,H,oe,Ee,we;g=g||{},oe=g.includeFallbackColors,Ee=g.level,we=g.size;for(var dt=0;dt<w.length;dt++)H=u.readability(d,w[dt]),H>j&&(j=H,O=u(w[dt]));return u.isReadable(d,O,{level:Ee,size:we})||!oe?O:(g.includeFallbackColors=!1,u.mostReadable(d,["#fff","#000"],g))};var S=u.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},A=u.hexNames=F(S);function F(d){var w={};for(var g in d)d.hasOwnProperty(g)&&(w[d[g]]=g);return w}function v(d){return d=parseFloat(d),(isNaN(d)||d<0||d>1)&&(d=1),d}function k(d,w){ue(d)&&(d="100%");var g=Re(d);return d=s(w,o(0,parseFloat(d))),g&&(d=parseInt(d*w,10)/100),t.abs(d-w)<1e-6?1:d%w/parseFloat(w)}function $(d){return s(1,o(0,d))}function q(d){return parseInt(d,16)}function ue(d){return typeof d=="string"&&d.indexOf(".")!=-1&&parseFloat(d)===1}function Re(d){return typeof d=="string"&&d.indexOf("%")!=-1}function se(d){return d.length==1?"0"+d:""+d}function De(d){return d<=1&&(d=d*100+"%"),d}function V(d){return t.round(parseFloat(d)*255).toString(16)}function Ke(d){return q(d)/255}var ot=function(){var d="[-\\+]?\\d+%?",w="[-\\+]?\\d*\\.\\d+%?",g="(?:"+w+")|(?:"+d+")",O="[\\s|\\(]+("+g+")[,|\\s]+("+g+")[,|\\s]+("+g+")\\s*\\)?",j="[\\s|\\(]+("+g+")[,|\\s]+("+g+")[,|\\s]+("+g+")[,|\\s]+("+g+")\\s*\\)?";return{CSS_UNIT:new RegExp(g),rgb:new RegExp("rgb"+O),rgba:new RegExp("rgba"+j),hsl:new RegExp("hsl"+O),hsla:new RegExp("hsla"+j),hsv:new RegExp("hsv"+O),hsva:new RegExp("hsva"+j),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function I(d){return!!ot.CSS_UNIT.exec(d)}function le(d){d=d.replace(e,"").replace(r,"").toLowerCase();var w=!1;if(S[d])d=S[d],w=!0;else if(d=="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var g;return(g=ot.rgb.exec(d))?{r:g[1],g:g[2],b:g[3]}:(g=ot.rgba.exec(d))?{r:g[1],g:g[2],b:g[3],a:g[4]}:(g=ot.hsl.exec(d))?{h:g[1],s:g[2],l:g[3]}:(g=ot.hsla.exec(d))?{h:g[1],s:g[2],l:g[3],a:g[4]}:(g=ot.hsv.exec(d))?{h:g[1],s:g[2],v:g[3]}:(g=ot.hsva.exec(d))?{h:g[1],s:g[2],v:g[3],a:g[4]}:(g=ot.hex8.exec(d))?{r:q(g[1]),g:q(g[2]),b:q(g[3]),a:Ke(g[4]),format:w?"name":"hex8"}:(g=ot.hex6.exec(d))?{r:q(g[1]),g:q(g[2]),b:q(g[3]),format:w?"name":"hex"}:(g=ot.hex4.exec(d))?{r:q(g[1]+""+g[1]),g:q(g[2]+""+g[2]),b:q(g[3]+""+g[3]),a:Ke(g[4]+""+g[4]),format:w?"name":"hex8"}:(g=ot.hex3.exec(d))?{r:q(g[1]+""+g[1]),g:q(g[2]+""+g[2]),b:q(g[3]+""+g[3]),format:w?"name":"hex"}:!1}function B(d){var w,g;return d=d||{level:"AA",size:"small"},w=(d.level||"AA").toUpperCase(),g=(d.size||"small").toLowerCase(),w!=="AA"&&w!=="AAA"&&(w="AA"),g!=="small"&&g!=="large"&&(g="small"),{level:w,size:g}}typeof Qo!="undefined"&&Qo.exports?Qo.exports=u:typeof define=="function"&&define.amd?define(function(){return u}):window.tinycolor=u})(Math)});var wE=b((v5,DE)=>{var ns=mE(),EE={r:256,g:256,b:256,a:1},bE={h:360,s:1,v:1,a:1};function Ff(t,e,r){let n={};for(let i in t)t.hasOwnProperty(i)&&(n[i]=r===0?0:(e[i]-t[i])/r);return n}function Tf(t,e,r,n){let i={};for(let s in e)e.hasOwnProperty(s)&&(i[s]=t[s]*r+e[s],i[s]=i[s]<0?i[s]+n[s]:n[s]!==1?i[s]%n[s]:i[s]);return i}function Rf(t,e,r){let n=t.color.toRgb(),i=e.color.toRgb(),s=Ff(n,i,r),o=[t.color];for(let a=1;a<r;a++){let u=Tf(s,n,a,EE);o.push(ns(u))}return o}function gE(t,e,r,n){let i=t.color.toHsv(),s=e.color.toHsv();if(i.s===0||s.s===0)return Rf(t,e,r);let o;if(typeof n=="boolean")o=n;else{let l=i.h<s.h&&s.h-i.h<180||i.h>s.h&&i.h-s.h>180;o=n==="long"&&l||n==="short"&&!l}let a=Ff(i,s,r),u=[t.color],c;i.h<=s.h&&!o||i.h>=s.h&&o?c=s.h-i.h:o?c=360-s.h+i.h:c=360-i.h+s.h,a.h=Math.pow(-1,o?1:0)*Math.abs(c)/r;for(let l=1;l<r;l++){let f=Tf(a,i,l,bE);u.push(ns(f))}return u}function yE(t,e){let r=t.length;if(e=parseInt(e,10),isNaN(e)||e<2)throw new Error("Invalid number of steps (< 2)");if(e<r)throw new Error("Number of steps cannot be inferior to number of stops");let n=[];for(let s=1;s<r;s++){let o=(e-1)*(t[s].pos-t[s-1].pos);n.push(Math.max(1,Math.round(o)))}let i=1;for(let s=r-1;s--;)i+=n[s];for(;i!==e;)if(i<e){let s=Math.min.apply(null,n);n[n.indexOf(s)]++,i++}else{let s=Math.max.apply(null,n);n[n.indexOf(s)]--,i--}return n}function _E(t,e,r,n){if(e<0||e>1)throw new Error("Position must be between 0 and 1");let i,s;for(let u=0,c=t.length;u<c-1;u++)if(e>=t[u].pos&&e<t[u+1].pos){i=t[u],s=t[u+1];break}i||(i=s=t[t.length-1]);let o=Ff(i.color[r](),s.color[r](),(s.pos-i.pos)*100),a=Tf(o,i.color[r](),(e-i.pos)*100,n);return ns(a)}var Kn=class{constructor(e){if(e.length<2)throw new Error("Invalid number of stops (< 2)");let r=e[0].pos!==void 0,n=e.length,i=-1,s=!1;this.stops=e.map((o,a)=>{let u=o.pos!==void 0;if(r^u)throw new Error("Cannot mix positionned and not posionned color stops");if(u){let c=o.color!==void 0;if(!c&&(s||a===0||a===n-1))throw new Error("Cannot define two consecutive position-only stops");if(s=!c,o={color:c?ns(o.color):null,colorLess:!c,pos:o.pos},o.pos<0||o.pos>1)throw new Error("Color stops positions must be between 0 and 1");if(o.pos<i)throw new Error("Color stops positions are not ordered");i=o.pos}else o={color:ns(o.color!==void 0?o.color:o),pos:a/(n-1)};return o}),this.stops[0].pos!==0&&(this.stops.unshift({color:this.stops[0].color,pos:0}),n++),this.stops[n-1].pos!==1&&this.stops.push({color:this.stops[n-1].color,pos:1})}reverse(){let e=[];return this.stops.forEach(function(r){e.push({color:r.color,pos:1-r.pos})}),new Kn(e.reverse())}loop(){let e=[],r=[];return this.stops.forEach(n=>{e.push({color:n.color,pos:n.pos/2})}),this.stops.slice(0,-1).forEach(n=>{r.push({color:n.color,pos:1-n.pos/2})}),new Kn(e.concat(r.reverse()))}rgb(e){let r=yE(this.stops,e),n=[];this.stops.forEach((i,s)=>{i.colorLess&&(i.color=Rf(this.stops[s-1],this.stops[s+1],2)[1])});for(let i=0,s=this.stops.length;i<s-1;i++){let o=Rf(this.stops[i],this.stops[i+1],r[i]);n.splice(n.length,0,...o)}return n.push(this.stops[this.stops.length-1].color),n}hsv(e,r){let n=yE(this.stops,e),i=[];this.stops.forEach((s,o)=>{s.colorLess&&(s.color=gE(this.stops[o-1],this.stops[o+1],2,r)[1])});for(let s=0,o=this.stops.length;s<o-1;s++){let a=gE(this.stops[s],this.stops[s+1],n[s],r);i.splice(i.length,0,...a)}return i.push(this.stops[this.stops.length-1].color),i}css(e,r){e=e||"linear",r=r||(e==="linear"?"to right":"ellipse at center");let n=e+"-gradient("+r;return this.stops.forEach(function(i){n+=", "+(i.colorLess?"":i.color.toRgbString()+" ")+i.pos*100+"%"}),n+=")",n}rgbAt(e){return _E(this.stops,e,"toRgb",EE)}hsvAt(e){return _E(this.stops,e,"toHsv",bE)}};DE.exports=function(t){if(arguments.length===1){if(!Array.isArray(arguments[0]))throw new Error('"stops" is not an array');t=arguments[0]}else t=Array.prototype.slice.call(arguments);return new Kn(t)}});var vE=b((R5,ea)=>{"use strict";var xE=require("chalk"),GP=wE(),AE=/\s/g;function Of(...t){let e=GP.apply(this,t),r=(n,i)=>WP(n?n.toString():"",e,i);return r.multiline=(n,i)=>zP(n?n.toString():"",e,i),r}var SE=(t,e,r)=>e.interpolation.toLowerCase()==="hsv"?t.hsv(r,e.hsvSpin.toLowerCase()):t.rgb(r);function WP(t,e,r){let n=CE(r),i=Math.max(t.replace(AE,"").length,e.stops.length),s=SE(e,n,i),o="";for(let a of t)o+=a.match(AE)?a:xE.hex(s.shift().toHex())(a);return o}function zP(t,e,r){let n=CE(r),i=t.split(`
`),s=Math.max.apply(null,i.map(u=>u.length).concat([e.stops.length])),o=SE(e,n,s),a=[];for(let u of i){let c=o.slice(0),l="";for(let f of u)l+=xE.hex(c.shift().toHex())(f);a.push(l)}return a.join(`
`)}function CE(t){let e={interpolation:"rgb",hsvSpin:"short",...t};if(t!==void 0&&typeof t!="object")throw new TypeError(`Expected \`options\` to be an \`object\`, got \`${typeof t}\``);if(typeof e.interpolation!="string")throw new TypeError(`Expected \`options.interpolation\` to be a \`string\`, got \`${typeof e.interpolation}\``);if(e.interpolation.toLowerCase()==="hsv"&&typeof e.hsvSpin!="string")throw new TypeError(`Expected \`options.hsvSpin\` to be a \`string\`, got \`${typeof e.hsvSpin}\``);return e}var is={atlas:{colors:["#feac5e","#c779d0","#4bc0c8"],options:{}},cristal:{colors:["#bdfff3","#4ac29a"],options:{}},teen:{colors:["#77a1d3","#79cbca","#e684ae"],options:{}},mind:{colors:["#473b7b","#3584a7","#30d2be"],options:{}},morning:{colors:["#ff5f6d","#ffc371"],options:{interpolation:"hsv"}},vice:{colors:["#5ee7df","#b490ca"],options:{interpolation:"hsv"}},passion:{colors:["#f43b47","#453a94"],options:{}},fruit:{colors:["#ff4e50","#f9d423"],options:{}},instagram:{colors:["#833ab4","#fd1d1d","#fcb045"],options:{}},retro:{colors:["#3f51b1","#5a55ae","#7b5fac","#8f6aae","#a86aa4","#cc6b8e","#f18271","#f3a469","#f7c978"],options:{}},summer:{colors:["#fdbb2d","#22c1c3"],options:{}},rainbow:{colors:["#ff0000","#ff0100"],options:{interpolation:"hsv",hsvSpin:"long"}},pastel:{colors:["#74ebd5","#74ecd5"],options:{interpolation:"hsv",hsvSpin:"long"}}};ea.exports=Of;for(let t in is)ea.exports[t]=e=>new Of(is[t].colors)(e,is[t].options),ea.exports[t].multiline=e=>new Of(is[t].colors).multiline(e,is[t].options)});var qE=b((P5,ME)=>{ME.exports=NE;NE.sync=i2;var IE=require("fs");function n2(t,e){var r=e.pathExt!==void 0?e.pathExt:process.env.PATHEXT;if(!r||(r=r.split(";"),r.indexOf("")!==-1))return!0;for(var n=0;n<r.length;n++){var i=r[n].toLowerCase();if(i&&t.substr(-i.length).toLowerCase()===i)return!0}return!1}function LE(t,e,r){return!t.isSymbolicLink()&&!t.isFile()?!1:n2(e,r)}function NE(t,e,r){IE.stat(t,function(n,i){r(n,n?!1:LE(i,t,e))})}function i2(t,e){return LE(IE.statSync(t),t,e)}});var GE=b((B5,UE)=>{UE.exports=jE;jE.sync=s2;var HE=require("fs");function jE(t,e,r){HE.stat(t,function(n,i){r(n,n?!1:$E(i,e))})}function s2(t,e){return $E(HE.statSync(t),e)}function $E(t,e){return t.isFile()&&o2(t,e)}function o2(t,e){var r=t.mode,n=t.uid,i=t.gid,s=e.uid!==void 0?e.uid:process.getuid&&process.getuid(),o=e.gid!==void 0?e.gid:process.getgid&&process.getgid(),a=parseInt("100",8),u=parseInt("010",8),c=parseInt("001",8),l=a|u,f=r&c||r&u&&i===o||r&a&&n===s||r&l&&s===0;return f}});var zE=b((L5,WE)=>{var I5=require("fs"),ta;process.platform==="win32"||global.TESTING_WINDOWS?ta=qE():ta=GE();WE.exports=Lf;Lf.sync=a2;function Lf(t,e,r){if(typeof e=="function"&&(r=e,e={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(n,i){Lf(t,e||{},function(s,o){s?i(s):n(o)})})}ta(t,e||{},function(n,i){n&&(n.code==="EACCES"||e&&e.ignoreErrors)&&(n=null,i=!1),r(n,i)})}function a2(t,e){try{return ta.sync(t,e||{})}catch(r){if(e&&e.ignoreErrors||r.code==="EACCES")return!1;throw r}}});var QE=b((N5,ZE)=>{var Xn=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",VE=require("path"),u2=Xn?";":":",YE=zE(),KE=t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),JE=(t,e)=>{let r=e.colon||u2,n=t.match(/\//)||Xn&&t.match(/\\/)?[""]:[...Xn?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(r)],i=Xn?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",s=Xn?i.split(r):[""];return Xn&&t.indexOf(".")!==-1&&s[0]!==""&&s.unshift(""),{pathEnv:n,pathExt:s,pathExtExe:i}},XE=(t,e,r)=>{typeof e=="function"&&(r=e,e={}),e||(e={});let{pathEnv:n,pathExt:i,pathExtExe:s}=JE(t,e),o=[],a=c=>new Promise((l,f)=>{if(c===n.length)return e.all&&o.length?l(o):f(KE(t));let h=n[c],m=/^".*"$/.test(h)?h.slice(1,-1):h,_=VE.join(m,t),y=!m&&/^\.[\\\/]/.test(t)?t.slice(0,2)+_:_;l(u(y,c,0))}),u=(c,l,f)=>new Promise((h,m)=>{if(f===i.length)return h(a(l+1));let _=i[f];YE(c+_,{pathExt:s},(y,p)=>{if(!y&&p)if(e.all)o.push(c+_);else return h(c+_);return h(u(c,l,f+1))})});return r?a(0).then(c=>r(null,c),r):a(0)},c2=(t,e)=>{e=e||{};let{pathEnv:r,pathExt:n,pathExtExe:i}=JE(t,e),s=[];for(let o=0;o<r.length;o++){let a=r[o],u=/^".*"$/.test(a)?a.slice(1,-1):a,c=VE.join(u,t),l=!u&&/^\.[\\\/]/.test(t)?t.slice(0,2)+c:c;for(let f=0;f<n.length;f++){let h=l+n[f];try{if(YE.sync(h,{pathExt:i}))if(e.all)s.push(h);else return h}catch{}}}if(e.all&&s.length)return s;if(e.nothrow)return null;throw KE(t)};ZE.exports=XE;XE.sync=c2});var Mf=b((M5,Nf)=>{"use strict";var eb=(t={})=>{let e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(n=>n.toUpperCase()==="PATH")||"Path"};Nf.exports=eb;Nf.exports.default=eb});var ib=b((q5,nb)=>{"use strict";var tb=require("path"),l2=QE(),f2=Mf();function rb(t,e){let r=t.options.env||process.env,n=process.cwd(),i=t.options.cwd!=null,s=i&&process.chdir!==void 0&&!process.chdir.disabled;if(s)try{process.chdir(t.options.cwd)}catch{}let o;try{o=l2.sync(t.command,{path:r[f2({env:r})],pathExt:e?tb.delimiter:void 0})}catch{}finally{s&&process.chdir(n)}return o&&(o=tb.resolve(i?t.options.cwd:"",o)),o}function h2(t){return rb(t)||rb(t,!0)}nb.exports=h2});var sb=b((H5,Hf)=>{"use strict";var qf=/([()\][%!^"`<>&|;, *?])/g;function d2(t){return t=t.replace(qf,"^$1"),t}function p2(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(qf,"^$1"),e&&(t=t.replace(qf,"^$1")),t}Hf.exports.command=d2;Hf.exports.argument=p2});var ab=b((j5,ob)=>{"use strict";ob.exports=/^#!(.*)/});var cb=b(($5,ub)=>{"use strict";var m2=ab();ub.exports=(t="")=>{let e=t.match(m2);if(!e)return null;let[r,n]=e[0].replace(/#! ?/,"").split(" "),i=r.split("/").pop();return i==="env"?n:n?`${i} ${n}`:i}});var fb=b((U5,lb)=>{"use strict";var jf=require("fs"),g2=cb();function y2(t){let r=Buffer.alloc(150),n;try{n=jf.openSync(t,"r"),jf.readSync(n,r,0,150,0),jf.closeSync(n)}catch{}return g2(r.toString())}lb.exports=y2});var mb=b((G5,pb)=>{"use strict";var _2=require("path"),hb=ib(),db=sb(),E2=fb(),b2=process.platform==="win32",D2=/\.(?:com|exe)$/i,w2=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function A2(t){t.file=hb(t);let e=t.file&&E2(t.file);return e?(t.args.unshift(t.file),t.command=e,hb(t)):t.file}function x2(t){if(!b2)return t;let e=A2(t),r=!D2.test(e);if(t.options.forceShell||r){let n=w2.test(e);t.command=_2.normalize(t.command),t.command=db.command(t.command),t.args=t.args.map(s=>db.argument(s,n));let i=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${i}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}function S2(t,e,r){e&&!Array.isArray(e)&&(r=e,e=null),e=e?e.slice(0):[],r=Object.assign({},r);let n={command:t,args:e,options:r,file:void 0,original:{command:t,args:e}};return r.shell?n:x2(n)}pb.exports=S2});var _b=b((W5,yb)=>{"use strict";var $f=process.platform==="win32";function Uf(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}function C2(t,e){if(!$f)return;let r=t.emit;t.emit=function(n,i){if(n==="exit"){let s=gb(i,e,"spawn");if(s)return r.call(t,"error",s)}return r.apply(t,arguments)}}function gb(t,e){return $f&&t===1&&!e.file?Uf(e.original,"spawn"):null}function v2(t,e){return $f&&t===1&&!e.file?Uf(e.original,"spawnSync"):null}yb.exports={hookChildProcess:C2,verifyENOENT:gb,verifyENOENTSync:v2,notFoundError:Uf}});var Db=b((z5,Zn)=>{"use strict";var Eb=require("child_process"),Gf=mb(),Wf=_b();function bb(t,e,r){let n=Gf(t,e,r),i=Eb.spawn(n.command,n.args,n.options);return Wf.hookChildProcess(i,n),i}function R2(t,e,r){let n=Gf(t,e,r),i=Eb.spawnSync(n.command,n.args,n.options);return i.error=i.error||Wf.verifyENOENTSync(i.status,n),i}Zn.exports=bb;Zn.exports.spawn=bb;Zn.exports.sync=R2;Zn.exports._parse=Gf;Zn.exports._enoent=Wf});var Ab=b((V5,wb)=>{"use strict";wb.exports=t=>{let e=typeof t=="string"?`
`:`
`.charCodeAt(),r=typeof t=="string"?"\r":"\r".charCodeAt();return t[t.length-1]===e&&(t=t.slice(0,t.length-1)),t[t.length-1]===r&&(t=t.slice(0,t.length-1)),t}});var Cb=b((Y5,as)=>{"use strict";var os=require("path"),xb=Mf(),Sb=t=>{t={cwd:process.cwd(),path:process.env[xb()],execPath:process.execPath,...t};let e,r=os.resolve(t.cwd),n=[];for(;e!==r;)n.push(os.join(r,"node_modules/.bin")),e=r,r=os.resolve(r,"..");let i=os.resolve(t.cwd,t.execPath,"..");return n.push(i),n.concat(t.path).join(os.delimiter)};as.exports=Sb;as.exports.default=Sb;as.exports.env=t=>{t={env:process.env,...t};let e={...t.env},r=xb({env:e});return t.path=e[r],e[r]=as.exports(t),e}});var vb=b(ra=>{"use strict";Object.defineProperty(ra,"__esModule",{value:!0});ra.SIGNALS=void 0;var F2=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];ra.SIGNALS=F2});var zf=b(Qn=>{"use strict";Object.defineProperty(Qn,"__esModule",{value:!0});Qn.SIGRTMAX=Qn.getRealtimeSignals=void 0;var T2=function(){let t=Fb-Rb+1;return Array.from({length:t},O2)};Qn.getRealtimeSignals=T2;var O2=function(t,e){return{name:`SIGRT${e+1}`,number:Rb+e,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}},Rb=34,Fb=64;Qn.SIGRTMAX=Fb});var Tb=b(na=>{"use strict";Object.defineProperty(na,"__esModule",{value:!0});na.getSignals=void 0;var k2=require("os"),P2=vb(),B2=zf(),I2=function(){let t=(0,B2.getRealtimeSignals)();return[...P2.SIGNALS,...t].map(L2)};na.getSignals=I2;var L2=function({name:t,number:e,description:r,action:n,forced:i=!1,standard:s}){let{signals:{[t]:o}}=k2.constants,a=o!==void 0;return{name:t,number:a?o:e,description:r,supported:a,action:n,forced:i,standard:s}}});var kb=b(ei=>{"use strict";Object.defineProperty(ei,"__esModule",{value:!0});ei.signalsByNumber=ei.signalsByName=void 0;var N2=require("os"),Ob=Tb(),M2=zf(),q2=function(){return(0,Ob.getSignals)().reduce(H2,{})},H2=function(t,{name:e,number:r,description:n,supported:i,action:s,forced:o,standard:a}){return{...t,[e]:{name:e,number:r,description:n,supported:i,action:s,forced:o,standard:a}}},j2=q2();ei.signalsByName=j2;var $2=function(){let t=(0,Ob.getSignals)(),e=M2.SIGRTMAX+1,r=Array.from({length:e},(n,i)=>U2(i,t));return Object.assign({},...r)},U2=function(t,e){let r=G2(t,e);if(r===void 0)return{};let{name:n,description:i,supported:s,action:o,forced:a,standard:u}=r;return{[t]:{name:n,number:t,description:i,supported:s,action:o,forced:a,standard:u}}},G2=function(t,e){let r=e.find(({name:n})=>N2.constants.signals[n]===t);return r!==void 0?r:e.find(n=>n.number===t)},W2=$2();ei.signalsByNumber=W2});var Bb=b((Q5,Pb)=>{"use strict";var{signalsByName:z2}=kb(),V2=({timedOut:t,timeout:e,errorCode:r,signal:n,signalDescription:i,exitCode:s,isCanceled:o})=>t?`timed out after ${e} milliseconds`:o?"was canceled":r!==void 0?`failed with ${r}`:n!==void 0?`was killed with ${n} (${i})`:s!==void 0?`failed with exit code ${s}`:"failed",Y2=({stdout:t,stderr:e,all:r,error:n,signal:i,exitCode:s,command:o,escapedCommand:a,timedOut:u,isCanceled:c,killed:l,parsed:{options:{timeout:f}}})=>{s=s===null?void 0:s,i=i===null?void 0:i;let h=i===void 0?void 0:z2[i].description,m=n&&n.code,y=`Command ${V2({timedOut:u,timeout:f,errorCode:m,signal:i,signalDescription:h,exitCode:s,isCanceled:c})}: ${o}`,p=Object.prototype.toString.call(n)==="[object Error]",D=p?`${y}
${n.message}`:y,C=[D,e,t].filter(Boolean).join(`
`);return p?(n.originalMessage=n.message,n.message=C):n=new Error(C),n.shortMessage=D,n.command=o,n.escapedCommand=a,n.exitCode=s,n.signal=i,n.signalDescription=h,n.stdout=t,n.stderr=e,r!==void 0&&(n.all=r),"bufferedData"in n&&delete n.bufferedData,n.failed=!0,n.timedOut=!!u,n.isCanceled=c,n.killed=l&&!u,n};Pb.exports=Y2});var Lb=b((eH,Vf)=>{"use strict";var ia=["stdin","stdout","stderr"],K2=t=>ia.some(e=>t[e]!==void 0),Ib=t=>{if(!t)return;let{stdio:e}=t;if(e===void 0)return ia.map(n=>t[n]);if(K2(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${ia.map(n=>`\`${n}\``).join(", ")}`);if(typeof e=="string")return e;if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,ia.length);return Array.from({length:r},(n,i)=>e[i])};Vf.exports=Ib;Vf.exports.node=t=>{let e=Ib(t);return e==="ipc"?"ipc":e===void 0||typeof e=="string"?[e,e,e,"ipc"]:e.includes("ipc")?e:[...e,"ipc"]}});var Mb=b((tH,Nb)=>{"use strict";var J2=require("os"),X2=Df(),Z2=1e3*5,Q2=(t,e="SIGTERM",r={})=>{let n=t(e);return eB(t,e,r,n),n},eB=(t,e,r,n)=>{if(!tB(e,r,n))return;let i=nB(r),s=setTimeout(()=>{t("SIGKILL")},i);s.unref&&s.unref()},tB=(t,{forceKillAfterTimeout:e},r)=>rB(t)&&e!==!1&&r,rB=t=>t===J2.constants.signals.SIGTERM||typeof t=="string"&&t.toUpperCase()==="SIGTERM",nB=({forceKillAfterTimeout:t=!0})=>{if(t===!0)return Z2;if(!Number.isFinite(t)||t<0)throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);return t},iB=(t,e)=>{t.kill()&&(e.isCanceled=!0)},sB=(t,e,r)=>{t.kill(e),r(Object.assign(new Error("Timed out"),{timedOut:!0,signal:e}))},oB=(t,{timeout:e,killSignal:r="SIGTERM"},n)=>{if(e===0||e===void 0)return n;let i,s=new Promise((a,u)=>{i=setTimeout(()=>{sB(t,r,u)},e)}),o=n.finally(()=>{clearTimeout(i)});return Promise.race([s,o])},aB=({timeout:t})=>{if(t!==void 0&&(!Number.isFinite(t)||t<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`)},uB=async(t,{cleanup:e,detached:r},n)=>{if(!e||r)return n;let i=X2(()=>{t.kill()});return n.finally(()=>{i()})};Nb.exports={spawnedKill:Q2,spawnedCancel:iB,setupTimeout:oB,validateTimeout:aB,setExitHandler:uB}});var Hb=b((rH,qb)=>{"use strict";var jt=t=>t!==null&&typeof t=="object"&&typeof t.pipe=="function";jt.writable=t=>jt(t)&&t.writable!==!1&&typeof t._write=="function"&&typeof t._writableState=="object";jt.readable=t=>jt(t)&&t.readable!==!1&&typeof t._read=="function"&&typeof t._readableState=="object";jt.duplex=t=>jt.writable(t)&&jt.readable(t);jt.transform=t=>jt.duplex(t)&&typeof t._transform=="function";qb.exports=jt});var $b=b((nH,jb)=>{"use strict";var{PassThrough:cB}=require("stream");jb.exports=t=>{t={...t};let{array:e}=t,{encoding:r}=t,n=r==="buffer",i=!1;e?i=!(r||n):r=r||"utf8",n&&(r=null);let s=new cB({objectMode:i});r&&s.setEncoding(r);let o=0,a=[];return s.on("data",u=>{a.push(u),i?o=a.length:o+=u.length}),s.getBufferedValue=()=>e?a:n?Buffer.concat(a,o):a.join(""),s.getBufferedLength=()=>o,s}});var Ub=b((iH,us)=>{"use strict";var{constants:lB}=require("buffer"),fB=require("stream"),{promisify:hB}=require("util"),dB=$b(),pB=hB(fB.pipeline),sa=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};async function Yf(t,e){if(!t)throw new Error("Expected a stream");e={maxBuffer:1/0,...e};let{maxBuffer:r}=e,n=dB(e);return await new Promise((i,s)=>{let o=a=>{a&&n.getBufferedLength()<=lB.MAX_LENGTH&&(a.bufferedData=n.getBufferedValue()),s(a)};(async()=>{try{await pB(t,n),i()}catch(a){o(a)}})(),n.on("data",()=>{n.getBufferedLength()>r&&o(new sa)})}),n.getBufferedValue()}us.exports=Yf;us.exports.buffer=(t,e)=>Yf(t,{...e,encoding:"buffer"});us.exports.array=(t,e)=>Yf(t,{...e,array:!0});us.exports.MaxBufferError=sa});var Wb=b((sH,Gb)=>{"use strict";var{PassThrough:mB}=require("stream");Gb.exports=function(){var t=[],e=new mB({objectMode:!0});return e.setMaxListeners(0),e.add=r,e.isEmpty=n,e.on("unpipe",i),Array.prototype.slice.call(arguments).forEach(r),e;function r(s){return Array.isArray(s)?(s.forEach(r),this):(t.push(s),s.once("end",i.bind(null,s)),s.once("error",e.emit.bind(e,"error")),s.pipe(e,{end:!1}),this)}function n(){return t.length==0}function i(s){t=t.filter(function(o){return o!==s}),!t.length&&e.readable&&e.end()}}});var Kb=b((oH,Yb)=>{"use strict";var Vb=Hb(),zb=Ub(),gB=Wb(),yB=(t,e)=>{e===void 0||t.stdin===void 0||(Vb(e)?e.pipe(t.stdin):t.stdin.end(e))},_B=(t,{all:e})=>{if(!e||!t.stdout&&!t.stderr)return;let r=gB();return t.stdout&&r.add(t.stdout),t.stderr&&r.add(t.stderr),r},Kf=async(t,e)=>{if(t){t.destroy();try{return await e}catch(r){return r.bufferedData}}},Jf=(t,{encoding:e,buffer:r,maxBuffer:n})=>{if(!(!t||!r))return e?zb(t,{encoding:e,maxBuffer:n}):zb.buffer(t,{maxBuffer:n})},EB=async({stdout:t,stderr:e,all:r},{encoding:n,buffer:i,maxBuffer:s},o)=>{let a=Jf(t,{encoding:n,buffer:i,maxBuffer:s}),u=Jf(e,{encoding:n,buffer:i,maxBuffer:s}),c=Jf(r,{encoding:n,buffer:i,maxBuffer:s*2});try{return await Promise.all([o,a,u,c])}catch(l){return Promise.all([{error:l,signal:l.signal,timedOut:l.timedOut},Kf(t,a),Kf(e,u),Kf(r,c)])}},bB=({input:t})=>{if(Vb(t))throw new TypeError("The `input` option cannot be a stream in sync mode")};Yb.exports={handleInput:yB,makeAllStream:_B,getSpawnedResult:EB,validateInputSync:bB}});var Xb=b((aH,Jb)=>{"use strict";var DB=(async()=>{})().constructor.prototype,wB=["then","catch","finally"].map(t=>[t,Reflect.getOwnPropertyDescriptor(DB,t)]),AB=(t,e)=>{for(let[r,n]of wB){let i=typeof e=="function"?(...s)=>Reflect.apply(n.value,e(),s):n.value.bind(e);Reflect.defineProperty(t,r,{...n,value:i})}return t},xB=t=>new Promise((e,r)=>{t.on("exit",(n,i)=>{e({exitCode:n,signal:i})}),t.on("error",n=>{r(n)}),t.stdin&&t.stdin.on("error",n=>{r(n)})});Jb.exports={mergePromise:AB,getSpawnedPromise:xB}});var eD=b((uH,Qb)=>{"use strict";var Zb=(t,e=[])=>Array.isArray(e)?[t,...e]:[t],SB=/^[\w.-]+$/,CB=/"/g,vB=t=>typeof t!="string"||SB.test(t)?t:`"${t.replace(CB,'\\"')}"`,RB=(t,e)=>Zb(t,e).join(" "),FB=(t,e)=>Zb(t,e).map(r=>vB(r)).join(" "),TB=/ +/g,OB=t=>{let e=[];for(let r of t.trim().split(TB)){let n=e[e.length-1];n&&n.endsWith("\\")?e[e.length-1]=`${n.slice(0,-1)} ${r}`:e.push(r)}return e};Qb.exports={joinCommand:RB,getEscapedCommand:FB,parseCommand:OB}});var aD=b((cH,ti)=>{"use strict";var kB=require("path"),Xf=require("child_process"),PB=Db(),BB=Ab(),IB=Cb(),LB=_f(),oa=Bb(),rD=Lb(),{spawnedKill:NB,spawnedCancel:MB,setupTimeout:qB,validateTimeout:HB,setExitHandler:jB}=Mb(),{handleInput:$B,getSpawnedResult:UB,makeAllStream:GB,validateInputSync:WB}=Kb(),{mergePromise:tD,getSpawnedPromise:zB}=Xb(),{joinCommand:nD,parseCommand:iD,getEscapedCommand:sD}=eD(),VB=1e3*1e3*100,YB=({env:t,extendEnv:e,preferLocal:r,localDir:n,execPath:i})=>{let s=e?{...process.env,...t}:t;return r?IB.env({env:s,cwd:n,execPath:i}):s},oD=(t,e,r={})=>{let n=PB._parse(t,e,r);return t=n.command,e=n.args,r=n.options,r={maxBuffer:VB,buffer:!0,stripFinalNewline:!0,extendEnv:!0,preferLocal:!1,localDir:r.cwd||process.cwd(),execPath:process.execPath,encoding:"utf8",reject:!0,cleanup:!0,all:!1,windowsHide:!0,...r},r.env=YB(r),r.stdio=rD(r),process.platform==="win32"&&kB.basename(t,".exe")==="cmd"&&e.unshift("/q"),{file:t,args:e,options:r,parsed:n}},cs=(t,e,r)=>typeof e!="string"&&!Buffer.isBuffer(e)?r===void 0?void 0:"":t.stripFinalNewline?BB(e):e,aa=(t,e,r)=>{let n=oD(t,e,r),i=nD(t,e),s=sD(t,e);HB(n.options);let o;try{o=Xf.spawn(n.file,n.args,n.options)}catch(m){let _=new Xf.ChildProcess,y=Promise.reject(oa({error:m,stdout:"",stderr:"",all:"",command:i,escapedCommand:s,parsed:n,timedOut:!1,isCanceled:!1,killed:!1}));return tD(_,y)}let a=zB(o),u=qB(o,n.options,a),c=jB(o,n.options,u),l={isCanceled:!1};o.kill=NB.bind(null,o.kill.bind(o)),o.cancel=MB.bind(null,o,l);let h=LB(async()=>{let[{error:m,exitCode:_,signal:y,timedOut:p},D,C,N]=await UB(o,n.options,c),U=cs(n.options,D),M=cs(n.options,C),Y=cs(n.options,N);if(m||_!==0||y!==null){let G=oa({error:m,exitCode:_,signal:y,stdout:U,stderr:M,all:Y,command:i,escapedCommand:s,parsed:n,timedOut:p,isCanceled:l.isCanceled,killed:o.killed});if(!n.options.reject)return G;throw G}return{command:i,escapedCommand:s,exitCode:0,stdout:U,stderr:M,all:Y,failed:!1,timedOut:!1,isCanceled:!1,killed:!1}});return $B(o,n.options.input),o.all=GB(o,n.options),tD(o,h)};ti.exports=aa;ti.exports.sync=(t,e,r)=>{let n=oD(t,e,r),i=nD(t,e),s=sD(t,e);WB(n.options);let o;try{o=Xf.spawnSync(n.file,n.args,n.options)}catch(c){throw oa({error:c,stdout:"",stderr:"",all:"",command:i,escapedCommand:s,parsed:n,timedOut:!1,isCanceled:!1,killed:!1})}let a=cs(n.options,o.stdout,o.error),u=cs(n.options,o.stderr,o.error);if(o.error||o.status!==0||o.signal!==null){let c=oa({stdout:a,stderr:u,error:o.error,signal:o.signal,exitCode:o.status,command:i,escapedCommand:s,parsed:n,timedOut:o.error&&o.error.code==="ETIMEDOUT",isCanceled:!1,killed:o.signal!==null});if(!n.options.reject)return c;throw c}return{command:i,escapedCommand:s,exitCode:0,stdout:a,stderr:u,failed:!1,timedOut:!1,isCanceled:!1,killed:!1}};ti.exports.command=(t,e)=>{let[r,...n]=iD(t);return aa(r,n,e)};ti.exports.commandSync=(t,e)=>{let[r,...n]=iD(t);return aa.sync(r,n,e)};ti.exports.node=(t,e,r={})=>{e&&!Array.isArray(e)&&typeof e=="object"&&(r=e,e=[]);let n=rD.node(r),i=process.execArgv.filter(a=>!a.startsWith("--inspect")),{nodePath:s=process.execPath,nodeOptions:o=i}=r;return aa(s,[...o,t,...Array.isArray(e)?e:[]],{...r,stdin:void 0,stdout:void 0,stderr:void 0,stdio:n,shell:!1})}});var ri=b((pH,lD)=>{"use strict";var cD=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);lD.exports=t=>t?Object.keys(t).map(e=>[cD.has(e)?cD.get(e):e,t[e]]).reduce((e,r)=>(e[r[0]]=r[1],e),Object.create(null)):{}});var pa=b((mH,ED)=>{"use strict";var fD=typeof process=="object"&&process?process:{stdout:null,stderr:null},ZB=require("events"),hD=require("stream"),dD=require("string_decoder").StringDecoder,or=Symbol("EOF"),ar=Symbol("maybeEmitEnd"),Lr=Symbol("emittedEnd"),ca=Symbol("emittingEnd"),fs=Symbol("emittedError"),la=Symbol("closed"),pD=Symbol("read"),fa=Symbol("flush"),mD=Symbol("flushChunk"),rt=Symbol("encoding"),ur=Symbol("decoder"),ha=Symbol("flowing"),hs=Symbol("paused"),ni=Symbol("resume"),Se=Symbol("buffer"),$t=Symbol("pipes"),Be=Symbol("bufferLength"),th=Symbol("bufferPush"),rh=Symbol("bufferShift"),Ue=Symbol("objectMode"),Ge=Symbol("destroyed"),nh=Symbol("emitData"),gD=Symbol("emitEnd"),ih=Symbol("emitEnd2"),cr=Symbol("async"),ds=t=>Promise.resolve().then(t),yD=global._MP_NO_ITERATOR_SYMBOLS_!=="1",QB=yD&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),eI=yD&&Symbol.iterator||Symbol("iterator not implemented"),tI=t=>t==="end"||t==="finish"||t==="prefinish",rI=t=>t instanceof ArrayBuffer||typeof t=="object"&&t.constructor&&t.constructor.name==="ArrayBuffer"&&t.byteLength>=0,nI=t=>!Buffer.isBuffer(t)&&ArrayBuffer.isView(t),da=class{constructor(e,r,n){this.src=e,this.dest=r,this.opts=n,this.ondrain=()=>e[ni](),r.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},sh=class extends da{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,r,n){super(e,r,n),this.proxyErrors=i=>r.emit("error",i),e.on("error",this.proxyErrors)}};ED.exports=class _D extends hD{constructor(e){super(),this[ha]=!1,this[hs]=!1,this[$t]=[],this[Se]=[],this[Ue]=e&&e.objectMode||!1,this[Ue]?this[rt]=null:this[rt]=e&&e.encoding||null,this[rt]==="buffer"&&(this[rt]=null),this[cr]=e&&!!e.async||!1,this[ur]=this[rt]?new dD(this[rt]):null,this[or]=!1,this[Lr]=!1,this[ca]=!1,this[la]=!1,this[fs]=null,this.writable=!0,this.readable=!0,this[Be]=0,this[Ge]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[Se]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[$t]})}get bufferLength(){return this[Be]}get encoding(){return this[rt]}set encoding(e){if(this[Ue])throw new Error("cannot set encoding in objectMode");if(this[rt]&&e!==this[rt]&&(this[ur]&&this[ur].lastNeed||this[Be]))throw new Error("cannot change encoding");this[rt]!==e&&(this[ur]=e?new dD(e):null,this[Se].length&&(this[Se]=this[Se].map(r=>this[ur].write(r)))),this[rt]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[Ue]}set objectMode(e){this[Ue]=this[Ue]||!!e}get async(){return this[cr]}set async(e){this[cr]=this[cr]||!!e}write(e,r,n){if(this[or])throw new Error("write after end");if(this[Ge])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof r=="function"&&(n=r,r="utf8"),r||(r="utf8");let i=this[cr]?ds:s=>s();return!this[Ue]&&!Buffer.isBuffer(e)&&(nI(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):rI(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[Ue]?(this.flowing&&this[Be]!==0&&this[fa](!0),this.flowing?this.emit("data",e):this[th](e),this[Be]!==0&&this.emit("readable"),n&&i(n),this.flowing):e.length?(typeof e=="string"&&!(r===this[rt]&&!this[ur].lastNeed)&&(e=Buffer.from(e,r)),Buffer.isBuffer(e)&&this[rt]&&(e=this[ur].write(e)),this.flowing&&this[Be]!==0&&this[fa](!0),this.flowing?this.emit("data",e):this[th](e),this[Be]!==0&&this.emit("readable"),n&&i(n),this.flowing):(this[Be]!==0&&this.emit("readable"),n&&i(n),this.flowing)}read(e){if(this[Ge])return null;if(this[Be]===0||e===0||e>this[Be])return this[ar](),null;this[Ue]&&(e=null),this[Se].length>1&&!this[Ue]&&(this.encoding?this[Se]=[this[Se].join("")]:this[Se]=[Buffer.concat(this[Se],this[Be])]);let r=this[pD](e||null,this[Se][0]);return this[ar](),r}[pD](e,r){return e===r.length||e===null?this[rh]():(this[Se][0]=r.slice(e),r=r.slice(0,e),this[Be]-=e),this.emit("data",r),!this[Se].length&&!this[or]&&this.emit("drain"),r}end(e,r,n){return typeof e=="function"&&(n=e,e=null),typeof r=="function"&&(n=r,r="utf8"),e&&this.write(e,r),n&&this.once("end",n),this[or]=!0,this.writable=!1,(this.flowing||!this[hs])&&this[ar](),this}[ni](){this[Ge]||(this[hs]=!1,this[ha]=!0,this.emit("resume"),this[Se].length?this[fa]():this[or]?this[ar]():this.emit("drain"))}resume(){return this[ni]()}pause(){this[ha]=!1,this[hs]=!0}get destroyed(){return this[Ge]}get flowing(){return this[ha]}get paused(){return this[hs]}[th](e){this[Ue]?this[Be]+=1:this[Be]+=e.length,this[Se].push(e)}[rh](){return this[Se].length&&(this[Ue]?this[Be]-=1:this[Be]-=this[Se][0].length),this[Se].shift()}[fa](e){do;while(this[mD](this[rh]()));!e&&!this[Se].length&&!this[or]&&this.emit("drain")}[mD](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,r){if(this[Ge])return;let n=this[Lr];return r=r||{},e===fD.stdout||e===fD.stderr?r.end=!1:r.end=r.end!==!1,r.proxyErrors=!!r.proxyErrors,n?r.end&&e.end():(this[$t].push(r.proxyErrors?new sh(this,e,r):new da(this,e,r)),this[cr]?ds(()=>this[ni]()):this[ni]()),e}unpipe(e){let r=this[$t].find(n=>n.dest===e);r&&(this[$t].splice(this[$t].indexOf(r),1),r.unpipe())}addListener(e,r){return this.on(e,r)}on(e,r){let n=super.on(e,r);return e==="data"&&!this[$t].length&&!this.flowing?this[ni]():e==="readable"&&this[Be]!==0?super.emit("readable"):tI(e)&&this[Lr]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[fs]&&(this[cr]?ds(()=>r.call(this,this[fs])):r.call(this,this[fs])),n}get emittedEnd(){return this[Lr]}[ar](){!this[ca]&&!this[Lr]&&!this[Ge]&&this[Se].length===0&&this[or]&&(this[ca]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[la]&&this.emit("close"),this[ca]=!1)}emit(e,r,...n){if(e!=="error"&&e!=="close"&&e!==Ge&&this[Ge])return;if(e==="data")return r?this[cr]?ds(()=>this[nh](r)):this[nh](r):!1;if(e==="end")return this[gD]();if(e==="close"){if(this[la]=!0,!this[Lr]&&!this[Ge])return;let s=super.emit("close");return this.removeAllListeners("close"),s}else if(e==="error"){this[fs]=r;let s=super.emit("error",r);return this[ar](),s}else if(e==="resume"){let s=super.emit("resume");return this[ar](),s}else if(e==="finish"||e==="prefinish"){let s=super.emit(e);return this.removeAllListeners(e),s}let i=super.emit(e,r,...n);return this[ar](),i}[nh](e){for(let n of this[$t])n.dest.write(e)===!1&&this.pause();let r=super.emit("data",e);return this[ar](),r}[gD](){this[Lr]||(this[Lr]=!0,this.readable=!1,this[cr]?ds(()=>this[ih]()):this[ih]())}[ih](){if(this[ur]){let r=this[ur].end();if(r){for(let n of this[$t])n.dest.write(r);super.emit("data",r)}}for(let r of this[$t])r.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[Ue]||(e.dataLength=0);let r=this.promise();return this.on("data",n=>{e.push(n),this[Ue]||(e.dataLength+=n.length)}),r.then(()=>e)}concat(){return this[Ue]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[Ue]?Promise.reject(new Error("cannot concat in objectMode")):this[rt]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,r)=>{this.on(Ge,()=>r(new Error("stream destroyed"))),this.on("error",n=>r(n)),this.on("end",()=>e())})}[QB](){return{next:()=>{let r=this.read();if(r!==null)return Promise.resolve({done:!1,value:r});if(this[or])return Promise.resolve({done:!0});let n=null,i=null,s=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",s),this.removeListener("end",a),this.pause(),n({value:c,done:!!this[or]})},a=()=>{this.removeListener("error",s),this.removeListener("data",o),n({done:!0})},u=()=>s(new Error("stream destroyed"));return new Promise((c,l)=>{i=l,n=c,this.once(Ge,u),this.once("error",s),this.once("end",a),this.once("data",o)})}}}[eI](){return{next:()=>{let r=this.read();return{value:r,done:r===null}}}}destroy(e){return this[Ge]?(e?this.emit("error",e):this.emit(Ge),this):(this[Ge]=!0,this[Se].length=0,this[Be]=0,typeof this.close=="function"&&!this[la]&&this.close(),e?this.emit("error",e):this.emit(Ge),this)}static isStream(e){return!!e&&(e instanceof _D||e instanceof hD||e instanceof ZB&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var DD=b((gH,bD)=>{var iI=require("zlib").constants||{ZLIB_VERNUM:4736};bD.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},iI))});var fh=b((yH,TD)=>{"use strict";var wD=typeof process=="object"&&process?process:{stdout:null,stderr:null},sI=require("events"),AD=require("stream"),xD=require("string_decoder").StringDecoder,lr=Symbol("EOF"),fr=Symbol("maybeEmitEnd"),Nr=Symbol("emittedEnd"),ma=Symbol("emittingEnd"),ps=Symbol("emittedError"),ga=Symbol("closed"),SD=Symbol("read"),ya=Symbol("flush"),CD=Symbol("flushChunk"),nt=Symbol("encoding"),hr=Symbol("decoder"),_a=Symbol("flowing"),ms=Symbol("paused"),ii=Symbol("resume"),Ie=Symbol("bufferLength"),oh=Symbol("bufferPush"),ah=Symbol("bufferShift"),We=Symbol("objectMode"),ze=Symbol("destroyed"),uh=Symbol("emitData"),vD=Symbol("emitEnd"),ch=Symbol("emitEnd2"),dr=Symbol("async"),gs=t=>Promise.resolve().then(t),RD=global._MP_NO_ITERATOR_SYMBOLS_!=="1",oI=RD&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),aI=RD&&Symbol.iterator||Symbol("iterator not implemented"),uI=t=>t==="end"||t==="finish"||t==="prefinish",cI=t=>t instanceof ArrayBuffer||typeof t=="object"&&t.constructor&&t.constructor.name==="ArrayBuffer"&&t.byteLength>=0,lI=t=>!Buffer.isBuffer(t)&&ArrayBuffer.isView(t),Ea=class{constructor(e,r,n){this.src=e,this.dest=r,this.opts=n,this.ondrain=()=>e[ii](),r.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},lh=class extends Ea{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,r,n){super(e,r,n),this.proxyErrors=i=>r.emit("error",i),e.on("error",this.proxyErrors)}};TD.exports=class FD extends AD{constructor(e){super(),this[_a]=!1,this[ms]=!1,this.pipes=[],this.buffer=[],this[We]=e&&e.objectMode||!1,this[We]?this[nt]=null:this[nt]=e&&e.encoding||null,this[nt]==="buffer"&&(this[nt]=null),this[dr]=e&&!!e.async||!1,this[hr]=this[nt]?new xD(this[nt]):null,this[lr]=!1,this[Nr]=!1,this[ma]=!1,this[ga]=!1,this[ps]=null,this.writable=!0,this.readable=!0,this[Ie]=0,this[ze]=!1}get bufferLength(){return this[Ie]}get encoding(){return this[nt]}set encoding(e){if(this[We])throw new Error("cannot set encoding in objectMode");if(this[nt]&&e!==this[nt]&&(this[hr]&&this[hr].lastNeed||this[Ie]))throw new Error("cannot change encoding");this[nt]!==e&&(this[hr]=e?new xD(e):null,this.buffer.length&&(this.buffer=this.buffer.map(r=>this[hr].write(r)))),this[nt]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[We]}set objectMode(e){this[We]=this[We]||!!e}get async(){return this[dr]}set async(e){this[dr]=this[dr]||!!e}write(e,r,n){if(this[lr])throw new Error("write after end");if(this[ze])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof r=="function"&&(n=r,r="utf8"),r||(r="utf8");let i=this[dr]?gs:s=>s();return!this[We]&&!Buffer.isBuffer(e)&&(lI(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):cI(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[We]?(this.flowing&&this[Ie]!==0&&this[ya](!0),this.flowing?this.emit("data",e):this[oh](e),this[Ie]!==0&&this.emit("readable"),n&&i(n),this.flowing):e.length?(typeof e=="string"&&!(r===this[nt]&&!this[hr].lastNeed)&&(e=Buffer.from(e,r)),Buffer.isBuffer(e)&&this[nt]&&(e=this[hr].write(e)),this.flowing&&this[Ie]!==0&&this[ya](!0),this.flowing?this.emit("data",e):this[oh](e),this[Ie]!==0&&this.emit("readable"),n&&i(n),this.flowing):(this[Ie]!==0&&this.emit("readable"),n&&i(n),this.flowing)}read(e){if(this[ze])return null;if(this[Ie]===0||e===0||e>this[Ie])return this[fr](),null;this[We]&&(e=null),this.buffer.length>1&&!this[We]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[Ie])]);let r=this[SD](e||null,this.buffer[0]);return this[fr](),r}[SD](e,r){return e===r.length||e===null?this[ah]():(this.buffer[0]=r.slice(e),r=r.slice(0,e),this[Ie]-=e),this.emit("data",r),!this.buffer.length&&!this[lr]&&this.emit("drain"),r}end(e,r,n){return typeof e=="function"&&(n=e,e=null),typeof r=="function"&&(n=r,r="utf8"),e&&this.write(e,r),n&&this.once("end",n),this[lr]=!0,this.writable=!1,(this.flowing||!this[ms])&&this[fr](),this}[ii](){this[ze]||(this[ms]=!1,this[_a]=!0,this.emit("resume"),this.buffer.length?this[ya]():this[lr]?this[fr]():this.emit("drain"))}resume(){return this[ii]()}pause(){this[_a]=!1,this[ms]=!0}get destroyed(){return this[ze]}get flowing(){return this[_a]}get paused(){return this[ms]}[oh](e){this[We]?this[Ie]+=1:this[Ie]+=e.length,this.buffer.push(e)}[ah](){return this.buffer.length&&(this[We]?this[Ie]-=1:this[Ie]-=this.buffer[0].length),this.buffer.shift()}[ya](e){do;while(this[CD](this[ah]()));!e&&!this.buffer.length&&!this[lr]&&this.emit("drain")}[CD](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,r){if(this[ze])return;let n=this[Nr];return r=r||{},e===wD.stdout||e===wD.stderr?r.end=!1:r.end=r.end!==!1,r.proxyErrors=!!r.proxyErrors,n?r.end&&e.end():(this.pipes.push(r.proxyErrors?new lh(this,e,r):new Ea(this,e,r)),this[dr]?gs(()=>this[ii]()):this[ii]()),e}unpipe(e){let r=this.pipes.find(n=>n.dest===e);r&&(this.pipes.splice(this.pipes.indexOf(r),1),r.unpipe())}addListener(e,r){return this.on(e,r)}on(e,r){let n=super.on(e,r);return e==="data"&&!this.pipes.length&&!this.flowing?this[ii]():e==="readable"&&this[Ie]!==0?super.emit("readable"):uI(e)&&this[Nr]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[ps]&&(this[dr]?gs(()=>r.call(this,this[ps])):r.call(this,this[ps])),n}get emittedEnd(){return this[Nr]}[fr](){!this[ma]&&!this[Nr]&&!this[ze]&&this.buffer.length===0&&this[lr]&&(this[ma]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[ga]&&this.emit("close"),this[ma]=!1)}emit(e,r,...n){if(e!=="error"&&e!=="close"&&e!==ze&&this[ze])return;if(e==="data")return r?this[dr]?gs(()=>this[uh](r)):this[uh](r):!1;if(e==="end")return this[vD]();if(e==="close"){if(this[ga]=!0,!this[Nr]&&!this[ze])return;let s=super.emit("close");return this.removeAllListeners("close"),s}else if(e==="error"){this[ps]=r;let s=super.emit("error",r);return this[fr](),s}else if(e==="resume"){let s=super.emit("resume");return this[fr](),s}else if(e==="finish"||e==="prefinish"){let s=super.emit(e);return this.removeAllListeners(e),s}let i=super.emit(e,r,...n);return this[fr](),i}[uh](e){for(let n of this.pipes)n.dest.write(e)===!1&&this.pause();let r=super.emit("data",e);return this[fr](),r}[vD](){this[Nr]||(this[Nr]=!0,this.readable=!1,this[dr]?gs(()=>this[ch]()):this[ch]())}[ch](){if(this[hr]){let r=this[hr].end();if(r){for(let n of this.pipes)n.dest.write(r);super.emit("data",r)}}for(let r of this.pipes)r.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[We]||(e.dataLength=0);let r=this.promise();return this.on("data",n=>{e.push(n),this[We]||(e.dataLength+=n.length)}),r.then(()=>e)}concat(){return this[We]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[We]?Promise.reject(new Error("cannot concat in objectMode")):this[nt]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,r)=>{this.on(ze,()=>r(new Error("stream destroyed"))),this.on("error",n=>r(n)),this.on("end",()=>e())})}[oI](){return{next:()=>{let r=this.read();if(r!==null)return Promise.resolve({done:!1,value:r});if(this[lr])return Promise.resolve({done:!0});let n=null,i=null,s=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",s),this.removeListener("end",a),this.pause(),n({value:c,done:!!this[lr]})},a=()=>{this.removeListener("error",s),this.removeListener("data",o),n({done:!0})},u=()=>s(new Error("stream destroyed"));return new Promise((c,l)=>{i=l,n=c,this.once(ze,u),this.once("error",s),this.once("end",a),this.once("data",o)})}}}[aI](){return{next:()=>{let r=this.read();return{value:r,done:r===null}}}}destroy(e){return this[ze]?(e?this.emit("error",e):this.emit(ze),this):(this[ze]=!0,this.buffer.length=0,this[Ie]=0,typeof this.close=="function"&&!this[ga]&&this.close(),e?this.emit("error",e):this.emit(ze),this)}static isStream(e){return!!e&&(e instanceof FD||e instanceof AD||e instanceof sI&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var vh=b(ct=>{"use strict";var gh=require("assert"),Mr=require("buffer").Buffer,PD=require("zlib"),dn=ct.constants=DD(),fI=fh(),OD=Mr.concat,pn=Symbol("_superWrite"),oi=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},hI=Symbol("opts"),ys=Symbol("flushFlag"),kD=Symbol("finishFlushFlag"),Ch=Symbol("fullFlushFlag"),he=Symbol("handle"),ba=Symbol("onError"),si=Symbol("sawError"),hh=Symbol("level"),dh=Symbol("strategy"),ph=Symbol("ended"),_H=Symbol("_defaultFullFlush"),Da=class extends fI{constructor(e,r){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[si]=!1,this[ph]=!1,this[hI]=e,this[ys]=e.flush,this[kD]=e.finishFlush;try{this[he]=new PD[r](e)}catch(n){throw new oi(n)}this[ba]=n=>{this[si]||(this[si]=!0,this.close(),this.emit("error",n))},this[he].on("error",n=>this[ba](new oi(n))),this.once("end",()=>this.close)}close(){this[he]&&(this[he].close(),this[he]=null,this.emit("close"))}reset(){if(!this[si])return gh(this[he],"zlib binding closed"),this[he].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[Ch]),this.write(Object.assign(Mr.alloc(0),{[ys]:e})))}end(e,r,n){return e&&this.write(e,r),this.flush(this[kD]),this[ph]=!0,super.end(null,null,n)}get ended(){return this[ph]}write(e,r,n){if(typeof r=="function"&&(n=r,r="utf8"),typeof e=="string"&&(e=Mr.from(e,r)),this[si])return;gh(this[he],"zlib binding closed");let i=this[he]._handle,s=i.close;i.close=()=>{};let o=this[he].close;this[he].close=()=>{},Mr.concat=c=>c;let a;try{let c=typeof e[ys]=="number"?e[ys]:this[ys];a=this[he]._processChunk(e,c),Mr.concat=OD}catch(c){Mr.concat=OD,this[ba](new oi(c))}finally{this[he]&&(this[he]._handle=i,i.close=s,this[he].close=o,this[he].removeAllListeners("error"))}this[he]&&this[he].on("error",c=>this[ba](new oi(c)));let u;if(a)if(Array.isArray(a)&&a.length>0){u=this[pn](Mr.from(a[0]));for(let c=1;c<a.length;c++)u=this[pn](a[c])}else u=this[pn](Mr.from(a));return n&&n(),u}[pn](e){return super.write(e)}},pr=class extends Da{constructor(e,r){e=e||{},e.flush=e.flush||dn.Z_NO_FLUSH,e.finishFlush=e.finishFlush||dn.Z_FINISH,super(e,r),this[Ch]=dn.Z_FULL_FLUSH,this[hh]=e.level,this[dh]=e.strategy}params(e,r){if(!this[si]){if(!this[he])throw new Error("cannot switch params when binding is closed");if(!this[he].params)throw new Error("not supported in this implementation");if(this[hh]!==e||this[dh]!==r){this.flush(dn.Z_SYNC_FLUSH),gh(this[he],"zlib binding closed");let n=this[he].flush;this[he].flush=(i,s)=>{this.flush(i),s()};try{this[he].params(e,r)}finally{this[he].flush=n}this[he]&&(this[hh]=e,this[dh]=r)}}}},yh=class extends pr{constructor(e){super(e,"Deflate")}},_h=class extends pr{constructor(e){super(e,"Inflate")}},mh=Symbol("_portable"),Eh=class extends pr{constructor(e){super(e,"Gzip"),this[mh]=e&&!!e.portable}[pn](e){return this[mh]?(this[mh]=!1,e[9]=255,super[pn](e)):super[pn](e)}},bh=class extends pr{constructor(e){super(e,"Gunzip")}},Dh=class extends pr{constructor(e){super(e,"DeflateRaw")}},wh=class extends pr{constructor(e){super(e,"InflateRaw")}},Ah=class extends pr{constructor(e){super(e,"Unzip")}},wa=class extends Da{constructor(e,r){e=e||{},e.flush=e.flush||dn.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||dn.BROTLI_OPERATION_FINISH,super(e,r),this[Ch]=dn.BROTLI_OPERATION_FLUSH}},xh=class extends wa{constructor(e){super(e,"BrotliCompress")}},Sh=class extends wa{constructor(e){super(e,"BrotliDecompress")}};ct.Deflate=yh;ct.Inflate=_h;ct.Gzip=Eh;ct.Gunzip=bh;ct.DeflateRaw=Dh;ct.InflateRaw=wh;ct.Unzip=Ah;typeof PD.BrotliCompress=="function"?(ct.BrotliCompress=xh,ct.BrotliDecompress=Sh):ct.BrotliCompress=ct.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var ai=b((DH,BD)=>{var dI=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;BD.exports=dI!=="win32"?t=>t:t=>t&&t.replace(/\\/g,"/")});var Aa=b((AH,ID)=>{"use strict";var pI=pa(),Rh=ai(),Fh=Symbol("slurp");ID.exports=class extends pI{constructor(e,r,n){switch(super(),this.pause(),this.extended=r,this.globalExtended=n,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=Rh(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=Rh(e.linkpath),this.uname=e.uname,this.gname=e.gname,r&&this[Fh](r),n&&this[Fh](n,!0)}write(e){let r=e.length;if(r>this.blockRemain)throw new Error("writing more to entry than is appropriate");let n=this.remain,i=this.blockRemain;return this.remain=Math.max(0,n-r),this.blockRemain=Math.max(0,i-r),this.ignore?!0:n>=r?super.write(e):super.write(e.slice(0,n))}[Fh](e,r){for(let n in e)e[n]!==null&&e[n]!==void 0&&!(r&&n==="path")&&(this[n]=n==="path"||n==="linkpath"?Rh(e[n]):e[n])}}});var Th=b(xa=>{"use strict";xa.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);xa.code=new Map(Array.from(xa.name).map(t=>[t[1],t[0]]))});var qD=b((SH,MD)=>{"use strict";var mI=(t,e)=>{if(Number.isSafeInteger(t))t<0?yI(t,e):gI(t,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},gI=(t,e)=>{e[0]=128;for(var r=e.length;r>1;r--)e[r-1]=t&255,t=Math.floor(t/256)},yI=(t,e)=>{e[0]=255;var r=!1;t=t*-1;for(var n=e.length;n>1;n--){var i=t&255;t=Math.floor(t/256),r?e[n-1]=LD(i):i===0?e[n-1]=0:(r=!0,e[n-1]=ND(i))}},_I=t=>{let e=t[0],r=e===128?bI(t.slice(1,t.length)):e===255?EI(t):null;if(r===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(r))throw Error("parsed number outside of javascript safe integer range");return r},EI=t=>{for(var e=t.length,r=0,n=!1,i=e-1;i>-1;i--){var s=t[i],o;n?o=LD(s):s===0?o=s:(n=!0,o=ND(s)),o!==0&&(r-=o*Math.pow(256,e-i-1))}return r},bI=t=>{for(var e=t.length,r=0,n=e-1;n>-1;n--){var i=t[n];i!==0&&(r+=i*Math.pow(256,e-n-1))}return r},LD=t=>(255^t)&255,ND=t=>(255^t)+1&255;MD.exports={encode:mI,parse:_I}});var ci=b((CH,jD)=>{"use strict";var Oh=Th(),ui=require("path").posix,HD=qD(),kh=Symbol("slurp"),lt=Symbol("type"),Ih=class{constructor(e,r,n,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[lt]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,r||0,n,i):e&&this.set(e)}decode(e,r,n,i){if(r||(r=0),!e||!(e.length>=r+512))throw new Error("need 512 bytes for header");if(this.path=mn(e,r,100),this.mode=qr(e,r+100,8),this.uid=qr(e,r+108,8),this.gid=qr(e,r+116,8),this.size=qr(e,r+124,12),this.mtime=Ph(e,r+136,12),this.cksum=qr(e,r+148,12),this[kh](n),this[kh](i,!0),this[lt]=mn(e,r+156,1),this[lt]===""&&(this[lt]="0"),this[lt]==="0"&&this.path.slice(-1)==="/"&&(this[lt]="5"),this[lt]==="5"&&(this.size=0),this.linkpath=mn(e,r+157,100),e.slice(r+257,r+265).toString()==="ustar\x0000")if(this.uname=mn(e,r+265,32),this.gname=mn(e,r+297,32),this.devmaj=qr(e,r+329,8),this.devmin=qr(e,r+337,8),e[r+475]!==0){let o=mn(e,r+345,155);this.path=o+"/"+this.path}else{let o=mn(e,r+345,130);o&&(this.path=o+"/"+this.path),this.atime=Ph(e,r+476,12),this.ctime=Ph(e,r+488,12)}let s=8*32;for(let o=r;o<r+148;o++)s+=e[o];for(let o=r+156;o<r+512;o++)s+=e[o];this.cksumValid=s===this.cksum,this.cksum===null&&s===8*32&&(this.nullBlock=!0)}[kh](e,r){for(let n in e)e[n]!==null&&e[n]!==void 0&&!(r&&n==="path")&&(this[n]=e[n])}encode(e,r){if(e||(e=this.block=Buffer.alloc(512),r=0),r||(r=0),!(e.length>=r+512))throw new Error("need 512 bytes for header");let n=this.ctime||this.atime?130:155,i=DI(this.path||"",n),s=i[0],o=i[1];this.needPax=i[2],this.needPax=gn(e,r,100,s)||this.needPax,this.needPax=Hr(e,r+100,8,this.mode)||this.needPax,this.needPax=Hr(e,r+108,8,this.uid)||this.needPax,this.needPax=Hr(e,r+116,8,this.gid)||this.needPax,this.needPax=Hr(e,r+124,12,this.size)||this.needPax,this.needPax=Bh(e,r+136,12,this.mtime)||this.needPax,e[r+156]=this[lt].charCodeAt(0),this.needPax=gn(e,r+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",r+257,8),this.needPax=gn(e,r+265,32,this.uname)||this.needPax,this.needPax=gn(e,r+297,32,this.gname)||this.needPax,this.needPax=Hr(e,r+329,8,this.devmaj)||this.needPax,this.needPax=Hr(e,r+337,8,this.devmin)||this.needPax,this.needPax=gn(e,r+345,n,o)||this.needPax,e[r+475]!==0?this.needPax=gn(e,r+345,155,o)||this.needPax:(this.needPax=gn(e,r+345,130,o)||this.needPax,this.needPax=Bh(e,r+476,12,this.atime)||this.needPax,this.needPax=Bh(e,r+488,12,this.ctime)||this.needPax);let a=8*32;for(let u=r;u<r+148;u++)a+=e[u];for(let u=r+156;u<r+512;u++)a+=e[u];return this.cksum=a,Hr(e,r+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let r in e)e[r]!==null&&e[r]!==void 0&&(this[r]=e[r])}get type(){return Oh.name.get(this[lt])||this[lt]}get typeKey(){return this[lt]}set type(e){Oh.code.has(e)?this[lt]=Oh.code.get(e):this[lt]=e}},DI=(t,e)=>{let n=t,i="",s,o=ui.parse(t).root||".";if(Buffer.byteLength(n)<100)s=[n,i,!1];else{i=ui.dirname(n),n=ui.basename(n);do Buffer.byteLength(n)<=100&&Buffer.byteLength(i)<=e?s=[n,i,!1]:Buffer.byteLength(n)>100&&Buffer.byteLength(i)<=e?s=[n.slice(0,100-1),i,!0]:(n=ui.join(ui.basename(i),n),i=ui.dirname(i));while(i!==o&&!s);s||(s=[t.slice(0,100-1),"",!0])}return s},mn=(t,e,r)=>t.slice(e,e+r).toString("utf8").replace(/\0.*/,""),Ph=(t,e,r)=>wI(qr(t,e,r)),wI=t=>t===null?null:new Date(t*1e3),qr=(t,e,r)=>t[e]&128?HD.parse(t.slice(e,e+r)):xI(t,e,r),AI=t=>isNaN(t)?null:t,xI=(t,e,r)=>AI(parseInt(t.slice(e,e+r).toString("utf8").replace(/\0.*$/,"").trim(),8)),SI={12:8589934591,8:2097151},Hr=(t,e,r,n)=>n===null?!1:n>SI[r]||n<0?(HD.encode(n,t.slice(e,e+r)),!0):(CI(t,e,r,n),!1),CI=(t,e,r,n)=>t.write(vI(n,r),e,r,"ascii"),vI=(t,e)=>RI(Math.floor(t).toString(8),e),RI=(t,e)=>(t.length===e-1?t:new Array(e-t.length-1).join("0")+t+" ")+"\0",Bh=(t,e,r,n)=>n===null?!1:Hr(t,e,r,n.getTime()/1e3),FI=new Array(156).join("\0"),gn=(t,e,r,n)=>n===null?!1:(t.write(n+FI,e,r,"utf8"),n.length!==Buffer.byteLength(n)||n.length>r);jD.exports=Ih});var Sa=b((vH,$D)=>{"use strict";var TI=ci(),OI=require("path"),_s=class{constructor(e,r){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=r||!1}encode(){let e=this.encodeBody();if(e==="")return null;let r=Buffer.byteLength(e),n=512*Math.ceil(1+r/512),i=Buffer.allocUnsafe(n);for(let s=0;s<512;s++)i[s]=0;new TI({path:("PaxHeader/"+OI.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:r,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,r,"utf8");for(let s=r+512;s<i.length;s++)i[s]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let r=this[e]instanceof Date?this[e].getTime()/1e3:this[e],n=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+r+`
`,i=Buffer.byteLength(n),s=Math.floor(Math.log(i)/Math.log(10))+1;return i+s>=Math.pow(10,s)&&(s+=1),s+i+n}};_s.parse=(t,e,r)=>new _s(kI(PI(t),e),r);var kI=(t,e)=>e?Object.keys(t).reduce((r,n)=>(r[n]=t[n],r),e):t,PI=t=>t.replace(/\n$/,"").split(`
`).reduce(BI,Object.create(null)),BI=(t,e)=>{let r=parseInt(e,10);if(r!==Buffer.byteLength(e)+1)return t;e=e.slice((r+" ").length);let n=e.split("="),i=n.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return t;let s=n.join("=");return t[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(s*1e3):/^[0-9]+$/.test(s)?+s:s,t};$D.exports=_s});var li=b((RH,UD)=>{UD.exports=t=>{let e=t.length-1,r=-1;for(;e>-1&&t.charAt(e)==="/";)r=e,e--;return r===-1?t:t.slice(0,r)}});var Ca=b((FH,GD)=>{"use strict";GD.exports=t=>class extends t{warn(e,r,n={}){this.file&&(n.file=this.file),this.cwd&&(n.cwd=this.cwd),n.code=r instanceof Error&&r.code||e,n.tarCode=e,!this.strict&&n.recoverable!==!1?(r instanceof Error&&(n=Object.assign(r,n),r=r.message),this.emit("warn",n.tarCode,r,n)):r instanceof Error?this.emit("error",Object.assign(r,n)):this.emit("error",Object.assign(new Error(`${e}: ${r}`),n))}}});var Nh=b((OH,WD)=>{"use strict";var va=["|","<",">","?",":"],Lh=va.map(t=>String.fromCharCode(61440+t.charCodeAt(0))),II=new Map(va.map((t,e)=>[t,Lh[e]])),LI=new Map(Lh.map((t,e)=>[t,va[e]]));WD.exports={encode:t=>va.reduce((e,r)=>e.split(r).join(II.get(r)),t),decode:t=>Lh.reduce((e,r)=>e.split(r).join(LI.get(r)),t)}});var Mh=b((kH,VD)=>{var{isAbsolute:NI,parse:zD}=require("path").win32;VD.exports=t=>{let e="",r=zD(t);for(;NI(t)||r.root;){let n=t.charAt(0)==="/"&&t.slice(0,4)!=="//?/"?"/":r.root;t=t.slice(n.length),e+=n,r=zD(t)}return[e,t]}});var KD=b((PH,YD)=>{"use strict";YD.exports=(t,e,r)=>(t&=4095,r&&(t=(t|384)&-19),e&&(t&256&&(t|=64),t&32&&(t|=8),t&4&&(t|=1)),t)});var Yh=b((LH,cw)=>{"use strict";var rw=pa(),nw=Sa(),iw=ci(),Gt=require("fs"),JD=require("path"),Ut=ai(),MI=li(),sw=(t,e)=>e?(t=Ut(t).replace(/^\.(\/|$)/,""),MI(e)+"/"+t):Ut(t),qI=16*1024*1024,XD=Symbol("process"),ZD=Symbol("file"),QD=Symbol("directory"),Hh=Symbol("symlink"),ew=Symbol("hardlink"),Es=Symbol("header"),Ra=Symbol("read"),jh=Symbol("lstat"),Fa=Symbol("onlstat"),$h=Symbol("onread"),Uh=Symbol("onreadlink"),Gh=Symbol("openfile"),Wh=Symbol("onopenfile"),jr=Symbol("close"),Ta=Symbol("mode"),zh=Symbol("awaitDrain"),qh=Symbol("ondrain"),Wt=Symbol("prefix"),tw=Symbol("hadError"),ow=Ca(),HI=Nh(),aw=Mh(),uw=KD(),Oa=ow(class extends rw{constructor(e,r){if(r=r||{},super(r),typeof e!="string")throw new TypeError("path is required");this.path=Ut(e),this.portable=!!r.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=r.maxReadSize||qI,this.linkCache=r.linkCache||new Map,this.statCache=r.statCache||new Map,this.preservePaths=!!r.preservePaths,this.cwd=Ut(r.cwd||process.cwd()),this.strict=!!r.strict,this.noPax=!!r.noPax,this.noMtime=!!r.noMtime,this.mtime=r.mtime||null,this.prefix=r.prefix?Ut(r.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof r.onwarn=="function"&&this.on("warn",r.onwarn);let n=!1;if(!this.preservePaths){let[i,s]=aw(this.path);i&&(this.path=s,n=i)}this.win32=!!r.win32||process.platform==="win32",this.win32&&(this.path=HI.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=Ut(r.absolute||JD.resolve(this.cwd,e)),this.path===""&&(this.path="./"),n&&this.warn("TAR_ENTRY_INFO",`stripping ${n} from absolute path`,{entry:this,path:n+this.path}),this.statCache.has(this.absolute)?this[Fa](this.statCache.get(this.absolute)):this[jh]()}emit(e,...r){return e==="error"&&(this[tw]=!0),super.emit(e,...r)}[jh](){Gt.lstat(this.absolute,(e,r)=>{if(e)return this.emit("error",e);this[Fa](r)})}[Fa](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=$I(e),this.emit("stat",e),this[XD]()}[XD](){switch(this.type){case"File":return this[ZD]();case"Directory":return this[QD]();case"SymbolicLink":return this[Hh]();default:return this.end()}}[Ta](e){return uw(e,this.type==="Directory",this.portable)}[Wt](e){return sw(e,this.prefix)}[Es](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new iw({path:this[Wt](this.path),linkpath:this.type==="Link"?this[Wt](this.linkpath):this.linkpath,mode:this[Ta](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new nw({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[Wt](this.path),linkpath:this.type==="Link"?this[Wt](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[QD](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[Es](),this.end()}[Hh](){Gt.readlink(this.absolute,(e,r)=>{if(e)return this.emit("error",e);this[Uh](r)})}[Uh](e){this.linkpath=Ut(e),this[Es](),this.end()}[ew](e){this.type="Link",this.linkpath=Ut(JD.relative(this.cwd,e)),this.stat.size=0,this[Es](),this.end()}[ZD](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let r=this.linkCache.get(e);if(r.indexOf(this.cwd)===0)return this[ew](r)}this.linkCache.set(e,this.absolute)}if(this[Es](),this.stat.size===0)return this.end();this[Gh]()}[Gh](){Gt.open(this.absolute,"r",(e,r)=>{if(e)return this.emit("error",e);this[Wh](r)})}[Wh](e){if(this.fd=e,this[tw])return this[jr]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let r=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(r),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[Ra]()}[Ra](){let{fd:e,buf:r,offset:n,length:i,pos:s}=this;Gt.read(e,r,n,i,s,(o,a)=>{if(o)return this[jr](()=>this.emit("error",o));this[$h](a)})}[jr](e){Gt.close(this.fd,e)}[$h](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[jr](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[jr](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let r=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(r)?this[qh]():this[zh](()=>this[qh]())}[zh](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let r=new Error("writing more data than expected");return r.path=this.absolute,this.emit("error",r)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[qh](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[jr](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[Ra]()}}),Vh=class extends Oa{[jh](){this[Fa](Gt.lstatSync(this.absolute))}[Hh](){this[Uh](Gt.readlinkSync(this.absolute))}[Gh](){this[Wh](Gt.openSync(this.absolute,"r"))}[Ra](){let e=!0;try{let{fd:r,buf:n,offset:i,length:s,pos:o}=this,a=Gt.readSync(r,n,i,s,o);this[$h](a),e=!1}finally{if(e)try{this[jr](()=>{})}catch{}}}[zh](e){e()}[jr](e){Gt.closeSync(this.fd),e()}},jI=ow(class extends rw{constructor(e,r){r=r||{},super(r),this.preservePaths=!!r.preservePaths,this.portable=!!r.portable,this.strict=!!r.strict,this.noPax=!!r.noPax,this.noMtime=!!r.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=r.prefix||null,this.path=Ut(e.path),this.mode=this[Ta](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:r.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=Ut(e.linkpath),typeof r.onwarn=="function"&&this.on("warn",r.onwarn);let n=!1;if(!this.preservePaths){let[i,s]=aw(this.path);i&&(this.path=s,n=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new iw({path:this[Wt](this.path),linkpath:this.type==="Link"?this[Wt](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),n&&this.warn("TAR_ENTRY_INFO",`stripping ${n} from absolute path`,{entry:this,path:n+this.path}),this.header.encode()&&!this.noPax&&super.write(new nw({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[Wt](this.path),linkpath:this.type==="Link"?this[Wt](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[Wt](e){return sw(e,this.prefix)}[Ta](e){return uw(e,this.type==="Directory",this.portable)}write(e){let r=e.length;if(r>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=r,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});Oa.Sync=Vh;Oa.Tar=jI;var $I=t=>t.isFile()?"File":t.isDirectory()?"Directory":t.isSymbolicLink()?"SymbolicLink":"Unsupported";cw.exports=Oa});var fw=b((NH,lw)=>{"use strict";lw.exports=function(t){t.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var Kh=b((MH,hw)=>{"use strict";hw.exports=te;te.Node=yn;te.create=te;function te(t){var e=this;if(e instanceof te||(e=new te),e.tail=null,e.head=null,e.length=0,t&&typeof t.forEach=="function")t.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var r=0,n=arguments.length;r<n;r++)e.push(arguments[r]);return e}te.prototype.removeNode=function(t){if(t.list!==this)throw new Error("removing node which does not belong to this list");var e=t.next,r=t.prev;return e&&(e.prev=r),r&&(r.next=e),t===this.head&&(this.head=e),t===this.tail&&(this.tail=r),t.list.length--,t.next=null,t.prev=null,t.list=null,e};te.prototype.unshiftNode=function(t){if(t!==this.head){t.list&&t.list.removeNode(t);var e=this.head;t.list=this,t.next=e,e&&(e.prev=t),this.head=t,this.tail||(this.tail=t),this.length++}};te.prototype.pushNode=function(t){if(t!==this.tail){t.list&&t.list.removeNode(t);var e=this.tail;t.list=this,t.prev=e,e&&(e.next=t),this.tail=t,this.head||(this.head=t),this.length++}};te.prototype.push=function(){for(var t=0,e=arguments.length;t<e;t++)GI(this,arguments[t]);return this.length};te.prototype.unshift=function(){for(var t=0,e=arguments.length;t<e;t++)WI(this,arguments[t]);return this.length};te.prototype.pop=function(){if(this.tail){var t=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,t}};te.prototype.shift=function(){if(this.head){var t=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,t}};te.prototype.forEach=function(t,e){e=e||this;for(var r=this.head,n=0;r!==null;n++)t.call(e,r.value,n,this),r=r.next};te.prototype.forEachReverse=function(t,e){e=e||this;for(var r=this.tail,n=this.length-1;r!==null;n--)t.call(e,r.value,n,this),r=r.prev};te.prototype.get=function(t){for(var e=0,r=this.head;r!==null&&e<t;e++)r=r.next;if(e===t&&r!==null)return r.value};te.prototype.getReverse=function(t){for(var e=0,r=this.tail;r!==null&&e<t;e++)r=r.prev;if(e===t&&r!==null)return r.value};te.prototype.map=function(t,e){e=e||this;for(var r=new te,n=this.head;n!==null;)r.push(t.call(e,n.value,this)),n=n.next;return r};te.prototype.mapReverse=function(t,e){e=e||this;for(var r=new te,n=this.tail;n!==null;)r.push(t.call(e,n.value,this)),n=n.prev;return r};te.prototype.reduce=function(t,e){var r,n=this.head;if(arguments.length>1)r=e;else if(this.head)n=this.head.next,r=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;n!==null;i++)r=t(r,n.value,i),n=n.next;return r};te.prototype.reduceReverse=function(t,e){var r,n=this.tail;if(arguments.length>1)r=e;else if(this.tail)n=this.tail.prev,r=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;n!==null;i--)r=t(r,n.value,i),n=n.prev;return r};te.prototype.toArray=function(){for(var t=new Array(this.length),e=0,r=this.head;r!==null;e++)t[e]=r.value,r=r.next;return t};te.prototype.toArrayReverse=function(){for(var t=new Array(this.length),e=0,r=this.tail;r!==null;e++)t[e]=r.value,r=r.prev;return t};te.prototype.slice=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var r=new te;if(e<t||e<0)return r;t<0&&(t=0),e>this.length&&(e=this.length);for(var n=0,i=this.head;i!==null&&n<t;n++)i=i.next;for(;i!==null&&n<e;n++,i=i.next)r.push(i.value);return r};te.prototype.sliceReverse=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var r=new te;if(e<t||e<0)return r;t<0&&(t=0),e>this.length&&(e=this.length);for(var n=this.length,i=this.tail;i!==null&&n>e;n--)i=i.prev;for(;i!==null&&n>t;n--,i=i.prev)r.push(i.value);return r};te.prototype.splice=function(t,e,...r){t>this.length&&(t=this.length-1),t<0&&(t=this.length+t);for(var n=0,i=this.head;i!==null&&n<t;n++)i=i.next;for(var s=[],n=0;i&&n<e;n++)s.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var n=0;n<r.length;n++)i=UI(this,i,r[n]);return s};te.prototype.reverse=function(){for(var t=this.head,e=this.tail,r=t;r!==null;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=e,this.tail=t,this};function UI(t,e,r){var n=e===t.head?new yn(r,null,e,t):new yn(r,e,e.next,t);return n.next===null&&(t.tail=n),n.prev===null&&(t.head=n),t.length++,n}function GI(t,e){t.tail=new yn(e,t.tail,null,t),t.head||(t.head=t.tail),t.length++}function WI(t,e){t.head=new yn(e,null,t.head,t),t.tail||(t.tail=t.head),t.length++}function yn(t,e,r,n){if(!(this instanceof yn))return new yn(t,e,r,n);this.list=n,this.value=t,e?(e.next=this,this.prev=e):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}try{fw()(te)}catch{}});var Ha=b((HH,Ew)=>{"use strict";var Ma=class{constructor(e,r){this.path=e||"./",this.absolute=r,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},zI=pa(),VI=vh(),YI=Aa(),id=Yh(),KI=id.Sync,JI=id.Tar,XI=Kh(),dw=Buffer.alloc(1024),Ba=Symbol("onStat"),ka=Symbol("ended"),zt=Symbol("queue"),fi=Symbol("current"),_n=Symbol("process"),Pa=Symbol("processing"),pw=Symbol("processJob"),Vt=Symbol("jobs"),Jh=Symbol("jobDone"),Ia=Symbol("addFSEntry"),mw=Symbol("addTarEntry"),ed=Symbol("stat"),td=Symbol("readdir"),La=Symbol("onreaddir"),Na=Symbol("pipe"),gw=Symbol("entry"),Xh=Symbol("entryOpt"),rd=Symbol("writeEntryClass"),_w=Symbol("write"),Zh=Symbol("ondrain"),qa=require("fs"),yw=require("path"),ZI=Ca(),Qh=ai(),sd=ZI(class extends zI{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=Qh(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[rd]=id,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new VI.Gzip(e.gzip),this.zip.on("data",r=>super.write(r)),this.zip.on("end",r=>super.end()),this.zip.on("drain",r=>this[Zh]()),this.on("resume",r=>this.zip.resume())):this.on("drain",this[Zh]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:r=>!0,this[zt]=new XI,this[Vt]=0,this.jobs=+e.jobs||4,this[Pa]=!1,this[ka]=!1}[_w](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[ka]=!0,this[_n](),this}write(e){if(this[ka])throw new Error("write after end");return e instanceof YI?this[mw](e):this[Ia](e),this.flowing}[mw](e){let r=Qh(yw.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let n=new Ma(e.path,r,!1);n.entry=new JI(e,this[Xh](n)),n.entry.on("end",i=>this[Jh](n)),this[Vt]+=1,this[zt].push(n)}this[_n]()}[Ia](e){let r=Qh(yw.resolve(this.cwd,e));this[zt].push(new Ma(e,r)),this[_n]()}[ed](e){e.pending=!0,this[Vt]+=1;let r=this.follow?"stat":"lstat";qa[r](e.absolute,(n,i)=>{e.pending=!1,this[Vt]-=1,n?this.emit("error",n):this[Ba](e,i)})}[Ba](e,r){this.statCache.set(e.absolute,r),e.stat=r,this.filter(e.path,r)||(e.ignore=!0),this[_n]()}[td](e){e.pending=!0,this[Vt]+=1,qa.readdir(e.absolute,(r,n)=>{if(e.pending=!1,this[Vt]-=1,r)return this.emit("error",r);this[La](e,n)})}[La](e,r){this.readdirCache.set(e.absolute,r),e.readdir=r,this[_n]()}[_n](){if(!this[Pa]){this[Pa]=!0;for(let e=this[zt].head;e!==null&&this[Vt]<this.jobs;e=e.next)if(this[pw](e.value),e.value.ignore){let r=e.next;this[zt].removeNode(e),e.next=r}this[Pa]=!1,this[ka]&&!this[zt].length&&this[Vt]===0&&(this.zip?this.zip.end(dw):(super.write(dw),super.end()))}}get[fi](){return this[zt]&&this[zt].head&&this[zt].head.value}[Jh](e){this[zt].shift(),this[Vt]-=1,this[_n]()}[pw](e){if(!e.pending){if(e.entry){e===this[fi]&&!e.piped&&this[Na](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[Ba](e,this.statCache.get(e.absolute)):this[ed](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[La](e,this.readdirCache.get(e.absolute)):this[td](e),!e.readdir))){if(e.entry=this[gw](e),!e.entry){e.ignore=!0;return}e===this[fi]&&!e.piped&&this[Na](e)}}}[Xh](e){return{onwarn:(r,n,i)=>this.warn(r,n,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[gw](e){this[Vt]+=1;try{return new this[rd](e.path,this[Xh](e)).on("end",()=>this[Jh](e)).on("error",r=>this.emit("error",r))}catch(r){this.emit("error",r)}}[Zh](){this[fi]&&this[fi].entry&&this[fi].entry.resume()}[Na](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let s=e.path,o=s==="./"?"":s.replace(/\/*$/,"/");this[Ia](o+i)});let r=e.entry,n=this.zip;n?r.on("data",i=>{n.write(i)||r.pause()}):r.on("data",i=>{super.write(i)||r.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),nd=class extends sd{constructor(e){super(e),this[rd]=KI}pause(){}resume(){}[ed](e){let r=this.follow?"statSync":"lstatSync";this[Ba](e,qa[r](e.absolute))}[td](e,r){this[La](e,qa.readdirSync(e.absolute))}[Na](e){let r=e.entry,n=this.zip;e.readdir&&e.readdir.forEach(i=>{let s=e.path,o=s==="./"?"":s.replace(/\/*$/,"/");this[Ia](o+i)}),n?r.on("data",i=>{n.write(i)}):r.on("data",i=>{super[_w](i)})}};sd.Sync=nd;Ew.exports=sd});var Ei=b(Ds=>{"use strict";var QI=fh(),eL=require("events").EventEmitter,it=require("fs"),ud=it.writev;if(!ud){let t=process.binding("fs"),e=t.FSReqWrap||t.FSReqCallback;ud=(r,n,i,s)=>{let o=(u,c)=>s(u,c,n),a=new e;a.oncomplete=o,t.writeBuffers(r,n,i,a)}}var yi=Symbol("_autoClose"),Ot=Symbol("_close"),bs=Symbol("_ended"),ce=Symbol("_fd"),bw=Symbol("_finished"),Ur=Symbol("_flags"),od=Symbol("_flush"),cd=Symbol("_handleChunk"),ld=Symbol("_makeBuf"),Wa=Symbol("_mode"),ja=Symbol("_needDrain"),mi=Symbol("_onerror"),_i=Symbol("_onopen"),ad=Symbol("_onread"),di=Symbol("_onwrite"),Gr=Symbol("_open"),mr=Symbol("_path"),En=Symbol("_pos"),Yt=Symbol("_queue"),pi=Symbol("_read"),Dw=Symbol("_readSize"),$r=Symbol("_reading"),$a=Symbol("_remain"),ww=Symbol("_size"),Ua=Symbol("_write"),hi=Symbol("_writing"),Ga=Symbol("_defaultFlag"),gi=Symbol("_errored"),za=class extends QI{constructor(e,r){if(r=r||{},super(r),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[gi]=!1,this[ce]=typeof r.fd=="number"?r.fd:null,this[mr]=e,this[Dw]=r.readSize||16*1024*1024,this[$r]=!1,this[ww]=typeof r.size=="number"?r.size:1/0,this[$a]=this[ww],this[yi]=typeof r.autoClose=="boolean"?r.autoClose:!0,typeof this[ce]=="number"?this[pi]():this[Gr]()}get fd(){return this[ce]}get path(){return this[mr]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[Gr](){it.open(this[mr],"r",(e,r)=>this[_i](e,r))}[_i](e,r){e?this[mi](e):(this[ce]=r,this.emit("open",r),this[pi]())}[ld](){return Buffer.allocUnsafe(Math.min(this[Dw],this[$a]))}[pi](){if(!this[$r]){this[$r]=!0;let e=this[ld]();if(e.length===0)return process.nextTick(()=>this[ad](null,0,e));it.read(this[ce],e,0,e.length,null,(r,n,i)=>this[ad](r,n,i))}}[ad](e,r,n){this[$r]=!1,e?this[mi](e):this[cd](r,n)&&this[pi]()}[Ot](){if(this[yi]&&typeof this[ce]=="number"){let e=this[ce];this[ce]=null,it.close(e,r=>r?this.emit("error",r):this.emit("close"))}}[mi](e){this[$r]=!0,this[Ot](),this.emit("error",e)}[cd](e,r){let n=!1;return this[$a]-=e,e>0&&(n=super.write(e<r.length?r.slice(0,e):r)),(e===0||this[$a]<=0)&&(n=!1,this[Ot](),super.end()),n}emit(e,r){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[ce]=="number"&&this[pi]();break;case"error":return this[gi]?void 0:(this[gi]=!0,super.emit(e,r));default:return super.emit(e,r)}}},fd=class extends za{[Gr](){let e=!0;try{this[_i](null,it.openSync(this[mr],"r")),e=!1}finally{e&&this[Ot]()}}[pi](){let e=!0;try{if(!this[$r]){this[$r]=!0;do{let r=this[ld](),n=r.length===0?0:it.readSync(this[ce],r,0,r.length,null);if(!this[cd](n,r))break}while(!0);this[$r]=!1}e=!1}finally{e&&this[Ot]()}}[Ot](){if(this[yi]&&typeof this[ce]=="number"){let e=this[ce];this[ce]=null,it.closeSync(e),this.emit("close")}}},Va=class extends eL{constructor(e,r){r=r||{},super(r),this.readable=!1,this.writable=!0,this[gi]=!1,this[hi]=!1,this[bs]=!1,this[ja]=!1,this[Yt]=[],this[mr]=e,this[ce]=typeof r.fd=="number"?r.fd:null,this[Wa]=r.mode===void 0?438:r.mode,this[En]=typeof r.start=="number"?r.start:null,this[yi]=typeof r.autoClose=="boolean"?r.autoClose:!0;let n=this[En]!==null?"r+":"w";this[Ga]=r.flags===void 0,this[Ur]=this[Ga]?n:r.flags,this[ce]===null&&this[Gr]()}emit(e,r){if(e==="error"){if(this[gi])return;this[gi]=!0}return super.emit(e,r)}get fd(){return this[ce]}get path(){return this[mr]}[mi](e){this[Ot](),this[hi]=!0,this.emit("error",e)}[Gr](){it.open(this[mr],this[Ur],this[Wa],(e,r)=>this[_i](e,r))}[_i](e,r){this[Ga]&&this[Ur]==="r+"&&e&&e.code==="ENOENT"?(this[Ur]="w",this[Gr]()):e?this[mi](e):(this[ce]=r,this.emit("open",r),this[od]())}end(e,r){return e&&this.write(e,r),this[bs]=!0,!this[hi]&&!this[Yt].length&&typeof this[ce]=="number"&&this[di](null,0),this}write(e,r){return typeof e=="string"&&(e=Buffer.from(e,r)),this[bs]?(this.emit("error",new Error("write() after end()")),!1):this[ce]===null||this[hi]||this[Yt].length?(this[Yt].push(e),this[ja]=!0,!1):(this[hi]=!0,this[Ua](e),!0)}[Ua](e){it.write(this[ce],e,0,e.length,this[En],(r,n)=>this[di](r,n))}[di](e,r){e?this[mi](e):(this[En]!==null&&(this[En]+=r),this[Yt].length?this[od]():(this[hi]=!1,this[bs]&&!this[bw]?(this[bw]=!0,this[Ot](),this.emit("finish")):this[ja]&&(this[ja]=!1,this.emit("drain"))))}[od](){if(this[Yt].length===0)this[bs]&&this[di](null,0);else if(this[Yt].length===1)this[Ua](this[Yt].pop());else{let e=this[Yt];this[Yt]=[],ud(this[ce],e,this[En],(r,n)=>this[di](r,n))}}[Ot](){if(this[yi]&&typeof this[ce]=="number"){let e=this[ce];this[ce]=null,it.close(e,r=>r?this.emit("error",r):this.emit("close"))}}},hd=class extends Va{[Gr](){let e;if(this[Ga]&&this[Ur]==="r+")try{e=it.openSync(this[mr],this[Ur],this[Wa])}catch(r){if(r.code==="ENOENT")return this[Ur]="w",this[Gr]();throw r}else e=it.openSync(this[mr],this[Ur],this[Wa]);this[_i](null,e)}[Ot](){if(this[yi]&&typeof this[ce]=="number"){let e=this[ce];this[ce]=null,it.closeSync(e),this.emit("close")}}[Ua](e){let r=!0;try{this[di](null,it.writeSync(this[ce],e,0,e.length,this[En])),r=!1}finally{if(r)try{this[Ot]()}catch{}}}};Ds.ReadStream=za;Ds.ReadStreamSync=fd;Ds.WriteStream=Va;Ds.WriteStreamSync=hd});var eu=b((UH,Tw)=>{"use strict";var tL=Ca(),rL=ci(),nL=require("events"),iL=Kh(),sL=1024*1024,oL=Aa(),Aw=Sa(),aL=vh(),{nextTick:uL}=require("process"),dd=Buffer.from([31,139]),_t=Symbol("state"),bn=Symbol("writeEntry"),gr=Symbol("readEntry"),pd=Symbol("nextEntry"),xw=Symbol("processEntry"),Et=Symbol("extendedHeader"),ws=Symbol("globalExtendedHeader"),Wr=Symbol("meta"),Sw=Symbol("emitMeta"),me=Symbol("buffer"),yr=Symbol("queue"),Dn=Symbol("ended"),Cw=Symbol("emittedEnd"),wn=Symbol("emit"),st=Symbol("unzip"),Ya=Symbol("consumeChunk"),Ka=Symbol("consumeChunkSub"),md=Symbol("consumeBody"),vw=Symbol("consumeMeta"),Rw=Symbol("consumeHeader"),Ja=Symbol("consuming"),gd=Symbol("bufferConcat"),yd=Symbol("maybeEnd"),As=Symbol("writing"),zr=Symbol("aborted"),Xa=Symbol("onDone"),An=Symbol("sawValidEntry"),Za=Symbol("sawNullBlock"),Qa=Symbol("sawEOF"),Fw=Symbol("closeStream"),cL=t=>!0;Tw.exports=tL(class extends nL{constructor(e){e=e||{},super(e),this.file=e.file||"",this[An]=null,this.on(Xa,r=>{(this[_t]==="begin"||this[An]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(Xa,e.ondone):this.on(Xa,r=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||sL,this.filter=typeof e.filter=="function"?e.filter:cL,this.writable=!0,this.readable=!1,this[yr]=new iL,this[me]=null,this[gr]=null,this[bn]=null,this[_t]="begin",this[Wr]="",this[Et]=null,this[ws]=null,this[Dn]=!1,this[st]=null,this[zr]=!1,this[Za]=!1,this[Qa]=!1,this.on("end",()=>this[Fw]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[Rw](e,r){this[An]===null&&(this[An]=!1);let n;try{n=new rL(e,r,this[Et],this[ws])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(n.nullBlock)this[Za]?(this[Qa]=!0,this[_t]==="begin"&&(this[_t]="header"),this[wn]("eof")):(this[Za]=!0,this[wn]("nullBlock"));else if(this[Za]=!1,!n.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:n});else if(!n.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:n});else{let i=n.type;if(/^(Symbolic)?Link$/.test(i)&&!n.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:n});else if(!/^(Symbolic)?Link$/.test(i)&&n.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:n});else{let s=this[bn]=new oL(n,this[Et],this[ws]);if(!this[An])if(s.remain){let o=()=>{s.invalid||(this[An]=!0)};s.on("end",o)}else this[An]=!0;s.meta?s.size>this.maxMetaEntrySize?(s.ignore=!0,this[wn]("ignoredEntry",s),this[_t]="ignore",s.resume()):s.size>0&&(this[Wr]="",s.on("data",o=>this[Wr]+=o),this[_t]="meta"):(this[Et]=null,s.ignore=s.ignore||!this.filter(s.path,s),s.ignore?(this[wn]("ignoredEntry",s),this[_t]=s.remain?"ignore":"header",s.resume()):(s.remain?this[_t]="body":(this[_t]="header",s.end()),this[gr]?this[yr].push(s):(this[yr].push(s),this[pd]())))}}}[Fw](){uL(()=>this.emit("close"))}[xw](e){let r=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[gr]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",n=>this[pd]()),r=!1)):(this[gr]=null,r=!1),r}[pd](){do;while(this[xw](this[yr].shift()));if(!this[yr].length){let e=this[gr];!e||e.flowing||e.size===e.remain?this[As]||this.emit("drain"):e.once("drain",n=>this.emit("drain"))}}[md](e,r){let n=this[bn],i=n.blockRemain,s=i>=e.length&&r===0?e:e.slice(r,r+i);return n.write(s),n.blockRemain||(this[_t]="header",this[bn]=null,n.end()),s.length}[vw](e,r){let n=this[bn],i=this[md](e,r);return this[bn]||this[Sw](n),i}[wn](e,r,n){!this[yr].length&&!this[gr]?this.emit(e,r,n):this[yr].push([e,r,n])}[Sw](e){switch(this[wn]("meta",this[Wr]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[Et]=Aw.parse(this[Wr],this[Et],!1);break;case"GlobalExtendedHeader":this[ws]=Aw.parse(this[Wr],this[ws],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[Et]=this[Et]||Object.create(null),this[Et].path=this[Wr].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[Et]=this[Et]||Object.create(null),this[Et].linkpath=this[Wr].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[zr]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[zr])return;if(this[st]===null&&e){if(this[me]&&(e=Buffer.concat([this[me],e]),this[me]=null),e.length<dd.length)return this[me]=e,!0;for(let n=0;this[st]===null&&n<dd.length;n++)e[n]!==dd[n]&&(this[st]=!1);if(this[st]===null){let n=this[Dn];this[Dn]=!1,this[st]=new aL.Unzip,this[st].on("data",s=>this[Ya](s)),this[st].on("error",s=>this.abort(s)),this[st].on("end",s=>{this[Dn]=!0,this[Ya]()}),this[As]=!0;let i=this[st][n?"end":"write"](e);return this[As]=!1,i}}this[As]=!0,this[st]?this[st].write(e):this[Ya](e),this[As]=!1;let r=this[yr].length?!1:this[gr]?this[gr].flowing:!0;return!r&&!this[yr].length&&this[gr].once("drain",n=>this.emit("drain")),r}[gd](e){e&&!this[zr]&&(this[me]=this[me]?Buffer.concat([this[me],e]):e)}[yd](){if(this[Dn]&&!this[Cw]&&!this[zr]&&!this[Ja]){this[Cw]=!0;let e=this[bn];if(e&&e.blockRemain){let r=this[me]?this[me].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${r} available)`,{entry:e}),this[me]&&e.write(this[me]),e.end()}this[wn](Xa)}}[Ya](e){if(this[Ja])this[gd](e);else if(!e&&!this[me])this[yd]();else{if(this[Ja]=!0,this[me]){this[gd](e);let r=this[me];this[me]=null,this[Ka](r)}else this[Ka](e);for(;this[me]&&this[me].length>=512&&!this[zr]&&!this[Qa];){let r=this[me];this[me]=null,this[Ka](r)}this[Ja]=!1}(!this[me]||this[Dn])&&this[yd]()}[Ka](e){let r=0,n=e.length;for(;r+512<=n&&!this[zr]&&!this[Qa];)switch(this[_t]){case"begin":case"header":this[Rw](e,r),r+=512;break;case"ignore":case"body":r+=this[md](e,r);break;case"meta":r+=this[vw](e,r);break;default:throw new Error("invalid state: "+this[_t])}r<n&&(this[me]?this[me]=Buffer.concat([e.slice(r),this[me]]):this[me]=e.slice(r))}end(e){this[zr]||(this[st]?this[st].end(e):(this[Dn]=!0,this.write(e)))}})});var tu=b((GH,Bw)=>{"use strict";var lL=ri(),kw=eu(),bi=require("fs"),fL=Ei(),Ow=require("path"),_d=li();Bw.exports=(t,e,r)=>{typeof t=="function"?(r=t,e=null,t={}):Array.isArray(t)&&(e=t,t={}),typeof e=="function"&&(r=e,e=null),e?e=Array.from(e):e=[];let n=lL(t);if(n.sync&&typeof r=="function")throw new TypeError("callback not supported for sync tar functions");if(!n.file&&typeof r=="function")throw new TypeError("callback only supported with file option");return e.length&&dL(n,e),n.noResume||hL(n),n.file&&n.sync?pL(n):n.file?mL(n,r):Pw(n)};var hL=t=>{let e=t.onentry;t.onentry=e?r=>{e(r),r.resume()}:r=>r.resume()},dL=(t,e)=>{let r=new Map(e.map(s=>[_d(s),!0])),n=t.filter,i=(s,o)=>{let a=o||Ow.parse(s).root||".",u=s===a?!1:r.has(s)?r.get(s):i(Ow.dirname(s),a);return r.set(s,u),u};t.filter=n?(s,o)=>n(s,o)&&i(_d(s)):s=>i(_d(s))},pL=t=>{let e=Pw(t),r=t.file,n=!0,i;try{let s=bi.statSync(r),o=t.maxReadSize||16*1024*1024;if(s.size<o)e.end(bi.readFileSync(r));else{let a=0,u=Buffer.allocUnsafe(o);for(i=bi.openSync(r,"r");a<s.size;){let c=bi.readSync(i,u,0,o,a);a+=c,e.write(u.slice(0,c))}e.end()}n=!1}finally{if(n&&i)try{bi.closeSync(i)}catch{}}},mL=(t,e)=>{let r=new kw(t),n=t.maxReadSize||16*1024*1024,i=t.file,s=new Promise((o,a)=>{r.on("error",a),r.on("end",o),bi.stat(i,(u,c)=>{if(u)a(u);else{let l=new fL.ReadStream(i,{readSize:n,size:c.size});l.on("error",a),l.pipe(r)}})});return e?s.then(e,e):s},Pw=t=>new kw(t)});var Hw=b((WH,qw)=>{"use strict";var gL=ri(),ru=Ha(),Iw=Ei(),Lw=tu(),Nw=require("path");qw.exports=(t,e,r)=>{if(typeof e=="function"&&(r=e),Array.isArray(t)&&(e=t,t={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let n=gL(t);if(n.sync&&typeof r=="function")throw new TypeError("callback not supported for sync tar functions");if(!n.file&&typeof r=="function")throw new TypeError("callback only supported with file option");return n.file&&n.sync?yL(n,e):n.file?_L(n,e,r):n.sync?EL(n,e):bL(n,e)};var yL=(t,e)=>{let r=new ru.Sync(t),n=new Iw.WriteStreamSync(t.file,{mode:t.mode||438});r.pipe(n),Mw(r,e)},_L=(t,e,r)=>{let n=new ru(t),i=new Iw.WriteStream(t.file,{mode:t.mode||438});n.pipe(i);let s=new Promise((o,a)=>{i.on("error",a),i.on("close",o),n.on("error",a)});return Ed(n,e),r?s.then(r,r):s},Mw=(t,e)=>{e.forEach(r=>{r.charAt(0)==="@"?Lw({file:Nw.resolve(t.cwd,r.slice(1)),sync:!0,noResume:!0,onentry:n=>t.add(n)}):t.add(r)}),t.end()},Ed=(t,e)=>{for(;e.length;){let r=e.shift();if(r.charAt(0)==="@")return Lw({file:Nw.resolve(t.cwd,r.slice(1)),noResume:!0,onentry:n=>t.add(n)}).then(n=>Ed(t,e));t.add(r)}t.end()},EL=(t,e)=>{let r=new ru.Sync(t);return Mw(r,e),r},bL=(t,e)=>{let r=new ru(t);return Ed(r,e),r}});var bd=b((zH,Vw)=>{"use strict";var DL=ri(),jw=Ha(),ft=require("fs"),$w=Ei(),Uw=tu(),Gw=require("path"),Ww=ci();Vw.exports=(t,e,r)=>{let n=DL(t);if(!n.file)throw new TypeError("file is required");if(n.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),n.sync?wL(n,e):xL(n,e,r)};var wL=(t,e)=>{let r=new jw.Sync(t),n=!0,i,s;try{try{i=ft.openSync(t.file,"r+")}catch(u){if(u.code==="ENOENT")i=ft.openSync(t.file,"w+");else throw u}let o=ft.fstatSync(i),a=Buffer.alloc(512);e:for(s=0;s<o.size;s+=512){for(let l=0,f=0;l<512;l+=f){if(f=ft.readSync(i,a,l,a.length-l,s+l),s===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!f)break e}let u=new Ww(a);if(!u.cksumValid)break;let c=512*Math.ceil(u.size/512);if(s+c+512>o.size)break;s+=c,t.mtimeCache&&t.mtimeCache.set(u.path,u.mtime)}n=!1,AL(t,r,s,i,e)}finally{if(n)try{ft.closeSync(i)}catch{}}},AL=(t,e,r,n,i)=>{let s=new $w.WriteStreamSync(t.file,{fd:n,start:r});e.pipe(s),SL(e,i)},xL=(t,e,r)=>{e=Array.from(e);let n=new jw(t),i=(o,a,u)=>{let c=(_,y)=>{_?ft.close(o,p=>u(_)):u(null,y)},l=0;if(a===0)return c(null,0);let f=0,h=Buffer.alloc(512),m=(_,y)=>{if(_)return c(_);if(f+=y,f<512&&y)return ft.read(o,h,f,h.length-f,l+f,m);if(l===0&&h[0]===31&&h[1]===139)return c(new Error("cannot append to compressed archives"));if(f<512)return c(null,l);let p=new Ww(h);if(!p.cksumValid)return c(null,l);let D=512*Math.ceil(p.size/512);if(l+D+512>a||(l+=D+512,l>=a))return c(null,l);t.mtimeCache&&t.mtimeCache.set(p.path,p.mtime),f=0,ft.read(o,h,0,512,l,m)};ft.read(o,h,0,512,l,m)},s=new Promise((o,a)=>{n.on("error",a);let u="r+",c=(l,f)=>{if(l&&l.code==="ENOENT"&&u==="r+")return u="w+",ft.open(t.file,u,c);if(l)return a(l);ft.fstat(f,(h,m)=>{if(h)return ft.close(f,()=>a(h));i(f,m.size,(_,y)=>{if(_)return a(_);let p=new $w.WriteStream(t.file,{fd:f,start:y});n.pipe(p),p.on("error",a),p.on("close",o),zw(n,e)})})};ft.open(t.file,u,c)});return r?s.then(r,r):s},SL=(t,e)=>{e.forEach(r=>{r.charAt(0)==="@"?Uw({file:Gw.resolve(t.cwd,r.slice(1)),sync:!0,noResume:!0,onentry:n=>t.add(n)}):t.add(r)}),t.end()},zw=(t,e)=>{for(;e.length;){let r=e.shift();if(r.charAt(0)==="@")return Uw({file:Gw.resolve(t.cwd,r.slice(1)),noResume:!0,onentry:n=>t.add(n)}).then(n=>zw(t,e));t.add(r)}t.end()}});var Kw=b((VH,Yw)=>{"use strict";var CL=ri(),vL=bd();Yw.exports=(t,e,r)=>{let n=CL(t);if(!n.file)throw new TypeError("file is required");if(n.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),RL(n),vL(n,e,r)};var RL=t=>{let e=t.filter;t.mtimeCache||(t.mtimeCache=new Map),t.filter=e?(r,n)=>e(r,n)&&!(t.mtimeCache.get(r)>n.mtime):(r,n)=>!(t.mtimeCache.get(r)>n.mtime)}});var Zw=b((YH,Xw)=>{var{promisify:Jw}=require("util"),Vr=require("fs"),FL=t=>{if(!t)t={mode:511,fs:Vr};else if(typeof t=="object")t={mode:511,fs:Vr,...t};else if(typeof t=="number")t={mode:t,fs:Vr};else if(typeof t=="string")t={mode:parseInt(t,8),fs:Vr};else throw new TypeError("invalid options argument");return t.mkdir=t.mkdir||t.fs.mkdir||Vr.mkdir,t.mkdirAsync=Jw(t.mkdir),t.stat=t.stat||t.fs.stat||Vr.stat,t.statAsync=Jw(t.stat),t.statSync=t.statSync||t.fs.statSync||Vr.statSync,t.mkdirSync=t.mkdirSync||t.fs.mkdirSync||Vr.mkdirSync,t};Xw.exports=FL});var eA=b((KH,Qw)=>{var TL=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:OL,parse:kL}=require("path"),PL=t=>{if(/\0/.test(t))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:t,code:"ERR_INVALID_ARG_VALUE"});if(t=OL(t),TL==="win32"){let e=/[*|"<>?:]/,{root:r}=kL(t);if(e.test(t.substr(r.length)))throw Object.assign(new Error("Illegal characters in path."),{path:t,code:"EINVAL"})}return t};Qw.exports=PL});var sA=b((JH,iA)=>{var{dirname:tA}=require("path"),rA=(t,e,r=void 0)=>r===e?Promise.resolve():t.statAsync(e).then(n=>n.isDirectory()?r:void 0,n=>n.code==="ENOENT"?rA(t,tA(e),e):void 0),nA=(t,e,r=void 0)=>{if(r!==e)try{return t.statSync(e).isDirectory()?r:void 0}catch(n){return n.code==="ENOENT"?nA(t,tA(e),e):void 0}};iA.exports={findMade:rA,findMadeSync:nA}});var Ad=b((XH,aA)=>{var{dirname:oA}=require("path"),Dd=(t,e,r)=>{e.recursive=!1;let n=oA(t);return n===t?e.mkdirAsync(t,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(t,e).then(()=>r||t,i=>{if(i.code==="ENOENT")return Dd(n,e).then(s=>Dd(t,e,s));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(t).then(s=>{if(s.isDirectory())return r;throw i},()=>{throw i})})},wd=(t,e,r)=>{let n=oA(t);if(e.recursive=!1,n===t)try{return e.mkdirSync(t,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(t,e),r||t}catch(i){if(i.code==="ENOENT")return wd(t,e,wd(n,e,r));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(t).isDirectory())throw i}catch{throw i}}};aA.exports={mkdirpManual:Dd,mkdirpManualSync:wd}});var lA=b((ZH,cA)=>{var{dirname:uA}=require("path"),{findMade:BL,findMadeSync:IL}=sA(),{mkdirpManual:LL,mkdirpManualSync:NL}=Ad(),ML=(t,e)=>(e.recursive=!0,uA(t)===t?e.mkdirAsync(t,e):BL(e,t).then(n=>e.mkdirAsync(t,e).then(()=>n).catch(i=>{if(i.code==="ENOENT")return LL(t,e);throw i}))),qL=(t,e)=>{if(e.recursive=!0,uA(t)===t)return e.mkdirSync(t,e);let n=IL(e,t);try{return e.mkdirSync(t,e),n}catch(i){if(i.code==="ENOENT")return NL(t,e);throw i}};cA.exports={mkdirpNative:ML,mkdirpNativeSync:qL}});var pA=b((QH,dA)=>{var fA=require("fs"),HL=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,xd=HL.replace(/^v/,"").split("."),hA=+xd[0]>10||+xd[0]==10&&+xd[1]>=12,jL=hA?t=>t.mkdir===fA.mkdir:()=>!1,$L=hA?t=>t.mkdirSync===fA.mkdirSync:()=>!1;dA.exports={useNative:jL,useNativeSync:$L}});var bA=b((e4,EA)=>{var Di=Zw(),wi=eA(),{mkdirpNative:mA,mkdirpNativeSync:gA}=lA(),{mkdirpManual:yA,mkdirpManualSync:_A}=Ad(),{useNative:UL,useNativeSync:GL}=pA(),Ai=(t,e)=>(t=wi(t),e=Di(e),UL(e)?mA(t,e):yA(t,e)),WL=(t,e)=>(t=wi(t),e=Di(e),GL(e)?gA(t,e):_A(t,e));Ai.sync=WL;Ai.native=(t,e)=>mA(wi(t),Di(e));Ai.manual=(t,e)=>yA(wi(t),Di(e));Ai.nativeSync=(t,e)=>gA(wi(t),Di(e));Ai.manualSync=(t,e)=>_A(wi(t),Di(e));EA.exports=Ai});var vA=b((t4,CA)=>{"use strict";var bt=require("fs"),xn=require("path"),zL=bt.lchown?"lchown":"chown",VL=bt.lchownSync?"lchownSync":"chownSync",wA=bt.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),DA=(t,e,r)=>{try{return bt[VL](t,e,r)}catch(n){if(n.code!=="ENOENT")throw n}},YL=(t,e,r)=>{try{return bt.chownSync(t,e,r)}catch(n){if(n.code!=="ENOENT")throw n}},KL=wA?(t,e,r,n)=>i=>{!i||i.code!=="EISDIR"?n(i):bt.chown(t,e,r,n)}:(t,e,r,n)=>n,Sd=wA?(t,e,r)=>{try{return DA(t,e,r)}catch(n){if(n.code!=="EISDIR")throw n;YL(t,e,r)}}:(t,e,r)=>DA(t,e,r),JL=process.version,AA=(t,e,r)=>bt.readdir(t,e,r),XL=(t,e)=>bt.readdirSync(t,e);/^v4\./.test(JL)&&(AA=(t,e,r)=>bt.readdir(t,r));var nu=(t,e,r,n)=>{bt[zL](t,e,r,KL(t,e,r,i=>{n(i&&i.code!=="ENOENT"?i:null)}))},xA=(t,e,r,n,i)=>{if(typeof e=="string")return bt.lstat(xn.resolve(t,e),(s,o)=>{if(s)return i(s.code!=="ENOENT"?s:null);o.name=e,xA(t,o,r,n,i)});if(e.isDirectory())Cd(xn.resolve(t,e.name),r,n,s=>{if(s)return i(s);let o=xn.resolve(t,e.name);nu(o,r,n,i)});else{let s=xn.resolve(t,e.name);nu(s,r,n,i)}},Cd=(t,e,r,n)=>{AA(t,{withFileTypes:!0},(i,s)=>{if(i){if(i.code==="ENOENT")return n();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return n(i)}if(i||!s.length)return nu(t,e,r,n);let o=s.length,a=null,u=c=>{if(!a){if(c)return n(a=c);if(--o===0)return nu(t,e,r,n)}};s.forEach(c=>xA(t,c,e,r,u))})},ZL=(t,e,r,n)=>{if(typeof e=="string")try{let i=bt.lstatSync(xn.resolve(t,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&SA(xn.resolve(t,e.name),r,n),Sd(xn.resolve(t,e.name),r,n)},SA=(t,e,r)=>{let n;try{n=XL(t,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Sd(t,e,r);throw i}return n&&n.length&&n.forEach(i=>ZL(t,i,e,r)),Sd(t,e,r)};CA.exports=Cd;Cd.sync=SA});var OA=b((r4,vd)=>{"use strict";var RA=bA(),Dt=require("fs"),iu=require("path"),FA=vA(),kt=ai(),su=class extends Error{constructor(e,r){super("Cannot extract through symbolic link"),this.path=r,this.symlink=e}get name(){return"SylinkError"}},ou=class extends Error{constructor(e,r){super(r+": Cannot cd into '"+e+"'"),this.path=e,this.code=r}get name(){return"CwdError"}},au=(t,e)=>t.get(kt(e)),xs=(t,e,r)=>t.set(kt(e),r),QL=(t,e)=>{Dt.stat(t,(r,n)=>{(r||!n.isDirectory())&&(r=new ou(t,r&&r.code||"ENOTDIR")),e(r)})};vd.exports=(t,e,r)=>{t=kt(t);let n=e.umask,i=e.mode|448,s=(i&n)!==0,o=e.uid,a=e.gid,u=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,l=e.unlink,f=e.cache,h=kt(e.cwd),m=(p,D)=>{p?r(p):(xs(f,t,!0),D&&u?FA(D,o,a,C=>m(C)):s?Dt.chmod(t,i,r):r())};if(f&&au(f,t)===!0)return m();if(t===h)return QL(t,m);if(c)return RA(t,{mode:i}).then(p=>m(null,p),m);let y=kt(iu.relative(h,t)).split("/");uu(h,y,i,f,l,h,null,m)};var uu=(t,e,r,n,i,s,o,a)=>{if(!e.length)return a(null,o);let u=e.shift(),c=kt(iu.resolve(t+"/"+u));if(au(n,c))return uu(c,e,r,n,i,s,o,a);Dt.mkdir(c,r,TA(c,e,r,n,i,s,o,a))},TA=(t,e,r,n,i,s,o,a)=>u=>{u?Dt.lstat(t,(c,l)=>{if(c)c.path=c.path&&kt(c.path),a(c);else if(l.isDirectory())uu(t,e,r,n,i,s,o,a);else if(i)Dt.unlink(t,f=>{if(f)return a(f);Dt.mkdir(t,r,TA(t,e,r,n,i,s,o,a))});else{if(l.isSymbolicLink())return a(new su(t,t+"/"+e.join("/")));a(u)}}):(o=o||t,uu(t,e,r,n,i,s,o,a))},eN=t=>{let e=!1,r="ENOTDIR";try{e=Dt.statSync(t).isDirectory()}catch(n){r=n.code}finally{if(!e)throw new ou(t,r)}};vd.exports.sync=(t,e)=>{t=kt(t);let r=e.umask,n=e.mode|448,i=(n&r)!==0,s=e.uid,o=e.gid,a=typeof s=="number"&&typeof o=="number"&&(s!==e.processUid||o!==e.processGid),u=e.preserve,c=e.unlink,l=e.cache,f=kt(e.cwd),h=p=>{xs(l,t,!0),p&&a&&FA.sync(p,s,o),i&&Dt.chmodSync(t,n)};if(l&&au(l,t)===!0)return h();if(t===f)return eN(f),h();if(u)return h(RA.sync(t,n));let _=kt(iu.relative(f,t)).split("/"),y=null;for(let p=_.shift(),D=f;p&&(D+="/"+p);p=_.shift())if(D=kt(iu.resolve(D)),!au(l,D))try{Dt.mkdirSync(D,n),y=y||D,xs(l,D,!0)}catch{let N=Dt.lstatSync(D);if(N.isDirectory()){xs(l,D,!0);continue}else if(c){Dt.unlinkSync(D),Dt.mkdirSync(D,n),y=y||D,xs(l,D,!0);continue}else if(N.isSymbolicLink())return new su(D,D+"/"+_.join("/"))}return h(y)}});var Fd=b((n4,kA)=>{var Rd=Object.create(null),{hasOwnProperty:tN}=Object.prototype;kA.exports=t=>(tN.call(Rd,t)||(Rd[t]=t.normalize("NFKD")),Rd[t])});var LA=b((i4,IA)=>{var PA=require("assert"),rN=Fd(),nN=li(),{join:BA}=require("path"),iN=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,sN=iN==="win32";IA.exports=()=>{let t=new Map,e=new Map,r=c=>c.split("/").slice(0,-1).reduce((f,h)=>(f.length&&(h=BA(f[f.length-1],h)),f.push(h||"/"),f),[]),n=new Set,i=c=>{let l=e.get(c);if(!l)throw new Error("function does not have any path reservations");return{paths:l.paths.map(f=>t.get(f)),dirs:[...l.dirs].map(f=>t.get(f))}},s=c=>{let{paths:l,dirs:f}=i(c);return l.every(h=>h[0]===c)&&f.every(h=>h[0]instanceof Set&&h[0].has(c))},o=c=>n.has(c)||!s(c)?!1:(n.add(c),c(()=>a(c)),!0),a=c=>{if(!n.has(c))return!1;let{paths:l,dirs:f}=e.get(c),h=new Set;return l.forEach(m=>{let _=t.get(m);PA.equal(_[0],c),_.length===1?t.delete(m):(_.shift(),typeof _[0]=="function"?h.add(_[0]):_[0].forEach(y=>h.add(y)))}),f.forEach(m=>{let _=t.get(m);PA(_[0]instanceof Set),_[0].size===1&&_.length===1?t.delete(m):_[0].size===1?(_.shift(),h.add(_[0])):_[0].delete(c)}),n.delete(c),h.forEach(m=>o(m)),!0};return{check:s,reserve:(c,l)=>{c=sN?["win32 parallelization disabled"]:c.map(h=>rN(nN(BA(h))).toLowerCase());let f=new Set(c.map(h=>r(h)).reduce((h,m)=>h.concat(m)));return e.set(l,{dirs:f,paths:c}),c.forEach(h=>{let m=t.get(h);m?m.push(l):t.set(h,[l])}),f.forEach(h=>{let m=t.get(h);m?m[m.length-1]instanceof Set?m[m.length-1].add(l):m.push(new Set([l])):t.set(h,[new Set([l])])}),o(l)}}}});var qA=b((s4,MA)=>{var oN=process.env.__FAKE_PLATFORM__||process.platform,aN=oN==="win32",uN=global.__FAKE_TESTING_FS__||require("fs"),{O_CREAT:cN,O_TRUNC:lN,O_WRONLY:fN,UV_FS_O_FILEMAP:NA=0}=uN.constants,hN=aN&&!!NA,dN=512*1024,pN=NA|lN|cN|fN;MA.exports=hN?t=>t<dN?pN:"w":()=>"w"});var Md=b((o4,QA)=>{"use strict";var mN=require("assert"),gN=eu(),re=require("fs"),yN=Ei(),_r=require("path"),JA=OA(),HA=Nh(),_N=LA(),EN=Mh(),ht=ai(),bN=li(),DN=Fd(),jA=Symbol("onEntry"),kd=Symbol("checkFs"),$A=Symbol("checkFs2"),fu=Symbol("pruneCache"),Pd=Symbol("isReusable"),wt=Symbol("makeFs"),Bd=Symbol("file"),Id=Symbol("directory"),hu=Symbol("link"),UA=Symbol("symlink"),GA=Symbol("hardlink"),WA=Symbol("unsupported"),zA=Symbol("checkPath"),Yr=Symbol("mkdir"),Ve=Symbol("onError"),cu=Symbol("pending"),VA=Symbol("pend"),xi=Symbol("unpend"),Td=Symbol("ended"),Od=Symbol("maybeClose"),Ld=Symbol("skip"),Ss=Symbol("doChown"),Cs=Symbol("uid"),vs=Symbol("gid"),Rs=Symbol("checkedCwd"),XA=require("crypto"),ZA=qA(),wN=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Fs=wN==="win32",AN=(t,e)=>{if(!Fs)return re.unlink(t,e);let r=t+".DELETE."+XA.randomBytes(16).toString("hex");re.rename(t,r,n=>{if(n)return e(n);re.unlink(r,e)})},xN=t=>{if(!Fs)return re.unlinkSync(t);let e=t+".DELETE."+XA.randomBytes(16).toString("hex");re.renameSync(t,e),re.unlinkSync(e)},YA=(t,e,r)=>t===t>>>0?t:e===e>>>0?e:r,KA=t=>DN(bN(ht(t))).toLowerCase(),SN=(t,e)=>{e=KA(e);for(let r of t.keys()){let n=KA(r);(n===e||n.indexOf(e+"/")===0)&&t.delete(r)}},CN=t=>{for(let e of t.keys())t.delete(e)},Ts=class extends gN{constructor(e){if(e||(e={}),e.ondone=r=>{this[Td]=!0,this[Od]()},super(e),this[Rs]=!1,this.reservations=_N(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[cu]=0,this[Td]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||Fs,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=ht(_r.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",r=>this[jA](r))}warn(e,r,n={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(n.recoverable=!1),super.warn(e,r,n)}[Od](){this[Td]&&this[cu]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[zA](e){if(this.strip){let r=ht(e.path).split("/");if(r.length<this.strip)return!1;if(e.path=r.slice(this.strip).join("/"),e.type==="Link"){let n=ht(e.linkpath).split("/");if(n.length>=this.strip)e.linkpath=n.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let r=ht(e.path),n=r.split("/");if(n.includes("..")||Fs&&/^[a-z]:\.\.$/i.test(n[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:r}),!1;let[i,s]=EN(r);i&&(e.path=s,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:r}))}if(_r.isAbsolute(e.path)?e.absolute=ht(_r.resolve(e.path)):e.absolute=ht(_r.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:ht(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:r}=_r.win32.parse(e.absolute);e.absolute=r+HA.encode(e.absolute.slice(r.length));let{root:n}=_r.win32.parse(e.path);e.path=n+HA.encode(e.path.slice(n.length))}return!0}[jA](e){if(!this[zA](e))return e.resume();switch(mN.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[kd](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[WA](e)}}[Ve](e,r){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:r}),this[xi](),r.resume())}[Yr](e,r,n){JA(ht(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:r,noChmod:this.noChmod},n)}[Ss](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[Cs](e){return YA(this.uid,e.uid,this.processUid)}[vs](e){return YA(this.gid,e.gid,this.processGid)}[Bd](e,r){let n=e.mode&4095||this.fmode,i=new yN.WriteStream(e.absolute,{flags:ZA(e.size),mode:n,autoClose:!1});i.on("error",u=>{i.fd&&re.close(i.fd,()=>{}),i.write=()=>!0,this[Ve](u,e),r()});let s=1,o=u=>{if(u){i.fd&&re.close(i.fd,()=>{}),this[Ve](u,e),r();return}--s===0&&re.close(i.fd,c=>{c?this[Ve](c,e):this[xi](),r()})};i.on("finish",u=>{let c=e.absolute,l=i.fd;if(e.mtime&&!this.noMtime){s++;let f=e.atime||new Date,h=e.mtime;re.futimes(l,f,h,m=>m?re.utimes(c,f,h,_=>o(_&&m)):o())}if(this[Ss](e)){s++;let f=this[Cs](e),h=this[vs](e);re.fchown(l,f,h,m=>m?re.chown(c,f,h,_=>o(_&&m)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",u=>{this[Ve](u,e),r()}),e.pipe(a)),a.pipe(i)}[Id](e,r){let n=e.mode&4095||this.dmode;this[Yr](e.absolute,n,i=>{if(i){this[Ve](i,e),r();return}let s=1,o=a=>{--s===0&&(r(),this[xi](),e.resume())};e.mtime&&!this.noMtime&&(s++,re.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[Ss](e)&&(s++,re.chown(e.absolute,this[Cs](e),this[vs](e),o)),o()})}[WA](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[UA](e,r){this[hu](e,e.linkpath,"symlink",r)}[GA](e,r){let n=ht(_r.resolve(this.cwd,e.linkpath));this[hu](e,n,"link",r)}[VA](){this[cu]++}[xi](){this[cu]--,this[Od]()}[Ld](e){this[xi](),e.resume()}[Pd](e,r){return e.type==="File"&&!this.unlink&&r.isFile()&&r.nlink<=1&&!Fs}[kd](e){this[VA]();let r=[e.path];e.linkpath&&r.push(e.linkpath),this.reservations.reserve(r,n=>this[$A](e,n))}[fu](e){e.type==="SymbolicLink"?CN(this.dirCache):e.type!=="Directory"&&SN(this.dirCache,e.absolute)}[$A](e,r){this[fu](e);let n=a=>{this[fu](e),r(a)},i=()=>{this[Yr](this.cwd,this.dmode,a=>{if(a){this[Ve](a,e),n();return}this[Rs]=!0,s()})},s=()=>{if(e.absolute!==this.cwd){let a=ht(_r.dirname(e.absolute));if(a!==this.cwd)return this[Yr](a,this.dmode,u=>{if(u){this[Ve](u,e),n();return}o()})}o()},o=()=>{re.lstat(e.absolute,(a,u)=>{if(u&&(this.keep||this.newer&&u.mtime>e.mtime)){this[Ld](e),n();return}if(a||this[Pd](e,u))return this[wt](null,e,n);if(u.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(u.mode&4095)!==e.mode,l=f=>this[wt](f,e,n);return c?re.chmod(e.absolute,e.mode,l):l()}if(e.absolute!==this.cwd)return re.rmdir(e.absolute,c=>this[wt](c,e,n))}if(e.absolute===this.cwd)return this[wt](null,e,n);AN(e.absolute,c=>this[wt](c,e,n))})};this[Rs]?s():i()}[wt](e,r,n){if(e){this[Ve](e,r),n();return}switch(r.type){case"File":case"OldFile":case"ContiguousFile":return this[Bd](r,n);case"Link":return this[GA](r,n);case"SymbolicLink":return this[UA](r,n);case"Directory":case"GNUDumpDir":return this[Id](r,n)}}[hu](e,r,n,i){re[n](r,e.absolute,s=>{s?this[Ve](s,e):(this[xi](),e.resume()),i()})}},lu=t=>{try{return[null,t()]}catch(e){return[e,null]}},Nd=class extends Ts{[wt](e,r){return super[wt](e,r,()=>{})}[kd](e){if(this[fu](e),!this[Rs]){let s=this[Yr](this.cwd,this.dmode);if(s)return this[Ve](s,e);this[Rs]=!0}if(e.absolute!==this.cwd){let s=ht(_r.dirname(e.absolute));if(s!==this.cwd){let o=this[Yr](s,this.dmode);if(o)return this[Ve](o,e)}}let[r,n]=lu(()=>re.lstatSync(e.absolute));if(n&&(this.keep||this.newer&&n.mtime>e.mtime))return this[Ld](e);if(r||this[Pd](e,n))return this[wt](null,e);if(n.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(n.mode&4095)!==e.mode,[a]=o?lu(()=>{re.chmodSync(e.absolute,e.mode)}):[];return this[wt](a,e)}let[s]=lu(()=>re.rmdirSync(e.absolute));this[wt](s,e)}let[i]=e.absolute===this.cwd?[]:lu(()=>xN(e.absolute));this[wt](i,e)}[Bd](e,r){let n=e.mode&4095||this.fmode,i=a=>{let u;try{re.closeSync(s)}catch(c){u=c}(a||u)&&this[Ve](a||u,e),r()},s;try{s=re.openSync(e.absolute,ZA(e.size),n)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[Ve](a,e)),e.pipe(o)),o.on("data",a=>{try{re.writeSync(s,a,0,a.length)}catch(u){i(u)}}),o.on("end",a=>{let u=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,l=e.mtime;try{re.futimesSync(s,c,l)}catch(f){try{re.utimesSync(e.absolute,c,l)}catch{u=f}}}if(this[Ss](e)){let c=this[Cs](e),l=this[vs](e);try{re.fchownSync(s,c,l)}catch(f){try{re.chownSync(e.absolute,c,l)}catch{u=u||f}}}i(u)})}[Id](e,r){let n=e.mode&4095||this.dmode,i=this[Yr](e.absolute,n);if(i){this[Ve](i,e),r();return}if(e.mtime&&!this.noMtime)try{re.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[Ss](e))try{re.chownSync(e.absolute,this[Cs](e),this[vs](e))}catch{}r(),e.resume()}[Yr](e,r){try{return JA.sync(ht(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:r})}catch(n){return n}}[hu](e,r,n,i){try{re[n+"Sync"](r,e.absolute),i(),e.resume()}catch(s){return this[Ve](s,e)}}};Ts.Sync=Nd;QA.exports=Ts});var ix=b((a4,nx)=>{"use strict";var vN=ri(),du=Md(),tx=require("fs"),rx=Ei(),ex=require("path"),qd=li();nx.exports=(t,e,r)=>{typeof t=="function"?(r=t,e=null,t={}):Array.isArray(t)&&(e=t,t={}),typeof e=="function"&&(r=e,e=null),e?e=Array.from(e):e=[];let n=vN(t);if(n.sync&&typeof r=="function")throw new TypeError("callback not supported for sync tar functions");if(!n.file&&typeof r=="function")throw new TypeError("callback only supported with file option");return e.length&&RN(n,e),n.file&&n.sync?FN(n):n.file?TN(n,r):n.sync?ON(n):kN(n)};var RN=(t,e)=>{let r=new Map(e.map(s=>[qd(s),!0])),n=t.filter,i=(s,o)=>{let a=o||ex.parse(s).root||".",u=s===a?!1:r.has(s)?r.get(s):i(ex.dirname(s),a);return r.set(s,u),u};t.filter=n?(s,o)=>n(s,o)&&i(qd(s)):s=>i(qd(s))},FN=t=>{let e=new du.Sync(t),r=t.file,n=tx.statSync(r),i=t.maxReadSize||16*1024*1024;new rx.ReadStreamSync(r,{readSize:i,size:n.size}).pipe(e)},TN=(t,e)=>{let r=new du(t),n=t.maxReadSize||16*1024*1024,i=t.file,s=new Promise((o,a)=>{r.on("error",a),r.on("close",o),tx.stat(i,(u,c)=>{if(u)a(u);else{let l=new rx.ReadStream(i,{readSize:n,size:c.size});l.on("error",a),l.pipe(r)}})});return e?s.then(e,e):s},ON=t=>new du.Sync(t),kN=t=>new du(t)});var sx=b(Te=>{"use strict";Te.c=Te.create=Hw();Te.r=Te.replace=bd();Te.t=Te.list=tu();Te.u=Te.update=Kw();Te.x=Te.extract=ix();Te.Pack=Ha();Te.Unpack=Md();Te.Parse=eu();Te.ReadEntry=Aa();Te.WriteEntry=Yh();Te.Header=ci();Te.Pax=Sa();Te.types=Th()});var br=b((Er,pu)=>{"use strict";Object.defineProperty(Er,"__esModule",{value:!0});var ox=["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","BigInt64Array","BigUint64Array"];function PN(t){return ox.includes(t)}var BN=["Function","Generator","AsyncGenerator","GeneratorFunction","AsyncGeneratorFunction","AsyncFunction","Observable","Array","Buffer","Blob","Object","RegExp","Date","Error","Map","Set","WeakMap","WeakSet","ArrayBuffer","SharedArrayBuffer","DataView","Promise","URL","FormData","URLSearchParams","HTMLElement",...ox];function IN(t){return BN.includes(t)}var LN=["null","undefined","string","number","bigint","boolean","symbol"];function NN(t){return LN.includes(t)}function Si(t){return e=>typeof e===t}var{toString:ax}=Object.prototype,Os=t=>{let e=ax.call(t).slice(8,-1);if(/HTML\w+Element/.test(e)&&E.domElement(t))return"HTMLElement";if(IN(e))return e},fe=t=>e=>Os(e)===t;function E(t){if(t===null)return"null";switch(typeof t){case"undefined":return"undefined";case"string":return"string";case"number":return"number";case"boolean":return"boolean";case"function":return"Function";case"bigint":return"bigint";case"symbol":return"symbol";default:}if(E.observable(t))return"Observable";if(E.array(t))return"Array";if(E.buffer(t))return"Buffer";let e=Os(t);if(e)return e;if(t instanceof String||t instanceof Boolean||t instanceof Number)throw new TypeError("Please don't use object wrappers for primitive types");return"Object"}E.undefined=Si("undefined");E.string=Si("string");var MN=Si("number");E.number=t=>MN(t)&&!E.nan(t);E.bigint=Si("bigint");E.function_=Si("function");E.null_=t=>t===null;E.class_=t=>E.function_(t)&&t.toString().startsWith("class ");E.boolean=t=>t===!0||t===!1;E.symbol=Si("symbol");E.numericString=t=>E.string(t)&&!E.emptyStringOrWhitespace(t)&&!Number.isNaN(Number(t));E.array=(t,e)=>Array.isArray(t)?E.function_(e)?t.every(e):!0:!1;E.buffer=t=>{var e,r,n,i;return(i=(n=(r=(e=t)===null||e===void 0?void 0:e.constructor)===null||r===void 0?void 0:r.isBuffer)===null||n===void 0?void 0:n.call(r,t))!==null&&i!==void 0?i:!1};E.blob=t=>fe("Blob")(t);E.nullOrUndefined=t=>E.null_(t)||E.undefined(t);E.object=t=>!E.null_(t)&&(typeof t=="object"||E.function_(t));E.iterable=t=>{var e;return E.function_((e=t)===null||e===void 0?void 0:e[Symbol.iterator])};E.asyncIterable=t=>{var e;return E.function_((e=t)===null||e===void 0?void 0:e[Symbol.asyncIterator])};E.generator=t=>{var e,r;return E.iterable(t)&&E.function_((e=t)===null||e===void 0?void 0:e.next)&&E.function_((r=t)===null||r===void 0?void 0:r.throw)};E.asyncGenerator=t=>E.asyncIterable(t)&&E.function_(t.next)&&E.function_(t.throw);E.nativePromise=t=>fe("Promise")(t);var qN=t=>{var e,r;return E.function_((e=t)===null||e===void 0?void 0:e.then)&&E.function_((r=t)===null||r===void 0?void 0:r.catch)};E.promise=t=>E.nativePromise(t)||qN(t);E.generatorFunction=fe("GeneratorFunction");E.asyncGeneratorFunction=t=>Os(t)==="AsyncGeneratorFunction";E.asyncFunction=t=>Os(t)==="AsyncFunction";E.boundFunction=t=>E.function_(t)&&!t.hasOwnProperty("prototype");E.regExp=fe("RegExp");E.date=fe("Date");E.error=fe("Error");E.map=t=>fe("Map")(t);E.set=t=>fe("Set")(t);E.weakMap=t=>fe("WeakMap")(t);E.weakSet=t=>fe("WeakSet")(t);E.int8Array=fe("Int8Array");E.uint8Array=fe("Uint8Array");E.uint8ClampedArray=fe("Uint8ClampedArray");E.int16Array=fe("Int16Array");E.uint16Array=fe("Uint16Array");E.int32Array=fe("Int32Array");E.uint32Array=fe("Uint32Array");E.float32Array=fe("Float32Array");E.float64Array=fe("Float64Array");E.bigInt64Array=fe("BigInt64Array");E.bigUint64Array=fe("BigUint64Array");E.arrayBuffer=fe("ArrayBuffer");E.sharedArrayBuffer=fe("SharedArrayBuffer");E.dataView=fe("DataView");E.enumCase=(t,e)=>Object.values(e).includes(t);E.directInstanceOf=(t,e)=>Object.getPrototypeOf(t)===e.prototype;E.urlInstance=t=>fe("URL")(t);E.urlString=t=>{if(!E.string(t))return!1;try{return new URL(t),!0}catch{return!1}};E.truthy=t=>!!t;E.falsy=t=>!t;E.nan=t=>Number.isNaN(t);E.primitive=t=>E.null_(t)||NN(typeof t);E.integer=t=>Number.isInteger(t);E.safeInteger=t=>Number.isSafeInteger(t);E.plainObject=t=>{if(ax.call(t)!=="[object Object]")return!1;let e=Object.getPrototypeOf(t);return e===null||e===Object.getPrototypeOf({})};E.typedArray=t=>PN(Os(t));var HN=t=>E.safeInteger(t)&&t>=0;E.arrayLike=t=>!E.nullOrUndefined(t)&&!E.function_(t)&&HN(t.length);E.inRange=(t,e)=>{if(E.number(e))return t>=Math.min(0,e)&&t<=Math.max(e,0);if(E.array(e)&&e.length===2)return t>=Math.min(...e)&&t<=Math.max(...e);throw new TypeError(`Invalid range: ${JSON.stringify(e)}`)};var jN=1,$N=["innerHTML","ownerDocument","style","attributes","nodeValue"];E.domElement=t=>E.object(t)&&t.nodeType===jN&&E.string(t.nodeName)&&!E.plainObject(t)&&$N.every(e=>e in t);E.observable=t=>{var e,r,n,i;return t?t===((r=(e=t)[Symbol.observable])===null||r===void 0?void 0:r.call(e))||t===((i=(n=t)["@@observable"])===null||i===void 0?void 0:i.call(n)):!1};E.nodeStream=t=>E.object(t)&&E.function_(t.pipe)&&!E.observable(t);E.infinite=t=>t===1/0||t===-1/0;var ux=t=>e=>E.integer(e)&&Math.abs(e%2)===t;E.evenInteger=ux(0);E.oddInteger=ux(1);E.emptyArray=t=>E.array(t)&&t.length===0;E.nonEmptyArray=t=>E.array(t)&&t.length>0;E.emptyString=t=>E.string(t)&&t.length===0;var UN=t=>E.string(t)&&!/\S/.test(t);E.emptyStringOrWhitespace=t=>E.emptyString(t)||UN(t);E.nonEmptyString=t=>E.string(t)&&t.length>0;E.nonEmptyStringAndNotWhitespace=t=>E.string(t)&&!E.emptyStringOrWhitespace(t);E.emptyObject=t=>E.object(t)&&!E.map(t)&&!E.set(t)&&Object.keys(t).length===0;E.nonEmptyObject=t=>E.object(t)&&!E.map(t)&&!E.set(t)&&Object.keys(t).length>0;E.emptySet=t=>E.set(t)&&t.size===0;E.nonEmptySet=t=>E.set(t)&&t.size>0;E.emptyMap=t=>E.map(t)&&t.size===0;E.nonEmptyMap=t=>E.map(t)&&t.size>0;E.propertyKey=t=>E.any([E.string,E.number,E.symbol],t);E.formData=t=>fe("FormData")(t);E.urlSearchParams=t=>fe("URLSearchParams")(t);var cx=(t,e,r)=>{if(!E.function_(e))throw new TypeError(`Invalid predicate: ${JSON.stringify(e)}`);if(r.length===0)throw new TypeError("Invalid number of values");return t.call(r,e)};E.any=(t,...e)=>(E.array(t)?t:[t]).some(n=>cx(Array.prototype.some,n,e));E.all=(t,...e)=>cx(Array.prototype.every,t,e);var P=(t,e,r,n={})=>{if(!t){let{multipleValues:i}=n,s=i?`received values of types ${[...new Set(r.map(o=>`\`${E(o)}\``))].join(", ")}`:`received value of type \`${E(r)}\``;throw new TypeError(`Expected value which is \`${e}\`, ${s}.`)}};Er.assert={undefined:t=>P(E.undefined(t),"undefined",t),string:t=>P(E.string(t),"string",t),number:t=>P(E.number(t),"number",t),bigint:t=>P(E.bigint(t),"bigint",t),function_:t=>P(E.function_(t),"Function",t),null_:t=>P(E.null_(t),"null",t),class_:t=>P(E.class_(t),"Class",t),boolean:t=>P(E.boolean(t),"boolean",t),symbol:t=>P(E.symbol(t),"symbol",t),numericString:t=>P(E.numericString(t),"string with a number",t),array:(t,e)=>{P(E.array(t),"Array",t),e&&t.forEach(e)},buffer:t=>P(E.buffer(t),"Buffer",t),blob:t=>P(E.blob(t),"Blob",t),nullOrUndefined:t=>P(E.nullOrUndefined(t),"null or undefined",t),object:t=>P(E.object(t),"Object",t),iterable:t=>P(E.iterable(t),"Iterable",t),asyncIterable:t=>P(E.asyncIterable(t),"AsyncIterable",t),generator:t=>P(E.generator(t),"Generator",t),asyncGenerator:t=>P(E.asyncGenerator(t),"AsyncGenerator",t),nativePromise:t=>P(E.nativePromise(t),"native Promise",t),promise:t=>P(E.promise(t),"Promise",t),generatorFunction:t=>P(E.generatorFunction(t),"GeneratorFunction",t),asyncGeneratorFunction:t=>P(E.asyncGeneratorFunction(t),"AsyncGeneratorFunction",t),asyncFunction:t=>P(E.asyncFunction(t),"AsyncFunction",t),boundFunction:t=>P(E.boundFunction(t),"Function",t),regExp:t=>P(E.regExp(t),"RegExp",t),date:t=>P(E.date(t),"Date",t),error:t=>P(E.error(t),"Error",t),map:t=>P(E.map(t),"Map",t),set:t=>P(E.set(t),"Set",t),weakMap:t=>P(E.weakMap(t),"WeakMap",t),weakSet:t=>P(E.weakSet(t),"WeakSet",t),int8Array:t=>P(E.int8Array(t),"Int8Array",t),uint8Array:t=>P(E.uint8Array(t),"Uint8Array",t),uint8ClampedArray:t=>P(E.uint8ClampedArray(t),"Uint8ClampedArray",t),int16Array:t=>P(E.int16Array(t),"Int16Array",t),uint16Array:t=>P(E.uint16Array(t),"Uint16Array",t),int32Array:t=>P(E.int32Array(t),"Int32Array",t),uint32Array:t=>P(E.uint32Array(t),"Uint32Array",t),float32Array:t=>P(E.float32Array(t),"Float32Array",t),float64Array:t=>P(E.float64Array(t),"Float64Array",t),bigInt64Array:t=>P(E.bigInt64Array(t),"BigInt64Array",t),bigUint64Array:t=>P(E.bigUint64Array(t),"BigUint64Array",t),arrayBuffer:t=>P(E.arrayBuffer(t),"ArrayBuffer",t),sharedArrayBuffer:t=>P(E.sharedArrayBuffer(t),"SharedArrayBuffer",t),dataView:t=>P(E.dataView(t),"DataView",t),enumCase:(t,e)=>P(E.enumCase(t,e),"EnumCase",t),urlInstance:t=>P(E.urlInstance(t),"URL",t),urlString:t=>P(E.urlString(t),"string with a URL",t),truthy:t=>P(E.truthy(t),"truthy",t),falsy:t=>P(E.falsy(t),"falsy",t),nan:t=>P(E.nan(t),"NaN",t),primitive:t=>P(E.primitive(t),"primitive",t),integer:t=>P(E.integer(t),"integer",t),safeInteger:t=>P(E.safeInteger(t),"integer",t),plainObject:t=>P(E.plainObject(t),"plain object",t),typedArray:t=>P(E.typedArray(t),"TypedArray",t),arrayLike:t=>P(E.arrayLike(t),"array-like",t),domElement:t=>P(E.domElement(t),"HTMLElement",t),observable:t=>P(E.observable(t),"Observable",t),nodeStream:t=>P(E.nodeStream(t),"Node.js Stream",t),infinite:t=>P(E.infinite(t),"infinite number",t),emptyArray:t=>P(E.emptyArray(t),"empty array",t),nonEmptyArray:t=>P(E.nonEmptyArray(t),"non-empty array",t),emptyString:t=>P(E.emptyString(t),"empty string",t),emptyStringOrWhitespace:t=>P(E.emptyStringOrWhitespace(t),"empty string or whitespace",t),nonEmptyString:t=>P(E.nonEmptyString(t),"non-empty string",t),nonEmptyStringAndNotWhitespace:t=>P(E.nonEmptyStringAndNotWhitespace(t),"non-empty string and not whitespace",t),emptyObject:t=>P(E.emptyObject(t),"empty object",t),nonEmptyObject:t=>P(E.nonEmptyObject(t),"non-empty object",t),emptySet:t=>P(E.emptySet(t),"empty set",t),nonEmptySet:t=>P(E.nonEmptySet(t),"non-empty set",t),emptyMap:t=>P(E.emptyMap(t),"empty map",t),nonEmptyMap:t=>P(E.nonEmptyMap(t),"non-empty map",t),propertyKey:t=>P(E.propertyKey(t),"PropertyKey",t),formData:t=>P(E.formData(t),"FormData",t),urlSearchParams:t=>P(E.urlSearchParams(t),"URLSearchParams",t),evenInteger:t=>P(E.evenInteger(t),"even integer",t),oddInteger:t=>P(E.oddInteger(t),"odd integer",t),directInstanceOf:(t,e)=>P(E.directInstanceOf(t,e),"T",t),inRange:(t,e)=>P(E.inRange(t,e),"in range",t),any:(t,...e)=>P(E.any(t,...e),"predicate returns truthy for any value",e,{multipleValues:!0}),all:(t,...e)=>P(E.all(t,...e),"predicate returns truthy for all values",e,{multipleValues:!0})};Object.defineProperties(E,{class:{value:E.class_},function:{value:E.function_},null:{value:E.null_}});Object.defineProperties(Er.assert,{class:{value:Er.assert.class_},function:{value:Er.assert.function_},null:{value:Er.assert.null_}});Er.default=E;pu.exports=E;pu.exports.default=E;pu.exports.assert=Er.assert});var lx=b((c4,Hd)=>{"use strict";var mu=class extends Error{constructor(e){super(e||"Promise was canceled"),this.name="CancelError"}get isCanceled(){return!0}},Ci=class{static fn(e){return(...r)=>new Ci((n,i,s)=>{r.push(s),e(...r).then(n,i)})}constructor(e){this._cancelHandlers=[],this._isPending=!0,this._isCanceled=!1,this._rejectOnCancel=!0,this._promise=new Promise((r,n)=>{this._reject=n;let i=a=>{(!this._isCanceled||!o.shouldReject)&&(this._isPending=!1,r(a))},s=a=>{this._isPending=!1,n(a)},o=a=>{if(!this._isPending)throw new Error("The `onCancel` handler was attached after the promise settled.");this._cancelHandlers.push(a)};return Object.defineProperties(o,{shouldReject:{get:()=>this._rejectOnCancel,set:a=>{this._rejectOnCancel=a}}}),e(i,s,o)})}then(e,r){return this._promise.then(e,r)}catch(e){return this._promise.catch(e)}finally(e){return this._promise.finally(e)}cancel(e){if(!(!this._isPending||this._isCanceled)){if(this._isCanceled=!0,this._cancelHandlers.length>0)try{for(let r of this._cancelHandlers)r()}catch(r){this._reject(r);return}this._rejectOnCancel&&this._reject(new mu(e))}}get isCanceled(){return this._isCanceled}};Object.setPrototypeOf(Ci.prototype,Promise.prototype);Hd.exports=Ci;Hd.exports.CancelError=mu});var fx=b(($d,Ud)=>{"use strict";Object.defineProperty($d,"__esModule",{value:!0});function GN(t){return t.encrypted}var jd=(t,e)=>{let r;typeof e=="function"?r={connect:e}:r=e;let n=typeof r.connect=="function",i=typeof r.secureConnect=="function",s=typeof r.close=="function",o=()=>{n&&r.connect(),GN(t)&&i&&(t.authorized?r.secureConnect():t.authorizationError||t.once("secureConnect",r.secureConnect)),s&&t.once("close",r.close)};t.writable&&!t.connecting?o():t.connecting?t.once("connect",o):t.destroyed&&s&&r.close(t._hadError)};$d.default=jd;Ud.exports=jd;Ud.exports.default=jd});var hx=b((Wd,zd)=>{"use strict";Object.defineProperty(Wd,"__esModule",{value:!0});var WN=fx(),zN=require("util"),VN=Number(process.versions.node.split(".")[0]),Gd=t=>{if(t.timings)return t.timings;let e={start:Date.now(),socket:void 0,lookup:void 0,connect:void 0,secureConnect:void 0,upload:void 0,response:void 0,end:void 0,error:void 0,abort:void 0,phases:{wait:void 0,dns:void 0,tcp:void 0,tls:void 0,request:void 0,firstByte:void 0,download:void 0,total:void 0}};t.timings=e;let r=a=>{let u=a.emit.bind(a);a.emit=(c,...l)=>(c==="error"&&(e.error=Date.now(),e.phases.total=e.error-e.start,a.emit=u),u(c,...l))};r(t);let n=()=>{e.abort=Date.now(),(!e.response||VN>=13)&&(e.phases.total=Date.now()-e.start)};t.prependOnceListener("abort",n);let i=a=>{if(e.socket=Date.now(),e.phases.wait=e.socket-e.start,zN.types.isProxy(a))return;let u=()=>{e.lookup=Date.now(),e.phases.dns=e.lookup-e.socket};a.prependOnceListener("lookup",u),WN.default(a,{connect:()=>{e.connect=Date.now(),e.lookup===void 0&&(a.removeListener("lookup",u),e.lookup=e.connect,e.phases.dns=e.lookup-e.socket),e.phases.tcp=e.connect-e.lookup},secureConnect:()=>{e.secureConnect=Date.now(),e.phases.tls=e.secureConnect-e.connect}})};t.socket?i(t.socket):t.prependOnceListener("socket",i);let s=()=>{var a;e.upload=Date.now(),e.phases.request=e.upload-((a=e.secureConnect)!==null&&a!==void 0?a:e.connect)};return(()=>typeof t.writableFinished=="boolean"?t.writableFinished:t.finished&&t.outputSize===0&&(!t.socket||t.socket.writableLength===0))()?s():t.prependOnceListener("finish",s),t.prependOnceListener("response",a=>{e.response=Date.now(),e.phases.firstByte=e.response-e.upload,a.timings=e,r(a),a.prependOnceListener("end",()=>{e.end=Date.now(),e.phases.download=e.end-e.response,e.phases.total=e.end-e.start}),a.prependOnceListener("aborted",n)}),e};Wd.default=Gd;zd.exports=Gd;zd.exports.default=Gd});var Ex=b((l4,Kd)=>{"use strict";var{V4MAPPED:YN,ADDRCONFIG:KN,ALL:_x,promises:{Resolver:dx},lookup:JN}=require("dns"),{promisify:Vd}=require("util"),XN=require("os"),vi=Symbol("cacheableLookupCreateConnection"),Yd=Symbol("cacheableLookupInstance"),px=Symbol("expires"),ZN=typeof _x=="number",mx=t=>{if(!(t&&typeof t.createConnection=="function"))throw new Error("Expected an Agent instance as the first argument")},QN=t=>{for(let e of t)e.family!==6&&(e.address=`::ffff:${e.address}`,e.family=6)},gx=()=>{let t=!1,e=!1;for(let r of Object.values(XN.networkInterfaces()))for(let n of r)if(!n.internal&&(n.family==="IPv6"?e=!0:t=!0,t&&e))return{has4:t,has6:e};return{has4:t,has6:e}},e3=t=>Symbol.iterator in t,yx={ttl:!0},t3={all:!0},gu=class{constructor({cache:e=new Map,maxTtl:r=1/0,fallbackDuration:n=3600,errorTtl:i=.15,resolver:s=new dx,lookup:o=JN}={}){if(this.maxTtl=r,this.errorTtl=i,this._cache=e,this._resolver=s,this._dnsLookup=Vd(o),this._resolver instanceof dx?(this._resolve4=this._resolver.resolve4.bind(this._resolver),this._resolve6=this._resolver.resolve6.bind(this._resolver)):(this._resolve4=Vd(this._resolver.resolve4.bind(this._resolver)),this._resolve6=Vd(this._resolver.resolve6.bind(this._resolver))),this._iface=gx(),this._pending={},this._nextRemovalTime=!1,this._hostnamesToFallback=new Set,n<1)this._fallback=!1;else{this._fallback=!0;let a=setInterval(()=>{this._hostnamesToFallback.clear()},n*1e3);a.unref&&a.unref()}this.lookup=this.lookup.bind(this),this.lookupAsync=this.lookupAsync.bind(this)}set servers(e){this.clear(),this._resolver.setServers(e)}get servers(){return this._resolver.getServers()}lookup(e,r,n){if(typeof r=="function"?(n=r,r={}):typeof r=="number"&&(r={family:r}),!n)throw new Error("Callback must be a function.");this.lookupAsync(e,r).then(i=>{r.all?n(null,i):n(null,i.address,i.family,i.expires,i.ttl)},n)}async lookupAsync(e,r={}){typeof r=="number"&&(r={family:r});let n=await this.query(e);if(r.family===6){let i=n.filter(s=>s.family===6);r.hints&YN&&(ZN&&r.hints&_x||i.length===0)?QN(n):n=i}else r.family===4&&(n=n.filter(i=>i.family===4));if(r.hints&KN){let{_iface:i}=this;n=n.filter(s=>s.family===6?i.has6:i.has4)}if(n.length===0){let i=new Error(`cacheableLookup ENOTFOUND ${e}`);throw i.code="ENOTFOUND",i.hostname=e,i}return r.all?n:n[0]}async query(e){let r=await this._cache.get(e);if(!r){let n=this._pending[e];if(n)r=await n;else{let i=this.queryAndCache(e);this._pending[e]=i;try{r=await i}finally{delete this._pending[e]}}}return r=r.map(n=>({...n})),r}async _resolve(e){let r=async c=>{try{return await c}catch(l){if(l.code==="ENODATA"||l.code==="ENOTFOUND")return[];throw l}},[n,i]=await Promise.all([this._resolve4(e,yx),this._resolve6(e,yx)].map(c=>r(c))),s=0,o=0,a=0,u=Date.now();for(let c of n)c.family=4,c.expires=u+c.ttl*1e3,s=Math.max(s,c.ttl);for(let c of i)c.family=6,c.expires=u+c.ttl*1e3,o=Math.max(o,c.ttl);return n.length>0?i.length>0?a=Math.min(s,o):a=s:a=o,{entries:[...n,...i],cacheTtl:a}}async _lookup(e){try{return{entries:await this._dnsLookup(e,{all:!0}),cacheTtl:0}}catch{return{entries:[],cacheTtl:0}}}async _set(e,r,n){if(this.maxTtl>0&&n>0){n=Math.min(n,this.maxTtl)*1e3,r[px]=Date.now()+n;try{await this._cache.set(e,r,n)}catch(i){this.lookupAsync=async()=>{let s=new Error("Cache Error. Please recreate the CacheableLookup instance.");throw s.cause=i,s}}e3(this._cache)&&this._tick(n)}}async queryAndCache(e){if(this._hostnamesToFallback.has(e))return this._dnsLookup(e,t3);let r=await this._resolve(e);r.entries.length===0&&this._fallback&&(r=await this._lookup(e),r.entries.length!==0&&this._hostnamesToFallback.add(e));let n=r.entries.length===0?this.errorTtl:r.cacheTtl;return await this._set(e,r.entries,n),r.entries}_tick(e){let r=this._nextRemovalTime;(!r||e<r)&&(clearTimeout(this._removalTimeout),this._nextRemovalTime=e,this._removalTimeout=setTimeout(()=>{this._nextRemovalTime=!1;let n=1/0,i=Date.now();for(let[s,o]of this._cache){let a=o[px];i>=a?this._cache.delete(s):a<n&&(n=a)}n!==1/0&&this._tick(n-i)},e),this._removalTimeout.unref&&this._removalTimeout.unref())}install(e){if(mx(e),vi in e)throw new Error("CacheableLookup has been already installed");e[vi]=e.createConnection,e[Yd]=this,e.createConnection=(r,n)=>("lookup"in r||(r.lookup=this.lookup),e[vi](r,n))}uninstall(e){if(mx(e),e[vi]){if(e[Yd]!==this)throw new Error("The agent is not owned by this CacheableLookup instance");e.createConnection=e[vi],delete e[vi],delete e[Yd]}}updateInterfaceInfo(){let{_iface:e}=this;this._iface=gx(),(e.has4&&!this._iface.has4||e.has6&&!this._iface.has6)&&this._cache.clear()}clear(e){if(e){this._cache.delete(e);return}this._cache.clear()}};Kd.exports=gu;Kd.exports.default=gu});var wx=b((f4,Dx)=>{"use strict";var r3="text/plain",n3="us-ascii",bx=(t,e)=>e.some(r=>r instanceof RegExp?r.test(t):r===t),i3=(t,{stripHash:e})=>{let r=/^data:(?<type>[^,]*?),(?<data>[^#]*?)(?:#(?<hash>.*))?$/.exec(t);if(!r)throw new Error(`Invalid URL: ${t}`);let{type:n,data:i,hash:s}=r.groups,o=n.split(";");s=e?"":s;let a=!1;o[o.length-1]==="base64"&&(o.pop(),a=!0);let u=(o.shift()||"").toLowerCase(),l=[...o.map(f=>{let[h,m=""]=f.split("=").map(_=>_.trim());return h==="charset"&&(m=m.toLowerCase(),m===n3)?"":`${h}${m?`=${m}`:""}`}).filter(Boolean)];return a&&l.push("base64"),(l.length!==0||u&&u!==r3)&&l.unshift(u),`data:${l.join(";")},${a?i.trim():i}${s?`#${s}`:""}`},s3=(t,e)=>{if(e={defaultProtocol:"http:",normalizeProtocol:!0,forceHttp:!1,forceHttps:!1,stripAuthentication:!0,stripHash:!1,stripTextFragment:!0,stripWWW:!0,removeQueryParameters:[/^utm_\w+/i],removeTrailingSlash:!0,removeSingleSlash:!0,removeDirectoryIndex:!1,sortQueryParameters:!0,...e},t=t.trim(),/^data:/i.test(t))return i3(t,e);if(/^view-source:/i.test(t))throw new Error("`view-source:` is not supported as it is a non-standard protocol");let r=t.startsWith("//");!r&&/^\.*\//.test(t)||(t=t.replace(/^(?!(?:\w+:)?\/\/)|^\/\//,e.defaultProtocol));let i=new URL(t);if(e.forceHttp&&e.forceHttps)throw new Error("The `forceHttp` and `forceHttps` options cannot be used together");if(e.forceHttp&&i.protocol==="https:"&&(i.protocol="http:"),e.forceHttps&&i.protocol==="http:"&&(i.protocol="https:"),e.stripAuthentication&&(i.username="",i.password=""),e.stripHash?i.hash="":e.stripTextFragment&&(i.hash=i.hash.replace(/#?:~:text.*?$/i,"")),i.pathname&&(i.pathname=i.pathname.replace(/(?<!\b(?:[a-z][a-z\d+\-.]{1,50}:))\/{2,}/g,"/")),i.pathname)try{i.pathname=decodeURI(i.pathname)}catch{}if(e.removeDirectoryIndex===!0&&(e.removeDirectoryIndex=[/^index\.[a-z]+$/]),Array.isArray(e.removeDirectoryIndex)&&e.removeDirectoryIndex.length>0){let o=i.pathname.split("/"),a=o[o.length-1];bx(a,e.removeDirectoryIndex)&&(o=o.slice(0,o.length-1),i.pathname=o.slice(1).join("/")+"/")}if(i.hostname&&(i.hostname=i.hostname.replace(/\.$/,""),e.stripWWW&&/^www\.(?!www\.)(?:[a-z\-\d]{1,63})\.(?:[a-z.\-\d]{2,63})$/.test(i.hostname)&&(i.hostname=i.hostname.replace(/^www\./,""))),Array.isArray(e.removeQueryParameters))for(let o of[...i.searchParams.keys()])bx(o,e.removeQueryParameters)&&i.searchParams.delete(o);e.removeQueryParameters===!0&&(i.search=""),e.sortQueryParameters&&i.searchParams.sort(),e.removeTrailingSlash&&(i.pathname=i.pathname.replace(/\/$/,""));let s=t;return t=i.toString(),!e.removeSingleSlash&&i.pathname==="/"&&!s.endsWith("/")&&i.hash===""&&(t=t.replace(/\/$/,"")),(e.removeTrailingSlash||i.pathname==="/")&&i.hash===""&&e.removeSingleSlash&&(t=t.replace(/\/$/,"")),r&&!e.normalizeProtocol&&(t=t.replace(/^http:\/\//,"//")),e.stripProtocol&&(t=t.replace(/^(?:https?:)?\/\//,"")),t};Dx.exports=s3});var Sx=b((h4,xx)=>{xx.exports=Ax;function Ax(t,e){if(t&&e)return Ax(t)(e);if(typeof t!="function")throw new TypeError("need wrapper function");return Object.keys(t).forEach(function(n){r[n]=t[n]}),r;function r(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var s=t.apply(this,n),o=n[n.length-1];return typeof s=="function"&&s!==o&&Object.keys(o).forEach(function(a){s[a]=o[a]}),s}}});var Xd=b((d4,Jd)=>{var Cx=Sx();Jd.exports=Cx(yu);Jd.exports.strict=Cx(vx);yu.proto=yu(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return yu(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return vx(this)},configurable:!0})});function yu(t){var e=function(){return e.called?e.value:(e.called=!0,e.value=t.apply(this,arguments))};return e.called=!1,e}function vx(t){var e=function(){if(e.called)throw new Error(e.onceError);return e.called=!0,e.value=t.apply(this,arguments)},r=t.name||"Function wrapped with `once`";return e.onceError=r+" shouldn't be called more than once",e.called=!1,e}});var Tx=b((p4,Fx)=>{var o3=Xd(),a3=function(){},u3=function(t){return t.setHeader&&typeof t.abort=="function"},c3=function(t){return t.stdio&&Array.isArray(t.stdio)&&t.stdio.length===3},Rx=function(t,e,r){if(typeof e=="function")return Rx(t,null,e);e||(e={}),r=o3(r||a3);var n=t._writableState,i=t._readableState,s=e.readable||e.readable!==!1&&t.readable,o=e.writable||e.writable!==!1&&t.writable,a=!1,u=function(){t.writable||c()},c=function(){o=!1,s||r.call(t)},l=function(){s=!1,o||r.call(t)},f=function(p){r.call(t,p?new Error("exited with error code: "+p):null)},h=function(p){r.call(t,p)},m=function(){process.nextTick(_)},_=function(){if(!a){if(s&&!(i&&i.ended&&!i.destroyed))return r.call(t,new Error("premature close"));if(o&&!(n&&n.ended&&!n.destroyed))return r.call(t,new Error("premature close"))}},y=function(){t.req.on("finish",c)};return u3(t)?(t.on("complete",c),t.on("abort",m),t.req?y():t.on("request",y)):o&&!n&&(t.on("end",u),t.on("close",u)),c3(t)&&t.on("exit",f),t.on("end",l),t.on("finish",c),e.error!==!1&&t.on("error",h),t.on("close",m),function(){a=!0,t.removeListener("complete",c),t.removeListener("abort",m),t.removeListener("request",y),t.req&&t.req.removeListener("finish",c),t.removeListener("end",u),t.removeListener("close",u),t.removeListener("finish",c),t.removeListener("exit",f),t.removeListener("end",l),t.removeListener("error",h),t.removeListener("close",m)}};Fx.exports=Rx});var Px=b((m4,kx)=>{var l3=Xd(),f3=Tx(),Zd=require("fs"),ks=function(){},h3=/^v?\.0/.test(process.version),_u=function(t){return typeof t=="function"},d3=function(t){return!h3||!Zd?!1:(t instanceof(Zd.ReadStream||ks)||t instanceof(Zd.WriteStream||ks))&&_u(t.close)},p3=function(t){return t.setHeader&&_u(t.abort)},m3=function(t,e,r,n){n=l3(n);var i=!1;t.on("close",function(){i=!0}),f3(t,{readable:e,writable:r},function(o){if(o)return n(o);i=!0,n()});var s=!1;return function(o){if(!i&&!s){if(s=!0,d3(t))return t.close(ks);if(p3(t))return t.abort();if(_u(t.destroy))return t.destroy();n(o||new Error("stream was destroyed"))}}},Ox=function(t){t()},g3=function(t,e){return t.pipe(e)},y3=function(){var t=Array.prototype.slice.call(arguments),e=_u(t[t.length-1]||ks)&&t.pop()||ks;if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Error("pump requires two streams per minimum");var r,n=t.map(function(i,s){var o=s<t.length-1,a=s>0;return m3(i,o,a,function(u){r||(r=u),u&&n.forEach(Ox),!o&&(n.forEach(Ox),e(r))})});return t.reduce(g3)};kx.exports=y3});var Ix=b((g4,Bx)=>{"use strict";var{PassThrough:_3}=require("stream");Bx.exports=t=>{t={...t};let{array:e}=t,{encoding:r}=t,n=r==="buffer",i=!1;e?i=!(r||n):r=r||"utf8",n&&(r=null);let s=new _3({objectMode:i});r&&s.setEncoding(r);let o=0,a=[];return s.on("data",u=>{a.push(u),i?o=a.length:o+=u.length}),s.getBufferedValue=()=>e?a:n?Buffer.concat(a,o):a.join(""),s.getBufferedLength=()=>o,s}});var Lx=b((y4,Ri)=>{"use strict";var{constants:E3}=require("buffer"),b3=Px(),D3=Ix(),Eu=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};async function bu(t,e){if(!t)return Promise.reject(new Error("Expected a stream"));e={maxBuffer:1/0,...e};let{maxBuffer:r}=e,n;return await new Promise((i,s)=>{let o=a=>{a&&n.getBufferedLength()<=E3.MAX_LENGTH&&(a.bufferedData=n.getBufferedValue()),s(a)};n=b3(t,D3(e),a=>{if(a){o(a);return}i()}),n.on("data",()=>{n.getBufferedLength()>r&&o(new Eu)})}),n.getBufferedValue()}Ri.exports=bu;Ri.exports.default=bu;Ri.exports.buffer=(t,e)=>bu(t,{...e,encoding:"buffer"});Ri.exports.array=(t,e)=>bu(t,{...e,array:!0});Ri.exports.MaxBufferError=Eu});var Mx=b((E4,Nx)=>{"use strict";var w3=new Set([200,203,204,206,300,301,308,404,405,410,414,501]),A3=new Set([200,203,204,300,301,302,303,307,308,404,405,410,414,501]),x3=new Set([500,502,503,504]),S3={date:!0,connection:!0,"keep-alive":!0,"proxy-authenticate":!0,"proxy-authorization":!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0},C3={"content-length":!0,"content-encoding":!0,"transfer-encoding":!0,"content-range":!0};function Sn(t){let e=parseInt(t,10);return isFinite(e)?e:0}function v3(t){return t?x3.has(t.status):!0}function Qd(t){let e={};if(!t)return e;let r=t.trim().split(/,/);for(let n of r){let[i,s]=n.split(/=/,2);e[i.trim()]=s===void 0?!0:s.trim().replace(/^"|"$/g,"")}return e}function R3(t){let e=[];for(let r in t){let n=t[r];e.push(n===!0?r:r+"="+n)}if(e.length)return e.join(", ")}Nx.exports=class{constructor(e,r,{shared:n,cacheHeuristic:i,immutableMinTimeToLive:s,ignoreCargoCult:o,_fromObject:a}={}){if(a){this._fromObject(a);return}if(!r||!r.headers)throw Error("Response headers missing");this._assertRequestHasHeaders(e),this._responseTime=this.now(),this._isShared=n!==!1,this._cacheHeuristic=i!==void 0?i:.1,this._immutableMinTtl=s!==void 0?s:24*3600*1e3,this._status="status"in r?r.status:200,this._resHeaders=r.headers,this._rescc=Qd(r.headers["cache-control"]),this._method="method"in e?e.method:"GET",this._url=e.url,this._host=e.headers.host,this._noAuthorization=!e.headers.authorization,this._reqHeaders=r.headers.vary?e.headers:null,this._reqcc=Qd(e.headers["cache-control"]),o&&"pre-check"in this._rescc&&"post-check"in this._rescc&&(delete this._rescc["pre-check"],delete this._rescc["post-check"],delete this._rescc["no-cache"],delete this._rescc["no-store"],delete this._rescc["must-revalidate"],this._resHeaders=Object.assign({},this._resHeaders,{"cache-control":R3(this._rescc)}),delete this._resHeaders.expires,delete this._resHeaders.pragma),r.headers["cache-control"]==null&&/no-cache/.test(r.headers.pragma)&&(this._rescc["no-cache"]=!0)}now(){return Date.now()}storable(){return!!(!this._reqcc["no-store"]&&(this._method==="GET"||this._method==="HEAD"||this._method==="POST"&&this._hasExplicitExpiration())&&A3.has(this._status)&&!this._rescc["no-store"]&&(!this._isShared||!this._rescc.private)&&(!this._isShared||this._noAuthorization||this._allowsStoringAuthenticated())&&(this._resHeaders.expires||this._rescc["max-age"]||this._isShared&&this._rescc["s-maxage"]||this._rescc.public||w3.has(this._status)))}_hasExplicitExpiration(){return this._isShared&&this._rescc["s-maxage"]||this._rescc["max-age"]||this._resHeaders.expires}_assertRequestHasHeaders(e){if(!e||!e.headers)throw Error("Request headers missing")}satisfiesWithoutRevalidation(e){this._assertRequestHasHeaders(e);let r=Qd(e.headers["cache-control"]);return r["no-cache"]||/no-cache/.test(e.headers.pragma)||r["max-age"]&&this.age()>r["max-age"]||r["min-fresh"]&&this.timeToLive()<1e3*r["min-fresh"]||this.stale()&&!(r["max-stale"]&&!this._rescc["must-revalidate"]&&(r["max-stale"]===!0||r["max-stale"]>this.age()-this.maxAge()))?!1:this._requestMatches(e,!1)}_requestMatches(e,r){return(!this._url||this._url===e.url)&&this._host===e.headers.host&&(!e.method||this._method===e.method||r&&e.method==="HEAD")&&this._varyMatches(e)}_allowsStoringAuthenticated(){return this._rescc["must-revalidate"]||this._rescc.public||this._rescc["s-maxage"]}_varyMatches(e){if(!this._resHeaders.vary)return!0;if(this._resHeaders.vary==="*")return!1;let r=this._resHeaders.vary.trim().toLowerCase().split(/\s*,\s*/);for(let n of r)if(e.headers[n]!==this._reqHeaders[n])return!1;return!0}_copyWithoutHopByHopHeaders(e){let r={};for(let n in e)S3[n]||(r[n]=e[n]);if(e.connection){let n=e.connection.trim().split(/\s*,\s*/);for(let i of n)delete r[i]}if(r.warning){let n=r.warning.split(/,/).filter(i=>!/^\s*1[0-9][0-9]/.test(i));n.length?r.warning=n.join(",").trim():delete r.warning}return r}responseHeaders(){let e=this._copyWithoutHopByHopHeaders(this._resHeaders),r=this.age();return r>3600*24&&!this._hasExplicitExpiration()&&this.maxAge()>3600*24&&(e.warning=(e.warning?`${e.warning}, `:"")+'113 - "rfc7234 5.5.4"'),e.age=`${Math.round(r)}`,e.date=new Date(this.now()).toUTCString(),e}date(){let e=Date.parse(this._resHeaders.date);return isFinite(e)?e:this._responseTime}age(){let e=this._ageValue(),r=(this.now()-this._responseTime)/1e3;return e+r}_ageValue(){return Sn(this._resHeaders.age)}maxAge(){if(!this.storable()||this._rescc["no-cache"]||this._isShared&&this._resHeaders["set-cookie"]&&!this._rescc.public&&!this._rescc.immutable||this._resHeaders.vary==="*")return 0;if(this._isShared){if(this._rescc["proxy-revalidate"])return 0;if(this._rescc["s-maxage"])return Sn(this._rescc["s-maxage"])}if(this._rescc["max-age"])return Sn(this._rescc["max-age"]);let e=this._rescc.immutable?this._immutableMinTtl:0,r=this.date();if(this._resHeaders.expires){let n=Date.parse(this._resHeaders.expires);return Number.isNaN(n)||n<r?0:Math.max(e,(n-r)/1e3)}if(this._resHeaders["last-modified"]){let n=Date.parse(this._resHeaders["last-modified"]);if(isFinite(n)&&r>n)return Math.max(e,(r-n)/1e3*this._cacheHeuristic)}return e}timeToLive(){let e=this.maxAge()-this.age(),r=e+Sn(this._rescc["stale-if-error"]),n=e+Sn(this._rescc["stale-while-revalidate"]);return Math.max(0,e,r,n)*1e3}stale(){return this.maxAge()<=this.age()}_useStaleIfError(){return this.maxAge()+Sn(this._rescc["stale-if-error"])>this.age()}useStaleWhileRevalidate(){return this.maxAge()+Sn(this._rescc["stale-while-revalidate"])>this.age()}static fromObject(e){return new this(void 0,void 0,{_fromObject:e})}_fromObject(e){if(this._responseTime)throw Error("Reinitialized");if(!e||e.v!==1)throw Error("Invalid serialization");this._responseTime=e.t,this._isShared=e.sh,this._cacheHeuristic=e.ch,this._immutableMinTtl=e.imm!==void 0?e.imm:24*3600*1e3,this._status=e.st,this._resHeaders=e.resh,this._rescc=e.rescc,this._method=e.m,this._url=e.u,this._host=e.h,this._noAuthorization=e.a,this._reqHeaders=e.reqh,this._reqcc=e.reqcc}toObject(){return{v:1,t:this._responseTime,sh:this._isShared,ch:this._cacheHeuristic,imm:this._immutableMinTtl,st:this._status,resh:this._resHeaders,rescc:this._rescc,m:this._method,u:this._url,h:this._host,a:this._noAuthorization,reqh:this._reqHeaders,reqcc:this._reqcc}}revalidationHeaders(e){this._assertRequestHasHeaders(e);let r=this._copyWithoutHopByHopHeaders(e.headers);if(delete r["if-range"],!this._requestMatches(e,!0)||!this.storable())return delete r["if-none-match"],delete r["if-modified-since"],r;if(this._resHeaders.etag&&(r["if-none-match"]=r["if-none-match"]?`${r["if-none-match"]}, ${this._resHeaders.etag}`:this._resHeaders.etag),r["accept-ranges"]||r["if-match"]||r["if-unmodified-since"]||this._method&&this._method!="GET"){if(delete r["if-modified-since"],r["if-none-match"]){let i=r["if-none-match"].split(/,/).filter(s=>!/^\s*W\//.test(s));i.length?r["if-none-match"]=i.join(",").trim():delete r["if-none-match"]}}else this._resHeaders["last-modified"]&&!r["if-modified-since"]&&(r["if-modified-since"]=this._resHeaders["last-modified"]);return r}revalidatedPolicy(e,r){if(this._assertRequestHasHeaders(e),this._useStaleIfError()&&v3(r))return{modified:!1,matches:!1,policy:this};if(!r||!r.headers)throw Error("Response headers missing");let n=!1;if(r.status!==void 0&&r.status!=304?n=!1:r.headers.etag&&!/^\s*W\//.test(r.headers.etag)?n=this._resHeaders.etag&&this._resHeaders.etag.replace(/^\s*W\//,"")===r.headers.etag:this._resHeaders.etag&&r.headers.etag?n=this._resHeaders.etag.replace(/^\s*W\//,"")===r.headers.etag.replace(/^\s*W\//,""):this._resHeaders["last-modified"]?n=this._resHeaders["last-modified"]===r.headers["last-modified"]:!this._resHeaders.etag&&!this._resHeaders["last-modified"]&&!r.headers.etag&&!r.headers["last-modified"]&&(n=!0),!n)return{policy:new this.constructor(e,r),modified:r.status!=304,matches:!1};let i={};for(let o in this._resHeaders)i[o]=o in r.headers&&!C3[o]?r.headers[o]:this._resHeaders[o];let s=Object.assign({},r,{status:this._status,method:this._method,headers:i});return{policy:new this.constructor(e,s,{shared:this._isShared,cacheHeuristic:this._cacheHeuristic,immutableMinTimeToLive:this._immutableMinTtl}),modified:!1,matches:!0}}}});var Du=b((b4,qx)=>{"use strict";qx.exports=t=>{let e={};for(let[r,n]of Object.entries(t))e[r.toLowerCase()]=n;return e}});var jx=b((D4,Hx)=>{"use strict";var F3=require("stream").Readable,T3=Du(),ep=class extends F3{constructor(e,r,n,i){if(typeof e!="number")throw new TypeError("Argument `statusCode` should be a number");if(typeof r!="object")throw new TypeError("Argument `headers` should be an object");if(!(n instanceof Buffer))throw new TypeError("Argument `body` should be a buffer");if(typeof i!="string")throw new TypeError("Argument `url` should be a string");super(),this.statusCode=e,this.headers=T3(r),this.body=n,this.url=i}_read(){this.push(this.body),this.push(null)}};Hx.exports=ep});var Ux=b((w4,$x)=>{"use strict";var O3=["destroy","setTimeout","socket","headers","trailers","rawHeaders","statusCode","httpVersion","httpVersionMinor","httpVersionMajor","rawTrailers","statusMessage"];$x.exports=(t,e)=>{let r=new Set(Object.keys(t).concat(O3));for(let n of r)n in e||(e[n]=typeof t[n]=="function"?t[n].bind(t):t[n])}});var Wx=b((A4,Gx)=>{"use strict";var k3=require("stream").PassThrough,P3=Ux(),B3=t=>{if(!(t&&t.pipe))throw new TypeError("Parameter `response` must be a response stream.");let e=new k3;return P3(t,e),t.pipe(e)};Gx.exports=B3});var zx=b(tp=>{tp.stringify=function t(e){if(typeof e=="undefined")return e;if(e&&Buffer.isBuffer(e))return JSON.stringify(":base64:"+e.toString("base64"));if(e&&e.toJSON&&(e=e.toJSON()),e&&typeof e=="object"){var r="",n=Array.isArray(e);r=n?"[":"{";var i=!0;for(var s in e){var o=typeof e[s]=="function"||!n&&typeof e[s]=="undefined";Object.hasOwnProperty.call(e,s)&&!o&&(i||(r+=","),i=!1,n?e[s]==null?r+="null":r+=t(e[s]):e[s]!==void 0&&(r+=t(s)+":"+t(e[s])))}return r+=n?"]":"}",r}else return typeof e=="string"?JSON.stringify(/^:/.test(e)?":"+e:e):typeof e=="undefined"?"null":JSON.stringify(e)};tp.parse=function(t){return JSON.parse(t,function(e,r){return typeof r=="string"?/^:base64:/.test(r)?Buffer.from(r.substring(8),"base64"):/^:/.test(r)?r.substring(1):r:r})}});var Jx=b((S4,Kx)=>{"use strict";var I3=require("events"),Vx=zx(),L3=t=>{let e={redis:"@keyv/redis",rediss:"@keyv/redis",mongodb:"@keyv/mongo",mongo:"@keyv/mongo",sqlite:"@keyv/sqlite",postgresql:"@keyv/postgres",postgres:"@keyv/postgres",mysql:"@keyv/mysql",etcd:"@keyv/etcd",offline:"@keyv/offline",tiered:"@keyv/tiered"};if(t.adapter||t.uri){let r=t.adapter||/^[^:+]*/.exec(t.uri)[0];return new(require(e[r]))(t)}return new Map},Yx=["sqlite","postgres","mysql","mongo","redis","tiered"],rp=class extends I3{constructor(e,{emitErrors:r=!0,...n}={}){if(super(),this.opts={namespace:"keyv",serialize:Vx.stringify,deserialize:Vx.parse,...typeof e=="string"?{uri:e}:e,...n},!this.opts.store){let s={...this.opts};this.opts.store=L3(s)}if(this.opts.compression){let s=this.opts.compression;this.opts.serialize=s.serialize.bind(s),this.opts.deserialize=s.deserialize.bind(s)}typeof this.opts.store.on=="function"&&r&&this.opts.store.on("error",s=>this.emit("error",s)),this.opts.store.namespace=this.opts.namespace;let i=s=>async function*(){for await(let[o,a]of typeof s=="function"?s(this.opts.store.namespace):s){let u=this.opts.deserialize(a);if(!(this.opts.store.namespace&&!o.includes(this.opts.store.namespace))){if(typeof u.expires=="number"&&Date.now()>u.expires){this.delete(o);continue}yield[this._getKeyUnprefix(o),u.value]}}};typeof this.opts.store[Symbol.iterator]=="function"&&this.opts.store instanceof Map?this.iterator=i(this.opts.store):typeof this.opts.store.iterator=="function"&&this.opts.store.opts&&this._checkIterableAdaptar()&&(this.iterator=i(this.opts.store.iterator.bind(this.opts.store)))}_checkIterableAdaptar(){return Yx.includes(this.opts.store.opts.dialect)||Yx.findIndex(e=>this.opts.store.opts.url.includes(e))>=0}_getKeyPrefix(e){return`${this.opts.namespace}:${e}`}_getKeyPrefixArray(e){return e.map(r=>`${this.opts.namespace}:${r}`)}_getKeyUnprefix(e){return e.split(":").splice(1).join(":")}get(e,r){let{store:n}=this.opts,i=Array.isArray(e),s=i?this._getKeyPrefixArray(e):this._getKeyPrefix(e);if(i&&n.getMany===void 0){let o=[];for(let a of s)o.push(Promise.resolve().then(()=>n.get(a)).then(u=>typeof u=="string"?this.opts.deserialize(u):this.opts.compression?this.opts.deserialize(u):u).then(u=>{if(u!=null)return typeof u.expires=="number"&&Date.now()>u.expires?this.delete(a).then(()=>{}):r&&r.raw?u:u.value}));return Promise.allSettled(o).then(a=>{let u=[];for(let c of a)u.push(c.value);return u})}return Promise.resolve().then(()=>i?n.getMany(s):n.get(s)).then(o=>typeof o=="string"?this.opts.deserialize(o):this.opts.compression?this.opts.deserialize(o):o).then(o=>{if(o!=null){if(i){let a=[];for(let u of o){if(typeof u=="string"&&(u=this.opts.deserialize(u)),u==null){a.push(void 0);continue}typeof u.expires=="number"&&Date.now()>u.expires?(this.delete(e).then(()=>{}),a.push(void 0)):a.push(r&&r.raw?u:u.value)}return a}return typeof o.expires=="number"&&Date.now()>o.expires?this.delete(e).then(()=>{}):r&&r.raw?o:o.value}})}set(e,r,n){let i=this._getKeyPrefix(e);typeof n=="undefined"&&(n=this.opts.ttl),n===0&&(n=void 0);let{store:s}=this.opts;return Promise.resolve().then(()=>{let o=typeof n=="number"?Date.now()+n:null;return typeof r=="symbol"&&this.emit("error","symbol cannot be serialized"),r={value:r,expires:o},this.opts.serialize(r)}).then(o=>s.set(i,o,n)).then(()=>!0)}delete(e){let{store:r}=this.opts;if(Array.isArray(e)){let i=this._getKeyPrefixArray(e);if(r.deleteMany===void 0){let s=[];for(let o of i)s.push(r.delete(o));return Promise.allSettled(s).then(o=>o.every(a=>a.value===!0))}return Promise.resolve().then(()=>r.deleteMany(i))}let n=this._getKeyPrefix(e);return Promise.resolve().then(()=>r.delete(n))}clear(){let{store:e}=this.opts;return Promise.resolve().then(()=>e.clear())}has(e){let r=this._getKeyPrefix(e),{store:n}=this.opts;return Promise.resolve().then(async()=>typeof n.has=="function"?n.has(r):await n.get(r)!==void 0)}disconnect(){let{store:e}=this.opts;if(typeof e.disconnect=="function")return e.disconnect()}};Kx.exports=rp});var Qx=b((C4,Zx)=>{"use strict";var N3=require("events"),wu=require("url"),M3=wx(),q3=Lx(),np=Mx(),Xx=jx(),H3=Du(),j3=Wx(),$3=Jx(),Pt=class{constructor(e,r){if(typeof e!="function")throw new TypeError("Parameter `request` must be a function");return this.cache=new $3({uri:typeof r=="string"&&r,store:typeof r!="string"&&r,namespace:"cacheable-request"}),this.createCacheableRequest(e)}createCacheableRequest(e){return(r,n)=>{let i;if(typeof r=="string")i=ip(wu.parse(r)),r={};else if(r instanceof wu.URL)i=ip(wu.parse(r.toString())),r={};else{let[f,...h]=(r.path||"").split("?"),m=h.length>0?`?${h.join("?")}`:"";i=ip({...r,pathname:f,search:m})}r={headers:{},method:"GET",cache:!0,strictTtl:!1,automaticFailover:!1,...r,...U3(i)},r.headers=H3(r.headers);let s=new N3,o=M3(wu.format(i),{stripWWW:!1,removeTrailingSlash:!1,stripAuthentication:!1}),a=`${r.method}:${o}`,u=!1,c=!1,l=f=>{c=!0;let h=!1,m,_=new Promise(p=>{m=()=>{h||(h=!0,p())}}),y=p=>{if(u&&!f.forceRefresh){p.status=p.statusCode;let C=np.fromObject(u.cachePolicy).revalidatedPolicy(f,p);if(!C.modified){let N=C.policy.responseHeaders();p=new Xx(u.statusCode,N,u.body,u.url),p.cachePolicy=C.policy,p.fromCache=!0}}p.fromCache||(p.cachePolicy=new np(f,p,f),p.fromCache=!1);let D;f.cache&&p.cachePolicy.storable()?(D=j3(p),(async()=>{try{let C=q3.buffer(p);if(await Promise.race([_,new Promise(Y=>p.once("end",Y))]),h)return;let N=await C,U={cachePolicy:p.cachePolicy.toObject(),url:p.url,statusCode:p.fromCache?u.statusCode:p.statusCode,body:N},M=f.strictTtl?p.cachePolicy.timeToLive():void 0;f.maxTtl&&(M=M?Math.min(M,f.maxTtl):f.maxTtl),await this.cache.set(a,U,M)}catch(C){s.emit("error",new Pt.CacheError(C))}})()):f.cache&&u&&(async()=>{try{await this.cache.delete(a)}catch(C){s.emit("error",new Pt.CacheError(C))}})(),s.emit("response",D||p),typeof n=="function"&&n(D||p)};try{let p=e(f,y);p.once("error",m),p.once("abort",m),s.emit("request",p)}catch(p){s.emit("error",new Pt.RequestError(p))}};return(async()=>{let f=async m=>{await Promise.resolve();let _=m.cache?await this.cache.get(a):void 0;if(typeof _=="undefined")return l(m);let y=np.fromObject(_.cachePolicy);if(y.satisfiesWithoutRevalidation(m)&&!m.forceRefresh){let p=y.responseHeaders(),D=new Xx(_.statusCode,p,_.body,_.url);D.cachePolicy=y,D.fromCache=!0,s.emit("response",D),typeof n=="function"&&n(D)}else u=_,m.headers=y.revalidationHeaders(m),l(m)},h=m=>s.emit("error",new Pt.CacheError(m));this.cache.once("error",h),s.on("response",()=>this.cache.removeListener("error",h));try{await f(r)}catch(m){r.automaticFailover&&!c&&l(r),s.emit("error",new Pt.CacheError(m))}})(),s}}};function U3(t){let e={...t};return e.path=`${t.pathname||"/"}${t.search||""}`,delete e.pathname,delete e.search,e}function ip(t){return{protocol:t.protocol,auth:t.auth,hostname:t.hostname||t.host||"localhost",port:t.port,pathname:t.pathname,search:t.search}}Pt.RequestError=class extends Error{constructor(t){super(t.message),this.name="RequestError",Object.assign(this,t)}};Pt.CacheError=class extends Error{constructor(t){super(t.message),this.name="CacheError",Object.assign(this,t)}};Zx.exports=Pt});var tS=b((F4,eS)=>{"use strict";var G3=["aborted","complete","headers","httpVersion","httpVersionMinor","httpVersionMajor","method","rawHeaders","rawTrailers","setTimeout","socket","statusCode","statusMessage","trailers","url"];eS.exports=(t,e)=>{if(e._readableState.autoDestroy)throw new Error("The second stream must have the `autoDestroy` option set to `false`");let r=new Set(Object.keys(t).concat(G3)),n={};for(let i of r)i in e||(n[i]={get(){let s=t[i];return typeof s=="function"?s.bind(t):s},set(s){t[i]=s},enumerable:!0,configurable:!1});return Object.defineProperties(e,n),t.once("aborted",()=>{e.destroy(),e.emit("aborted")}),t.once("close",()=>{t.complete&&e.readable?e.once("end",()=>{e.emit("close")}):e.emit("close")}),e}});var nS=b((T4,rS)=>{"use strict";var{Transform:W3,PassThrough:z3}=require("stream"),sp=require("zlib"),V3=tS();rS.exports=t=>{let e=(t.headers["content-encoding"]||"").toLowerCase();if(!["gzip","deflate","br"].includes(e))return t;let r=e==="br";if(r&&typeof sp.createBrotliDecompress!="function")return t.destroy(new Error("Brotli is not supported on Node.js < 12")),t;let n=!0,i=new W3({transform(a,u,c){n=!1,c(null,a)},flush(a){a()}}),s=new z3({autoDestroy:!1,destroy(a,u){t.destroy(),u(a)}}),o=r?sp.createBrotliDecompress():sp.createUnzip();return o.once("error",a=>{if(n&&!t.readable){s.end();return}s.destroy(a)}),V3(t,s),t.pipe(i).pipe(o).pipe(s),s}});var ap=b((O4,iS)=>{"use strict";var op=class{constructor(e={}){if(!(e.maxSize&&e.maxSize>0))throw new TypeError("`maxSize` must be a number greater than 0");this.maxSize=e.maxSize,this.onEviction=e.onEviction,this.cache=new Map,this.oldCache=new Map,this._size=0}_set(e,r){if(this.cache.set(e,r),this._size++,this._size>=this.maxSize){if(this._size=0,typeof this.onEviction=="function")for(let[n,i]of this.oldCache.entries())this.onEviction(n,i);this.oldCache=this.cache,this.cache=new Map}}get(e){if(this.cache.has(e))return this.cache.get(e);if(this.oldCache.has(e)){let r=this.oldCache.get(e);return this.oldCache.delete(e),this._set(e,r),r}}set(e,r){return this.cache.has(e)?this.cache.set(e,r):this._set(e,r),this}has(e){return this.cache.has(e)||this.oldCache.has(e)}peek(e){if(this.cache.has(e))return this.cache.get(e);if(this.oldCache.has(e))return this.oldCache.get(e)}delete(e){let r=this.cache.delete(e);return r&&this._size--,this.oldCache.delete(e)||r}clear(){this.cache.clear(),this.oldCache.clear(),this._size=0}*keys(){for(let[e]of this)yield e}*values(){for(let[,e]of this)yield e}*[Symbol.iterator](){for(let e of this.cache)yield e;for(let e of this.oldCache){let[r]=e;this.cache.has(r)||(yield e)}}get size(){let e=0;for(let r of this.oldCache.keys())this.cache.has(r)||e++;return Math.min(this._size+e,this.maxSize)}};iS.exports=op});var cp=b((k4,uS)=>{"use strict";var Y3=require("events"),K3=require("tls"),J3=require("http2"),X3=ap(),Ze=Symbol("currentStreamsCount"),sS=Symbol("request"),At=Symbol("cachedOriginSet"),Fi=Symbol("gracefullyClosing"),Z3=["maxDeflateDynamicTableSize","maxSessionMemory","maxHeaderListPairs","maxOutstandingPings","maxReservedRemoteStreams","maxSendHeaderBlockLength","paddingStrategy","localAddress","path","rejectUnauthorized","minDHSize","ca","cert","clientCertEngine","ciphers","key","pfx","servername","minVersion","maxVersion","secureProtocol","crl","honorCipherOrder","ecdhCurve","dhparam","secureOptions","sessionIdContext"],Q3=(t,e,r)=>{let n=0,i=t.length;for(;n<i;){let s=n+i>>>1;r(t[s],e)?n=s+1:i=s}return n},e8=(t,e)=>t.remoteSettings.maxConcurrentStreams>e.remoteSettings.maxConcurrentStreams,up=(t,e)=>{for(let r of t)r[At].length<e[At].length&&r[At].every(n=>e[At].includes(n))&&r[Ze]+e[Ze]<=e.remoteSettings.maxConcurrentStreams&&aS(r)},t8=(t,e)=>{for(let r of t)e[At].length<r[At].length&&e[At].every(n=>r[At].includes(n))&&e[Ze]+r[Ze]<=r.remoteSettings.maxConcurrentStreams&&aS(e)},oS=({agent:t,isFree:e})=>{let r={};for(let n in t.sessions){let s=t.sessions[n].filter(o=>{let a=o[Kt.kCurrentStreamsCount]<o.remoteSettings.maxConcurrentStreams;return e?a:!a});s.length!==0&&(r[n]=s)}return r},aS=t=>{t[Fi]=!0,t[Ze]===0&&t.close()},Kt=class extends Y3{constructor({timeout:e=6e4,maxSessions:r=1/0,maxFreeSessions:n=10,maxCachedTlsSessions:i=100}={}){super(),this.sessions={},this.queue={},this.timeout=e,this.maxSessions=r,this.maxFreeSessions=n,this._freeSessionsCount=0,this._sessionsCount=0,this.settings={enablePush:!1},this.tlsSessionCache=new X3({maxSize:i})}static normalizeOrigin(e,r){return typeof e=="string"&&(e=new URL(e)),r&&e.hostname!==r&&(e.hostname=r),e.origin}normalizeOptions(e){let r="";if(e)for(let n of Z3)e[n]&&(r+=`:${e[n]}`);return r}_tryToCreateNewSession(e,r){if(!(e in this.queue)||!(r in this.queue[e]))return;let n=this.queue[e][r];this._sessionsCount<this.maxSessions&&!n.completed&&(n.completed=!0,n())}getSession(e,r,n){return new Promise((i,s)=>{Array.isArray(n)?(n=[...n],i()):n=[{resolve:i,reject:s}];let o=this.normalizeOptions(r),a=Kt.normalizeOrigin(e,r&&r.servername);if(a===void 0){for(let{reject:l}of n)l(new TypeError("The `origin` argument needs to be a string or an URL object"));return}if(o in this.sessions){let l=this.sessions[o],f=-1,h=-1,m;for(let _ of l){let y=_.remoteSettings.maxConcurrentStreams;if(y<f)break;if(_[At].includes(a)){let p=_[Ze];if(p>=y||_[Fi]||_.destroyed)continue;m||(f=y),p>h&&(m=_,h=p)}}if(m){if(n.length!==1){for(let{reject:_}of n){let y=new Error(`Expected the length of listeners to be 1, got ${n.length}.
Please report this to https://github.com/szmarczak/http2-wrapper/`);_(y)}return}n[0].resolve(m);return}}if(o in this.queue){if(a in this.queue[o]){this.queue[o][a].listeners.push(...n),this._tryToCreateNewSession(o,a);return}}else this.queue[o]={};let u=()=>{o in this.queue&&this.queue[o][a]===c&&(delete this.queue[o][a],Object.keys(this.queue[o]).length===0&&delete this.queue[o])},c=()=>{let l=`${a}:${o}`,f=!1;try{let h=J3.connect(e,{createConnection:this.createConnection,settings:this.settings,session:this.tlsSessionCache.get(l),...r});h[Ze]=0,h[Fi]=!1;let m=()=>h[Ze]<h.remoteSettings.maxConcurrentStreams,_=!0;h.socket.once("session",p=>{this.tlsSessionCache.set(l,p)}),h.once("error",p=>{for(let{reject:D}of n)D(p);this.tlsSessionCache.delete(l)}),h.setTimeout(this.timeout,()=>{h.destroy()}),h.once("close",()=>{if(f){_&&this._freeSessionsCount--,this._sessionsCount--;let p=this.sessions[o];p.splice(p.indexOf(h),1),p.length===0&&delete this.sessions[o]}else{let p=new Error("Session closed without receiving a SETTINGS frame");p.code="HTTP2WRAPPER_NOSETTINGS";for(let{reject:D}of n)D(p);u()}this._tryToCreateNewSession(o,a)});let y=()=>{if(!(!(o in this.queue)||!m())){for(let p of h[At])if(p in this.queue[o]){let{listeners:D}=this.queue[o][p];for(;D.length!==0&&m();)D.shift().resolve(h);let C=this.queue[o];if(C[p].listeners.length===0&&(delete C[p],Object.keys(C).length===0)){delete this.queue[o];break}if(!m())break}}};h.on("origin",()=>{h[At]=h.originSet,m()&&(y(),up(this.sessions[o],h))}),h.once("remoteSettings",()=>{if(h.ref(),h.unref(),this._sessionsCount++,c.destroyed){let p=new Error("Agent has been destroyed");for(let D of n)D.reject(p);h.destroy();return}h[At]=h.originSet;{let p=this.sessions;if(o in p){let D=p[o];D.splice(Q3(D,h,e8),0,h)}else p[o]=[h]}this._freeSessionsCount+=1,f=!0,this.emit("session",h),y(),u(),h[Ze]===0&&this._freeSessionsCount>this.maxFreeSessions&&h.close(),n.length!==0&&(this.getSession(a,r,n),n.length=0),h.on("remoteSettings",()=>{y(),up(this.sessions[o],h)})}),h[sS]=h.request,h.request=(p,D)=>{if(h[Fi])throw new Error("The session is gracefully closing. No new streams are allowed.");let C=h[sS](p,D);return h.ref(),++h[Ze],h[Ze]===h.remoteSettings.maxConcurrentStreams&&this._freeSessionsCount--,C.once("close",()=>{if(_=m(),--h[Ze],!h.destroyed&&!h.closed&&(t8(this.sessions[o],h),m()&&!h.closed)){_||(this._freeSessionsCount++,_=!0);let N=h[Ze]===0;N&&h.unref(),N&&(this._freeSessionsCount>this.maxFreeSessions||h[Fi])?h.close():(up(this.sessions[o],h),y())}}),C}}catch(h){for(let m of n)m.reject(h);u()}};c.listeners=n,c.completed=!1,c.destroyed=!1,this.queue[o][a]=c,this._tryToCreateNewSession(o,a)})}request(e,r,n,i){return new Promise((s,o)=>{this.getSession(e,r,[{reject:o,resolve:a=>{try{s(a.request(n,i))}catch(u){o(u)}}}])})}createConnection(e,r){return Kt.connect(e,r)}static connect(e,r){r.ALPNProtocols=["h2"];let n=e.port||443,i=e.hostname||e.host;return typeof r.servername=="undefined"&&(r.servername=i),K3.connect(n,i,r)}closeFreeSessions(){for(let e of Object.values(this.sessions))for(let r of e)r[Ze]===0&&r.close()}destroy(e){for(let r of Object.values(this.sessions))for(let n of r)n.destroy(e);for(let r of Object.values(this.queue))for(let n of Object.values(r))n.destroyed=!0;this.queue={}}get freeSessions(){return oS({agent:this,isFree:!0})}get busySessions(){return oS({agent:this,isFree:!1})}};Kt.kCurrentStreamsCount=Ze;Kt.kGracefullyClosing=Fi;uS.exports={Agent:Kt,globalAgent:new Kt}});var fp=b((P4,cS)=>{"use strict";var{Readable:r8}=require("stream"),lp=class extends r8{constructor(e,r){super({highWaterMark:r,autoDestroy:!1}),this.statusCode=null,this.statusMessage="",this.httpVersion="2.0",this.httpVersionMajor=2,this.httpVersionMinor=0,this.headers={},this.trailers={},this.req=null,this.aborted=!1,this.complete=!1,this.upgrade=null,this.rawHeaders=[],this.rawTrailers=[],this.socket=e,this.connection=e,this._dumped=!1}_destroy(e){this.req._request.destroy(e)}setTimeout(e,r){return this.req.setTimeout(e,r),this}_dump(){this._dumped||(this._dumped=!0,this.removeAllListeners("data"),this.resume())}_read(){this.req&&this.req._request.resume()}};cS.exports=lp});var hp=b((B4,lS)=>{"use strict";lS.exports=t=>{let e={protocol:t.protocol,hostname:typeof t.hostname=="string"&&t.hostname.startsWith("[")?t.hostname.slice(1,-1):t.hostname,host:t.host,hash:t.hash,search:t.search,pathname:t.pathname,href:t.href,path:`${t.pathname||""}${t.search||""}`};return typeof t.port=="string"&&t.port.length!==0&&(e.port=Number(t.port)),(t.username||t.password)&&(e.auth=`${t.username||""}:${t.password||""}`),e}});var hS=b((I4,fS)=>{"use strict";fS.exports=(t,e,r)=>{for(let n of r)t.on(n,(...i)=>e.emit(n,...i))}});var pS=b((L4,dS)=>{"use strict";dS.exports=t=>{switch(t){case":method":case":scheme":case":authority":case":path":return!0;default:return!1}}});var gS=b((M4,mS)=>{"use strict";var Ti=(t,e,r)=>{mS.exports[e]=class extends t{constructor(...i){super(typeof r=="string"?r:r(i)),this.name=`${super.name} [${e}]`,this.code=e}}};Ti(TypeError,"ERR_INVALID_ARG_TYPE",t=>{let e=t[0].includes(".")?"property":"argument",r=t[1],n=Array.isArray(r);return n&&(r=`${r.slice(0,-1).join(", ")} or ${r.slice(-1)}`),`The "${t[0]}" ${e} must be ${n?"one of":"of"} type ${r}. Received ${typeof t[2]}`});Ti(TypeError,"ERR_INVALID_PROTOCOL",t=>`Protocol "${t[0]}" not supported. Expected "${t[1]}"`);Ti(Error,"ERR_HTTP_HEADERS_SENT",t=>`Cannot ${t[0]} headers after they are sent to the client`);Ti(TypeError,"ERR_INVALID_HTTP_TOKEN",t=>`${t[0]} must be a valid HTTP token [${t[1]}]`);Ti(TypeError,"ERR_HTTP_INVALID_HEADER_VALUE",t=>`Invalid value "${t[0]} for header "${t[1]}"`);Ti(TypeError,"ERR_INVALID_CHAR",t=>`Invalid character in ${t[0]} [${t[1]}]`)});var yp=b((q4,AS)=>{"use strict";var n8=require("http2"),{Writable:i8}=require("stream"),{Agent:yS,globalAgent:s8}=cp(),o8=fp(),a8=hp(),u8=hS(),c8=pS(),{ERR_INVALID_ARG_TYPE:dp,ERR_INVALID_PROTOCOL:l8,ERR_HTTP_HEADERS_SENT:_S,ERR_INVALID_HTTP_TOKEN:f8,ERR_HTTP_INVALID_HEADER_VALUE:h8,ERR_INVALID_CHAR:d8}=gS(),{HTTP2_HEADER_STATUS:ES,HTTP2_HEADER_METHOD:bS,HTTP2_HEADER_PATH:DS,HTTP2_METHOD_CONNECT:p8}=n8.constants,Ye=Symbol("headers"),pp=Symbol("origin"),mp=Symbol("session"),wS=Symbol("options"),Au=Symbol("flushedHeaders"),Ps=Symbol("jobs"),m8=/^[\^`\-\w!#$%&*+.|~]+$/,g8=/[^\t\u0020-\u007E\u0080-\u00FF]/,gp=class extends i8{constructor(e,r,n){super({autoDestroy:!1});let i=typeof e=="string"||e instanceof URL;if(i&&(e=a8(e instanceof URL?e:new URL(e))),typeof r=="function"||r===void 0?(n=r,r=i?e:{...e}):r={...e,...r},r.h2session)this[mp]=r.h2session;else if(r.agent===!1)this.agent=new yS({maxFreeSessions:0});else if(typeof r.agent=="undefined"||r.agent===null)typeof r.createConnection=="function"?(this.agent=new yS({maxFreeSessions:0}),this.agent.createConnection=r.createConnection):this.agent=s8;else if(typeof r.agent.request=="function")this.agent=r.agent;else throw new dp("options.agent",["Agent-like Object","undefined","false"],r.agent);if(r.protocol&&r.protocol!=="https:")throw new l8(r.protocol,"https:");let s=r.port||r.defaultPort||this.agent&&this.agent.defaultPort||443,o=r.hostname||r.host||"localhost";delete r.hostname,delete r.host,delete r.port;let{timeout:a}=r;if(r.timeout=void 0,this[Ye]=Object.create(null),this[Ps]=[],this.socket=null,this.connection=null,this.method=r.method||"GET",this.path=r.path,this.res=null,this.aborted=!1,this.reusedSocket=!1,r.headers)for(let[u,c]of Object.entries(r.headers))this.setHeader(u,c);r.auth&&!("authorization"in this[Ye])&&(this[Ye].authorization="Basic "+Buffer.from(r.auth).toString("base64")),r.session=r.tlsSession,r.path=r.socketPath,this[wS]=r,s===443?(this[pp]=`https://${o}`,":authority"in this[Ye]||(this[Ye][":authority"]=o)):(this[pp]=`https://${o}:${s}`,":authority"in this[Ye]||(this[Ye][":authority"]=`${o}:${s}`)),a&&this.setTimeout(a),n&&this.once("response",n),this[Au]=!1}get method(){return this[Ye][bS]}set method(e){e&&(this[Ye][bS]=e.toUpperCase())}get path(){return this[Ye][DS]}set path(e){e&&(this[Ye][DS]=e)}get _mustNotHaveABody(){return this.method==="GET"||this.method==="HEAD"||this.method==="DELETE"}_write(e,r,n){if(this._mustNotHaveABody){n(new Error("The GET, HEAD and DELETE methods must NOT have a body"));return}this.flushHeaders();let i=()=>this._request.write(e,r,n);this._request?i():this[Ps].push(i)}_final(e){if(this.destroyed)return;this.flushHeaders();let r=()=>{if(this._mustNotHaveABody){e();return}this._request.end(e)};this._request?r():this[Ps].push(r)}abort(){this.res&&this.res.complete||(this.aborted||process.nextTick(()=>this.emit("abort")),this.aborted=!0,this.destroy())}_destroy(e,r){this.res&&this.res._dump(),this._request&&this._request.destroy(),r(e)}async flushHeaders(){if(this[Au]||this.destroyed)return;this[Au]=!0;let e=this.method===p8,r=n=>{if(this._request=n,this.destroyed){n.destroy();return}e||u8(n,this,["timeout","continue","close","error"]);let i=o=>(...a)=>{!this.writable&&!this.destroyed?o(...a):this.once("finish",()=>{o(...a)})};n.once("response",i((o,a,u)=>{let c=new o8(this.socket,n.readableHighWaterMark);this.res=c,c.req=this,c.statusCode=o[ES],c.headers=o,c.rawHeaders=u,c.once("end",()=>{this.aborted?(c.aborted=!0,c.emit("aborted")):(c.complete=!0,c.socket=null,c.connection=null)}),e?(c.upgrade=!0,this.emit("connect",c,n,Buffer.alloc(0))?this.emit("close"):n.destroy()):(n.on("data",l=>{!c._dumped&&!c.push(l)&&n.pause()}),n.once("end",()=>{c.push(null)}),this.emit("response",c)||c._dump())})),n.once("headers",i(o=>this.emit("information",{statusCode:o[ES]}))),n.once("trailers",i((o,a,u)=>{let{res:c}=this;c.trailers=o,c.rawTrailers=u}));let{socket:s}=n.session;this.socket=s,this.connection=s;for(let o of this[Ps])o();this.emit("socket",this.socket)};if(this[mp])try{r(this[mp].request(this[Ye]))}catch(n){this.emit("error",n)}else{this.reusedSocket=!0;try{r(await this.agent.request(this[pp],this[wS],this[Ye]))}catch(n){this.emit("error",n)}}}getHeader(e){if(typeof e!="string")throw new dp("name","string",e);return this[Ye][e.toLowerCase()]}get headersSent(){return this[Au]}removeHeader(e){if(typeof e!="string")throw new dp("name","string",e);if(this.headersSent)throw new _S("remove");delete this[Ye][e.toLowerCase()]}setHeader(e,r){if(this.headersSent)throw new _S("set");if(typeof e!="string"||!m8.test(e)&&!c8(e))throw new f8("Header name",e);if(typeof r=="undefined")throw new h8(r,e);if(g8.test(r))throw new d8("header content",e);this[Ye][e.toLowerCase()]=r}setNoDelay(){}setSocketKeepAlive(){}setTimeout(e,r){let n=()=>this._request.setTimeout(e,r);return this._request?n():this[Ps].push(n),this}get maxHeadersCount(){if(!this.destroyed&&this._request)return this._request.session.localSettings.maxHeaderListSize}set maxHeadersCount(e){}};AS.exports=gp});var SS=b((H4,xS)=>{"use strict";var y8=require("tls");xS.exports=(t={},e=y8.connect)=>new Promise((r,n)=>{let i=!1,s,o=async()=>{await u,s.off("timeout",a),s.off("error",n),t.resolveSocket?(r({alpnProtocol:s.alpnProtocol,socket:s,timeout:i}),i&&(await Promise.resolve(),s.emit("timeout"))):(s.destroy(),r({alpnProtocol:s.alpnProtocol,timeout:i}))},a=async()=>{i=!0,o()},u=(async()=>{try{s=await e(t,o),s.on("error",n),s.once("timeout",a)}catch(c){n(c)}})()})});var vS=b((j4,CS)=>{"use strict";var _8=require("net");CS.exports=t=>{let e=t.host,r=t.headers&&t.headers.host;return r&&(r.startsWith("[")?r.indexOf("]")===-1?e=r:e=r.slice(1,-1):e=r.split(":",1)[0]),_8.isIP(e)?"":e}});var TS=b(($4,Ep)=>{"use strict";var RS=require("http"),_p=require("https"),E8=SS(),b8=ap(),D8=yp(),w8=vS(),A8=hp(),xu=new b8({maxSize:100}),Bs=new Map,FS=(t,e,r)=>{e._httpMessage={shouldKeepAlive:!0};let n=()=>{t.emit("free",e,r)};e.on("free",n);let i=()=>{t.removeSocket(e,r)};e.on("close",i);let s=()=>{t.removeSocket(e,r),e.off("close",i),e.off("free",n),e.off("agentRemove",s)};e.on("agentRemove",s),t.emit("free",e,r)},x8=async t=>{let e=`${t.host}:${t.port}:${t.ALPNProtocols.sort()}`;if(!xu.has(e)){if(Bs.has(e))return(await Bs.get(e)).alpnProtocol;let{path:r,agent:n}=t;t.path=t.socketPath;let i=E8(t);Bs.set(e,i);try{let{socket:s,alpnProtocol:o}=await i;if(xu.set(e,o),t.path=r,o==="h2")s.destroy();else{let{globalAgent:a}=_p,u=_p.Agent.prototype.createConnection;n?n.createConnection===u?FS(n,s,t):s.destroy():a.createConnection===u?FS(a,s,t):s.destroy()}return Bs.delete(e),o}catch(s){throw Bs.delete(e),s}}return xu.get(e)};Ep.exports=async(t,e,r)=>{if((typeof t=="string"||t instanceof URL)&&(t=A8(new URL(t))),typeof e=="function"&&(r=e,e=void 0),e={ALPNProtocols:["h2","http/1.1"],...t,...e,resolveSocket:!0},!Array.isArray(e.ALPNProtocols)||e.ALPNProtocols.length===0)throw new Error("The `ALPNProtocols` option must be an Array with at least one entry");e.protocol=e.protocol||"https:";let n=e.protocol==="https:";e.host=e.hostname||e.host||"localhost",e.session=e.tlsSession,e.servername=e.servername||w8(e),e.port=e.port||(n?443:80),e._defaultAgent=n?_p.globalAgent:RS.globalAgent;let i=e.agent;if(i){if(i.addRequest)throw new Error("The `options.agent` object can contain only `http`, `https` or `http2` properties");e.agent=i[n?"https":"http"]}return n&&await x8(e)==="h2"?(i&&(e.agent=i.http2),new D8(e,r)):RS.request(e,r)};Ep.exports.protocolCache=xu});var kS=b((U4,OS)=>{"use strict";var S8=require("http2"),C8=cp(),bp=yp(),v8=fp(),R8=TS(),F8=(t,e,r)=>new bp(t,e,r),T8=(t,e,r)=>{let n=new bp(t,e,r);return n.end(),n};OS.exports={...S8,ClientRequest:bp,IncomingMessage:v8,...C8,request:F8,get:T8,auto:R8}});var wp=b(Dp=>{"use strict";Object.defineProperty(Dp,"__esModule",{value:!0});var PS=br();Dp.default=t=>PS.default.nodeStream(t)&&PS.default.function_(t.getBoundary)});var NS=b(Ap=>{"use strict";Object.defineProperty(Ap,"__esModule",{value:!0});var IS=require("fs"),LS=require("util"),BS=br(),O8=wp(),k8=LS.promisify(IS.stat);Ap.default=async(t,e)=>{if(e&&"content-length"in e)return Number(e["content-length"]);if(!t)return 0;if(BS.default.string(t))return Buffer.byteLength(t);if(BS.default.buffer(t))return t.length;if(O8.default(t))return LS.promisify(t.getLength.bind(t))();if(t instanceof IS.ReadStream){let{size:r}=await k8(t.path);return r===0?void 0:r}}});var Sp=b(xp=>{"use strict";Object.defineProperty(xp,"__esModule",{value:!0});function P8(t,e,r){let n={};for(let i of r)n[i]=(...s)=>{e.emit(i,...s)},t.on(i,n[i]);return()=>{for(let i of r)t.off(i,n[i])}}xp.default=P8});var MS=b(Cp=>{"use strict";Object.defineProperty(Cp,"__esModule",{value:!0});Cp.default=()=>{let t=[];return{once(e,r,n){e.once(r,n),t.push({origin:e,event:r,fn:n})},unhandleAll(){for(let e of t){let{origin:r,event:n,fn:i}=e;r.removeListener(n,i)}t.length=0}}}});var HS=b(Is=>{"use strict";Object.defineProperty(Is,"__esModule",{value:!0});Is.TimeoutError=void 0;var B8=require("net"),I8=MS(),qS=Symbol("reentry"),L8=()=>{},Su=class extends Error{constructor(e,r){super(`Timeout awaiting '${r}' for ${e}ms`),this.event=r,this.name="TimeoutError",this.code="ETIMEDOUT"}};Is.TimeoutError=Su;Is.default=(t,e,r)=>{if(qS in t)return L8;t[qS]=!0;let n=[],{once:i,unhandleAll:s}=I8.default(),o=(f,h,m)=>{var _;let y=setTimeout(h,f,f,m);(_=y.unref)===null||_===void 0||_.call(y);let p=()=>{clearTimeout(y)};return n.push(p),p},{host:a,hostname:u}=r,c=(f,h)=>{t.destroy(new Su(f,h))},l=()=>{for(let f of n)f();s()};if(t.once("error",f=>{if(l(),t.listenerCount("error")===0)throw f}),t.once("close",l),i(t,"response",f=>{i(f,"end",l)}),typeof e.request!="undefined"&&o(e.request,c,"request"),typeof e.socket!="undefined"){let f=()=>{c(e.socket,"socket")};t.setTimeout(e.socket,f),n.push(()=>{t.removeListener("timeout",f)})}return i(t,"socket",f=>{var h;let{socketPath:m}=t;if(f.connecting){let _=!!(m!=null?m:B8.isIP((h=u!=null?u:a)!==null&&h!==void 0?h:"")!==0);if(typeof e.lookup!="undefined"&&!_&&typeof f.address().address=="undefined"){let y=o(e.lookup,c,"lookup");i(f,"lookup",y)}if(typeof e.connect!="undefined"){let y=()=>o(e.connect,c,"connect");_?i(f,"connect",y()):i(f,"lookup",p=>{p===null&&i(f,"connect",y())})}typeof e.secureConnect!="undefined"&&r.protocol==="https:"&&i(f,"connect",()=>{let y=o(e.secureConnect,c,"secureConnect");i(f,"secureConnect",y)})}if(typeof e.send!="undefined"){let _=()=>o(e.send,c,"send");f.connecting?i(f,"connect",()=>{i(t,"upload-complete",_())}):i(t,"upload-complete",_())}}),typeof e.response!="undefined"&&i(t,"upload-complete",()=>{let f=o(e.response,c,"response");i(t,"response",f)}),l}});var $S=b(vp=>{"use strict";Object.defineProperty(vp,"__esModule",{value:!0});var jS=br();vp.default=t=>{t=t;let e={protocol:t.protocol,hostname:jS.default.string(t.hostname)&&t.hostname.startsWith("[")?t.hostname.slice(1,-1):t.hostname,host:t.host,hash:t.hash,search:t.search,pathname:t.pathname,href:t.href,path:`${t.pathname||""}${t.search||""}`};return jS.default.string(t.port)&&t.port.length>0&&(e.port=Number(t.port)),(t.username||t.password)&&(e.auth=`${t.username||""}:${t.password||""}`),e}});var US=b(Rp=>{"use strict";Object.defineProperty(Rp,"__esModule",{value:!0});var N8=require("url"),M8=["protocol","host","hostname","port","pathname","search"];Rp.default=(t,e)=>{var r,n;if(e.path){if(e.pathname)throw new TypeError("Parameters `path` and `pathname` are mutually exclusive.");if(e.search)throw new TypeError("Parameters `path` and `search` are mutually exclusive.");if(e.searchParams)throw new TypeError("Parameters `path` and `searchParams` are mutually exclusive.")}if(e.search&&e.searchParams)throw new TypeError("Parameters `search` and `searchParams` are mutually exclusive.");if(!t){if(!e.protocol)throw new TypeError("No URL protocol specified");t=`${e.protocol}//${(n=(r=e.hostname)!==null&&r!==void 0?r:e.host)!==null&&n!==void 0?n:""}`}let i=new N8.URL(t);if(e.path){let s=e.path.indexOf("?");s===-1?e.pathname=e.path:(e.pathname=e.path.slice(0,s),e.search=e.path.slice(s+1)),delete e.path}for(let s of M8)e[s]&&(i[s]=e[s].toString());return i}});var GS=b(Tp=>{"use strict";Object.defineProperty(Tp,"__esModule",{value:!0});var Fp=class{constructor(){this.weakMap=new WeakMap,this.map=new Map}set(e,r){typeof e=="object"?this.weakMap.set(e,r):this.map.set(e,r)}get(e){return typeof e=="object"?this.weakMap.get(e):this.map.get(e)}has(e){return typeof e=="object"?this.weakMap.has(e):this.map.has(e)}};Tp.default=Fp});var kp=b(Op=>{"use strict";Object.defineProperty(Op,"__esModule",{value:!0});var q8=async t=>{let e=[],r=0;for await(let n of t)e.push(n),r+=Buffer.byteLength(n);return Buffer.isBuffer(e[0])?Buffer.concat(e,r):Buffer.from(e.join(""))};Op.default=q8});var zS=b(Cn=>{"use strict";Object.defineProperty(Cn,"__esModule",{value:!0});Cn.dnsLookupIpVersionToFamily=Cn.isDnsLookupIpVersion=void 0;var WS={auto:0,ipv4:4,ipv6:6};Cn.isDnsLookupIpVersion=t=>t in WS;Cn.dnsLookupIpVersionToFamily=t=>{if(Cn.isDnsLookupIpVersion(t))return WS[t];throw new Error("Invalid DNS lookup IP version")}});var Pp=b(Cu=>{"use strict";Object.defineProperty(Cu,"__esModule",{value:!0});Cu.isResponseOk=void 0;Cu.isResponseOk=t=>{let{statusCode:e}=t,r=t.request.options.followRedirect?299:399;return e>=200&&e<=r||e===304}});var YS=b(Bp=>{"use strict";Object.defineProperty(Bp,"__esModule",{value:!0});var VS=new Set;Bp.default=t=>{VS.has(t)||(VS.add(t),process.emitWarning(`Got: ${t}`,{type:"DeprecationWarning"}))}});var KS=b(Ip=>{"use strict";Object.defineProperty(Ip,"__esModule",{value:!0});var ge=br(),H8=(t,e)=>{if(ge.default.null_(t.encoding))throw new TypeError("To get a Buffer, set `options.responseType` to `buffer` instead");ge.assert.any([ge.default.string,ge.default.undefined],t.encoding),ge.assert.any([ge.default.boolean,ge.default.undefined],t.resolveBodyOnly),ge.assert.any([ge.default.boolean,ge.default.undefined],t.methodRewriting),ge.assert.any([ge.default.boolean,ge.default.undefined],t.isStream),ge.assert.any([ge.default.string,ge.default.undefined],t.responseType),t.responseType===void 0&&(t.responseType="text");let{retry:r}=t;if(e?t.retry={...e.retry}:t.retry={calculateDelay:n=>n.computedValue,limit:0,methods:[],statusCodes:[],errorCodes:[],maxRetryAfter:void 0},ge.default.object(r)?(t.retry={...t.retry,...r},t.retry.methods=[...new Set(t.retry.methods.map(n=>n.toUpperCase()))],t.retry.statusCodes=[...new Set(t.retry.statusCodes)],t.retry.errorCodes=[...new Set(t.retry.errorCodes)]):ge.default.number(r)&&(t.retry.limit=r),ge.default.undefined(t.retry.maxRetryAfter)&&(t.retry.maxRetryAfter=Math.min(...[t.timeout.request,t.timeout.connect].filter(ge.default.number))),ge.default.object(t.pagination)){e&&(t.pagination={...e.pagination,...t.pagination});let{pagination:n}=t;if(!ge.default.function_(n.transform))throw new Error("`options.pagination.transform` must be implemented");if(!ge.default.function_(n.shouldContinue))throw new Error("`options.pagination.shouldContinue` must be implemented");if(!ge.default.function_(n.filter))throw new TypeError("`options.pagination.filter` must be implemented");if(!ge.default.function_(n.paginate))throw new Error("`options.pagination.paginate` must be implemented")}return t.responseType==="json"&&t.headers.accept===void 0&&(t.headers.accept="application/json"),t};Ip.default=H8});var JS=b(Ls=>{"use strict";Object.defineProperty(Ls,"__esModule",{value:!0});Ls.retryAfterStatusCodes=void 0;Ls.retryAfterStatusCodes=new Set([413,429,503]);var j8=({attemptCount:t,retryOptions:e,error:r,retryAfter:n})=>{if(t>e.limit)return 0;let i=e.methods.includes(r.options.method),s=e.errorCodes.includes(r.code),o=r.response&&e.statusCodes.includes(r.response.statusCode);if(!i||!s&&!o)return 0;if(r.response){if(n)return e.maxRetryAfter===void 0||n>e.maxRetryAfter?0:n;if(r.response.statusCode===413)return 0}let a=Math.random()*100;return 2**(t-1)*1e3+a};Ls.default=j8});var qs=b(ne=>{"use strict";Object.defineProperty(ne,"__esModule",{value:!0});ne.UnsupportedProtocolError=ne.ReadError=ne.TimeoutError=ne.UploadError=ne.CacheError=ne.HTTPError=ne.MaxRedirectsError=ne.RequestError=ne.setNonEnumerableProperties=ne.knownHookEvents=ne.withoutBody=ne.kIsNormalizedAlready=void 0;var XS=require("util"),ZS=require("stream"),$8=require("fs"),Kr=require("url"),QS=require("http"),Lp=require("http"),U8=require("https"),G8=hx(),W8=Ex(),eC=Qx(),z8=nS(),V8=kS(),Y8=Du(),x=br(),K8=NS(),tC=wp(),J8=Sp(),rC=HS(),X8=$S(),nC=US(),Z8=GS(),Q8=kp(),iC=zS(),eM=Pp(),Jr=YS(),tM=KS(),rM=JS(),Np,Ne=Symbol("request"),Fu=Symbol("response"),Oi=Symbol("responseSize"),ki=Symbol("downloadedSize"),Pi=Symbol("bodySize"),Bi=Symbol("uploadedSize"),vu=Symbol("serverResponsesPiped"),sC=Symbol("unproxyEvents"),oC=Symbol("isFromCache"),Mp=Symbol("cancelTimeouts"),aC=Symbol("startedReading"),Ii=Symbol("stopReading"),Ru=Symbol("triggerRead"),Xr=Symbol("body"),Ns=Symbol("jobs"),uC=Symbol("originalResponse"),cC=Symbol("retryTimeout");ne.kIsNormalizedAlready=Symbol("isNormalizedAlready");var nM=x.default.string(process.versions.brotli);ne.withoutBody=new Set(["GET","HEAD"]);ne.knownHookEvents=["init","beforeRequest","beforeRedirect","beforeError","beforeRetry","afterResponse"];function iM(t){for(let e in t){let r=t[e];if(!x.default.string(r)&&!x.default.number(r)&&!x.default.boolean(r)&&!x.default.null_(r)&&!x.default.undefined(r))throw new TypeError(`The \`searchParams\` value '${String(r)}' must be a string, number, boolean or null`)}}function sM(t){return x.default.object(t)&&!("statusCode"in t)}var qp=new Z8.default,oM=async t=>new Promise((e,r)=>{let n=i=>{r(i)};t.pending||e(),t.once("error",n),t.once("ready",()=>{t.off("error",n),e()})}),aM=new Set([300,301,302,303,304,307,308]),uM=["context","body","json","form"];ne.setNonEnumerableProperties=(t,e)=>{let r={};for(let n of t)if(n)for(let i of uM)i in n&&(r[i]={writable:!0,configurable:!0,enumerable:!1,value:n[i]});Object.defineProperties(e,r)};var Ce=class extends Error{constructor(e,r,n){var i,s;if(super(e),Error.captureStackTrace(this,this.constructor),this.name="RequestError",this.code=(i=r.code)!==null&&i!==void 0?i:"ERR_GOT_REQUEST_ERROR",n instanceof Lu?(Object.defineProperty(this,"request",{enumerable:!1,value:n}),Object.defineProperty(this,"response",{enumerable:!1,value:n[Fu]}),Object.defineProperty(this,"options",{enumerable:!1,value:n.options})):Object.defineProperty(this,"options",{enumerable:!1,value:n}),this.timings=(s=this.request)===null||s===void 0?void 0:s.timings,x.default.string(r.stack)&&x.default.string(this.stack)){let o=this.stack.indexOf(this.message)+this.message.length,a=this.stack.slice(o).split(`
`).reverse(),u=r.stack.slice(r.stack.indexOf(r.message)+r.message.length).split(`
`).reverse();for(;u.length!==0&&u[0]===a[0];)a.shift();this.stack=`${this.stack.slice(0,o)}${a.reverse().join(`
`)}${u.reverse().join(`
`)}`}}};ne.RequestError=Ce;var Tu=class extends Ce{constructor(e){super(`Redirected ${e.options.maxRedirects} times. Aborting.`,{},e),this.name="MaxRedirectsError",this.code="ERR_TOO_MANY_REDIRECTS"}};ne.MaxRedirectsError=Tu;var Ou=class extends Ce{constructor(e){super(`Response code ${e.statusCode} (${e.statusMessage})`,{},e.request),this.name="HTTPError",this.code="ERR_NON_2XX_3XX_RESPONSE"}};ne.HTTPError=Ou;var ku=class extends Ce{constructor(e,r){super(e.message,e,r),this.name="CacheError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_CACHE_ACCESS":this.code}};ne.CacheError=ku;var Pu=class extends Ce{constructor(e,r){super(e.message,e,r),this.name="UploadError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_UPLOAD":this.code}};ne.UploadError=Pu;var Bu=class extends Ce{constructor(e,r,n){super(e.message,e,n),this.name="TimeoutError",this.event=e.event,this.timings=r}};ne.TimeoutError=Bu;var Ms=class extends Ce{constructor(e,r){super(e.message,e,r),this.name="ReadError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_READING_RESPONSE_STREAM":this.code}};ne.ReadError=Ms;var Iu=class extends Ce{constructor(e){super(`Unsupported protocol "${e.url.protocol}"`,{},e),this.name="UnsupportedProtocolError",this.code="ERR_UNSUPPORTED_PROTOCOL"}};ne.UnsupportedProtocolError=Iu;var cM=["socket","connect","continue","information","upgrade","timeout"],Lu=class extends ZS.Duplex{constructor(e,r={},n){super({autoDestroy:!1,highWaterMark:0}),this[ki]=0,this[Bi]=0,this.requestInitialized=!1,this[vu]=new Set,this.redirects=[],this[Ii]=!1,this[Ru]=!1,this[Ns]=[],this.retryCount=0,this._progressCallbacks=[];let i=()=>this._unlockWrite(),s=()=>this._lockWrite();this.on("pipe",c=>{c.prependListener("data",i),c.on("data",s),c.prependListener("end",i),c.on("end",s)}),this.on("unpipe",c=>{c.off("data",i),c.off("data",s),c.off("end",i),c.off("end",s)}),this.on("pipe",c=>{c instanceof Lp.IncomingMessage&&(this.options.headers={...c.headers,...this.options.headers})});let{json:o,body:a,form:u}=r;if((o||a||u)&&this._lockWrite(),ne.kIsNormalizedAlready in r)this.options=r;else try{this.options=this.constructor.normalizeArguments(e,r,n)}catch(c){x.default.nodeStream(r.body)&&r.body.destroy(),this.destroy(c);return}(async()=>{var c;try{this.options.body instanceof $8.ReadStream&&await oM(this.options.body);let{url:l}=this.options;if(!l)throw new TypeError("Missing `url` property");if(this.requestUrl=l.toString(),decodeURI(this.requestUrl),await this._finalizeBody(),await this._makeRequest(),this.destroyed){(c=this[Ne])===null||c===void 0||c.destroy();return}for(let f of this[Ns])f();this[Ns].length=0,this.requestInitialized=!0}catch(l){if(l instanceof Ce){this._beforeError(l);return}this.destroyed||this.destroy(l)}})()}static normalizeArguments(e,r,n){var i,s,o,a,u;let c=r;if(x.default.object(e)&&!x.default.urlInstance(e))r={...n,...e,...r};else{if(e&&r&&r.url!==void 0)throw new TypeError("The `url` option is mutually exclusive with the `input` argument");r={...n,...r},e!==void 0&&(r.url=e),x.default.urlInstance(r.url)&&(r.url=new Kr.URL(r.url.toString()))}if(r.cache===!1&&(r.cache=void 0),r.dnsCache===!1&&(r.dnsCache=void 0),x.assert.any([x.default.string,x.default.undefined],r.method),x.assert.any([x.default.object,x.default.undefined],r.headers),x.assert.any([x.default.string,x.default.urlInstance,x.default.undefined],r.prefixUrl),x.assert.any([x.default.object,x.default.undefined],r.cookieJar),x.assert.any([x.default.object,x.default.string,x.default.undefined],r.searchParams),x.assert.any([x.default.object,x.default.string,x.default.undefined],r.cache),x.assert.any([x.default.object,x.default.number,x.default.undefined],r.timeout),x.assert.any([x.default.object,x.default.undefined],r.context),x.assert.any([x.default.object,x.default.undefined],r.hooks),x.assert.any([x.default.boolean,x.default.undefined],r.decompress),x.assert.any([x.default.boolean,x.default.undefined],r.ignoreInvalidCookies),x.assert.any([x.default.boolean,x.default.undefined],r.followRedirect),x.assert.any([x.default.number,x.default.undefined],r.maxRedirects),x.assert.any([x.default.boolean,x.default.undefined],r.throwHttpErrors),x.assert.any([x.default.boolean,x.default.undefined],r.http2),x.assert.any([x.default.boolean,x.default.undefined],r.allowGetBody),x.assert.any([x.default.string,x.default.undefined],r.localAddress),x.assert.any([iC.isDnsLookupIpVersion,x.default.undefined],r.dnsLookupIpVersion),x.assert.any([x.default.object,x.default.undefined],r.https),x.assert.any([x.default.boolean,x.default.undefined],r.rejectUnauthorized),r.https&&(x.assert.any([x.default.boolean,x.default.undefined],r.https.rejectUnauthorized),x.assert.any([x.default.function_,x.default.undefined],r.https.checkServerIdentity),x.assert.any([x.default.string,x.default.object,x.default.array,x.default.undefined],r.https.certificateAuthority),x.assert.any([x.default.string,x.default.object,x.default.array,x.default.undefined],r.https.key),x.assert.any([x.default.string,x.default.object,x.default.array,x.default.undefined],r.https.certificate),x.assert.any([x.default.string,x.default.undefined],r.https.passphrase),x.assert.any([x.default.string,x.default.buffer,x.default.array,x.default.undefined],r.https.pfx)),x.assert.any([x.default.object,x.default.undefined],r.cacheOptions),x.default.string(r.method)?r.method=r.method.toUpperCase():r.method="GET",r.headers===(n==null?void 0:n.headers)?r.headers={...r.headers}:r.headers=Y8({...n==null?void 0:n.headers,...r.headers}),"slashes"in r)throw new TypeError("The legacy `url.Url` has been deprecated. Use `URL` instead.");if("auth"in r)throw new TypeError("Parameter `auth` is deprecated. Use `username` / `password` instead.");if("searchParams"in r&&r.searchParams&&r.searchParams!==(n==null?void 0:n.searchParams)){let m;if(x.default.string(r.searchParams)||r.searchParams instanceof Kr.URLSearchParams)m=new Kr.URLSearchParams(r.searchParams);else{iM(r.searchParams),m=new Kr.URLSearchParams;for(let _ in r.searchParams){let y=r.searchParams[_];y===null?m.append(_,""):y!==void 0&&m.append(_,y)}}(i=n==null?void 0:n.searchParams)===null||i===void 0||i.forEach((_,y)=>{m.has(y)||m.append(y,_)}),r.searchParams=m}if(r.username=(s=r.username)!==null&&s!==void 0?s:"",r.password=(o=r.password)!==null&&o!==void 0?o:"",x.default.undefined(r.prefixUrl)?r.prefixUrl=(a=n==null?void 0:n.prefixUrl)!==null&&a!==void 0?a:"":(r.prefixUrl=r.prefixUrl.toString(),r.prefixUrl!==""&&!r.prefixUrl.endsWith("/")&&(r.prefixUrl+="/")),x.default.string(r.url)){if(r.url.startsWith("/"))throw new Error("`input` must not start with a slash when using `prefixUrl`");r.url=nC.default(r.prefixUrl+r.url,r)}else(x.default.undefined(r.url)&&r.prefixUrl!==""||r.protocol)&&(r.url=nC.default(r.prefixUrl,r));if(r.url){"port"in r&&delete r.port;let{prefixUrl:m}=r;Object.defineProperty(r,"prefixUrl",{set:y=>{let p=r.url;if(!p.href.startsWith(y))throw new Error(`Cannot change \`prefixUrl\` from ${m} to ${y}: ${p.href}`);r.url=new Kr.URL(y+p.href.slice(m.length)),m=y},get:()=>m});let{protocol:_}=r.url;if(_==="unix:"&&(_="http:",r.url=new Kr.URL(`http://unix${r.url.pathname}${r.url.search}`)),r.searchParams&&(r.url.search=r.searchParams.toString()),_!=="http:"&&_!=="https:")throw new Iu(r);r.username===""?r.username=r.url.username:r.url.username=r.username,r.password===""?r.password=r.url.password:r.url.password=r.password}let{cookieJar:l}=r;if(l){let{setCookie:m,getCookieString:_}=l;x.assert.function_(m),x.assert.function_(_),m.length===4&&_.length===0&&(m=XS.promisify(m.bind(r.cookieJar)),_=XS.promisify(_.bind(r.cookieJar)),r.cookieJar={setCookie:m,getCookieString:_})}let{cache:f}=r;if(f&&(qp.has(f)||qp.set(f,new eC((m,_)=>{let y=m[Ne](m,_);return x.default.promise(y)&&(y.once=(p,D)=>{if(p==="error")y.catch(D);else if(p==="abort")(async()=>{try{(await y).once("abort",D)}catch{}})();else throw new Error(`Unknown HTTP2 promise event: ${p}`);return y}),y},f))),r.cacheOptions={...r.cacheOptions},r.dnsCache===!0)Np||(Np=new W8.default),r.dnsCache=Np;else if(!x.default.undefined(r.dnsCache)&&!r.dnsCache.lookup)throw new TypeError(`Parameter \`dnsCache\` must be a CacheableLookup instance or a boolean, got ${x.default(r.dnsCache)}`);x.default.number(r.timeout)?r.timeout={request:r.timeout}:n&&r.timeout!==n.timeout?r.timeout={...n.timeout,...r.timeout}:r.timeout={...r.timeout},r.context||(r.context={});let h=r.hooks===(n==null?void 0:n.hooks);r.hooks={...r.hooks};for(let m of ne.knownHookEvents)if(m in r.hooks)if(x.default.array(r.hooks[m]))r.hooks[m]=[...r.hooks[m]];else throw new TypeError(`Parameter \`${m}\` must be an Array, got ${x.default(r.hooks[m])}`);else r.hooks[m]=[];if(n&&!h)for(let m of ne.knownHookEvents)n.hooks[m].length>0&&(r.hooks[m]=[...n.hooks[m],...r.hooks[m]]);if("family"in r&&Jr.default('"options.family" was never documented, please use "options.dnsLookupIpVersion"'),n!=null&&n.https&&(r.https={...n.https,...r.https}),"rejectUnauthorized"in r&&Jr.default('"options.rejectUnauthorized" is now deprecated, please use "options.https.rejectUnauthorized"'),"checkServerIdentity"in r&&Jr.default('"options.checkServerIdentity" was never documented, please use "options.https.checkServerIdentity"'),"ca"in r&&Jr.default('"options.ca" was never documented, please use "options.https.certificateAuthority"'),"key"in r&&Jr.default('"options.key" was never documented, please use "options.https.key"'),"cert"in r&&Jr.default('"options.cert" was never documented, please use "options.https.certificate"'),"passphrase"in r&&Jr.default('"options.passphrase" was never documented, please use "options.https.passphrase"'),"pfx"in r&&Jr.default('"options.pfx" was never documented, please use "options.https.pfx"'),"followRedirects"in r)throw new TypeError("The `followRedirects` option does not exist. Use `followRedirect` instead.");if(r.agent){for(let m in r.agent)if(m!=="http"&&m!=="https"&&m!=="http2")throw new TypeError(`Expected the \`options.agent\` properties to be \`http\`, \`https\` or \`http2\`, got \`${m}\``)}return r.maxRedirects=(u=r.maxRedirects)!==null&&u!==void 0?u:0,ne.setNonEnumerableProperties([n,c],r),tM.default(r,n)}_lockWrite(){let e=()=>{throw new TypeError("The payload has been already provided")};this.write=e,this.end=e}_unlockWrite(){this.write=super.write,this.end=super.end}async _finalizeBody(){let{options:e}=this,{headers:r}=e,n=!x.default.undefined(e.form),i=!x.default.undefined(e.json),s=!x.default.undefined(e.body),o=n||i||s,a=ne.withoutBody.has(e.method)&&!(e.method==="GET"&&e.allowGetBody);if(this._cannotHaveBody=a,o){if(a)throw new TypeError(`The \`${e.method}\` method cannot be used with a body`);if([s,n,i].filter(u=>u).length>1)throw new TypeError("The `body`, `json` and `form` options are mutually exclusive");if(s&&!(e.body instanceof ZS.Readable)&&!x.default.string(e.body)&&!x.default.buffer(e.body)&&!tC.default(e.body))throw new TypeError("The `body` option must be a stream.Readable, string or Buffer");if(n&&!x.default.object(e.form))throw new TypeError("The `form` option must be an Object");{let u=!x.default.string(r["content-type"]);s?(tC.default(e.body)&&u&&(r["content-type"]=`multipart/form-data; boundary=${e.body.getBoundary()}`),this[Xr]=e.body):n?(u&&(r["content-type"]="application/x-www-form-urlencoded"),this[Xr]=new Kr.URLSearchParams(e.form).toString()):(u&&(r["content-type"]="application/json"),this[Xr]=e.stringifyJson(e.json));let c=await K8.default(this[Xr],e.headers);x.default.undefined(r["content-length"])&&x.default.undefined(r["transfer-encoding"])&&!a&&!x.default.undefined(c)&&(r["content-length"]=String(c))}}else a?this._lockWrite():this._unlockWrite();this[Pi]=Number(r["content-length"])||void 0}async _onResponseBase(e){let{options:r}=this,{url:n}=r;this[uC]=e,r.decompress&&(e=z8(e));let i=e.statusCode,s=e;s.statusMessage=s.statusMessage?s.statusMessage:QS.STATUS_CODES[i],s.url=r.url.toString(),s.requestUrl=this.requestUrl,s.redirectUrls=this.redirects,s.request=this,s.isFromCache=e.fromCache||!1,s.ip=this.ip,s.retryCount=this.retryCount,this[oC]=s.isFromCache,this[Oi]=Number(e.headers["content-length"])||void 0,this[Fu]=e,e.once("end",()=>{this[Oi]=this[ki],this.emit("downloadProgress",this.downloadProgress)}),e.once("error",a=>{e.destroy(),this._beforeError(new Ms(a,this))}),e.once("aborted",()=>{this._beforeError(new Ms({name:"Error",message:"The server aborted pending request",code:"ECONNRESET"},this))}),this.emit("downloadProgress",this.downloadProgress);let o=e.headers["set-cookie"];if(x.default.object(r.cookieJar)&&o){let a=o.map(async u=>r.cookieJar.setCookie(u,n.toString()));r.ignoreInvalidCookies&&(a=a.map(async u=>u.catch(()=>{})));try{await Promise.all(a)}catch(u){this._beforeError(u);return}}if(r.followRedirect&&e.headers.location&&aM.has(i)){if(e.resume(),this[Ne]&&(this[Mp](),delete this[Ne],this[sC]()),(i===303&&r.method!=="GET"&&r.method!=="HEAD"||!r.methodRewriting)&&(r.method="GET","body"in r&&delete r.body,"json"in r&&delete r.json,"form"in r&&delete r.form,this[Xr]=void 0,delete r.headers["content-length"]),this.redirects.length>=r.maxRedirects){this._beforeError(new Tu(this));return}try{let f=function(h){return h.protocol==="unix:"||h.hostname==="unix"},u=Buffer.from(e.headers.location,"binary").toString(),c=new Kr.URL(u,n),l=c.toString();if(decodeURI(l),!f(n)&&f(c)){this._beforeError(new Ce("Cannot redirect to UNIX socket",{},this));return}c.hostname!==n.hostname||c.port!==n.port?("host"in r.headers&&delete r.headers.host,"cookie"in r.headers&&delete r.headers.cookie,"authorization"in r.headers&&delete r.headers.authorization,(r.username||r.password)&&(r.username="",r.password="")):(c.username=r.username,c.password=r.password),this.redirects.push(l),r.url=c;for(let h of r.hooks.beforeRedirect)await h(r,s);this.emit("redirect",s,r),await this._makeRequest()}catch(u){this._beforeError(u);return}return}if(r.isStream&&r.throwHttpErrors&&!eM.isResponseOk(s)){this._beforeError(new Ou(s));return}e.on("readable",()=>{this[Ru]&&this._read()}),this.on("resume",()=>{e.resume()}),this.on("pause",()=>{e.pause()}),e.once("end",()=>{this.push(null)}),this.emit("response",e);for(let a of this[vu])if(!a.headersSent){for(let u in e.headers){let c=r.decompress?u!=="content-encoding":!0,l=e.headers[u];c&&a.setHeader(u,l)}a.statusCode=i}}async _onResponse(e){try{await this._onResponseBase(e)}catch(r){this._beforeError(r)}}_onRequest(e){let{options:r}=this,{timeout:n,url:i}=r;G8.default(e),this[Mp]=rC.default(e,n,i);let s=r.cache?"cacheableResponse":"response";e.once(s,u=>{this._onResponse(u)}),e.once("error",u=>{var c;e.destroy(),(c=e.res)===null||c===void 0||c.removeAllListeners("end"),u=u instanceof rC.TimeoutError?new Bu(u,this.timings,this):new Ce(u.message,u,this),this._beforeError(u)}),this[sC]=J8.default(e,this,cM),this[Ne]=e,this.emit("uploadProgress",this.uploadProgress);let o=this[Xr],a=this.redirects.length===0?this:e;x.default.nodeStream(o)?(o.pipe(a),o.once("error",u=>{this._beforeError(new Pu(u,this))})):(this._unlockWrite(),x.default.undefined(o)?(this._cannotHaveBody||this._noPipe)&&(a.end(),this._lockWrite()):(this._writeRequest(o,void 0,()=>{}),a.end(),this._lockWrite())),this.emit("request",e)}async _createCacheableRequest(e,r){return new Promise((n,i)=>{Object.assign(r,X8.default(e)),delete r.url;let s,o=qp.get(r.cache)(r,async a=>{a._readableState.autoDestroy=!1,s&&(await s).emit("cacheableResponse",a),n(a)});r.url=e,o.once("error",i),o.once("request",async a=>{s=a,n(s)})})}async _makeRequest(){var e,r,n,i,s;let{options:o}=this,{headers:a}=o;for(let D in a)if(x.default.undefined(a[D]))delete a[D];else if(x.default.null_(a[D]))throw new TypeError(`Use \`undefined\` instead of \`null\` to delete the \`${D}\` header`);if(o.decompress&&x.default.undefined(a["accept-encoding"])&&(a["accept-encoding"]=nM?"gzip, deflate, br":"gzip, deflate"),o.cookieJar){let D=await o.cookieJar.getCookieString(o.url.toString());x.default.nonEmptyString(D)&&(o.headers.cookie=D)}for(let D of o.hooks.beforeRequest){let C=await D(o);if(!x.default.undefined(C)){o.request=()=>C;break}}o.body&&this[Xr]!==o.body&&(this[Xr]=o.body);let{agent:u,request:c,timeout:l,url:f}=o;if(o.dnsCache&&!("lookup"in o)&&(o.lookup=o.dnsCache.lookup),f.hostname==="unix"){let D=/(?<socketPath>.+?):(?<path>.+)/.exec(`${f.pathname}${f.search}`);if(D!=null&&D.groups){let{socketPath:C,path:N}=D.groups;Object.assign(o,{socketPath:C,path:N,host:""})}}let h=f.protocol==="https:",m;o.http2?m=V8.auto:m=h?U8.request:QS.request;let _=(e=o.request)!==null&&e!==void 0?e:m,y=o.cache?this._createCacheableRequest:_;u&&!o.http2&&(o.agent=u[h?"https":"http"]),o[Ne]=_,delete o.request,delete o.timeout;let p=o;if(p.shared=(r=o.cacheOptions)===null||r===void 0?void 0:r.shared,p.cacheHeuristic=(n=o.cacheOptions)===null||n===void 0?void 0:n.cacheHeuristic,p.immutableMinTimeToLive=(i=o.cacheOptions)===null||i===void 0?void 0:i.immutableMinTimeToLive,p.ignoreCargoCult=(s=o.cacheOptions)===null||s===void 0?void 0:s.ignoreCargoCult,o.dnsLookupIpVersion!==void 0)try{p.family=iC.dnsLookupIpVersionToFamily(o.dnsLookupIpVersion)}catch{throw new Error("Invalid `dnsLookupIpVersion` option value")}o.https&&("rejectUnauthorized"in o.https&&(p.rejectUnauthorized=o.https.rejectUnauthorized),o.https.checkServerIdentity&&(p.checkServerIdentity=o.https.checkServerIdentity),o.https.certificateAuthority&&(p.ca=o.https.certificateAuthority),o.https.certificate&&(p.cert=o.https.certificate),o.https.key&&(p.key=o.https.key),o.https.passphrase&&(p.passphrase=o.https.passphrase),o.https.pfx&&(p.pfx=o.https.pfx));try{let D=await y(f,p);x.default.undefined(D)&&(D=m(f,p)),o.request=c,o.timeout=l,o.agent=u,o.https&&("rejectUnauthorized"in o.https&&delete p.rejectUnauthorized,o.https.checkServerIdentity&&delete p.checkServerIdentity,o.https.certificateAuthority&&delete p.ca,o.https.certificate&&delete p.cert,o.https.key&&delete p.key,o.https.passphrase&&delete p.passphrase,o.https.pfx&&delete p.pfx),sM(D)?this._onRequest(D):this.writable?(this.once("finish",()=>{this._onResponse(D)}),this._unlockWrite(),this.end(),this._lockWrite()):this._onResponse(D)}catch(D){throw D instanceof eC.CacheError?new ku(D,this):new Ce(D.message,D,this)}}async _error(e){try{for(let r of this.options.hooks.beforeError)e=await r(e)}catch(r){e=new Ce(r.message,r,this)}this.destroy(e)}_beforeError(e){if(this[Ii])return;let{options:r}=this,n=this.retryCount+1;this[Ii]=!0,e instanceof Ce||(e=new Ce(e.message,e,this));let i=e,{response:s}=i;(async()=>{if(s&&!s.body){s.setEncoding(this._readableState.encoding);try{s.rawBody=await Q8.default(s),s.body=s.rawBody.toString()}catch{}}if(this.listenerCount("retry")!==0){let o;try{let a;s&&"retry-after"in s.headers&&(a=Number(s.headers["retry-after"]),Number.isNaN(a)?(a=Date.parse(s.headers["retry-after"])-Date.now(),a<=0&&(a=1)):a*=1e3),o=await r.retry.calculateDelay({attemptCount:n,retryOptions:r.retry,error:i,retryAfter:a,computedValue:rM.default({attemptCount:n,retryOptions:r.retry,error:i,retryAfter:a,computedValue:0})})}catch(a){this._error(new Ce(a.message,a,this));return}if(o){let a=async()=>{try{for(let u of this.options.hooks.beforeRetry)await u(this.options,i,n)}catch(u){this._error(new Ce(u.message,e,this));return}this.destroyed||(this.destroy(),this.emit("retry",n,e))};this[cC]=setTimeout(a,o);return}}this._error(i)})()}_read(){this[Ru]=!0;let e=this[Fu];if(e&&!this[Ii]){e.readableLength&&(this[Ru]=!1);let r;for(;(r=e.read())!==null;){this[ki]+=r.length,this[aC]=!0;let n=this.downloadProgress;n.percent<1&&this.emit("downloadProgress",n),this.push(r)}}}_write(e,r,n){let i=()=>{this._writeRequest(e,r,n)};this.requestInitialized?i():this[Ns].push(i)}_writeRequest(e,r,n){this[Ne].destroyed||(this._progressCallbacks.push(()=>{this[Bi]+=Buffer.byteLength(e,r);let i=this.uploadProgress;i.percent<1&&this.emit("uploadProgress",i)}),this[Ne].write(e,r,i=>{!i&&this._progressCallbacks.length>0&&this._progressCallbacks.shift()(),n(i)}))}_final(e){let r=()=>{for(;this._progressCallbacks.length!==0;)this._progressCallbacks.shift()();if(!(Ne in this)){e();return}if(this[Ne].destroyed){e();return}this[Ne].end(n=>{n||(this[Pi]=this[Bi],this.emit("uploadProgress",this.uploadProgress),this[Ne].emit("upload-complete")),e(n)})};this.requestInitialized?r():this[Ns].push(r)}_destroy(e,r){var n;this[Ii]=!0,clearTimeout(this[cC]),Ne in this&&(this[Mp](),!((n=this[Fu])===null||n===void 0)&&n.complete||this[Ne].destroy()),e!==null&&!x.default.undefined(e)&&!(e instanceof Ce)&&(e=new Ce(e.message,e,this)),r(e)}get _isAboutToError(){return this[Ii]}get ip(){var e;return(e=this.socket)===null||e===void 0?void 0:e.remoteAddress}get aborted(){var e,r,n;return((r=(e=this[Ne])===null||e===void 0?void 0:e.destroyed)!==null&&r!==void 0?r:this.destroyed)&&!(!((n=this[uC])===null||n===void 0)&&n.complete)}get socket(){var e,r;return(r=(e=this[Ne])===null||e===void 0?void 0:e.socket)!==null&&r!==void 0?r:void 0}get downloadProgress(){let e;return this[Oi]?e=this[ki]/this[Oi]:this[Oi]===this[ki]?e=1:e=0,{percent:e,transferred:this[ki],total:this[Oi]}}get uploadProgress(){let e;return this[Pi]?e=this[Bi]/this[Pi]:this[Pi]===this[Bi]?e=1:e=0,{percent:e,transferred:this[Bi],total:this[Pi]}}get timings(){var e;return(e=this[Ne])===null||e===void 0?void 0:e.timings}get isFromCache(){return this[oC]}pipe(e,r){if(this[aC])throw new Error("Failed to pipe. The response has been emitted already.");return e instanceof Lp.ServerResponse&&this[vu].add(e),super.pipe(e,r)}unpipe(e){return e instanceof Lp.ServerResponse&&this[vu].delete(e),super.unpipe(e),this}};ne.default=Lu});var Hs=b(Bt=>{"use strict";var lM=Bt&&Bt.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),fM=Bt&&Bt.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&lM(e,t,r)};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.CancelError=Bt.ParseError=void 0;var lC=qs(),Hp=class extends lC.RequestError{constructor(e,r){let{options:n}=r.request;super(`${e.message} in "${n.url.toString()}"`,e,r.request),this.name="ParseError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_BODY_PARSE_FAILURE":this.code}};Bt.ParseError=Hp;var jp=class extends lC.RequestError{constructor(e){super("Promise was canceled",{},e),this.name="CancelError",this.code="ERR_CANCELED"}get isCanceled(){return!0}};Bt.CancelError=jp;fM(qs(),Bt)});var hC=b($p=>{"use strict";Object.defineProperty($p,"__esModule",{value:!0});var fC=Hs(),hM=(t,e,r,n)=>{let{rawBody:i}=t;try{if(e==="text")return i.toString(n);if(e==="json")return i.length===0?"":r(i.toString());if(e==="buffer")return i;throw new fC.ParseError({message:`Unknown body type '${e}'`,name:"Error"},t)}catch(s){throw new fC.ParseError(s,t)}};$p.default=hM});var Up=b(Zr=>{"use strict";var dM=Zr&&Zr.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),pM=Zr&&Zr.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&dM(e,t,r)};Object.defineProperty(Zr,"__esModule",{value:!0});var mM=require("events"),gM=br(),yM=lx(),Nu=Hs(),dC=hC(),pC=qs(),_M=Sp(),EM=kp(),mC=Pp(),bM=["request","response","redirect","uploadProgress","downloadProgress"];function gC(t){let e,r,n=new mM.EventEmitter,i=new yM((o,a,u)=>{let c=l=>{let f=new pC.default(void 0,t);f.retryCount=l,f._noPipe=!0,u(()=>f.destroy()),u.shouldReject=!1,u(()=>a(new Nu.CancelError(f))),e=f,f.once("response",async _=>{var y;if(_.retryCount=l,_.request.aborted)return;let p;try{p=await EM.default(f),_.rawBody=p}catch{return}if(f._isAboutToError)return;let D=((y=_.headers["content-encoding"])!==null&&y!==void 0?y:"").toLowerCase(),C=["gzip","deflate","br"].includes(D),{options:N}=f;if(C&&!N.decompress)_.body=p;else try{_.body=dC.default(_,N.responseType,N.parseJson,N.encoding)}catch(U){if(_.body=p.toString(),mC.isResponseOk(_)){f._beforeError(U);return}}try{for(let[U,M]of N.hooks.afterResponse.entries())_=await M(_,async Y=>{let G=pC.default.normalizeArguments(void 0,{...Y,retry:{calculateDelay:()=>0},throwHttpErrors:!1,resolveBodyOnly:!1},N);G.hooks.afterResponse=G.hooks.afterResponse.slice(0,U);for(let Q of G.hooks.beforeRetry)await Q(G);let z=gC(G);return u(()=>{z.catch(()=>{}),z.cancel()}),z})}catch(U){f._beforeError(new Nu.RequestError(U.message,U,f));return}if(r=_,!mC.isResponseOk(_)){f._beforeError(new Nu.HTTPError(_));return}f.destroy(),o(f.options.resolveBodyOnly?_.body:_)});let h=_=>{if(i.isCanceled)return;let{options:y}=f;if(_ instanceof Nu.HTTPError&&!y.throwHttpErrors){let{response:p}=_;o(f.options.resolveBodyOnly?p.body:p);return}a(_)};f.once("error",h);let m=f.options.body;f.once("retry",(_,y)=>{var p,D;if(m===((p=y.request)===null||p===void 0?void 0:p.options.body)&&gM.default.nodeStream((D=y.request)===null||D===void 0?void 0:D.options.body)){h(y);return}c(_)}),_M.default(f,n,bM)};c(0)});i.on=(o,a)=>(n.on(o,a),i);let s=o=>{let a=(async()=>{await i;let{options:u}=r.request;return dC.default(r,o,u.parseJson,u.encoding)})();return Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)),a};return i.json=()=>{let{headers:o}=e.options;return!e.writableFinished&&o.accept===void 0&&(o.accept="application/json"),s("json")},i.buffer=()=>s("buffer"),i.text=()=>s("text"),i}Zr.default=gC;pM(Hs(),Zr)});var yC=b(Gp=>{"use strict";Object.defineProperty(Gp,"__esModule",{value:!0});var DM=Hs();function wM(t,...e){let r=(async()=>{if(t instanceof DM.RequestError)try{for(let i of e)if(i)for(let s of i)t=await s(t)}catch(i){t=i}throw t})(),n=()=>r;return r.json=n,r.text=n,r.buffer=n,r.on=n,r}Gp.default=wM});var bC=b(Wp=>{"use strict";Object.defineProperty(Wp,"__esModule",{value:!0});var _C=br();function EC(t){for(let e of Object.values(t))(_C.default.plainObject(e)||_C.default.array(e))&&EC(e);return Object.freeze(t)}Wp.default=EC});var wC=b(DC=>{"use strict";Object.defineProperty(DC,"__esModule",{value:!0})});var zp=b(St=>{"use strict";var AM=St&&St.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),xM=St&&St.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&AM(e,t,r)};Object.defineProperty(St,"__esModule",{value:!0});St.defaultHandler=void 0;var AC=br(),xt=Up(),SM=yC(),qu=qs(),CM=bC(),vM={RequestError:xt.RequestError,CacheError:xt.CacheError,ReadError:xt.ReadError,HTTPError:xt.HTTPError,MaxRedirectsError:xt.MaxRedirectsError,TimeoutError:xt.TimeoutError,ParseError:xt.ParseError,CancelError:xt.CancelError,UnsupportedProtocolError:xt.UnsupportedProtocolError,UploadError:xt.UploadError},RM=async t=>new Promise(e=>{setTimeout(e,t)}),{normalizeArguments:Mu}=qu.default,xC=(...t)=>{let e;for(let r of t)e=Mu(void 0,r,e);return e},FM=t=>t.isStream?new qu.default(void 0,t):xt.default(t),TM=t=>"defaults"in t&&"options"in t.defaults,OM=["get","post","put","patch","head","delete"];St.defaultHandler=(t,e)=>e(t);var SC=(t,e)=>{if(t)for(let r of t)r(e)},CC=t=>{t._rawHandlers=t.handlers,t.handlers=t.handlers.map(n=>(i,s)=>{let o,a=n(i,u=>(o=s(u),o));if(a!==o&&!i.isStream&&o){let u=a,{then:c,catch:l,finally:f}=u;Object.setPrototypeOf(u,Object.getPrototypeOf(o)),Object.defineProperties(u,Object.getOwnPropertyDescriptors(o)),u.then=c,u.catch=l,u.finally=f}return a});let e=(n,i={},s)=>{var o,a;let u=0,c=l=>t.handlers[u++](l,u===t.handlers.length?FM:c);if(AC.default.plainObject(n)){let l={...n,...i};qu.setNonEnumerableProperties([n,i],l),i=l,n=void 0}try{let l;try{SC(t.options.hooks.init,i),SC((o=i.hooks)===null||o===void 0?void 0:o.init,i)}catch(h){l=h}let f=Mu(n,i,s!=null?s:t.options);if(f[qu.kIsNormalizedAlready]=!0,l)throw new xt.RequestError(l.message,l,f);return c(f)}catch(l){if(i.isStream)throw l;return SM.default(l,t.options.hooks.beforeError,(a=i.hooks)===null||a===void 0?void 0:a.beforeError)}};e.extend=(...n)=>{let i=[t.options],s=[...t._rawHandlers],o;for(let a of n)TM(a)?(i.push(a.defaults.options),s.push(...a.defaults._rawHandlers),o=a.defaults.mutableDefaults):(i.push(a),"handlers"in a&&s.push(...a.handlers),o=a.mutableDefaults);return s=s.filter(a=>a!==St.defaultHandler),s.length===0&&s.push(St.defaultHandler),CC({options:xC(...i),handlers:s,mutableDefaults:!!o})};let r=async function*(n,i){let s=Mu(n,i,t.options);s.resolveBodyOnly=!1;let o=s.pagination;if(!AC.default.object(o))throw new TypeError("`options.pagination` must be implemented");let a=[],{countLimit:u}=o,c=0;for(;c<o.requestLimit;){c!==0&&await RM(o.backoff);let l=await e(void 0,void 0,s),f=await o.transform(l),h=[];for(let _ of f)if(o.filter(_,a,h)&&(!o.shouldContinue(_,a,h)||(yield _,o.stackAllItems&&a.push(_),h.push(_),--u<=0)))return;let m=o.paginate(l,a,h);if(m===!1)return;m===l.request.options?s=l.request.options:m!==void 0&&(s=Mu(void 0,m,s)),c++}};e.paginate=r,e.paginate.all=async(n,i)=>{let s=[];for await(let o of r(n,i))s.push(o);return s},e.paginate.each=r,e.stream=(n,i)=>e(n,{...i,isStream:!0});for(let n of OM)e[n]=(i,s)=>e(i,{...s,method:n}),e.stream[n]=(i,s)=>e(i,{...s,method:n,isStream:!0});return Object.assign(e,vM),Object.defineProperty(e,"defaults",{value:t.mutableDefaults?t:CM.default(t),writable:t.mutableDefaults,configurable:t.mutableDefaults,enumerable:!0}),e.mergeOptions=xC,e};St.default=CC;xM(wC(),St)});var FC=b((Dr,Hu)=>{"use strict";var kM=Dr&&Dr.__createBinding||(Object.create?function(t,e,r,n){n===void 0&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}),vC=Dr&&Dr.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&kM(e,t,r)};Object.defineProperty(Dr,"__esModule",{value:!0});var PM=require("url"),RC=zp(),BM={options:{method:"GET",retry:{limit:2,methods:["GET","PUT","HEAD","DELETE","OPTIONS","TRACE"],statusCodes:[408,413,429,500,502,503,504,521,522,524],errorCodes:["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"],maxRetryAfter:void 0,calculateDelay:({computedValue:t})=>t},timeout:{},headers:{"user-agent":"got (https://github.com/sindresorhus/got)"},hooks:{init:[],beforeRequest:[],beforeRedirect:[],beforeRetry:[],beforeError:[],afterResponse:[]},cache:void 0,dnsCache:void 0,decompress:!0,throwHttpErrors:!0,followRedirect:!0,isStream:!1,responseType:"text",resolveBodyOnly:!1,maxRedirects:10,prefixUrl:"",methodRewriting:!0,ignoreInvalidCookies:!1,context:{},http2:!1,allowGetBody:!1,https:void 0,pagination:{transform:t=>t.request.options.responseType==="json"?t.body:JSON.parse(t.body),paginate:t=>{if(!Reflect.has(t.headers,"link"))return!1;let e=t.headers.link.split(","),r;for(let n of e){let i=n.split(";");if(i[1].includes("next")){r=i[0].trimStart().trim(),r=r.slice(1,-1);break}}return r?{url:new PM.URL(r)}:!1},filter:()=>!0,shouldContinue:()=>!0,countLimit:1/0,backoff:0,requestLimit:1e4,stackAllItems:!0},parseJson:t=>JSON.parse(t),stringifyJson:t=>JSON.stringify(t),cacheOptions:{}},handlers:[RC.defaultHandler],mutableDefaults:!1},Vp=RC.default(BM);Dr.default=Vp;Hu.exports=Vp;Hu.exports.default=Vp;Hu.exports.__esModule=!0;vC(zp(),Dr);vC(Up(),Dr)});var LC=b((p9,IC)=>{function Ct(t,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(t)),this._timeouts=t,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}IC.exports=Ct;Ct.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};Ct.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};Ct.prototype.retry=function(t){if(this._timeout&&clearTimeout(this._timeout),!t)return!1;var e=new Date().getTime();if(t&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(t),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(t);var r=this._timeouts.shift();if(r===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),r=this._cachedTimeouts.slice(-1);else return!1;var n=this;return this._timer=setTimeout(function(){n._attempts++,n._operationTimeoutCb&&(n._timeout=setTimeout(function(){n._operationTimeoutCb(n._attempts)},n._operationTimeout),n._options.unref&&n._timeout.unref()),n._fn(n._attempts)},r),this._options.unref&&this._timer.unref(),!0};Ct.prototype.attempt=function(t,e){this._fn=t,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var r=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){r._operationTimeoutCb()},r._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};Ct.prototype.try=function(t){console.log("Using RetryOperation.try() is deprecated"),this.attempt(t)};Ct.prototype.start=function(t){console.log("Using RetryOperation.start() is deprecated"),this.attempt(t)};Ct.prototype.start=Ct.prototype.try;Ct.prototype.errors=function(){return this._errors};Ct.prototype.attempts=function(){return this._attempts};Ct.prototype.mainError=function(){if(this._errors.length===0)return null;for(var t={},e=null,r=0,n=0;n<this._errors.length;n++){var i=this._errors[n],s=i.message,o=(t[s]||0)+1;t[s]=o,o>=r&&(e=i,r=o)}return e}});var NC=b(vn=>{var LM=LC();vn.operation=function(t){var e=vn.timeouts(t);return new LM(e,{forever:t&&(t.forever||t.retries===1/0),unref:t&&t.unref,maxRetryTime:t&&t.maxRetryTime})};vn.timeouts=function(t){if(t instanceof Array)return[].concat(t);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var r in t)e[r]=t[r];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var n=[],i=0;i<e.retries;i++)n.push(this.createTimeout(i,e));return t&&t.forever&&!n.length&&n.push(this.createTimeout(i,e)),n.sort(function(s,o){return s-o}),n};vn.createTimeout=function(t,e){var r=e.randomize?Math.random()+1:1,n=Math.round(r*Math.max(e.minTimeout,1)*Math.pow(e.factor,t));return n=Math.min(n,e.maxTimeout),n};vn.wrap=function(t,e,r){if(e instanceof Array&&(r=e,e=null),!r){r=[];for(var n in t)typeof t[n]=="function"&&r.push(n)}for(var i=0;i<r.length;i++){var s=r[i],o=t[s];t[s]=function(u){var c=vn.operation(e),l=Array.prototype.slice.call(arguments,1),f=l.pop();l.push(function(h){c.retry(h)||(h&&(arguments[0]=c.mainError()),f.apply(this,arguments))}),c.attempt(function(){u.apply(t,l)})}.bind(t,o),t[s].options=e}}});var qC=b((g9,MC)=>{MC.exports=NC()});var jC=b((y9,HC)=>{var NM=qC();function MM(t,e){function r(n,i){var s=e||{},o;"randomize"in s||(s.randomize=!0),o=NM.operation(s);function a(l){i(l||new Error("Aborted"))}function u(l,f){if(l.bail){a(l);return}o.retry(l)?s.onRetry&&s.onRetry(l,f):i(o.mainError())}function c(l){var f;try{f=t(a,l)}catch(h){u(h,l);return}Promise.resolve(f).then(n).catch(function(m){u(m,l)})}o.attempt(c)}return new Promise(r)}HC.exports=MM});var pv=X(require("http")),mv=X(require("https")),gv=X(require("chalk")),qe=require("commander");var Bv=X(Dm());function ec(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function qi(t){return qi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qi(t)}function tc(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function rc(t,e){if(e&&(qi(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tc(t)}function Qr(t){return Qr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Qr(t)}function Zt(t,e){return Zt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zt(t,e)}function nc(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Zt(t,e)}function ic(t){return Function.toString.call(t).indexOf("[native code]")!==-1}function sc(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Fn(t,e,r){return sc()?Fn=Reflect.construct.bind():Fn=function(i,s,o){var a=[null];a.push.apply(a,s);var u=Function.bind.apply(i,a),c=new u;return o&&Zt(c,o.prototype),c},Fn.apply(null,arguments)}function Hi(t){var e=typeof Map=="function"?new Map:void 0;return Hi=function(n){if(n===null||!ic(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(n))return e.get(n);e.set(n,i)}function i(){return Fn(n,arguments,Qr(this).constructor)}return i.prototype=Object.create(n.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Zt(i,n)},Hi(t)}var hc=X(Mm()),Zs=X(require("path")),Qs=X(require("fs-extra")),Iv=function(t){nc(e,t);function e(r){var n;return ec(this,e),n=rc(this,Qr(e).call(this,"No package.json could be found upwards from the directory ".concat(r))),n.directory=r,n}return e}(Hi(Error));function Lv(t,e){try{var r=Qs.default.readJsonSync(Zs.default.join(t,"package.json"));if(e.current===void 0&&(e.current=t),r.workspaces||r.bolt)return t}catch(n){if(n.code!=="ENOENT")throw n}}function Nv(t){try{var e=Qs.default.readJsonSync(Zs.default.join(t,"lerna.json"));if(e.useWorkspaces!==!0)return t}catch(r){if(r.code!=="ENOENT")throw r}}function Mv(t){var e=Qs.default.existsSync(Zs.default.join(t,"pnpm-workspace.yaml"));if(e)return t}function qm(t){var e={current:void 0},r=(0,hc.sync)(function(n){return[Nv(n),Lv(n,e),Mv(n)].find(function(i){return i})},{cwd:t,type:"directory"});if(e.current===void 0)throw new Iv(t);return r===void 0?e.current:r}var qv=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,Hv=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,jv=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/,dc={Space_Separator:qv,ID_Start:Hv,ID_Continue:jv},Ae={isSpaceSeparator(t){return typeof t=="string"&&dc.Space_Separator.test(t)},isIdStartChar(t){return typeof t=="string"&&(t>="a"&&t<="z"||t>="A"&&t<="Z"||t==="$"||t==="_"||dc.ID_Start.test(t))},isIdContinueChar(t){return typeof t=="string"&&(t>="a"&&t<="z"||t>="A"&&t<="Z"||t>="0"&&t<="9"||t==="$"||t==="_"||t==="\u200C"||t==="\u200D"||dc.ID_Continue.test(t))},isDigit(t){return typeof t=="string"&&/[0-9]/.test(t)},isHexDigit(t){return typeof t=="string"&&/[0-9A-Fa-f]/.test(t)}},mc,Je,er,to,Sr,Tt,Oe,_c,$i,$v=function(e,r){mc=String(e),Je="start",er=[],to=0,Sr=1,Tt=0,Oe=void 0,_c=void 0,$i=void 0;do Oe=Uv(),zv[Je]();while(Oe.type!=="eof");return typeof r=="function"?gc({"":$i},"",r):$i};function gc(t,e,r){let n=t[e];if(n!=null&&typeof n=="object")if(Array.isArray(n))for(let i=0;i<n.length;i++){let s=String(i),o=gc(n,s,r);o===void 0?delete n[s]:Object.defineProperty(n,s,{value:o,writable:!0,enumerable:!0,configurable:!0})}else for(let i in n){let s=gc(n,i,r);s===void 0?delete n[i]:Object.defineProperty(n,i,{value:s,writable:!0,enumerable:!0,configurable:!0})}return r.call(t,e,n)}var Z,J,ji,Qt,ee;function Uv(){for(Z="default",J="",ji=!1,Qt=1;;){ee=tr();let t=jm[Z]();if(t)return t}}function tr(){if(mc[to])return String.fromCodePoint(mc.codePointAt(to))}function T(){let t=tr();return t===`
`?(Sr++,Tt=0):t?Tt+=t.length:Tt++,t&&(to+=t.length),t}var jm={default(){switch(ee){case"	":case"\v":case"\f":case" ":case"\xA0":case"\uFEFF":case`
`:case"\r":case"\u2028":case"\u2029":T();return;case"/":T(),Z="comment";return;case void 0:return T(),de("eof")}if(Ae.isSpaceSeparator(ee)){T();return}return jm[Je]()},comment(){switch(ee){case"*":T(),Z="multiLineComment";return;case"/":T(),Z="singleLineComment";return}throw pe(T())},multiLineComment(){switch(ee){case"*":T(),Z="multiLineCommentAsterisk";return;case void 0:throw pe(T())}T()},multiLineCommentAsterisk(){switch(ee){case"*":T();return;case"/":T(),Z="default";return;case void 0:throw pe(T())}T(),Z="multiLineComment"},singleLineComment(){switch(ee){case`
`:case"\r":case"\u2028":case"\u2029":T(),Z="default";return;case void 0:return T(),de("eof")}T()},value(){switch(ee){case"{":case"[":return de("punctuator",T());case"n":return T(),en("ull"),de("null",null);case"t":return T(),en("rue"),de("boolean",!0);case"f":return T(),en("alse"),de("boolean",!1);case"-":case"+":T()==="-"&&(Qt=-1),Z="sign";return;case".":J=T(),Z="decimalPointLeading";return;case"0":J=T(),Z="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":J=T(),Z="decimalInteger";return;case"I":return T(),en("nfinity"),de("numeric",1/0);case"N":return T(),en("aN"),de("numeric",NaN);case'"':case"'":ji=T()==='"',J="",Z="string";return}throw pe(T())},identifierNameStartEscape(){if(ee!=="u")throw pe(T());T();let t=yc();switch(t){case"$":case"_":break;default:if(!Ae.isIdStartChar(t))throw Hm();break}J+=t,Z="identifierName"},identifierName(){switch(ee){case"$":case"_":case"\u200C":case"\u200D":J+=T();return;case"\\":T(),Z="identifierNameEscape";return}if(Ae.isIdContinueChar(ee)){J+=T();return}return de("identifier",J)},identifierNameEscape(){if(ee!=="u")throw pe(T());T();let t=yc();switch(t){case"$":case"_":case"\u200C":case"\u200D":break;default:if(!Ae.isIdContinueChar(t))throw Hm();break}J+=t,Z="identifierName"},sign(){switch(ee){case".":J=T(),Z="decimalPointLeading";return;case"0":J=T(),Z="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":J=T(),Z="decimalInteger";return;case"I":return T(),en("nfinity"),de("numeric",Qt*(1/0));case"N":return T(),en("aN"),de("numeric",NaN)}throw pe(T())},zero(){switch(ee){case".":J+=T(),Z="decimalPoint";return;case"e":case"E":J+=T(),Z="decimalExponent";return;case"x":case"X":J+=T(),Z="hexadecimal";return}return de("numeric",Qt*0)},decimalInteger(){switch(ee){case".":J+=T(),Z="decimalPoint";return;case"e":case"E":J+=T(),Z="decimalExponent";return}if(Ae.isDigit(ee)){J+=T();return}return de("numeric",Qt*Number(J))},decimalPointLeading(){if(Ae.isDigit(ee)){J+=T(),Z="decimalFraction";return}throw pe(T())},decimalPoint(){switch(ee){case"e":case"E":J+=T(),Z="decimalExponent";return}if(Ae.isDigit(ee)){J+=T(),Z="decimalFraction";return}return de("numeric",Qt*Number(J))},decimalFraction(){switch(ee){case"e":case"E":J+=T(),Z="decimalExponent";return}if(Ae.isDigit(ee)){J+=T();return}return de("numeric",Qt*Number(J))},decimalExponent(){switch(ee){case"+":case"-":J+=T(),Z="decimalExponentSign";return}if(Ae.isDigit(ee)){J+=T(),Z="decimalExponentInteger";return}throw pe(T())},decimalExponentSign(){if(Ae.isDigit(ee)){J+=T(),Z="decimalExponentInteger";return}throw pe(T())},decimalExponentInteger(){if(Ae.isDigit(ee)){J+=T();return}return de("numeric",Qt*Number(J))},hexadecimal(){if(Ae.isHexDigit(ee)){J+=T(),Z="hexadecimalInteger";return}throw pe(T())},hexadecimalInteger(){if(Ae.isHexDigit(ee)){J+=T();return}return de("numeric",Qt*Number(J))},string(){switch(ee){case"\\":T(),J+=Gv();return;case'"':if(ji)return T(),de("string",J);J+=T();return;case"'":if(!ji)return T(),de("string",J);J+=T();return;case`
`:case"\r":throw pe(T());case"\u2028":case"\u2029":Vv(ee);break;case void 0:throw pe(T())}J+=T()},start(){switch(ee){case"{":case"[":return de("punctuator",T())}Z="value"},beforePropertyName(){switch(ee){case"$":case"_":J=T(),Z="identifierName";return;case"\\":T(),Z="identifierNameStartEscape";return;case"}":return de("punctuator",T());case'"':case"'":ji=T()==='"',Z="string";return}if(Ae.isIdStartChar(ee)){J+=T(),Z="identifierName";return}throw pe(T())},afterPropertyName(){if(ee===":")return de("punctuator",T());throw pe(T())},beforePropertyValue(){Z="value"},afterPropertyValue(){switch(ee){case",":case"}":return de("punctuator",T())}throw pe(T())},beforeArrayValue(){if(ee==="]")return de("punctuator",T());Z="value"},afterArrayValue(){switch(ee){case",":case"]":return de("punctuator",T())}throw pe(T())},end(){throw pe(T())}};function de(t,e){return{type:t,value:e,line:Sr,column:Tt}}function en(t){for(let e of t){if(tr()!==e)throw pe(T());T()}}function Gv(){switch(tr()){case"b":return T(),"\b";case"f":return T(),"\f";case"n":return T(),`
`;case"r":return T(),"\r";case"t":return T(),"	";case"v":return T(),"\v";case"0":if(T(),Ae.isDigit(tr()))throw pe(T());return"\0";case"x":return T(),Wv();case"u":return T(),yc();case`
`:case"\u2028":case"\u2029":return T(),"";case"\r":return T(),tr()===`
`&&T(),"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw pe(T());case void 0:throw pe(T())}return T()}function Wv(){let t="",e=tr();if(!Ae.isHexDigit(e)||(t+=T(),e=tr(),!Ae.isHexDigit(e)))throw pe(T());return t+=T(),String.fromCodePoint(parseInt(t,16))}function yc(){let t="",e=4;for(;e-- >0;){let r=tr();if(!Ae.isHexDigit(r))throw pe(T());t+=T()}return String.fromCodePoint(parseInt(t,16))}var zv={start(){if(Oe.type==="eof")throw tn();pc()},beforePropertyName(){switch(Oe.type){case"identifier":case"string":_c=Oe.value,Je="afterPropertyName";return;case"punctuator":eo();return;case"eof":throw tn()}},afterPropertyName(){if(Oe.type==="eof")throw tn();Je="beforePropertyValue"},beforePropertyValue(){if(Oe.type==="eof")throw tn();pc()},beforeArrayValue(){if(Oe.type==="eof")throw tn();if(Oe.type==="punctuator"&&Oe.value==="]"){eo();return}pc()},afterPropertyValue(){if(Oe.type==="eof")throw tn();switch(Oe.value){case",":Je="beforePropertyName";return;case"}":eo()}},afterArrayValue(){if(Oe.type==="eof")throw tn();switch(Oe.value){case",":Je="beforeArrayValue";return;case"]":eo()}},end(){}};function pc(){let t;switch(Oe.type){case"punctuator":switch(Oe.value){case"{":t={};break;case"[":t=[];break}break;case"null":case"boolean":case"numeric":case"string":t=Oe.value;break}if($i===void 0)$i=t;else{let e=er[er.length-1];Array.isArray(e)?e.push(t):Object.defineProperty(e,_c,{value:t,writable:!0,enumerable:!0,configurable:!0})}if(t!==null&&typeof t=="object")er.push(t),Array.isArray(t)?Je="beforeArrayValue":Je="beforePropertyName";else{let e=er[er.length-1];e==null?Je="end":Array.isArray(e)?Je="afterArrayValue":Je="afterPropertyValue"}}function eo(){er.pop();let t=er[er.length-1];t==null?Je="end":Array.isArray(t)?Je="afterArrayValue":Je="afterPropertyValue"}function pe(t){return ro(t===void 0?`JSON5: invalid end of input at ${Sr}:${Tt}`:`JSON5: invalid character '${$m(t)}' at ${Sr}:${Tt}`)}function tn(){return ro(`JSON5: invalid end of input at ${Sr}:${Tt}`)}function Hm(){return Tt-=5,ro(`JSON5: invalid identifier character at ${Sr}:${Tt}`)}function Vv(t){console.warn(`JSON5: '${$m(t)}' in strings is not valid ECMAScript; consider escaping`)}function $m(t){let e={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(e[t])return e[t];if(t<" "){let r=t.charCodeAt(0).toString(16);return"\\x"+("00"+r).substring(r.length)}return t}function ro(t){let e=new SyntaxError(t);return e.lineNumber=Sr,e.columnNumber=Tt,e}var Yv=function(e,r,n){let i=[],s="",o,a,u="",c;if(r!=null&&typeof r=="object"&&!Array.isArray(r)&&(n=r.space,c=r.quote,r=r.replacer),typeof r=="function")a=r;else if(Array.isArray(r)){o=[];for(let y of r){let p;typeof y=="string"?p=y:(typeof y=="number"||y instanceof String||y instanceof Number)&&(p=String(y)),p!==void 0&&o.indexOf(p)<0&&o.push(p)}}return n instanceof Number?n=Number(n):n instanceof String&&(n=String(n)),typeof n=="number"?n>0&&(n=Math.min(10,Math.floor(n)),u="          ".substr(0,n)):typeof n=="string"&&(u=n.substr(0,10)),l("",{"":e});function l(y,p){let D=p[y];switch(D!=null&&(typeof D.toJSON5=="function"?D=D.toJSON5(y):typeof D.toJSON=="function"&&(D=D.toJSON(y))),a&&(D=a.call(p,y,D)),D instanceof Number?D=Number(D):D instanceof String?D=String(D):D instanceof Boolean&&(D=D.valueOf()),D){case null:return"null";case!0:return"true";case!1:return"false"}if(typeof D=="string")return f(D,!1);if(typeof D=="number")return String(D);if(typeof D=="object")return Array.isArray(D)?_(D):h(D)}function f(y){let p={"'":.1,'"':.2},D={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"},C="";for(let U=0;U<y.length;U++){let M=y[U];switch(M){case"'":case'"':p[M]++,C+=M;continue;case"\0":if(Ae.isDigit(y[U+1])){C+="\\x00";continue}}if(D[M]){C+=D[M];continue}if(M<" "){let Y=M.charCodeAt(0).toString(16);C+="\\x"+("00"+Y).substring(Y.length);continue}C+=M}let N=c||Object.keys(p).reduce((U,M)=>p[U]<p[M]?U:M);return C=C.replace(new RegExp(N,"g"),D[N]),N+C+N}function h(y){if(i.indexOf(y)>=0)throw TypeError("Converting circular structure to JSON5");i.push(y);let p=s;s=s+u;let D=o||Object.keys(y),C=[];for(let U of D){let M=l(U,y);if(M!==void 0){let Y=m(U)+":";u!==""&&(Y+=" "),Y+=M,C.push(Y)}}let N;if(C.length===0)N="{}";else{let U;if(u==="")U=C.join(","),N="{"+U+"}";else{let M=`,
`+s;U=C.join(M),N=`{
`+s+U+`,
`+p+"}"}}return i.pop(),s=p,N}function m(y){if(y.length===0)return f(y,!0);let p=String.fromCodePoint(y.codePointAt(0));if(!Ae.isIdStartChar(p))return f(y,!0);for(let D=p.length;D<y.length;D++)if(!Ae.isIdContinueChar(String.fromCodePoint(y.codePointAt(D))))return f(y,!0);return y}function _(y){if(i.indexOf(y)>=0)throw TypeError("Converting circular structure to JSON5");i.push(y);let p=s;s=s+u;let D=[];for(let N=0;N<y.length;N++){let U=l(String(N),y);D.push(U!==void 0?U:"null")}let C;if(D.length===0)C="[]";else if(u==="")C="["+D.join(",")+"]";else{let N=`,
`+s,U=D.join(N);C=`[
`+s+U+`,
`+p+"]"}return i.pop(),s=p,C}},Kv={parse:$v,stringify:Yv},Jv=Kv,no=Jv;var Ec=X(require("fs")),Ui=X(require("path"));function Gi({target:t,cwd:e,contentCheck:r}){let n=Ui.default.parse(e).root,i=!1,s=e;for(;!i&&s!==n;){if(r)try{let o=Ec.default.readFileSync(Ui.default.join(s,t)).toString();if(r(o)){i=!0;break}}catch{}else if(Ec.default.existsSync(Ui.default.join(s,t))){i=!0;break}s=Ui.default.dirname(s)}return i?s:null}function Xv(t){let e=no.parse(t);return!(e&&"extends"in e)}var bc={};function Wi(t,e){var s;let r=(s=e==null?void 0:e.cache)!=null?s:!0,n=t||process.cwd();if(r&&bc[n])return bc[n];let i=Gi({target:"turbo.json",cwd:n,contentCheck:Xv});if(!i)try{if(i=qm(n),!i)return null}catch{return null}return r&&(bc[n]=i),i}var ss=X(require("fs")),hn=X(require("path"));function i0(t){return typeof t=="undefined"||t===null}function Zv(t){return typeof t=="object"&&t!==null}function Qv(t){return Array.isArray(t)?t:i0(t)?[]:[t]}function e1(t,e){var r,n,i,s;if(e)for(s=Object.keys(e),r=0,n=s.length;r<n;r+=1)i=s[r],t[i]=e[i];return t}function t1(t,e){var r="",n;for(n=0;n<e;n+=1)r+=t;return r}function r1(t){return t===0&&Number.NEGATIVE_INFINITY===1/t}var n1=i0,i1=Zv,s1=Qv,o1=t1,a1=r1,u1=e1,Fe={isNothing:n1,isObject:i1,toArray:s1,repeat:o1,isNegativeZero:a1,extend:u1};function s0(t,e){var r="",n=t.reason||"(unknown reason)";return t.mark?(t.mark.name&&(r+='in "'+t.mark.name+'" '),r+="("+(t.mark.line+1)+":"+(t.mark.column+1)+")",!e&&t.mark.snippet&&(r+=`

`+t.mark.snippet),n+" "+r):n}function Vi(t,e){Error.call(this),this.name="YAMLException",this.reason=t,this.mark=e,this.message=s0(this,!1),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack||""}Vi.prototype=Object.create(Error.prototype);Vi.prototype.constructor=Vi;Vi.prototype.toString=function(e){return this.name+": "+s0(this,e)};var Xe=Vi;function Dc(t,e,r,n,i){var s="",o="",a=Math.floor(i/2)-1;return n-e>a&&(s=" ... ",e=n-a+s.length),r-n>a&&(o=" ...",r=n+a-o.length),{str:s+t.slice(e,r).replace(/\t/g,"\u2192")+o,pos:n-e+s.length}}function wc(t,e){return Fe.repeat(" ",e-t.length)+t}function c1(t,e){if(e=Object.create(e||null),!t.buffer)return null;e.maxLength||(e.maxLength=79),typeof e.indent!="number"&&(e.indent=1),typeof e.linesBefore!="number"&&(e.linesBefore=3),typeof e.linesAfter!="number"&&(e.linesAfter=2);for(var r=/\r?\n|\r|\0/g,n=[0],i=[],s,o=-1;s=r.exec(t.buffer);)i.push(s.index),n.push(s.index+s[0].length),t.position<=s.index&&o<0&&(o=n.length-2);o<0&&(o=n.length-1);var a="",u,c,l=Math.min(t.line+e.linesAfter,i.length).toString().length,f=e.maxLength-(e.indent+l+3);for(u=1;u<=e.linesBefore&&!(o-u<0);u++)c=Dc(t.buffer,n[o-u],i[o-u],t.position-(n[o]-n[o-u]),f),a=Fe.repeat(" ",e.indent)+wc((t.line-u+1).toString(),l)+" | "+c.str+`
`+a;for(c=Dc(t.buffer,n[o],i[o],t.position,f),a+=Fe.repeat(" ",e.indent)+wc((t.line+1).toString(),l)+" | "+c.str+`
`,a+=Fe.repeat("-",e.indent+l+3+c.pos)+`^
`,u=1;u<=e.linesAfter&&!(o+u>=i.length);u++)c=Dc(t.buffer,n[o+u],i[o+u],t.position-(n[o]-n[o+u]),f),a+=Fe.repeat(" ",e.indent)+wc((t.line+u+1).toString(),l)+" | "+c.str+`
`;return a.replace(/\n$/,"")}var l1=c1,f1=["kind","multi","resolve","construct","instanceOf","predicate","represent","representName","defaultStyle","styleAliases"],h1=["scalar","sequence","mapping"];function d1(t){var e={};return t!==null&&Object.keys(t).forEach(function(r){t[r].forEach(function(n){e[String(n)]=r})}),e}function p1(t,e){if(e=e||{},Object.keys(e).forEach(function(r){if(f1.indexOf(r)===-1)throw new Xe('Unknown option "'+r+'" is met in definition of "'+t+'" YAML type.')}),this.options=e,this.tag=t,this.kind=e.kind||null,this.resolve=e.resolve||function(){return!0},this.construct=e.construct||function(r){return r},this.instanceOf=e.instanceOf||null,this.predicate=e.predicate||null,this.represent=e.represent||null,this.representName=e.representName||null,this.defaultStyle=e.defaultStyle||null,this.multi=e.multi||!1,this.styleAliases=d1(e.styleAliases||null),h1.indexOf(this.kind)===-1)throw new Xe('Unknown kind "'+this.kind+'" is specified for "'+t+'" YAML type.')}var Le=p1;function Um(t,e){var r=[];return t[e].forEach(function(n){var i=r.length;r.forEach(function(s,o){s.tag===n.tag&&s.kind===n.kind&&s.multi===n.multi&&(i=o)}),r[i]=n}),r}function m1(){var t={scalar:{},sequence:{},mapping:{},fallback:{},multi:{scalar:[],sequence:[],mapping:[],fallback:[]}},e,r;function n(i){i.multi?(t.multi[i.kind].push(i),t.multi.fallback.push(i)):t[i.kind][i.tag]=t.fallback[i.tag]=i}for(e=0,r=arguments.length;e<r;e+=1)arguments[e].forEach(n);return t}function xc(t){return this.extend(t)}xc.prototype.extend=function(e){var r=[],n=[];if(e instanceof Le)n.push(e);else if(Array.isArray(e))n=n.concat(e);else if(e&&(Array.isArray(e.implicit)||Array.isArray(e.explicit)))e.implicit&&(r=r.concat(e.implicit)),e.explicit&&(n=n.concat(e.explicit));else throw new Xe("Schema.extend argument should be a Type, [ Type ], or a schema definition ({ implicit: [...], explicit: [...] })");r.forEach(function(s){if(!(s instanceof Le))throw new Xe("Specified list of YAML types (or a single Type object) contains a non-Type object.");if(s.loadKind&&s.loadKind!=="scalar")throw new Xe("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.");if(s.multi)throw new Xe("There is a multi type in the implicit list of a schema. Multi tags can only be listed as explicit.")}),n.forEach(function(s){if(!(s instanceof Le))throw new Xe("Specified list of YAML types (or a single Type object) contains a non-Type object.")});var i=Object.create(xc.prototype);return i.implicit=(this.implicit||[]).concat(r),i.explicit=(this.explicit||[]).concat(n),i.compiledImplicit=Um(i,"implicit"),i.compiledExplicit=Um(i,"explicit"),i.compiledTypeMap=m1(i.compiledImplicit,i.compiledExplicit),i};var o0=xc,a0=new Le("tag:yaml.org,2002:str",{kind:"scalar",construct:function(t){return t!==null?t:""}}),u0=new Le("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(t){return t!==null?t:[]}}),c0=new Le("tag:yaml.org,2002:map",{kind:"mapping",construct:function(t){return t!==null?t:{}}}),l0=new o0({explicit:[a0,u0,c0]});function g1(t){if(t===null)return!0;var e=t.length;return e===1&&t==="~"||e===4&&(t==="null"||t==="Null"||t==="NULL")}function y1(){return null}function _1(t){return t===null}var f0=new Le("tag:yaml.org,2002:null",{kind:"scalar",resolve:g1,construct:y1,predicate:_1,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"},empty:function(){return""}},defaultStyle:"lowercase"});function E1(t){if(t===null)return!1;var e=t.length;return e===4&&(t==="true"||t==="True"||t==="TRUE")||e===5&&(t==="false"||t==="False"||t==="FALSE")}function b1(t){return t==="true"||t==="True"||t==="TRUE"}function D1(t){return Object.prototype.toString.call(t)==="[object Boolean]"}var h0=new Le("tag:yaml.org,2002:bool",{kind:"scalar",resolve:E1,construct:b1,predicate:D1,represent:{lowercase:function(t){return t?"true":"false"},uppercase:function(t){return t?"TRUE":"FALSE"},camelcase:function(t){return t?"True":"False"}},defaultStyle:"lowercase"});function w1(t){return 48<=t&&t<=57||65<=t&&t<=70||97<=t&&t<=102}function A1(t){return 48<=t&&t<=55}function x1(t){return 48<=t&&t<=57}function S1(t){if(t===null)return!1;var e=t.length,r=0,n=!1,i;if(!e)return!1;if(i=t[r],(i==="-"||i==="+")&&(i=t[++r]),i==="0"){if(r+1===e)return!0;if(i=t[++r],i==="b"){for(r++;r<e;r++)if(i=t[r],i!=="_"){if(i!=="0"&&i!=="1")return!1;n=!0}return n&&i!=="_"}if(i==="x"){for(r++;r<e;r++)if(i=t[r],i!=="_"){if(!w1(t.charCodeAt(r)))return!1;n=!0}return n&&i!=="_"}if(i==="o"){for(r++;r<e;r++)if(i=t[r],i!=="_"){if(!A1(t.charCodeAt(r)))return!1;n=!0}return n&&i!=="_"}}if(i==="_")return!1;for(;r<e;r++)if(i=t[r],i!=="_"){if(!x1(t.charCodeAt(r)))return!1;n=!0}return!(!n||i==="_")}function C1(t){var e=t,r=1,n;if(e.indexOf("_")!==-1&&(e=e.replace(/_/g,"")),n=e[0],(n==="-"||n==="+")&&(n==="-"&&(r=-1),e=e.slice(1),n=e[0]),e==="0")return 0;if(n==="0"){if(e[1]==="b")return r*parseInt(e.slice(2),2);if(e[1]==="x")return r*parseInt(e.slice(2),16);if(e[1]==="o")return r*parseInt(e.slice(2),8)}return r*parseInt(e,10)}function v1(t){return Object.prototype.toString.call(t)==="[object Number]"&&t%1===0&&!Fe.isNegativeZero(t)}var d0=new Le("tag:yaml.org,2002:int",{kind:"scalar",resolve:S1,construct:C1,predicate:v1,represent:{binary:function(t){return t>=0?"0b"+t.toString(2):"-0b"+t.toString(2).slice(1)},octal:function(t){return t>=0?"0o"+t.toString(8):"-0o"+t.toString(8).slice(1)},decimal:function(t){return t.toString(10)},hexadecimal:function(t){return t>=0?"0x"+t.toString(16).toUpperCase():"-0x"+t.toString(16).toUpperCase().slice(1)}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}}),R1=new RegExp("^(?:[-+]?(?:[0-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$");function F1(t){return!(t===null||!R1.test(t)||t[t.length-1]==="_")}function T1(t){var e,r;return e=t.replace(/_/g,"").toLowerCase(),r=e[0]==="-"?-1:1,"+-".indexOf(e[0])>=0&&(e=e.slice(1)),e===".inf"?r===1?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:e===".nan"?NaN:r*parseFloat(e,10)}var O1=/^[-+]?[0-9]+e/;function k1(t,e){var r;if(isNaN(t))switch(e){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===t)switch(e){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===t)switch(e){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(Fe.isNegativeZero(t))return"-0.0";return r=t.toString(10),O1.test(r)?r.replace("e",".e"):r}function P1(t){return Object.prototype.toString.call(t)==="[object Number]"&&(t%1!==0||Fe.isNegativeZero(t))}var p0=new Le("tag:yaml.org,2002:float",{kind:"scalar",resolve:F1,construct:T1,predicate:P1,represent:k1,defaultStyle:"lowercase"}),m0=l0.extend({implicit:[f0,h0,d0,p0]}),g0=m0,y0=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),_0=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");function B1(t){return t===null?!1:y0.exec(t)!==null||_0.exec(t)!==null}function I1(t){var e,r,n,i,s,o,a,u=0,c=null,l,f,h;if(e=y0.exec(t),e===null&&(e=_0.exec(t)),e===null)throw new Error("Date resolve error");if(r=+e[1],n=+e[2]-1,i=+e[3],!e[4])return new Date(Date.UTC(r,n,i));if(s=+e[4],o=+e[5],a=+e[6],e[7]){for(u=e[7].slice(0,3);u.length<3;)u+="0";u=+u}return e[9]&&(l=+e[10],f=+(e[11]||0),c=(l*60+f)*6e4,e[9]==="-"&&(c=-c)),h=new Date(Date.UTC(r,n,i,s,o,a,u)),c&&h.setTime(h.getTime()-c),h}function L1(t){return t.toISOString()}var E0=new Le("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:B1,construct:I1,instanceOf:Date,represent:L1});function N1(t){return t==="<<"||t===null}var b0=new Le("tag:yaml.org,2002:merge",{kind:"scalar",resolve:N1}),Fc=`ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=
\r`;function M1(t){if(t===null)return!1;var e,r,n=0,i=t.length,s=Fc;for(r=0;r<i;r++)if(e=s.indexOf(t.charAt(r)),!(e>64)){if(e<0)return!1;n+=6}return n%8===0}function q1(t){var e,r,n=t.replace(/[\r\n=]/g,""),i=n.length,s=Fc,o=0,a=[];for(e=0;e<i;e++)e%4===0&&e&&(a.push(o>>16&255),a.push(o>>8&255),a.push(o&255)),o=o<<6|s.indexOf(n.charAt(e));return r=i%4*6,r===0?(a.push(o>>16&255),a.push(o>>8&255),a.push(o&255)):r===18?(a.push(o>>10&255),a.push(o>>2&255)):r===12&&a.push(o>>4&255),new Uint8Array(a)}function H1(t){var e="",r=0,n,i,s=t.length,o=Fc;for(n=0;n<s;n++)n%3===0&&n&&(e+=o[r>>18&63],e+=o[r>>12&63],e+=o[r>>6&63],e+=o[r&63]),r=(r<<8)+t[n];return i=s%3,i===0?(e+=o[r>>18&63],e+=o[r>>12&63],e+=o[r>>6&63],e+=o[r&63]):i===2?(e+=o[r>>10&63],e+=o[r>>4&63],e+=o[r<<2&63],e+=o[64]):i===1&&(e+=o[r>>2&63],e+=o[r<<4&63],e+=o[64],e+=o[64]),e}function j1(t){return Object.prototype.toString.call(t)==="[object Uint8Array]"}var D0=new Le("tag:yaml.org,2002:binary",{kind:"scalar",resolve:M1,construct:q1,predicate:j1,represent:H1}),$1=Object.prototype.hasOwnProperty,U1=Object.prototype.toString;function G1(t){if(t===null)return!0;var e=[],r,n,i,s,o,a=t;for(r=0,n=a.length;r<n;r+=1){if(i=a[r],o=!1,U1.call(i)!=="[object Object]")return!1;for(s in i)if($1.call(i,s))if(!o)o=!0;else return!1;if(!o)return!1;if(e.indexOf(s)===-1)e.push(s);else return!1}return!0}function W1(t){return t!==null?t:[]}var w0=new Le("tag:yaml.org,2002:omap",{kind:"sequence",resolve:G1,construct:W1}),z1=Object.prototype.toString;function V1(t){if(t===null)return!0;var e,r,n,i,s,o=t;for(s=new Array(o.length),e=0,r=o.length;e<r;e+=1){if(n=o[e],z1.call(n)!=="[object Object]"||(i=Object.keys(n),i.length!==1))return!1;s[e]=[i[0],n[i[0]]]}return!0}function Y1(t){if(t===null)return[];var e,r,n,i,s,o=t;for(s=new Array(o.length),e=0,r=o.length;e<r;e+=1)n=o[e],i=Object.keys(n),s[e]=[i[0],n[i[0]]];return s}var A0=new Le("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:V1,construct:Y1}),K1=Object.prototype.hasOwnProperty;function J1(t){if(t===null)return!0;var e,r=t;for(e in r)if(K1.call(r,e)&&r[e]!==null)return!1;return!0}function X1(t){return t!==null?t:{}}var x0=new Le("tag:yaml.org,2002:set",{kind:"mapping",resolve:J1,construct:X1}),Tc=g0.extend({implicit:[E0,b0],explicit:[D0,w0,A0,x0]}),vr=Object.prototype.hasOwnProperty,io=1,S0=2,C0=3,so=4,Ac=1,Z1=2,Gm=3,Q1=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,eR=/[\x85\u2028\u2029]/,tR=/[,\[\]\{\}]/,v0=/^(?:!|!!|![a-z\-]+!)$/i,R0=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i;function Wm(t){return Object.prototype.toString.call(t)}function It(t){return t===10||t===13}function nn(t){return t===9||t===32}function et(t){return t===9||t===32||t===10||t===13}function kn(t){return t===44||t===91||t===93||t===123||t===125}function rR(t){var e;return 48<=t&&t<=57?t-48:(e=t|32,97<=e&&e<=102?e-97+10:-1)}function nR(t){return t===120?2:t===117?4:t===85?8:0}function iR(t){return 48<=t&&t<=57?t-48:-1}function zm(t){return t===48?"\0":t===97?"\x07":t===98?"\b":t===116||t===9?"	":t===110?`
`:t===118?"\v":t===102?"\f":t===114?"\r":t===101?"\x1B":t===32?" ":t===34?'"':t===47?"/":t===92?"\\":t===78?"\x85":t===95?"\xA0":t===76?"\u2028":t===80?"\u2029":""}function sR(t){return t<=65535?String.fromCharCode(t):String.fromCharCode((t-65536>>10)+55296,(t-65536&1023)+56320)}var F0=new Array(256),T0=new Array(256);for(rn=0;rn<256;rn++)F0[rn]=zm(rn)?1:0,T0[rn]=zm(rn);var rn;function oR(t,e){this.input=t,this.filename=e.filename||null,this.schema=e.schema||Tc,this.onWarning=e.onWarning||null,this.legacy=e.legacy||!1,this.json=e.json||!1,this.listener=e.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=t.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.firstTabInLine=-1,this.documents=[]}function O0(t,e){var r={name:t.filename,buffer:t.input.slice(0,-1),position:t.position,line:t.line,column:t.position-t.lineStart};return r.snippet=l1(r),new Xe(e,r)}function W(t,e){throw O0(t,e)}function oo(t,e){t.onWarning&&t.onWarning.call(null,O0(t,e))}var Vm={YAML:function(e,r,n){var i,s,o;e.version!==null&&W(e,"duplication of %YAML directive"),n.length!==1&&W(e,"YAML directive accepts exactly one argument"),i=/^([0-9]+)\.([0-9]+)$/.exec(n[0]),i===null&&W(e,"ill-formed argument of the YAML directive"),s=parseInt(i[1],10),o=parseInt(i[2],10),s!==1&&W(e,"unacceptable YAML version of the document"),e.version=n[0],e.checkLineBreaks=o<2,o!==1&&o!==2&&oo(e,"unsupported YAML version of the document")},TAG:function(e,r,n){var i,s;n.length!==2&&W(e,"TAG directive accepts exactly two arguments"),i=n[0],s=n[1],v0.test(i)||W(e,"ill-formed tag handle (first argument) of the TAG directive"),vr.call(e.tagMap,i)&&W(e,'there is a previously declared suffix for "'+i+'" tag handle'),R0.test(s)||W(e,"ill-formed tag prefix (second argument) of the TAG directive");try{s=decodeURIComponent(s)}catch{W(e,"tag prefix is malformed: "+s)}e.tagMap[i]=s}};function Cr(t,e,r,n){var i,s,o,a;if(e<r){if(a=t.input.slice(e,r),n)for(i=0,s=a.length;i<s;i+=1)o=a.charCodeAt(i),o===9||32<=o&&o<=1114111||W(t,"expected valid JSON character");else Q1.test(a)&&W(t,"the stream contains non-printable characters");t.result+=a}}function Ym(t,e,r,n){var i,s,o,a;for(Fe.isObject(r)||W(t,"cannot merge mappings; the provided source object is unacceptable"),i=Object.keys(r),o=0,a=i.length;o<a;o+=1)s=i[o],vr.call(e,s)||(e[s]=r[s],n[s]=!0)}function Pn(t,e,r,n,i,s,o,a,u){var c,l;if(Array.isArray(i))for(i=Array.prototype.slice.call(i),c=0,l=i.length;c<l;c+=1)Array.isArray(i[c])&&W(t,"nested arrays are not supported inside keys"),typeof i=="object"&&Wm(i[c])==="[object Object]"&&(i[c]="[object Object]");if(typeof i=="object"&&Wm(i)==="[object Object]"&&(i="[object Object]"),i=String(i),e===null&&(e={}),n==="tag:yaml.org,2002:merge")if(Array.isArray(s))for(c=0,l=s.length;c<l;c+=1)Ym(t,e,s[c],r);else Ym(t,e,s,r);else!t.json&&!vr.call(r,i)&&vr.call(e,i)&&(t.line=o||t.line,t.lineStart=a||t.lineStart,t.position=u||t.position,W(t,"duplicated mapping key")),i==="__proto__"?Object.defineProperty(e,i,{configurable:!0,enumerable:!0,writable:!0,value:s}):e[i]=s,delete r[i];return e}function Oc(t){var e;e=t.input.charCodeAt(t.position),e===10?t.position++:e===13?(t.position++,t.input.charCodeAt(t.position)===10&&t.position++):W(t,"a line break is expected"),t.line+=1,t.lineStart=t.position,t.firstTabInLine=-1}function ve(t,e,r){for(var n=0,i=t.input.charCodeAt(t.position);i!==0;){for(;nn(i);)i===9&&t.firstTabInLine===-1&&(t.firstTabInLine=t.position),i=t.input.charCodeAt(++t.position);if(e&&i===35)do i=t.input.charCodeAt(++t.position);while(i!==10&&i!==13&&i!==0);if(It(i))for(Oc(t),i=t.input.charCodeAt(t.position),n++,t.lineIndent=0;i===32;)t.lineIndent++,i=t.input.charCodeAt(++t.position);else break}return r!==-1&&n!==0&&t.lineIndent<r&&oo(t,"deficient indentation"),n}function co(t){var e=t.position,r;return r=t.input.charCodeAt(e),!!((r===45||r===46)&&r===t.input.charCodeAt(e+1)&&r===t.input.charCodeAt(e+2)&&(e+=3,r=t.input.charCodeAt(e),r===0||et(r)))}function kc(t,e){e===1?t.result+=" ":e>1&&(t.result+=Fe.repeat(`
`,e-1))}function aR(t,e,r){var n,i,s,o,a,u,c,l,f=t.kind,h=t.result,m;if(m=t.input.charCodeAt(t.position),et(m)||kn(m)||m===35||m===38||m===42||m===33||m===124||m===62||m===39||m===34||m===37||m===64||m===96||(m===63||m===45)&&(i=t.input.charCodeAt(t.position+1),et(i)||r&&kn(i)))return!1;for(t.kind="scalar",t.result="",s=o=t.position,a=!1;m!==0;){if(m===58){if(i=t.input.charCodeAt(t.position+1),et(i)||r&&kn(i))break}else if(m===35){if(n=t.input.charCodeAt(t.position-1),et(n))break}else{if(t.position===t.lineStart&&co(t)||r&&kn(m))break;if(It(m))if(u=t.line,c=t.lineStart,l=t.lineIndent,ve(t,!1,-1),t.lineIndent>=e){a=!0,m=t.input.charCodeAt(t.position);continue}else{t.position=o,t.line=u,t.lineStart=c,t.lineIndent=l;break}}a&&(Cr(t,s,o,!1),kc(t,t.line-u),s=o=t.position,a=!1),nn(m)||(o=t.position+1),m=t.input.charCodeAt(++t.position)}return Cr(t,s,o,!1),t.result?!0:(t.kind=f,t.result=h,!1)}function uR(t,e){var r,n,i;if(r=t.input.charCodeAt(t.position),r!==39)return!1;for(t.kind="scalar",t.result="",t.position++,n=i=t.position;(r=t.input.charCodeAt(t.position))!==0;)if(r===39)if(Cr(t,n,t.position,!0),r=t.input.charCodeAt(++t.position),r===39)n=t.position,t.position++,i=t.position;else return!0;else It(r)?(Cr(t,n,i,!0),kc(t,ve(t,!1,e)),n=i=t.position):t.position===t.lineStart&&co(t)?W(t,"unexpected end of the document within a single quoted scalar"):(t.position++,i=t.position);W(t,"unexpected end of the stream within a single quoted scalar")}function cR(t,e){var r,n,i,s,o,a;if(a=t.input.charCodeAt(t.position),a!==34)return!1;for(t.kind="scalar",t.result="",t.position++,r=n=t.position;(a=t.input.charCodeAt(t.position))!==0;){if(a===34)return Cr(t,r,t.position,!0),t.position++,!0;if(a===92){if(Cr(t,r,t.position,!0),a=t.input.charCodeAt(++t.position),It(a))ve(t,!1,e);else if(a<256&&F0[a])t.result+=T0[a],t.position++;else if((o=nR(a))>0){for(i=o,s=0;i>0;i--)a=t.input.charCodeAt(++t.position),(o=rR(a))>=0?s=(s<<4)+o:W(t,"expected hexadecimal character");t.result+=sR(s),t.position++}else W(t,"unknown escape sequence");r=n=t.position}else It(a)?(Cr(t,r,n,!0),kc(t,ve(t,!1,e)),r=n=t.position):t.position===t.lineStart&&co(t)?W(t,"unexpected end of the document within a double quoted scalar"):(t.position++,n=t.position)}W(t,"unexpected end of the stream within a double quoted scalar")}function lR(t,e){var r=!0,n,i,s,o=t.tag,a,u=t.anchor,c,l,f,h,m,_=Object.create(null),y,p,D,C;if(C=t.input.charCodeAt(t.position),C===91)l=93,m=!1,a=[];else if(C===123)l=125,m=!0,a={};else return!1;for(t.anchor!==null&&(t.anchorMap[t.anchor]=a),C=t.input.charCodeAt(++t.position);C!==0;){if(ve(t,!0,e),C=t.input.charCodeAt(t.position),C===l)return t.position++,t.tag=o,t.anchor=u,t.kind=m?"mapping":"sequence",t.result=a,!0;r?C===44&&W(t,"expected the node content, but found ','"):W(t,"missed comma between flow collection entries"),p=y=D=null,f=h=!1,C===63&&(c=t.input.charCodeAt(t.position+1),et(c)&&(f=h=!0,t.position++,ve(t,!0,e))),n=t.line,i=t.lineStart,s=t.position,Bn(t,e,io,!1,!0),p=t.tag,y=t.result,ve(t,!0,e),C=t.input.charCodeAt(t.position),(h||t.line===n)&&C===58&&(f=!0,C=t.input.charCodeAt(++t.position),ve(t,!0,e),Bn(t,e,io,!1,!0),D=t.result),m?Pn(t,a,_,p,y,D,n,i,s):f?a.push(Pn(t,null,_,p,y,D,n,i,s)):a.push(y),ve(t,!0,e),C=t.input.charCodeAt(t.position),C===44?(r=!0,C=t.input.charCodeAt(++t.position)):r=!1}W(t,"unexpected end of the stream within a flow collection")}function fR(t,e){var r,n,i=Ac,s=!1,o=!1,a=e,u=0,c=!1,l,f;if(f=t.input.charCodeAt(t.position),f===124)n=!1;else if(f===62)n=!0;else return!1;for(t.kind="scalar",t.result="";f!==0;)if(f=t.input.charCodeAt(++t.position),f===43||f===45)Ac===i?i=f===43?Gm:Z1:W(t,"repeat of a chomping mode identifier");else if((l=iR(f))>=0)l===0?W(t,"bad explicit indentation width of a block scalar; it cannot be less than one"):o?W(t,"repeat of an indentation width identifier"):(a=e+l-1,o=!0);else break;if(nn(f)){do f=t.input.charCodeAt(++t.position);while(nn(f));if(f===35)do f=t.input.charCodeAt(++t.position);while(!It(f)&&f!==0)}for(;f!==0;){for(Oc(t),t.lineIndent=0,f=t.input.charCodeAt(t.position);(!o||t.lineIndent<a)&&f===32;)t.lineIndent++,f=t.input.charCodeAt(++t.position);if(!o&&t.lineIndent>a&&(a=t.lineIndent),It(f)){u++;continue}if(t.lineIndent<a){i===Gm?t.result+=Fe.repeat(`
`,s?1+u:u):i===Ac&&s&&(t.result+=`
`);break}for(n?nn(f)?(c=!0,t.result+=Fe.repeat(`
`,s?1+u:u)):c?(c=!1,t.result+=Fe.repeat(`
`,u+1)):u===0?s&&(t.result+=" "):t.result+=Fe.repeat(`
`,u):t.result+=Fe.repeat(`
`,s?1+u:u),s=!0,o=!0,u=0,r=t.position;!It(f)&&f!==0;)f=t.input.charCodeAt(++t.position);Cr(t,r,t.position,!1)}return!0}function Km(t,e){var r,n=t.tag,i=t.anchor,s=[],o,a=!1,u;if(t.firstTabInLine!==-1)return!1;for(t.anchor!==null&&(t.anchorMap[t.anchor]=s),u=t.input.charCodeAt(t.position);u!==0&&(t.firstTabInLine!==-1&&(t.position=t.firstTabInLine,W(t,"tab characters must not be used in indentation")),!(u!==45||(o=t.input.charCodeAt(t.position+1),!et(o))));){if(a=!0,t.position++,ve(t,!0,-1)&&t.lineIndent<=e){s.push(null),u=t.input.charCodeAt(t.position);continue}if(r=t.line,Bn(t,e,C0,!1,!0),s.push(t.result),ve(t,!0,-1),u=t.input.charCodeAt(t.position),(t.line===r||t.lineIndent>e)&&u!==0)W(t,"bad indentation of a sequence entry");else if(t.lineIndent<e)break}return a?(t.tag=n,t.anchor=i,t.kind="sequence",t.result=s,!0):!1}function hR(t,e,r){var n,i,s,o,a,u,c=t.tag,l=t.anchor,f={},h=Object.create(null),m=null,_=null,y=null,p=!1,D=!1,C;if(t.firstTabInLine!==-1)return!1;for(t.anchor!==null&&(t.anchorMap[t.anchor]=f),C=t.input.charCodeAt(t.position);C!==0;){if(!p&&t.firstTabInLine!==-1&&(t.position=t.firstTabInLine,W(t,"tab characters must not be used in indentation")),n=t.input.charCodeAt(t.position+1),s=t.line,(C===63||C===58)&&et(n))C===63?(p&&(Pn(t,f,h,m,_,null,o,a,u),m=_=y=null),D=!0,p=!0,i=!0):p?(p=!1,i=!0):W(t,"incomplete explicit mapping pair; a key node is missed; or followed by a non-tabulated empty line"),t.position+=1,C=n;else{if(o=t.line,a=t.lineStart,u=t.position,!Bn(t,r,S0,!1,!0))break;if(t.line===s){for(C=t.input.charCodeAt(t.position);nn(C);)C=t.input.charCodeAt(++t.position);if(C===58)C=t.input.charCodeAt(++t.position),et(C)||W(t,"a whitespace character is expected after the key-value separator within a block mapping"),p&&(Pn(t,f,h,m,_,null,o,a,u),m=_=y=null),D=!0,p=!1,i=!1,m=t.tag,_=t.result;else if(D)W(t,"can not read an implicit mapping pair; a colon is missed");else return t.tag=c,t.anchor=l,!0}else if(D)W(t,"can not read a block mapping entry; a multiline key may not be an implicit key");else return t.tag=c,t.anchor=l,!0}if((t.line===s||t.lineIndent>e)&&(p&&(o=t.line,a=t.lineStart,u=t.position),Bn(t,e,so,!0,i)&&(p?_=t.result:y=t.result),p||(Pn(t,f,h,m,_,y,o,a,u),m=_=y=null),ve(t,!0,-1),C=t.input.charCodeAt(t.position)),(t.line===s||t.lineIndent>e)&&C!==0)W(t,"bad indentation of a mapping entry");else if(t.lineIndent<e)break}return p&&Pn(t,f,h,m,_,null,o,a,u),D&&(t.tag=c,t.anchor=l,t.kind="mapping",t.result=f),D}function dR(t){var e,r=!1,n=!1,i,s,o;if(o=t.input.charCodeAt(t.position),o!==33)return!1;if(t.tag!==null&&W(t,"duplication of a tag property"),o=t.input.charCodeAt(++t.position),o===60?(r=!0,o=t.input.charCodeAt(++t.position)):o===33?(n=!0,i="!!",o=t.input.charCodeAt(++t.position)):i="!",e=t.position,r){do o=t.input.charCodeAt(++t.position);while(o!==0&&o!==62);t.position<t.length?(s=t.input.slice(e,t.position),o=t.input.charCodeAt(++t.position)):W(t,"unexpected end of the stream within a verbatim tag")}else{for(;o!==0&&!et(o);)o===33&&(n?W(t,"tag suffix cannot contain exclamation marks"):(i=t.input.slice(e-1,t.position+1),v0.test(i)||W(t,"named tag handle cannot contain such characters"),n=!0,e=t.position+1)),o=t.input.charCodeAt(++t.position);s=t.input.slice(e,t.position),tR.test(s)&&W(t,"tag suffix cannot contain flow indicator characters")}s&&!R0.test(s)&&W(t,"tag name cannot contain such characters: "+s);try{s=decodeURIComponent(s)}catch{W(t,"tag name is malformed: "+s)}return r?t.tag=s:vr.call(t.tagMap,i)?t.tag=t.tagMap[i]+s:i==="!"?t.tag="!"+s:i==="!!"?t.tag="tag:yaml.org,2002:"+s:W(t,'undeclared tag handle "'+i+'"'),!0}function pR(t){var e,r;if(r=t.input.charCodeAt(t.position),r!==38)return!1;for(t.anchor!==null&&W(t,"duplication of an anchor property"),r=t.input.charCodeAt(++t.position),e=t.position;r!==0&&!et(r)&&!kn(r);)r=t.input.charCodeAt(++t.position);return t.position===e&&W(t,"name of an anchor node must contain at least one character"),t.anchor=t.input.slice(e,t.position),!0}function mR(t){var e,r,n;if(n=t.input.charCodeAt(t.position),n!==42)return!1;for(n=t.input.charCodeAt(++t.position),e=t.position;n!==0&&!et(n)&&!kn(n);)n=t.input.charCodeAt(++t.position);return t.position===e&&W(t,"name of an alias node must contain at least one character"),r=t.input.slice(e,t.position),vr.call(t.anchorMap,r)||W(t,'unidentified alias "'+r+'"'),t.result=t.anchorMap[r],ve(t,!0,-1),!0}function Bn(t,e,r,n,i){var s,o,a,u=1,c=!1,l=!1,f,h,m,_,y,p;if(t.listener!==null&&t.listener("open",t),t.tag=null,t.anchor=null,t.kind=null,t.result=null,s=o=a=so===r||C0===r,n&&ve(t,!0,-1)&&(c=!0,t.lineIndent>e?u=1:t.lineIndent===e?u=0:t.lineIndent<e&&(u=-1)),u===1)for(;dR(t)||pR(t);)ve(t,!0,-1)?(c=!0,a=s,t.lineIndent>e?u=1:t.lineIndent===e?u=0:t.lineIndent<e&&(u=-1)):a=!1;if(a&&(a=c||i),(u===1||so===r)&&(io===r||S0===r?y=e:y=e+1,p=t.position-t.lineStart,u===1?a&&(Km(t,p)||hR(t,p,y))||lR(t,y)?l=!0:(o&&fR(t,y)||uR(t,y)||cR(t,y)?l=!0:mR(t)?(l=!0,(t.tag!==null||t.anchor!==null)&&W(t,"alias node should not have any properties")):aR(t,y,io===r)&&(l=!0,t.tag===null&&(t.tag="?")),t.anchor!==null&&(t.anchorMap[t.anchor]=t.result)):u===0&&(l=a&&Km(t,p))),t.tag===null)t.anchor!==null&&(t.anchorMap[t.anchor]=t.result);else if(t.tag==="?"){for(t.result!==null&&t.kind!=="scalar"&&W(t,'unacceptable node kind for !<?> tag; it should be "scalar", not "'+t.kind+'"'),f=0,h=t.implicitTypes.length;f<h;f+=1)if(_=t.implicitTypes[f],_.resolve(t.result)){t.result=_.construct(t.result),t.tag=_.tag,t.anchor!==null&&(t.anchorMap[t.anchor]=t.result);break}}else if(t.tag!=="!"){if(vr.call(t.typeMap[t.kind||"fallback"],t.tag))_=t.typeMap[t.kind||"fallback"][t.tag];else for(_=null,m=t.typeMap.multi[t.kind||"fallback"],f=0,h=m.length;f<h;f+=1)if(t.tag.slice(0,m[f].tag.length)===m[f].tag){_=m[f];break}_||W(t,"unknown tag !<"+t.tag+">"),t.result!==null&&_.kind!==t.kind&&W(t,"unacceptable node kind for !<"+t.tag+'> tag; it should be "'+_.kind+'", not "'+t.kind+'"'),_.resolve(t.result,t.tag)?(t.result=_.construct(t.result,t.tag),t.anchor!==null&&(t.anchorMap[t.anchor]=t.result)):W(t,"cannot resolve a node with !<"+t.tag+"> explicit tag")}return t.listener!==null&&t.listener("close",t),t.tag!==null||t.anchor!==null||l}function gR(t){var e=t.position,r,n,i,s=!1,o;for(t.version=null,t.checkLineBreaks=t.legacy,t.tagMap=Object.create(null),t.anchorMap=Object.create(null);(o=t.input.charCodeAt(t.position))!==0&&(ve(t,!0,-1),o=t.input.charCodeAt(t.position),!(t.lineIndent>0||o!==37));){for(s=!0,o=t.input.charCodeAt(++t.position),r=t.position;o!==0&&!et(o);)o=t.input.charCodeAt(++t.position);for(n=t.input.slice(r,t.position),i=[],n.length<1&&W(t,"directive name must not be less than one character in length");o!==0;){for(;nn(o);)o=t.input.charCodeAt(++t.position);if(o===35){do o=t.input.charCodeAt(++t.position);while(o!==0&&!It(o));break}if(It(o))break;for(r=t.position;o!==0&&!et(o);)o=t.input.charCodeAt(++t.position);i.push(t.input.slice(r,t.position))}o!==0&&Oc(t),vr.call(Vm,n)?Vm[n](t,n,i):oo(t,'unknown document directive "'+n+'"')}if(ve(t,!0,-1),t.lineIndent===0&&t.input.charCodeAt(t.position)===45&&t.input.charCodeAt(t.position+1)===45&&t.input.charCodeAt(t.position+2)===45?(t.position+=3,ve(t,!0,-1)):s&&W(t,"directives end mark is expected"),Bn(t,t.lineIndent-1,so,!1,!0),ve(t,!0,-1),t.checkLineBreaks&&eR.test(t.input.slice(e,t.position))&&oo(t,"non-ASCII line breaks are interpreted as content"),t.documents.push(t.result),t.position===t.lineStart&&co(t)){t.input.charCodeAt(t.position)===46&&(t.position+=3,ve(t,!0,-1));return}if(t.position<t.length-1)W(t,"end of the stream or a document separator is expected");else return}function k0(t,e){t=String(t),e=e||{},t.length!==0&&(t.charCodeAt(t.length-1)!==10&&t.charCodeAt(t.length-1)!==13&&(t+=`
`),t.charCodeAt(0)===65279&&(t=t.slice(1)));var r=new oR(t,e),n=t.indexOf("\0");for(n!==-1&&(r.position=n,W(r,"null byte is not allowed in input")),r.input+="\0";r.input.charCodeAt(r.position)===32;)r.lineIndent+=1,r.position+=1;for(;r.position<r.length-1;)gR(r);return r.documents}function yR(t,e,r){e!==null&&typeof e=="object"&&typeof r=="undefined"&&(r=e,e=null);var n=k0(t,r);if(typeof e!="function")return n;for(var i=0,s=n.length;i<s;i+=1)e(n[i])}function _R(t,e){var r=k0(t,e);if(r.length!==0){if(r.length===1)return r[0];throw new Xe("expected a single document in the stream, but found more")}}var ER=yR,bR=_R,P0={loadAll:ER,load:bR},B0=Object.prototype.toString,I0=Object.prototype.hasOwnProperty,Pc=65279,DR=9,Yi=10,wR=13,AR=32,xR=33,SR=34,Sc=35,CR=37,vR=38,RR=39,FR=42,L0=44,TR=45,ao=58,OR=61,kR=62,PR=63,BR=64,N0=91,M0=93,IR=96,q0=123,LR=124,H0=125,He={};He[0]="\\0";He[7]="\\a";He[8]="\\b";He[9]="\\t";He[10]="\\n";He[11]="\\v";He[12]="\\f";He[13]="\\r";He[27]="\\e";He[34]='\\"';He[92]="\\\\";He[133]="\\N";He[160]="\\_";He[8232]="\\L";He[8233]="\\P";var NR=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],MR=/^[-+]?[0-9_]+(?::[0-9_]+)+(?:\.[0-9_]*)?$/;function qR(t,e){var r,n,i,s,o,a,u;if(e===null)return{};for(r={},n=Object.keys(e),i=0,s=n.length;i<s;i+=1)o=n[i],a=String(e[o]),o.slice(0,2)==="!!"&&(o="tag:yaml.org,2002:"+o.slice(2)),u=t.compiledTypeMap.fallback[o],u&&I0.call(u.styleAliases,a)&&(a=u.styleAliases[a]),r[o]=a;return r}function HR(t){var e,r,n;if(e=t.toString(16).toUpperCase(),t<=255)r="x",n=2;else if(t<=65535)r="u",n=4;else if(t<=4294967295)r="U",n=8;else throw new Xe("code point within a string may not be greater than 0xFFFFFFFF");return"\\"+r+Fe.repeat("0",n-e.length)+e}var jR=1,Ki=2;function $R(t){this.schema=t.schema||Tc,this.indent=Math.max(1,t.indent||2),this.noArrayIndent=t.noArrayIndent||!1,this.skipInvalid=t.skipInvalid||!1,this.flowLevel=Fe.isNothing(t.flowLevel)?-1:t.flowLevel,this.styleMap=qR(this.schema,t.styles||null),this.sortKeys=t.sortKeys||!1,this.lineWidth=t.lineWidth||80,this.noRefs=t.noRefs||!1,this.noCompatMode=t.noCompatMode||!1,this.condenseFlow=t.condenseFlow||!1,this.quotingType=t.quotingType==='"'?Ki:jR,this.forceQuotes=t.forceQuotes||!1,this.replacer=typeof t.replacer=="function"?t.replacer:null,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function Jm(t,e){for(var r=Fe.repeat(" ",e),n=0,i=-1,s="",o,a=t.length;n<a;)i=t.indexOf(`
`,n),i===-1?(o=t.slice(n),n=a):(o=t.slice(n,i+1),n=i+1),o.length&&o!==`
`&&(s+=r),s+=o;return s}function Cc(t,e){return`
`+Fe.repeat(" ",t.indent*e)}function UR(t,e){var r,n,i;for(r=0,n=t.implicitTypes.length;r<n;r+=1)if(i=t.implicitTypes[r],i.resolve(e))return!0;return!1}function uo(t){return t===AR||t===DR}function Ji(t){return 32<=t&&t<=126||161<=t&&t<=55295&&t!==8232&&t!==8233||57344<=t&&t<=65533&&t!==Pc||65536<=t&&t<=1114111}function Xm(t){return Ji(t)&&t!==Pc&&t!==wR&&t!==Yi}function Zm(t,e,r){var n=Xm(t),i=n&&!uo(t);return(r?n:n&&t!==L0&&t!==N0&&t!==M0&&t!==q0&&t!==H0)&&t!==Sc&&!(e===ao&&!i)||Xm(e)&&!uo(e)&&t===Sc||e===ao&&i}function GR(t){return Ji(t)&&t!==Pc&&!uo(t)&&t!==TR&&t!==PR&&t!==ao&&t!==L0&&t!==N0&&t!==M0&&t!==q0&&t!==H0&&t!==Sc&&t!==vR&&t!==FR&&t!==xR&&t!==LR&&t!==OR&&t!==kR&&t!==RR&&t!==SR&&t!==CR&&t!==BR&&t!==IR}function WR(t){return!uo(t)&&t!==ao}function zi(t,e){var r=t.charCodeAt(e),n;return r>=55296&&r<=56319&&e+1<t.length&&(n=t.charCodeAt(e+1),n>=56320&&n<=57343)?(r-55296)*1024+n-56320+65536:r}function j0(t){var e=/^\n* /;return e.test(t)}var $0=1,vc=2,U0=3,G0=4,On=5;function zR(t,e,r,n,i,s,o,a){var u,c=0,l=null,f=!1,h=!1,m=n!==-1,_=-1,y=GR(zi(t,0))&&WR(zi(t,t.length-1));if(e||o)for(u=0;u<t.length;c>=65536?u+=2:u++){if(c=zi(t,u),!Ji(c))return On;y=y&&Zm(c,l,a),l=c}else{for(u=0;u<t.length;c>=65536?u+=2:u++){if(c=zi(t,u),c===Yi)f=!0,m&&(h=h||u-_-1>n&&t[_+1]!==" ",_=u);else if(!Ji(c))return On;y=y&&Zm(c,l,a),l=c}h=h||m&&u-_-1>n&&t[_+1]!==" "}return!f&&!h?y&&!o&&!i(t)?$0:s===Ki?On:vc:r>9&&j0(t)?On:o?s===Ki?On:vc:h?G0:U0}function VR(t,e,r,n,i){t.dump=function(){if(e.length===0)return t.quotingType===Ki?'""':"''";if(!t.noCompatMode&&(NR.indexOf(e)!==-1||MR.test(e)))return t.quotingType===Ki?'"'+e+'"':"'"+e+"'";var s=t.indent*Math.max(1,r),o=t.lineWidth===-1?-1:Math.max(Math.min(t.lineWidth,40),t.lineWidth-s),a=n||t.flowLevel>-1&&r>=t.flowLevel;function u(c){return UR(t,c)}switch(zR(e,a,t.indent,o,u,t.quotingType,t.forceQuotes&&!n,i)){case $0:return e;case vc:return"'"+e.replace(/'/g,"''")+"'";case U0:return"|"+Qm(e,t.indent)+e0(Jm(e,s));case G0:return">"+Qm(e,t.indent)+e0(Jm(YR(e,o),s));case On:return'"'+KR(e)+'"';default:throw new Xe("impossible error: invalid scalar style")}}()}function Qm(t,e){var r=j0(t)?String(e):"",n=t[t.length-1]===`
`,i=n&&(t[t.length-2]===`
`||t===`
`),s=i?"+":n?"":"-";return r+s+`
`}function e0(t){return t[t.length-1]===`
`?t.slice(0,-1):t}function YR(t,e){for(var r=/(\n+)([^\n]*)/g,n=function(){var c=t.indexOf(`
`);return c=c!==-1?c:t.length,r.lastIndex=c,t0(t.slice(0,c),e)}(),i=t[0]===`
`||t[0]===" ",s,o;o=r.exec(t);){var a=o[1],u=o[2];s=u[0]===" ",n+=a+(!i&&!s&&u!==""?`
`:"")+t0(u,e),i=s}return n}function t0(t,e){if(t===""||t[0]===" ")return t;for(var r=/ [^ ]/g,n,i=0,s,o=0,a=0,u="";n=r.exec(t);)a=n.index,a-i>e&&(s=o>i?o:a,u+=`
`+t.slice(i,s),i=s+1),o=a;return u+=`
`,t.length-i>e&&o>i?u+=t.slice(i,o)+`
`+t.slice(o+1):u+=t.slice(i),u.slice(1)}function KR(t){for(var e="",r=0,n,i=0;i<t.length;r>=65536?i+=2:i++)r=zi(t,i),n=He[r],!n&&Ji(r)?(e+=t[i],r>=65536&&(e+=t[i+1])):e+=n||HR(r);return e}function JR(t,e,r){var n="",i=t.tag,s,o,a;for(s=0,o=r.length;s<o;s+=1)a=r[s],t.replacer&&(a=t.replacer.call(r,String(s),a)),(rr(t,e,a,!1,!1)||typeof a=="undefined"&&rr(t,e,null,!1,!1))&&(n!==""&&(n+=","+(t.condenseFlow?"":" ")),n+=t.dump);t.tag=i,t.dump="["+n+"]"}function r0(t,e,r,n){var i="",s=t.tag,o,a,u;for(o=0,a=r.length;o<a;o+=1)u=r[o],t.replacer&&(u=t.replacer.call(r,String(o),u)),(rr(t,e+1,u,!0,!0,!1,!0)||typeof u=="undefined"&&rr(t,e+1,null,!0,!0,!1,!0))&&((!n||i!=="")&&(i+=Cc(t,e)),t.dump&&Yi===t.dump.charCodeAt(0)?i+="-":i+="- ",i+=t.dump);t.tag=s,t.dump=i||"[]"}function XR(t,e,r){var n="",i=t.tag,s=Object.keys(r),o,a,u,c,l;for(o=0,a=s.length;o<a;o+=1)l="",n!==""&&(l+=", "),t.condenseFlow&&(l+='"'),u=s[o],c=r[u],t.replacer&&(c=t.replacer.call(r,u,c)),rr(t,e,u,!1,!1)&&(t.dump.length>1024&&(l+="? "),l+=t.dump+(t.condenseFlow?'"':"")+":"+(t.condenseFlow?"":" "),rr(t,e,c,!1,!1)&&(l+=t.dump,n+=l));t.tag=i,t.dump="{"+n+"}"}function ZR(t,e,r,n){var i="",s=t.tag,o=Object.keys(r),a,u,c,l,f,h;if(t.sortKeys===!0)o.sort();else if(typeof t.sortKeys=="function")o.sort(t.sortKeys);else if(t.sortKeys)throw new Xe("sortKeys must be a boolean or a function");for(a=0,u=o.length;a<u;a+=1)h="",(!n||i!=="")&&(h+=Cc(t,e)),c=o[a],l=r[c],t.replacer&&(l=t.replacer.call(r,c,l)),rr(t,e+1,c,!0,!0,!0)&&(f=t.tag!==null&&t.tag!=="?"||t.dump&&t.dump.length>1024,f&&(t.dump&&Yi===t.dump.charCodeAt(0)?h+="?":h+="? "),h+=t.dump,f&&(h+=Cc(t,e)),rr(t,e+1,l,!0,f)&&(t.dump&&Yi===t.dump.charCodeAt(0)?h+=":":h+=": ",h+=t.dump,i+=h));t.tag=s,t.dump=i||"{}"}function n0(t,e,r){var n,i,s,o,a,u;for(i=r?t.explicitTypes:t.implicitTypes,s=0,o=i.length;s<o;s+=1)if(a=i[s],(a.instanceOf||a.predicate)&&(!a.instanceOf||typeof e=="object"&&e instanceof a.instanceOf)&&(!a.predicate||a.predicate(e))){if(r?a.multi&&a.representName?t.tag=a.representName(e):t.tag=a.tag:t.tag="?",a.represent){if(u=t.styleMap[a.tag]||a.defaultStyle,B0.call(a.represent)==="[object Function]")n=a.represent(e,u);else if(I0.call(a.represent,u))n=a.represent[u](e,u);else throw new Xe("!<"+a.tag+'> tag resolver accepts not "'+u+'" style');t.dump=n}return!0}return!1}function rr(t,e,r,n,i,s,o){t.tag=null,t.dump=r,n0(t,r,!1)||n0(t,r,!0);var a=B0.call(t.dump),u=n,c;n&&(n=t.flowLevel<0||t.flowLevel>e);var l=a==="[object Object]"||a==="[object Array]",f,h;if(l&&(f=t.duplicates.indexOf(r),h=f!==-1),(t.tag!==null&&t.tag!=="?"||h||t.indent!==2&&e>0)&&(i=!1),h&&t.usedDuplicates[f])t.dump="*ref_"+f;else{if(l&&h&&!t.usedDuplicates[f]&&(t.usedDuplicates[f]=!0),a==="[object Object]")n&&Object.keys(t.dump).length!==0?(ZR(t,e,t.dump,i),h&&(t.dump="&ref_"+f+t.dump)):(XR(t,e,t.dump),h&&(t.dump="&ref_"+f+" "+t.dump));else if(a==="[object Array]")n&&t.dump.length!==0?(t.noArrayIndent&&!o&&e>0?r0(t,e-1,t.dump,i):r0(t,e,t.dump,i),h&&(t.dump="&ref_"+f+t.dump)):(JR(t,e,t.dump),h&&(t.dump="&ref_"+f+" "+t.dump));else if(a==="[object String]")t.tag!=="?"&&VR(t,t.dump,e,s,u);else{if(a==="[object Undefined]")return!1;if(t.skipInvalid)return!1;throw new Xe("unacceptable kind of an object to dump "+a)}t.tag!==null&&t.tag!=="?"&&(c=encodeURI(t.tag[0]==="!"?t.tag.slice(1):t.tag).replace(/!/g,"%21"),t.tag[0]==="!"?c="!"+c:c.slice(0,18)==="tag:yaml.org,2002:"?c="!!"+c.slice(18):c="!<"+c+">",t.dump=c+" "+t.dump)}return!0}function QR(t,e){var r=[],n=[],i,s;for(Rc(t,r,n),i=0,s=n.length;i<s;i+=1)e.duplicates.push(r[n[i]]);e.usedDuplicates=new Array(s)}function Rc(t,e,r){var n,i,s;if(t!==null&&typeof t=="object")if(i=e.indexOf(t),i!==-1)r.indexOf(i)===-1&&r.push(i);else if(e.push(t),Array.isArray(t))for(i=0,s=t.length;i<s;i+=1)Rc(t[i],e,r);else for(n=Object.keys(t),i=0,s=n.length;i<s;i+=1)Rc(t[n[i]],e,r)}function eF(t,e){e=e||{};var r=new $R(e);r.noRefs||QR(t,r);var n=t;return r.replacer&&(n=r.replacer.call({"":n},"",n)),rr(r,0,n,!0,!0)?r.dump+`
`:""}var tF=eF,rF={dump:tF};function Bc(t,e){return function(){throw new Error("Function yaml."+t+" is removed in js-yaml 4. Use yaml."+e+" instead, which is now safe by default.")}}var nF=Le,iF=o0,sF=l0,oF=m0,aF=g0,uF=Tc,cF=P0.load,lF=P0.loadAll,fF=rF.dump,hF=Xe,dF={binary:D0,float:p0,map:c0,null:f0,pairs:A0,set:x0,timestamp:E0,bool:h0,int:d0,merge:b0,omap:w0,seq:u0,str:a0},pF=Bc("safeLoad","load"),mF=Bc("safeLoadAll","loadAll"),gF=Bc("safeDump","dump"),yF={Type:nF,Schema:iF,FAILSAFE_SCHEMA:sF,JSON_SCHEMA:oF,CORE_SCHEMA:aF,DEFAULT_SCHEMA:uF,load:cF,loadAll:lF,dump:fF,YAMLException:hF,types:dF,safeLoad:pF,safeLoadAll:mF,safeDump:gF},W0=yF;var BE=X(k_());var L={};xv(L,{bold:()=>JP,dimmed:()=>ZP,error:()=>yt,grey:()=>QP,info:()=>KP,item:()=>e2,log:()=>gt,turboBlue:()=>Jn,turboGradient:()=>YP,turboLoader:()=>kf,turboRed:()=>kE,underline:()=>XP,warn:()=>Pf,yellow:()=>PE});var Ir=X(require("chalk")),RE=X(pE()),FE=X(vE()),TE="#0099F7",OE="#F11712",VP="#FFFF00",YP=(0,FE.default)(TE,OE),Jn=Ir.default.hex(TE),kE=Ir.default.hex(OE),PE=Ir.default.hex(VP),kf=t=>(0,RE.default)({text:t,spinner:{frames:["   ",Jn(">  "),Jn(">> "),Jn(">>>")]}}),KP=(...t)=>{gt(Jn.bold(">>>"),...t)},JP=(...t)=>{gt(Ir.default.bold(...t))},XP=(...t)=>{gt(Ir.default.underline(...t))},ZP=(...t)=>{gt(Ir.default.dim(...t))},QP=(...t)=>{gt(Ir.default.grey(...t))},e2=(...t)=>{gt(Jn.bold("  \u2022"),...t)},gt=(...t)=>{console.log(...t)},Pf=(...t)=>{console.error(PE.bold(">>>"),...t)},yt=(...t)=>{console.error(kE.bold(">>>"),...t)};var t2="turbo.json";var Bf={};function r2(t){try{if(ss.default.existsSync(hn.default.join(t,"pnpm-workspace.yaml")))return W0.load(ss.default.readFileSync(hn.default.join(t,"pnpm-workspace.yaml"),"utf8")).packages||[];let e=JSON.parse(ss.default.readFileSync(hn.default.join(t,"package.json"),"utf8"));if(e.workspaces){if("packages"in e.workspaces)return e.workspaces.packages||[];if(Array.isArray(e.workspaces))return e.workspaces}return[]}catch{return[]}}function If(t,e){var s;let r=Wi(t,e),n=[],i=(s=e==null?void 0:e.cache)!=null?s:!0;if(i&&t&&t in Bf)return Bf[t];if(r){let a=r2(r).map(c=>`${c}/turbo.json`);(0,BE.sync)([t2,...a],{cwd:r,onlyFiles:!0,followSymbolicLinks:!1,suppressErrors:!0}).map(c=>hn.default.join(r,c)).forEach(c=>{try{let l=ss.default.readFileSync(c,"utf8"),f=no.parse(l),h=hn.default.dirname(c)===r;if(h){if("extends"in f)return}else if(!("extends"in f))return;n.push({config:f,turboConfigPath:c,workspacePath:hn.default.dirname(c),isRootConfig:h})}catch(l){Pf(l)}})}return i&&t&&(Bf[t]=n),n}var KB=X(require("os")),JB=X(aD());var uD=X(require("fs-extra")),XB=[".DS_Store",".git",".gitattributes",".gitignore",".gitlab-ci.yml",".hg",".hgcheck",".hgignore",".idea",".npmignore",".travis.yml","LICENSE","Thumbs.db","docs","mkdocs.yml","npm-debug.log","yarn-debug.log","yarn-error.log","yarnrc.yml",".yarn"];function ls(t){let e=uD.default.readdirSync(t).filter(r=>!XB.includes(r)).filter(r=>!r.endsWith(".iml"));return{isEmpty:e.length===0,conflicts:e}}var Zf=X(require("path")),Qf=X(require("fs-extra")),eh=X(require("chalk"));function ua(t){let e=Zf.default.resolve(t),r=Zf.default.basename(e),n=Qf.default.existsSync(e),i=Qf.default.lstatSync(e,{throwIfNoEntry:!1});if(i&&!i.isDirectory())return{valid:!1,root:e,projectName:r,error:`${eh.default.dim(r)} is not a directory - please try a different location`};if(n){let{isEmpty:s,conflicts:o}=ls(e);if(!s)return{valid:!1,root:e,projectName:r,error:`${eh.default.dim(r)} (${e}) has ${o.length} conflicting ${o.length===1?"file":"files"} - please try a different location`}}return{valid:!0,root:e,projectName:r}}var TC=require("stream"),OC=require("util"),kC=require("path"),PC=require("os"),js=require("fs"),Yp=X(sx()),$u=X(FC()),IM=(0,OC.promisify)(TC.Stream.pipeline);async function ju(t){try{return(await $u.default.head(t)).statusCode===200}catch{return!1}}async function Kp(t,e){let[,r,n,i,s,...o]=t.pathname.split("/"),a=e?e.replace(/^\//,""):o.join("/");if(i===void 0||i===""&&s===void 0)try{let c=await(0,$u.default)(`https://api.github.com/repos/${r}/${n}`),l=JSON.parse(c.body);return{username:r,name:n,branch:l.default_branch,filePath:a}}catch{return}let u=e?`${s}/${o.join("/")}`.replace(new RegExp(`/${a}|/$`),""):s;if(r&&n&&u&&i==="tree")return{username:r,name:n,branch:u,filePath:a}}function Jp({username:t,name:e,branch:r,filePath:n}){let i=`https://api.github.com/repos/${t}/${e}/contents`,s=`${n?`/${n}`:""}/package.json`;return ju(`${i+s}?ref=${r}`)}function Xp(t){try{let e=new URL(t);return ju(e.href)}catch{return ju(`https://api.github.com/repos/vercel/turbo/contents/examples/${encodeURIComponent(t)}`)}}async function BC(t,e){let r=(0,kC.join)((0,PC.tmpdir)(),`${e}.temp-${Date.now()}`);return await IM($u.default.stream(t),(0,js.createWriteStream)(r)),r}async function Zp(t,{username:e,name:r,branch:n,filePath:i}){let s=await BC(`https://codeload.github.com/${e}/${r}/tar.gz/${n}`,"turbo-ct-example");await(0,Yp.x)({file:s,cwd:t,strip:i?i.split("/").length+1:1,filter:o=>o.startsWith(`${r}-${n.replace(/\//g,"-")}${i?`/${i}/`:"/"}`)}),await js.promises.unlink(s)}async function Qp(t,e){let r=await BC("https://codeload.github.com/vercel/turbo/tar.gz/main","turbo-ct-example");await(0,Yp.x)({file:r,cwd:t,strip:2+e.split("/").length,filter:n=>n.includes(`turbo-main/examples/${e}/`)}),await js.promises.unlink(r)}var Uu=require("fs-extra");async function em(t){try{return await(0,Uu.access)(t,Uu.constants.W_OK),!0}catch{return!1}}var Li=X(require("path")),tm=X(jC()),wr=X(require("chalk")),Ni=require("fs-extra");function qM(t){return typeof t=="object"&&t!==null&&typeof t.message=="string"}var Gu=class extends Error{};async function rm({appPath:t,example:e,isDefaultExample:r,examplePath:n}){let i,s;if(r)i={username:"vercel",name:"turbo",branch:"main",filePath:"examples/basic"};else{try{s=new URL(e)}catch(p){p.code!=="ERR_INVALID_URL"&&(yt(p),process.exit(1))}s?(s.origin!=="https://github.com"&&(yt(`Invalid URL: ${wr.default.red(`"${e}"`)}. Only GitHub repositories are supported. Please use a GitHub URL and try again.`),process.exit(1)),i=await Kp(s,n),i||(yt(`Unable to fetch repository information from: ${wr.default.red(`"${e}"`)}. Please fix the URL and try again.`),process.exit(1)),await Jp(i)||(yt(`Could not locate the repository for ${wr.default.red(`"${e}"`)}. Please check that the repository exists and try again.`),process.exit(1))):await Xp(e)||(yt(`Could not locate an example named ${wr.default.red(`"${e}"`)}. It could be due to the following:
`,`1. Your spelling of example ${wr.default.red(`"${e}"`)} might be incorrect.
`,"2. You might not be connected to the internet or you are behind a proxy."),process.exit(1))}let o=Li.default.resolve(t);await em(Li.default.dirname(o))||(yt("The application path is not writable, please check folder permissions and try again."),yt("It is likely you do not have write permissions for this folder."),process.exit(1));let a=Li.default.basename(o);try{await(0,Ni.mkdir)(o,{recursive:!0})}catch(p){yt("Unable to create project directory"),yt(p),process.exit(1)}let{isEmpty:u,conflicts:c}=ls(o);u||(yt(`${wr.default.dim(o)} has ${c.length} conflicting ${c.length===1?"file":"files"} - please try a different location`),process.exit(1));let l=process.cwd();process.chdir(o);let f=kf("Downloading files...");try{!r&&i?(gt(`
Downloading files from repo ${wr.default.cyan(e)}. This might take a moment.`),gt(),f.start(),await(0,tm.default)(()=>Zp(o,i),{retries:3})):(gt(`
Downloading files${r?"":` for example ${wr.default.cyan(e)}`}. This might take a moment.`),gt(),f.start(),await(0,tm.default)(()=>Qp(o,e),{retries:3}))}catch(p){throw new Gu(qM(p)?p.message:String(p))}finally{f.stop()}let h=Li.default.join(o,"package.json"),m=(0,Ni.existsSync)(h),_=[];if(m){let p;try{p=(0,Ni.readJsonSync)(h)}catch{}p&&_.push(...Object.keys(p.scripts||{}))}let y=t;return Li.default.join(l,a)===t&&(y=a),{cdPath:y,hasPackageJson:m,availableScripts:_,repoInfo:i}}function nm(t,e={to:"camel"}){switch(e.to){case"camel":return t.replace(/(?:[-_][a-z])/g,r=>r.toUpperCase().replace("-","").replace("_",""));default:throw new Error("Not implemented")}}var yv=require("proxy-agent");var $s={name:"@turbo/gen",version:"1.13.4",description:"Extend a Turborepo",homepage:"https://turbo.build/repo",license:"MPL-2.0",repository:{type:"git",url:"https://github.com/vercel/turbo",directory:"packages/turbo-gen"},bugs:{url:"https://github.com/vercel/turbo/issues"},bin:"dist/cli.js",types:"dist/types.d.ts",scripts:{build:"tsup",test:"jest",lint:"eslint src/","check-types":"tsc --noEmit"},dependencies:{"@turbo/workspaces":"workspace:*",chalk:"2.4.2",commander:"^10.0.0","fs-extra":"^10.1.0",inquirer:"^8.2.4",minimatch:"^9.0.0","node-plop":"^0.26.3","proxy-agent":"^6.2.2","ts-node":"^10.9.1","update-check":"^1.5.4","validate-npm-package-name":"^5.0.0"},devDependencies:{"@turbo/eslint-config":"workspace:*","@turbo/test-utils":"workspace:*","@turbo/tsconfig":"workspace:*","@turbo/utils":"workspace:*","@types/fs-extra":"^9.0.13","@types/inquirer":"^8.2.5","@types/jest":"^27.4.0","@types/node":"^18.17.2","@types/validate-npm-package-name":"^4.0.0",jest:"^27.4.3","ts-jest":"^27.1.1",tsup:"^6.7.0",typescript:"5.3.3"},files:["dist"],publishConfig:{access:"public"}};var $C=X(require("chalk")),UC=X(require("update-check"));var jM=(0,UC.default)($s).catch(()=>null);async function im(){try{let t=await jM;t!=null&&t.latest&&(L.log(),L.log($C.default.yellow.bold(`A new version of \`${$s.name}\` is available!`)),L.log()),process.exit()}catch{}}var GC=require("@turbo/workspaces");async function Wu({root:t}){let e=t||process.cwd(),r=Wi(e);if(!r)L.error("Unable to infer repository root - override with --root");else try{return(0,GC.getWorkspaceDetails)({root:r})}catch{L.error(`Unable to determine workspace details. Make sure "${e}" is the root, or add "packageManager" to "package.json" or ensure a lockfile is present.`)}process.exit(1)}var Ar=X(require("path")),Vu=X(require("fs-extra")),zu=X(require("node-plop")),WC=require("ts-node"),zC=require("inquirer");var Me=class extends Error{constructor(r,n){var i;super(r);this.name="GenerateError",this.type=(i=n==null?void 0:n.type)!=null?i:"unknown",Error.captureStackTrace(this,Me)}};var VC=["ts","js","cjs"],$M=Ar.default.join("turbo","generators"),YC=VC.map(t=>Ar.default.join($M,`config.${t}`)),UM=[...YC,...VC.map(t=>Ar.default.join(`plopfile.${t}`))];function KC({project:t,configPath:e}){(0,WC.register)({transpileOnly:!0,cwd:t.paths.root,compilerOptions:{module:"nodenext",moduleResolution:"nodenext"}});let r=WM({project:t}),n;if(e){if(!Vu.default.existsSync(e))throw new Me(`No config at "${e}"`,{type:"plop_no_config"});try{n=(0,zu.default)(e,{destBasePath:e,force:!1})}catch(i){L.error(i)}}else{for(let i of UM){let s=Ar.default.join(t.paths.root,i);if(Vu.default.existsSync(s))try{n=(0,zu.default)(s,{destBasePath:t.paths.root,force:!1});break}catch(o){L.error(o)}}!n&&r.length>0&&(n=(0,zu.default)(r[0].config,{destBasePath:r[0].root,force:!1}),r.shift())}return n&&r.forEach(i=>{try{n==null||n.load(i.config,{destBasePath:i.root,force:!1})}catch(s){L.error(s)}}),n}function sm({project:t,configPath:e}){let r=KC({project:t,configPath:e});if(!r)return[];let i=r.getGeneratorList().map(a=>r.getGenerator(a.name)),s={};i.forEach(a=>{let u=a,c=t.workspaceData.workspaces.find(l=>{if(u.basePath===t.paths.root)return!1;let f=u.basePath.split(Ar.default.sep);return f.pop(),f.pop(),Ar.default.join("/",...f)===l.paths.root});c?(c.name in s||(s[c.name]=[]),s[c.name].push(u)):("root"in s||(s.root=[]),s.root.push(u))});let o=[];return Object.keys(s).forEach(a=>{o.push(new zC.Separator(a)),o.push(...s[a])}),o}function GM({project:t,generator:e}){let r={cwd:process.cwd(),root:t.paths.root,workspace:e.basePath?Gi({cwd:e.basePath,target:"package.json"}):void 0},n={};try{n=If(e.basePath)}catch{}return{turbo:{paths:r,configs:n}}}function WM({project:t}){let e=[];return t.workspaceData.workspaces.forEach(r=>{for(let n of YC)Vu.default.existsSync(Ar.default.join(r.paths.root,n))&&e.push({config:Ar.default.join(r.paths.root,n),root:r.paths.root})}),e}async function JC({project:t,generator:e,bypassArgs:r,configPath:n}){let i=KC({project:t,configPath:n});if(!i)throw new Me("Unable to load generators",{type:"plop_unable_to_load_config"});let s=i.getGenerator(e);if(!s)throw new Me(`Generator ${e} not found`,{type:"plop_generator_not_found"});let o=await s.runPrompts(r),a=await s.runActions({...o,...GM({project:t,generator:s})},{onComment:u=>{L.dimmed(u)}});if(a.failures.length>0)throw a.failures.forEach(u=>{u instanceof Error?L.error(`Error - ${u.message}`):L.error(`Error - ${u.error}. Unable to ${u.type} to "${u.path}"`)}),new Me(`Failed to run "${e}" generator`,{type:"plop_error_running_generator"});a.changes.length>0&&(L.info("Changes made:"),a.changes.forEach(u=>{u.path&&L.item(`${u.path} (${u.type})`)}))}var Rn=require("inquirer");async function XC({generators:t,generator:e}){if(e){if(t.find(n=>!(n instanceof Rn.Separator)&&n.name===e))return{selectedGenerator:e};L.warn(`Generator "${e}" not found`),L.log()}return await(0,Rn.prompt)({type:"list",name:"selectedGenerator",message:"Select generator to run",choices:t.map(n=>n instanceof Rn.Separator?n:{name:n.description?`  ${n.name}: ${n.description}`:`  ${n.name}`,value:n.name})})}async function ZC(){return(0,Rn.prompt)({type:"list",name:"answer",message:"Should the generator config be created with TS or JS?",default:"ts",choices:[{name:"JavaScript",value:"js"},{name:"TypeScript",value:"ts"}]})}async function QC({message:t}){return(0,Rn.prompt)({type:"confirm",name:"answer",message:t})}var om=X(require("path")),Yu=require("fs-extra");async function ev({project:t,template:e}){let r=om.default.join(t.paths.root,"turbo","generators"),n=`simple-${e}`;if(await(0,Yu.pathExists)(r))throw new Me(`Generator config directory already exists at ${r}`,{type:"config_directory_already_exists"});await(0,Yu.copy)(om.default.join(__dirname,"templates",n),r,{recursive:!0})}async function am({generator:t,project:e,opts:r}){let n=!1,i=sm({project:e,configPath:r.config});if(!i.length){L.error("No generators found."),L.log();let{answer:o}=await QC({message:`Would you like to add a config with a sample custom generator to ${e.name}?`});if(o){n=!0;let{answer:a}=await ZC();try{await ev({project:e,template:a})}catch(u){throw u instanceof Me||L.error("Failed to create generator config"),u}if(L.log(),L.info("Generator config successfully created!"),L.info("Loading generator config..."),L.log(),i=sm({project:e,configPath:r.config}),!i.length){L.error("Error loading generator");return}}else{L.log(),L.dimmed("Learn more about custom Turborepo generators - https://turbo.build/repo/docs/core-concepts/monorepos/code-generation#custom-generators");return}}let{selectedGenerator:s}=await XC({generators:i,generator:t});try{await JC({project:e,generator:s,bypassArgs:r.args,configPath:r.config})}catch(o){if(o instanceof Me)throw o;let a="Failed to run generator";throw o instanceof Error&&(a=o.message),new Me(a,{type:"plop_error_running_generator"})}finally{n&&(L.log(),L.info("Congrats! You just ran your first Turborepo generator"),L.dimmed("Learn more about custom Turborepo generators - https://turbo.build/repo/docs/core-concepts/monorepos/code-generation#custom-generators"))}L.log(),L.bold(L.turboGradient(">>> Success!"))}var fm=X(require("path")),Gs=require("fs-extra"),fv=X(require("chalk"));var Us=X(require("path")),iv=require("fs-extra"),vt=require("inquirer"),sv=require("minimatch"),lm=X(require("validate-npm-package-name"));var um=X(require("path"));var tv=X(require("path"));function rv({project:t}){let r=t.workspaceData.workspaces.map(i=>tv.default.relative(t.paths.root,i.paths.root)),n=new Set;return t.workspaceData.globs.forEach(i=>{if(!r.includes(i)){if(!i.startsWith("!")){let o=i.split("/")[0];n.add(o)}}}),Array.from(n)}function cm({project:t,workspace:e}){return um.default.relative(t.paths.root,e.paths.root).split(um.default.sep)[0]}function Ku({project:t}){let e=rv({project:t}),r=e.includes("apps"),n=e.includes("packages"),i={},s=[];return t.workspaceData.workspaces.forEach(o=>{let a=cm({project:t,workspace:o});a!=="apps"&&s.push(o),a in i||(i[a]=[]),i[a].push(o)}),{hasRootApps:r,hasRootPackages:n,workspacesByGroup:i,nonAppWorkspaces:s}}var nv=require("inquirer");function Ju({project:t,type:e,showAllDependencies:r}){let n=Ku({project:t}),i=[],s=t.workspaceData.workspaces;r||(e==="app"&&n.hasRootApps?s=n.workspacesByGroup.apps:e==="package"&&n.nonAppWorkspaces.length>0&&(s=n.nonAppWorkspaces));let o;return s.forEach(a=>{let u=cm({project:t,workspace:a});u!==o&&i.push(new nv.Separator(u)),o=u,i.push(a)}),i}async function ov({override:t,suggestion:e,workspaceType:r}){let{validForNewPackages:n}=(0,lm.default)(t||"");return t&&n?{answer:t}:(0,vt.prompt)({type:"input",name:"answer",default:e,validate:i=>{let{validForNewPackages:s}=(0,lm.default)(i);return s||`Invalid ${r} name`},message:`What is the name of the ${r}?`})}async function av({override:t,message:e}){return t?{answer:t}:(0,vt.prompt)({type:"list",name:"answer",message:e!=null?e:"What type of workspace should be added?",choices:[{name:"app",value:"app"},{name:"package",value:"package"}]})}async function uv({workspaceType:t,workspaceName:e,destination:r,project:n}){let i=e.includes("/")?e.split("/")[1]:e;if(r){let{valid:u,root:c}=ua(r);if(u)return{absolute:c,relative:Us.default.relative(n.paths.root,c)}}let s,o=Ku({project:n});t==="app"&&o.hasRootApps?s=`${n.paths.root}/apps/${i}`:t==="package"&&o.hasRootPackages&&(s=`${n.paths.root}/packages/${i}`);let{answer:a}=await(0,vt.prompt)({type:"input",name:"answer",message:`Where should "${e}" be added?`,default:s?Us.default.relative(n.paths.root,s):void 0,validate:u=>{let c=Us.default.join(n.paths.root,u),{valid:l,error:f}=ua(c),h=n.workspaceData.globs.some(m=>(0,sv.minimatch)(u,m));return l&&h?!0:h?f:`${u} is not a valid workspace location`}});return{absolute:Us.default.join(n.paths.root,a),relative:a}}async function cv({override:t,workspaces:e,workspaceName:r}){if(t){let i=e.find(s=>s instanceof vt.Separator?!1:s.name===t);if(i)return{answer:i};L.warn(`Workspace "${t}" not found`),L.log()}return await(0,vt.prompt)({type:"list",name:"answer",loop:!1,pageSize:25,message:`Which workspace should "${r}" start from?`,choices:e.map(i=>i instanceof vt.Separator?i:{name:`  ${i.name}`,value:i})})}async function lv({workspaceName:t,project:e,workspaceSource:r,showAllDependencies:n}){let i={dependencies:{},devDependencies:{},peerDependencies:{},optionalDependencies:{}},{answer:s}=await VM({message:`Add workspace dependencies to "${t}"?`});if(!s)return i;let{answer:o}=await(0,vt.prompt)({type:"checkbox",name:"answer",message:`Select all dependencies types to modify for "${t}"`,loop:!1,choices:[{name:"dependencies",value:"dependencies"},{name:"devDependencies",value:"devDependencies"},{name:"peerDependencies",value:"peerDependencies"},{name:"optionalDependencies",value:"optionalDependencies"}]}),a=Ju({project:e,type:"package",showAllDependencies:n}),u=r?(0,iv.readJsonSync)(r.paths.packageJson):void 0;for(let c of o){let{answer:l}=await(0,vt.prompt)({type:"checkbox",name:"answer",default:u&&Object.keys(u[c]||{}),pageSize:15,message:`Which packages should be added as ${c} to "${t}?`,loop:!1,choices:a.map(h=>h instanceof vt.Separator?h:{name:`  ${h.name}`,value:h.name})}),f=(u==null?void 0:u[c])||{};if(Object.keys(f).length){let h=new Set(Object.keys(f));l.forEach(m=>{h.has(m)||(f[m]=e.packageManager==="pnpm"?"workspace:*":"*")}),i[c]=f}else i[c]=l.reduce((h,m)=>({...h,[m]:e.packageManager==="pnpm"?"workspace:*":"*"}),{})}return i}async function VM({message:t}){return(0,vt.prompt)({type:"confirm",name:"answer",message:t})}async function Xu({project:t,opts:e}){var u;let r,n=e.method==="copy"&&e.copy.type==="external"?((u=e.examplePath)==null?void 0:u.split("/").pop())||e.copy.source.split("/").pop():void 0,{answer:i}=await av({override:e.type,message:e.method==="copy"&&e.copy.source==="external"&&n?`What type of workspace should "${n}" be created as?`:void 0}),{answer:s}=await ov({override:e.name,workspaceType:i,suggestion:n});if(e.method==="copy"&&e.copy.type==="internal"){let{answer:c}=await cv({override:e.copy.source,workspaces:Ju({project:t,type:i}),workspaceName:s});r=c}let o=await uv({workspaceType:i,workspaceName:s,project:t,destination:e.destination}),a=await lv({workspaceName:s,project:t,workspaceSource:r,showAllDependencies:e.showAllDependencies});return{type:i,name:s,location:o,source:r,dependencies:a}}async function hm({project:t,opts:e}){let{name:r,location:n,dependencies:i}=await Xu({project:t,opts:e}),s={name:r,version:"0.0.0",private:!0,scripts:{dev:"echo 'Add dev script here'",build:"echo 'Add build script here'",test:"echo 'Add test script here'",lint:"echo 'Add lint script here'"}};Object.keys(i).forEach(o=>{let a=i[o];a&&Object.keys(a).length>0&&(s[o]=a)}),(0,Gs.mkdirSync)(n.absolute,{recursive:!0}),(0,Gs.writeFileSync)(fm.default.join(n.absolute,"package.json"),JSON.stringify(s,null,2)),(0,Gs.writeFileSync)(fm.default.join(n.absolute,"README.md"),`# \`${r}\``),L.log(),L.log(`${fv.default.bold(L.turboGradient(">>> Success!"))} Created ${r} at "${n.relative}"`)}var dm=X(require("path")),Rt=require("fs-extra"),hv=X(require("chalk"));async function pm({project:t,opts:e}){let{name:r,type:n,location:i,source:s,dependencies:o}=await Xu({project:t,opts:e}),a=dm.default.join(i.absolute,"package.json");if(e.copy.type==="external"){L.log(),L.warn("Some manual modifications may be required."),L.dimmed(`This ${n} may require local dependencies or a different package manager than what is available in this repo`),await rm({appPath:i.absolute,example:e.copy.source,examplePath:e.examplePath});try{if((0,Rt.existsSync)(a)){if((await(0,Rt.readJSON)(a)).workspaces)throw new Error("New workspace root detected - unexpected 'workspaces' field in package.json")}else throw new Error("New workspace is missing a package.json file");if((0,Rt.existsSync)(dm.default.join(i.absolute,"pnpm-workspace.yaml")))throw new Error("New workspace root detected - unexpected pnpm-workspace.yaml")}catch(c){let l="UNKNOWN_ERROR";c instanceof Error&&(l=c.message),L.error(l),await(0,Rt.rm)(i.absolute,{recursive:!0,force:!0});return}}else if(s){let c=async f=>Promise.resolve(!f.includes("node_modules")),l=L.turboLoader(`Creating "${r}" from "${s.name}"...`);l.start(),await(0,Rt.copy)(s.paths.root,i.absolute,{filter:c}),l.stop()}let u=await(0,Rt.readJSON)(a);u.name=r,Object.keys(o).forEach(c=>{let l=o[c];l&&Object.keys(l).length>0&&(u[c]=l)}),await(0,Rt.writeJSON)(a,u,{spaces:2}),L.log(),L.log(`${hv.default.bold(L.turboGradient(">>> Success!"))} Created ${r} at "${i.relative}"`)}function KM(t){let{copy:e,...r}=t,n=e===!0||typeof e=="string"?"copy":"empty",i=typeof e=="string"?e:"",s=typeof e=="string"&&e.startsWith("https://")?"external":"internal";return{method:n,copy:{type:s,source:i},...r}}async function Ws(t){let e=await Wu(t),r=KM(t);L.log();let n={project:e,opts:r};r.method==="copy"?(r.copy.type==="external"?L.info(`Copy a remote workspace from ${r.copy.source}`):L.info(`Copy an existing workspace from "${e.name}"`),L.log(),await pm(n)):(L.info(`Add an empty workspace to "${e.name}"`),L.log(),await hm(n))}async function zs(t,e){let r=await Wu(e);L.log(),L.info(`Modify "${r.name}" using custom generators`),L.log(),await am({generator:t,project:r,opts:e})}function dv(t){try{let e=JSON.parse(t),r={};for(let n in e){let i=nm(n,{to:"camel"});r[i]=e[n]}return r}catch(e){L.error("Error parsing arguments",e),process.exit(1)}}async function mm(t,e){var r;if(t==="workspace"){let n=dv(e.json);n.showAllDependencies=(r=n.showAllDependencies)!=null?r:!1;let i=!1,s=n.empty||!0;n.copy===""||n.copy===!0?(i=!0,s=!1):n.copy&&n.copy.length>0&&(i=n.copy,s=!1),n.copy=i,n.empty=s,await Ws(n)}else if(t==="run"){let n=dv(e.json),{generatorName:i,...s}=n;await zs(i,s)}else L.error(`Received unknown command - "${t}" (must be one of "workspace" | "run")`),process.exit(1)}var _v=new yv.ProxyAgent;pv.default.globalAgent=_v;mv.default.globalAgent=_v;var Vs=new qe.Command;Vs.name(gv.default.bold(L.turboGradient("@turbo/gen"))).description("Extend your Turborepo").version($s.version,"-v, --version","Output the current version").helpOption("-h, --help","Display help for command").showHelpAfterError(!1);Vs.command("run",{isDefault:!0}).alias("r").description("Run custom generators").addArgument(new qe.Argument("[generator-name]","The name of the generator to run")).addOption(new qe.Option("-c, --config <config>","Generator configuration file (default: turbo/generators/config.js")).addOption(new qe.Option("-r, --root <dir>","The root of your repository (default: directory with root turbo.json)")).addOption(new qe.Option("-a, --args <args...>","Arguments passed directly to generator").default([])).action(zs);Vs.command("workspace").aliases(["w"]).description("Add a new package or app to your project").addOption(new qe.Option("-n, --name <workspace-name>","Name for the new workspace")).addOption(new qe.Option("-b, --empty","Generate an empty workspace").conflicts("copy").default(!0)).addOption(new qe.Option("-c, --copy [source]",`Generate a workspace using an existing workspace as a template. Can be the name of a local workspace
      within your monorepo, or a fully qualified GitHub URL with any branch and/or subdirectory.
      `).conflicts("empty")).addOption(new qe.Option("-d, --destination <dir>","Where the new workspace should be created")).addOption(new qe.Option("-t, --type <type>","The type of workspace to create").choices(["app","package"])).addOption(new qe.Option("-r, --root <dir>","The root of your repository (default: directory with root turbo.json)")).addOption(new qe.Option("-p, --example-path <path-to-example>",`In a rare case, your GitHub URL might contain a branch name with
a slash (e.g. bug/fix-1) and the path to the example (e.g. foo/bar).
In this case, you must specify the path to the example separately:
--example-path foo/bar
`).implies({copy:!0})).addOption(new qe.Option("--show-all-dependencies","Do not filter available dependencies by the workspace type").default(!1)).action(Ws);Vs.command("raw",{hidden:!0}).argument("<type>","The type of generator to run").addOption(new qe.Option("--json <arguments>","Arguments as raw JSON")).action(mm);Vs.parseAsync().then(im).catch(async t=>{L.log(),t instanceof Me?L.error(t.message):(L.error("Unexpected error. Please report it as a bug:"),L.log(t)),L.log(),await im(),process.exit(1)});
/*! Bundled license information:

@babel/runtime/helpers/regeneratorRuntime.js:
  (*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE *)

is-extglob/index.js:
  (*!
   * is-extglob <https://github.com/jonschlinkert/is-extglob>
   *
   * Copyright (c) 2014-2016, Jon Schlinkert.
   * Licensed under the MIT License.
   *)

is-glob/index.js:
  (*!
   * is-glob <https://github.com/jonschlinkert/is-glob>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)

is-number/index.js:
  (*!
   * is-number <https://github.com/jonschlinkert/is-number>
   *
   * Copyright (c) 2014-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

to-regex-range/index.js:
  (*!
   * to-regex-range <https://github.com/micromatch/to-regex-range>
   *
   * Copyright (c) 2015-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

fill-range/index.js:
  (*!
   * fill-range <https://github.com/jonschlinkert/fill-range>
   *
   * Copyright (c) 2014-present, Jon Schlinkert.
   * Licensed under the MIT License.
   *)

queue-microtask/index.js:
  (*! queue-microtask. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)

run-parallel/index.js:
  (*! run-parallel. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/
