{"name": "@turbo/gen", "version": "1.13.4", "description": "Extend a Turborepo", "homepage": "https://turbo.build/repo", "license": "MPL-2.0", "repository": {"type": "git", "url": "https://github.com/vercel/turbo", "directory": "packages/turbo-gen"}, "bugs": {"url": "https://github.com/vercel/turbo/issues"}, "bin": "dist/cli.js", "types": "dist/types.d.ts", "dependencies": {"chalk": "2.4.2", "commander": "^10.0.0", "fs-extra": "^10.1.0", "inquirer": "^8.2.4", "minimatch": "^9.0.0", "node-plop": "^0.26.3", "proxy-agent": "^6.2.2", "ts-node": "^10.9.1", "update-check": "^1.5.4", "validate-npm-package-name": "^5.0.0", "@turbo/workspaces": "1.13.4"}, "devDependencies": {"@types/fs-extra": "^9.0.13", "@types/inquirer": "^8.2.5", "@types/jest": "^27.4.0", "@types/node": "^18.17.2", "@types/validate-npm-package-name": "^4.0.0", "jest": "^27.4.3", "ts-jest": "^27.1.1", "tsup": "^6.7.0", "typescript": "5.3.3", "@turbo/eslint-config": "0.0.0", "@turbo/test-utils": "0.0.0", "@turbo/utils": "0.0.0", "@turbo/tsconfig": "0.0.0"}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "test": "jest", "lint": "eslint src/", "check-types": "tsc --noEmit"}}