var il=Object.create;var Ks=Object.defineProperty;var nl=Object.getOwnPropertyDescriptor;var ol=Object.getOwnPropertyNames;var ul=Object.getPrototypeOf,al=Object.prototype.hasOwnProperty;var A=(r=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(r,{get:(e,t)=>(typeof require!="undefined"?require:e)[t]}):r)(function(r){if(typeof require!="undefined")return require.apply(this,arguments);throw new Error('Dynamic require of "'+r+'" is not supported')});var cl=(r,e)=>()=>(r&&(e=r(r=0)),e);var y=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ll=(r,e)=>{for(var t in e)Ks(r,t,{get:e[t],enumerable:!0})},hl=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of ol(e))!al.call(r,i)&&i!==t&&Ks(r,i,{get:()=>e[i],enumerable:!(s=nl(e,i))||s.enumerable});return r};var Br=(r,e,t)=>(t=r!=null?il(ul(r)):{},hl(e||!r||!r.__esModule?Ks(t,"default",{value:r,enumerable:!0}):t,r));var u=cl(()=>{});var fo=y((Lm,we)=>{u();function Zs(r){return we.exports=Zs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},we.exports.__esModule=!0,we.exports.default=we.exports,Zs(r)}we.exports=Zs,we.exports.__esModule=!0,we.exports.default=we.exports});var Do=y((Nm,Fe)=>{u();var po=fo().default;function mo(){"use strict";Fe.exports=mo=function(){return e},Fe.exports.__esModule=!0,Fe.exports.default=Fe.exports;var r,e={},t=Object.prototype,s=t.hasOwnProperty,i=Object.defineProperty||function(E,f,D){E[f]=D.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(E,f,D){return Object.defineProperty(E,f,{value:D,enumerable:!0,configurable:!0,writable:!0}),E[f]}try{c({},"")}catch{c=function(D,C,b){return D[C]=b}}function h(E,f,D,C){var b=f&&f.prototype instanceof O?f:O,F=Object.create(b.prototype),L=new Ys(C||[]);return i(F,"_invoke",{value:rl(E,D,L)}),F}function d(E,f,D){try{return{type:"normal",arg:E.call(f,D)}}catch(C){return{type:"throw",arg:C}}}e.wrap=h;var g="suspendedStart",w="suspendedYield",S="executing",P="completed",k={};function O(){}function ot(){}function me(){}var Gs={};c(Gs,o,function(){return this});var Hs=Object.getPrototypeOf,_r=Hs&&Hs(Hs(Vs([])));_r&&_r!==t&&s.call(_r,o)&&(Gs=_r);var kt=me.prototype=O.prototype=Object.create(Gs);function so(E){["next","throw","return"].forEach(function(f){c(E,f,function(D){return this._invoke(f,D)})})}function Rr(E,f){function D(b,F,L,H){var J=d(E[b],E,F);if(J.type!=="throw"){var ut=J.arg,qe=ut.value;return qe&&po(qe)=="object"&&s.call(qe,"__await")?f.resolve(qe.__await).then(function(at){D("next",at,L,H)},function(at){D("throw",at,L,H)}):f.resolve(qe).then(function(at){ut.value=at,L(ut)},function(at){return D("throw",at,L,H)})}H(J.arg)}var C;i(this,"_invoke",{value:function(F,L){function H(){return new f(function(J,ut){D(F,L,J,ut)})}return C=C?C.then(H,H):H()}})}function rl(E,f,D){var C=g;return function(b,F){if(C===S)throw new Error("Generator is already running");if(C===P){if(b==="throw")throw F;return{value:r,done:!0}}for(D.method=b,D.arg=F;;){var L=D.delegate;if(L){var H=io(L,D);if(H){if(H===k)continue;return H}}if(D.method==="next")D.sent=D._sent=D.arg;else if(D.method==="throw"){if(C===g)throw C=P,D.arg;D.dispatchException(D.arg)}else D.method==="return"&&D.abrupt("return",D.arg);C=S;var J=d(E,f,D);if(J.type==="normal"){if(C=D.done?P:w,J.arg===k)continue;return{value:J.arg,done:D.done}}J.type==="throw"&&(C=P,D.method="throw",D.arg=J.arg)}}}function io(E,f){var D=f.method,C=E.iterator[D];if(C===r)return f.delegate=null,D==="throw"&&E.iterator.return&&(f.method="return",f.arg=r,io(E,f),f.method==="throw")||D!=="return"&&(f.method="throw",f.arg=new TypeError("The iterator does not provide a '"+D+"' method")),k;var b=d(C,E.iterator,f.arg);if(b.type==="throw")return f.method="throw",f.arg=b.arg,f.delegate=null,k;var F=b.arg;return F?F.done?(f[E.resultName]=F.value,f.next=E.nextLoc,f.method!=="return"&&(f.method="next",f.arg=r),f.delegate=null,k):F:(f.method="throw",f.arg=new TypeError("iterator result is not an object"),f.delegate=null,k)}function sl(E){var f={tryLoc:E[0]};1 in E&&(f.catchLoc=E[1]),2 in E&&(f.finallyLoc=E[2],f.afterLoc=E[3]),this.tryEntries.push(f)}function Js(E){var f=E.completion||{};f.type="normal",delete f.arg,E.completion=f}function Ys(E){this.tryEntries=[{tryLoc:"root"}],E.forEach(sl,this),this.reset(!0)}function Vs(E){if(E||E===""){var f=E[o];if(f)return f.call(E);if(typeof E.next=="function")return E;if(!isNaN(E.length)){var D=-1,C=function b(){for(;++D<E.length;)if(s.call(E,D))return b.value=E[D],b.done=!1,b;return b.value=r,b.done=!0,b};return C.next=C}}throw new TypeError(po(E)+" is not iterable")}return ot.prototype=me,i(kt,"constructor",{value:me,configurable:!0}),i(me,"constructor",{value:ot,configurable:!0}),ot.displayName=c(me,l,"GeneratorFunction"),e.isGeneratorFunction=function(E){var f=typeof E=="function"&&E.constructor;return!!f&&(f===ot||(f.displayName||f.name)==="GeneratorFunction")},e.mark=function(E){return Object.setPrototypeOf?Object.setPrototypeOf(E,me):(E.__proto__=me,c(E,l,"GeneratorFunction")),E.prototype=Object.create(kt),E},e.awrap=function(E){return{__await:E}},so(Rr.prototype),c(Rr.prototype,a,function(){return this}),e.AsyncIterator=Rr,e.async=function(E,f,D,C,b){b===void 0&&(b=Promise);var F=new Rr(h(E,f,D,C),b);return e.isGeneratorFunction(f)?F:F.next().then(function(L){return L.done?L.value:F.next()})},so(kt),c(kt,l,"Generator"),c(kt,o,function(){return this}),c(kt,"toString",function(){return"[object Generator]"}),e.keys=function(E){var f=Object(E),D=[];for(var C in f)D.push(C);return D.reverse(),function b(){for(;D.length;){var F=D.pop();if(F in f)return b.value=F,b.done=!1,b}return b.done=!0,b}},e.values=Vs,Ys.prototype={constructor:Ys,reset:function(f){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(Js),!f)for(var D in this)D.charAt(0)==="t"&&s.call(this,D)&&!isNaN(+D.slice(1))&&(this[D]=r)},stop:function(){this.done=!0;var f=this.tryEntries[0].completion;if(f.type==="throw")throw f.arg;return this.rval},dispatchException:function(f){if(this.done)throw f;var D=this;function C(ut,qe){return L.type="throw",L.arg=f,D.next=ut,qe&&(D.method="next",D.arg=r),!!qe}for(var b=this.tryEntries.length-1;b>=0;--b){var F=this.tryEntries[b],L=F.completion;if(F.tryLoc==="root")return C("end");if(F.tryLoc<=this.prev){var H=s.call(F,"catchLoc"),J=s.call(F,"finallyLoc");if(H&&J){if(this.prev<F.catchLoc)return C(F.catchLoc,!0);if(this.prev<F.finallyLoc)return C(F.finallyLoc)}else if(H){if(this.prev<F.catchLoc)return C(F.catchLoc,!0)}else{if(!J)throw new Error("try statement without catch or finally");if(this.prev<F.finallyLoc)return C(F.finallyLoc)}}}},abrupt:function(f,D){for(var C=this.tryEntries.length-1;C>=0;--C){var b=this.tryEntries[C];if(b.tryLoc<=this.prev&&s.call(b,"finallyLoc")&&this.prev<b.finallyLoc){var F=b;break}}F&&(f==="break"||f==="continue")&&F.tryLoc<=D&&D<=F.finallyLoc&&(F=null);var L=F?F.completion:{};return L.type=f,L.arg=D,F?(this.method="next",this.next=F.finallyLoc,k):this.complete(L)},complete:function(f,D){if(f.type==="throw")throw f.arg;return f.type==="break"||f.type==="continue"?this.next=f.arg:f.type==="return"?(this.rval=this.arg=f.arg,this.method="return",this.next="end"):f.type==="normal"&&D&&(this.next=D),k},finish:function(f){for(var D=this.tryEntries.length-1;D>=0;--D){var C=this.tryEntries[D];if(C.finallyLoc===f)return this.complete(C.completion,C.afterLoc),Js(C),k}},catch:function(f){for(var D=this.tryEntries.length-1;D>=0;--D){var C=this.tryEntries[D];if(C.tryLoc===f){var b=C.completion;if(b.type==="throw"){var F=b.arg;Js(C)}return F}}throw new Error("illegal catch attempt")},delegateYield:function(f,D,C){return this.delegate={iterator:Vs(f),resultName:D,nextLoc:C},this.method==="next"&&(this.arg=r),k}},e}Fe.exports=mo,Fe.exports.__esModule=!0,Fe.exports.default=Fe.exports});var Eo=y((Im,go)=>{u();var Or=Do()();go.exports=Or;try{regeneratorRuntime=Or}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=Or:Function("r","regeneratorRuntime = r")(Or)}});var Ao=y((nd,ii)=>{"use strict";u();var yo=(r,...e)=>new Promise(t=>{t(r(...e))});ii.exports=yo;ii.exports.default=yo});var wo=y((od,ni)=>{"use strict";u();var Sl=Ao(),Co=r=>{if(!((Number.isInteger(r)||r===1/0)&&r>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],t=0,s=()=>{t--,e.length>0&&e.shift()()},i=(a,l,...c)=>{t++;let h=Sl(a,...c);l(h),h.then(s,s)},n=(a,l,...c)=>{t<r?i(a,l,...c):e.push(i.bind(null,a,l,...c))},o=(a,...l)=>new Promise(c=>n(a,c,...l));return Object.defineProperties(o,{activeCount:{get:()=>t},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};ni.exports=Co;ni.exports.default=Co});var So=y((ud,oi)=>{"use strict";u();var Fo=wo(),Tr=class extends Error{constructor(e){super(),this.value=e}},kl=async(r,e)=>e(await r),_l=async r=>{let e=await Promise.all(r);if(e[1]===!0)throw new Tr(e[0]);return!1},bo=async(r,e,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let s=Fo(t.concurrency),i=[...r].map(o=>[o,s(kl,o,e)]),n=Fo(t.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>n(_l,o)))}catch(o){if(o instanceof Tr)return o.value;throw o}};oi.exports=bo;oi.exports.default=bo});var xo=y((ad,ui)=>{"use strict";u();var ko=A("path"),Pr=A("fs"),{promisify:_o}=A("util"),Rl=So(),Bl=_o(Pr.stat),vl=_o(Pr.lstat),Ro={directory:"isDirectory",file:"isFile"};function Bo({type:r}){if(!(r in Ro))throw new Error(`Invalid type specified: ${r}`)}var vo=(r,e)=>r===void 0||e[Ro[r]]();ui.exports=async(r,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},Bo(e);let t=e.allowSymlinks?Bl:vl;return Rl(r,async s=>{try{let i=await t(ko.resolve(e.cwd,s));return vo(e.type,i)}catch{return!1}},e)};ui.exports.sync=(r,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},Bo(e);let t=e.allowSymlinks?Pr.statSync:Pr.lstatSync;for(let s of r)try{let i=t(ko.resolve(e.cwd,s));if(vo(e.type,i))return s}catch{}}});var To=y((cd,ai)=>{"use strict";u();var Oo=A("fs"),{promisify:xl}=A("util"),Ol=xl(Oo.access);ai.exports=async r=>{try{return await Ol(r),!0}catch{return!1}};ai.exports.sync=r=>{try{return Oo.accessSync(r),!0}catch{return!1}}});var Lo=y((ld,Bt)=>{"use strict";u();var je=A("path"),Lr=xo(),Po=To(),ci=Symbol("findUp.stop");Bt.exports=async(r,e={})=>{let t=je.resolve(e.cwd||""),{root:s}=je.parse(t),i=[].concat(r),n=async o=>{if(typeof r!="function")return Lr(i,o);let a=await r(o.cwd);return typeof a=="string"?Lr([a],o):a};for(;;){let o=await n({...e,cwd:t});if(o===ci)return;if(o)return je.resolve(t,o);if(t===s)return;t=je.dirname(t)}};Bt.exports.sync=(r,e={})=>{let t=je.resolve(e.cwd||""),{root:s}=je.parse(t),i=[].concat(r),n=o=>{if(typeof r!="function")return Lr.sync(i,o);let a=r(o.cwd);return typeof a=="string"?Lr.sync([a],o):a};for(;;){let o=n({...e,cwd:t});if(o===ci)return;if(o)return je.resolve(t,o);if(t===s)return;t=je.dirname(t)}};Bt.exports.exists=Po;Bt.exports.sync.exists=Po.sync;Bt.exports.stop=ci});var vt=y((Gd,qo)=>{"use strict";u();var Mo=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);qo.exports=r=>r?Object.keys(r).map(e=>[Mo.has(e)?Mo.get(e):e,r[e]]).reduce((e,t)=>(e[t[0]]=t[1],e),Object.create(null)):{}});var jr=y((Hd,Yo)=>{"use strict";u();var Uo=typeof process=="object"&&process?process:{stdout:null,stderr:null},Il=A("events"),jo=A("stream"),$o=A("string_decoder").StringDecoder,Se=Symbol("EOF"),ke=Symbol("maybeEmitEnd"),$e=Symbol("emittedEnd"),Nr=Symbol("emittingEnd"),or=Symbol("emittedError"),Ir=Symbol("closed"),zo=Symbol("read"),Mr=Symbol("flush"),Wo=Symbol("flushChunk"),Y=Symbol("encoding"),_e=Symbol("decoder"),qr=Symbol("flowing"),ur=Symbol("paused"),xt=Symbol("resume"),N=Symbol("buffer"),de=Symbol("pipes"),q=Symbol("bufferLength"),li=Symbol("bufferPush"),hi=Symbol("bufferShift"),j=Symbol("objectMode"),$=Symbol("destroyed"),fi=Symbol("emitData"),Go=Symbol("emitEnd"),pi=Symbol("emitEnd2"),Re=Symbol("async"),ar=r=>Promise.resolve().then(r),Ho=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Ml=Ho&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),ql=Ho&&Symbol.iterator||Symbol("iterator not implemented"),Ul=r=>r==="end"||r==="finish"||r==="prefinish",jl=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,$l=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Ur=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[xt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},mi=class extends Ur{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};Yo.exports=class Jo extends jo{constructor(e){super(),this[qr]=!1,this[ur]=!1,this[de]=[],this[N]=[],this[j]=e&&e.objectMode||!1,this[j]?this[Y]=null:this[Y]=e&&e.encoding||null,this[Y]==="buffer"&&(this[Y]=null),this[Re]=e&&!!e.async||!1,this[_e]=this[Y]?new $o(this[Y]):null,this[Se]=!1,this[$e]=!1,this[Nr]=!1,this[Ir]=!1,this[or]=null,this.writable=!0,this.readable=!0,this[q]=0,this[$]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[N]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[de]})}get bufferLength(){return this[q]}get encoding(){return this[Y]}set encoding(e){if(this[j])throw new Error("cannot set encoding in objectMode");if(this[Y]&&e!==this[Y]&&(this[_e]&&this[_e].lastNeed||this[q]))throw new Error("cannot change encoding");this[Y]!==e&&(this[_e]=e?new $o(e):null,this[N].length&&(this[N]=this[N].map(t=>this[_e].write(t)))),this[Y]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[j]}set objectMode(e){this[j]=this[j]||!!e}get async(){return this[Re]}set async(e){this[Re]=this[Re]||!!e}write(e,t,s){if(this[Se])throw new Error("write after end");if(this[$])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Re]?ar:n=>n();return!this[j]&&!Buffer.isBuffer(e)&&($l(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):jl(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[j]?(this.flowing&&this[q]!==0&&this[Mr](!0),this.flowing?this.emit("data",e):this[li](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[Y]&&!this[_e].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[Y]&&(e=this[_e].write(e)),this.flowing&&this[q]!==0&&this[Mr](!0),this.flowing?this.emit("data",e):this[li](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[$])return null;if(this[q]===0||e===0||e>this[q])return this[ke](),null;this[j]&&(e=null),this[N].length>1&&!this[j]&&(this.encoding?this[N]=[this[N].join("")]:this[N]=[Buffer.concat(this[N],this[q])]);let t=this[zo](e||null,this[N][0]);return this[ke](),t}[zo](e,t){return e===t.length||e===null?this[hi]():(this[N][0]=t.slice(e),t=t.slice(0,e),this[q]-=e),this.emit("data",t),!this[N].length&&!this[Se]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[Se]=!0,this.writable=!1,(this.flowing||!this[ur])&&this[ke](),this}[xt](){this[$]||(this[ur]=!1,this[qr]=!0,this.emit("resume"),this[N].length?this[Mr]():this[Se]?this[ke]():this.emit("drain"))}resume(){return this[xt]()}pause(){this[qr]=!1,this[ur]=!0}get destroyed(){return this[$]}get flowing(){return this[qr]}get paused(){return this[ur]}[li](e){this[j]?this[q]+=1:this[q]+=e.length,this[N].push(e)}[hi](){return this[N].length&&(this[j]?this[q]-=1:this[q]-=this[N][0].length),this[N].shift()}[Mr](e){do;while(this[Wo](this[hi]()));!e&&!this[N].length&&!this[Se]&&this.emit("drain")}[Wo](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[$])return;let s=this[$e];return t=t||{},e===Uo.stdout||e===Uo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this[de].push(t.proxyErrors?new mi(this,e,t):new Ur(this,e,t)),this[Re]?ar(()=>this[xt]()):this[xt]()),e}unpipe(e){let t=this[de].find(s=>s.dest===e);t&&(this[de].splice(this[de].indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this[de].length&&!this.flowing?this[xt]():e==="readable"&&this[q]!==0?super.emit("readable"):Ul(e)&&this[$e]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[or]&&(this[Re]?ar(()=>t.call(this,this[or])):t.call(this,this[or])),s}get emittedEnd(){return this[$e]}[ke](){!this[Nr]&&!this[$e]&&!this[$]&&this[N].length===0&&this[Se]&&(this[Nr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Ir]&&this.emit("close"),this[Nr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==$&&this[$])return;if(e==="data")return t?this[Re]?ar(()=>this[fi](t)):this[fi](t):!1;if(e==="end")return this[Go]();if(e==="close"){if(this[Ir]=!0,!this[$e]&&!this[$])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[or]=t;let n=super.emit("error",t);return this[ke](),n}else if(e==="resume"){let n=super.emit("resume");return this[ke](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[ke](),i}[fi](e){for(let s of this[de])s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[ke](),t}[Go](){this[$e]||(this[$e]=!0,this.readable=!1,this[Re]?ar(()=>this[pi]()):this[pi]())}[pi](){if(this[_e]){let t=this[_e].end();if(t){for(let s of this[de])s.dest.write(t);super.emit("data",t)}}for(let t of this[de])t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[j]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[j]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[j]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[j]?Promise.reject(new Error("cannot concat in objectMode")):this[Y]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on($,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Ml](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[Se])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[Se]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once($,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[ql](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[$]?(e?this.emit("error",e):this.emit($),this):(this[$]=!0,this[N].length=0,this[q]=0,typeof this.close=="function"&&!this[Ir]&&this.close(),e?this.emit("error",e):this.emit($),this)}static isStream(e){return!!e&&(e instanceof Jo||e instanceof jo||e instanceof Il&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Ko=y((Jd,Vo)=>{u();var zl=A("zlib").constants||{ZLIB_VERNUM:4736};Vo.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},zl))});var Ai=y((Yd,nu)=>{"use strict";u();var Zo=typeof process=="object"&&process?process:{stdout:null,stderr:null},Wl=A("events"),Xo=A("stream"),Qo=A("string_decoder").StringDecoder,Be=Symbol("EOF"),ve=Symbol("maybeEmitEnd"),ze=Symbol("emittedEnd"),$r=Symbol("emittingEnd"),cr=Symbol("emittedError"),zr=Symbol("closed"),eu=Symbol("read"),Wr=Symbol("flush"),tu=Symbol("flushChunk"),V=Symbol("encoding"),xe=Symbol("decoder"),Gr=Symbol("flowing"),lr=Symbol("paused"),Ot=Symbol("resume"),U=Symbol("bufferLength"),di=Symbol("bufferPush"),Di=Symbol("bufferShift"),z=Symbol("objectMode"),W=Symbol("destroyed"),gi=Symbol("emitData"),ru=Symbol("emitEnd"),Ei=Symbol("emitEnd2"),Oe=Symbol("async"),hr=r=>Promise.resolve().then(r),su=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Gl=su&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),Hl=su&&Symbol.iterator||Symbol("iterator not implemented"),Jl=r=>r==="end"||r==="finish"||r==="prefinish",Yl=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,Vl=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Hr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Ot](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},yi=class extends Hr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};nu.exports=class iu extends Xo{constructor(e){super(),this[Gr]=!1,this[lr]=!1,this.pipes=[],this.buffer=[],this[z]=e&&e.objectMode||!1,this[z]?this[V]=null:this[V]=e&&e.encoding||null,this[V]==="buffer"&&(this[V]=null),this[Oe]=e&&!!e.async||!1,this[xe]=this[V]?new Qo(this[V]):null,this[Be]=!1,this[ze]=!1,this[$r]=!1,this[zr]=!1,this[cr]=null,this.writable=!0,this.readable=!0,this[U]=0,this[W]=!1}get bufferLength(){return this[U]}get encoding(){return this[V]}set encoding(e){if(this[z])throw new Error("cannot set encoding in objectMode");if(this[V]&&e!==this[V]&&(this[xe]&&this[xe].lastNeed||this[U]))throw new Error("cannot change encoding");this[V]!==e&&(this[xe]=e?new Qo(e):null,this.buffer.length&&(this.buffer=this.buffer.map(t=>this[xe].write(t)))),this[V]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[z]}set objectMode(e){this[z]=this[z]||!!e}get async(){return this[Oe]}set async(e){this[Oe]=this[Oe]||!!e}write(e,t,s){if(this[Be])throw new Error("write after end");if(this[W])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Oe]?hr:n=>n();return!this[z]&&!Buffer.isBuffer(e)&&(Vl(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Yl(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[z]?(this.flowing&&this[U]!==0&&this[Wr](!0),this.flowing?this.emit("data",e):this[di](e),this[U]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[V]&&!this[xe].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[V]&&(e=this[xe].write(e)),this.flowing&&this[U]!==0&&this[Wr](!0),this.flowing?this.emit("data",e):this[di](e),this[U]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[U]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[W])return null;if(this[U]===0||e===0||e>this[U])return this[ve](),null;this[z]&&(e=null),this.buffer.length>1&&!this[z]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[U])]);let t=this[eu](e||null,this.buffer[0]);return this[ve](),t}[eu](e,t){return e===t.length||e===null?this[Di]():(this.buffer[0]=t.slice(e),t=t.slice(0,e),this[U]-=e),this.emit("data",t),!this.buffer.length&&!this[Be]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[Be]=!0,this.writable=!1,(this.flowing||!this[lr])&&this[ve](),this}[Ot](){this[W]||(this[lr]=!1,this[Gr]=!0,this.emit("resume"),this.buffer.length?this[Wr]():this[Be]?this[ve]():this.emit("drain"))}resume(){return this[Ot]()}pause(){this[Gr]=!1,this[lr]=!0}get destroyed(){return this[W]}get flowing(){return this[Gr]}get paused(){return this[lr]}[di](e){this[z]?this[U]+=1:this[U]+=e.length,this.buffer.push(e)}[Di](){return this.buffer.length&&(this[z]?this[U]-=1:this[U]-=this.buffer[0].length),this.buffer.shift()}[Wr](e){do;while(this[tu](this[Di]()));!e&&!this.buffer.length&&!this[Be]&&this.emit("drain")}[tu](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[W])return;let s=this[ze];return t=t||{},e===Zo.stdout||e===Zo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this.pipes.push(t.proxyErrors?new yi(this,e,t):new Hr(this,e,t)),this[Oe]?hr(()=>this[Ot]()):this[Ot]()),e}unpipe(e){let t=this.pipes.find(s=>s.dest===e);t&&(this.pipes.splice(this.pipes.indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this.pipes.length&&!this.flowing?this[Ot]():e==="readable"&&this[U]!==0?super.emit("readable"):Jl(e)&&this[ze]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[cr]&&(this[Oe]?hr(()=>t.call(this,this[cr])):t.call(this,this[cr])),s}get emittedEnd(){return this[ze]}[ve](){!this[$r]&&!this[ze]&&!this[W]&&this.buffer.length===0&&this[Be]&&(this[$r]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[zr]&&this.emit("close"),this[$r]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==W&&this[W])return;if(e==="data")return t?this[Oe]?hr(()=>this[gi](t)):this[gi](t):!1;if(e==="end")return this[ru]();if(e==="close"){if(this[zr]=!0,!this[ze]&&!this[W])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[cr]=t;let n=super.emit("error",t);return this[ve](),n}else if(e==="resume"){let n=super.emit("resume");return this[ve](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[ve](),i}[gi](e){for(let s of this.pipes)s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[ve](),t}[ru](){this[ze]||(this[ze]=!0,this.readable=!1,this[Oe]?hr(()=>this[Ei]()):this[Ei]())}[Ei](){if(this[xe]){let t=this[xe].end();if(t){for(let s of this.pipes)s.dest.write(t);super.emit("data",t)}}for(let t of this.pipes)t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[z]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[z]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[z]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[z]?Promise.reject(new Error("cannot concat in objectMode")):this[V]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(W,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Gl](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[Be])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[Be]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(W,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[Hl](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[W]?(e?this.emit("error",e):this.emit(W),this):(this[W]=!0,this.buffer.length=0,this[U]=0,typeof this.close=="function"&&!this[zr]&&this.close(),e?this.emit("error",e):this.emit(W),this)}static isStream(e){return!!e&&(e instanceof iu||e instanceof Xo||e instanceof Wl&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Ni=y(X=>{"use strict";u();var Si=A("assert"),We=A("buffer").Buffer,au=A("zlib"),ht=X.constants=Ko(),Kl=Ai(),ou=We.concat,ft=Symbol("_superWrite"),Pt=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},Zl=Symbol("opts"),fr=Symbol("flushFlag"),uu=Symbol("finishFlushFlag"),Li=Symbol("fullFlushFlag"),x=Symbol("handle"),Jr=Symbol("onError"),Tt=Symbol("sawError"),Ci=Symbol("level"),wi=Symbol("strategy"),Fi=Symbol("ended"),Vd=Symbol("_defaultFullFlush"),Yr=class extends Kl{constructor(e,t){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[Tt]=!1,this[Fi]=!1,this[Zl]=e,this[fr]=e.flush,this[uu]=e.finishFlush;try{this[x]=new au[t](e)}catch(s){throw new Pt(s)}this[Jr]=s=>{this[Tt]||(this[Tt]=!0,this.close(),this.emit("error",s))},this[x].on("error",s=>this[Jr](new Pt(s))),this.once("end",()=>this.close)}close(){this[x]&&(this[x].close(),this[x]=null,this.emit("close"))}reset(){if(!this[Tt])return Si(this[x],"zlib binding closed"),this[x].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[Li]),this.write(Object.assign(We.alloc(0),{[fr]:e})))}end(e,t,s){return e&&this.write(e,t),this.flush(this[uu]),this[Fi]=!0,super.end(null,null,s)}get ended(){return this[Fi]}write(e,t,s){if(typeof t=="function"&&(s=t,t="utf8"),typeof e=="string"&&(e=We.from(e,t)),this[Tt])return;Si(this[x],"zlib binding closed");let i=this[x]._handle,n=i.close;i.close=()=>{};let o=this[x].close;this[x].close=()=>{},We.concat=c=>c;let a;try{let c=typeof e[fr]=="number"?e[fr]:this[fr];a=this[x]._processChunk(e,c),We.concat=ou}catch(c){We.concat=ou,this[Jr](new Pt(c))}finally{this[x]&&(this[x]._handle=i,i.close=n,this[x].close=o,this[x].removeAllListeners("error"))}this[x]&&this[x].on("error",c=>this[Jr](new Pt(c)));let l;if(a)if(Array.isArray(a)&&a.length>0){l=this[ft](We.from(a[0]));for(let c=1;c<a.length;c++)l=this[ft](a[c])}else l=this[ft](We.from(a));return s&&s(),l}[ft](e){return super.write(e)}},Te=class extends Yr{constructor(e,t){e=e||{},e.flush=e.flush||ht.Z_NO_FLUSH,e.finishFlush=e.finishFlush||ht.Z_FINISH,super(e,t),this[Li]=ht.Z_FULL_FLUSH,this[Ci]=e.level,this[wi]=e.strategy}params(e,t){if(!this[Tt]){if(!this[x])throw new Error("cannot switch params when binding is closed");if(!this[x].params)throw new Error("not supported in this implementation");if(this[Ci]!==e||this[wi]!==t){this.flush(ht.Z_SYNC_FLUSH),Si(this[x],"zlib binding closed");let s=this[x].flush;this[x].flush=(i,n)=>{this.flush(i),n()};try{this[x].params(e,t)}finally{this[x].flush=s}this[x]&&(this[Ci]=e,this[wi]=t)}}}},ki=class extends Te{constructor(e){super(e,"Deflate")}},_i=class extends Te{constructor(e){super(e,"Inflate")}},bi=Symbol("_portable"),Ri=class extends Te{constructor(e){super(e,"Gzip"),this[bi]=e&&!!e.portable}[ft](e){return this[bi]?(this[bi]=!1,e[9]=255,super[ft](e)):super[ft](e)}},Bi=class extends Te{constructor(e){super(e,"Gunzip")}},vi=class extends Te{constructor(e){super(e,"DeflateRaw")}},xi=class extends Te{constructor(e){super(e,"InflateRaw")}},Oi=class extends Te{constructor(e){super(e,"Unzip")}},Vr=class extends Yr{constructor(e,t){e=e||{},e.flush=e.flush||ht.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||ht.BROTLI_OPERATION_FINISH,super(e,t),this[Li]=ht.BROTLI_OPERATION_FLUSH}},Ti=class extends Vr{constructor(e){super(e,"BrotliCompress")}},Pi=class extends Vr{constructor(e){super(e,"BrotliDecompress")}};X.Deflate=ki;X.Inflate=_i;X.Gzip=Ri;X.Gunzip=Bi;X.DeflateRaw=vi;X.InflateRaw=xi;X.Unzip=Oi;typeof au.BrotliCompress=="function"?(X.BrotliCompress=Ti,X.BrotliDecompress=Pi):X.BrotliCompress=X.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var Lt=y((Xd,cu)=>{u();var Xl=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;cu.exports=Xl!=="win32"?r=>r:r=>r&&r.replace(/\\/g,"/")});var Kr=y((e0,lu)=>{"use strict";u();var Ql=jr(),Ii=Lt(),Mi=Symbol("slurp");lu.exports=class extends Ql{constructor(e,t,s){switch(super(),this.pause(),this.extended=t,this.globalExtended=s,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=Ii(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=Ii(e.linkpath),this.uname=e.uname,this.gname=e.gname,t&&this[Mi](t),s&&this[Mi](s,!0)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");let s=this.remain,i=this.blockRemain;return this.remain=Math.max(0,s-t),this.blockRemain=Math.max(0,i-t),this.ignore?!0:s>=t?super.write(e):super.write(e.slice(0,s))}[Mi](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=s==="path"||s==="linkpath"?Ii(e[s]):e[s])}}});var qi=y(Zr=>{"use strict";u();Zr.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);Zr.code=new Map(Array.from(Zr.name).map(r=>[r[1],r[0]]))});var mu=y((r0,pu)=>{"use strict";u();var eh=(r,e)=>{if(Number.isSafeInteger(r))r<0?rh(r,e):th(r,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},th=(r,e)=>{e[0]=128;for(var t=e.length;t>1;t--)e[t-1]=r&255,r=Math.floor(r/256)},rh=(r,e)=>{e[0]=255;var t=!1;r=r*-1;for(var s=e.length;s>1;s--){var i=r&255;r=Math.floor(r/256),t?e[s-1]=hu(i):i===0?e[s-1]=0:(t=!0,e[s-1]=fu(i))}},sh=r=>{let e=r[0],t=e===128?nh(r.slice(1,r.length)):e===255?ih(r):null;if(t===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(t))throw Error("parsed number outside of javascript safe integer range");return t},ih=r=>{for(var e=r.length,t=0,s=!1,i=e-1;i>-1;i--){var n=r[i],o;s?o=hu(n):n===0?o=n:(s=!0,o=fu(n)),o!==0&&(t-=o*Math.pow(256,e-i-1))}return t},nh=r=>{for(var e=r.length,t=0,s=e-1;s>-1;s--){var i=r[s];i!==0&&(t+=i*Math.pow(256,e-s-1))}return t},hu=r=>(255^r)&255,fu=r=>(255^r)+1&255;pu.exports={encode:eh,parse:sh}});var It=y((s0,Du)=>{"use strict";u();var Ui=qi(),Nt=A("path").posix,du=mu(),ji=Symbol("slurp"),Q=Symbol("type"),Wi=class{constructor(e,t,s,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[Q]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,t||0,s,i):e&&this.set(e)}decode(e,t,s,i){if(t||(t=0),!e||!(e.length>=t+512))throw new Error("need 512 bytes for header");if(this.path=pt(e,t,100),this.mode=Ge(e,t+100,8),this.uid=Ge(e,t+108,8),this.gid=Ge(e,t+116,8),this.size=Ge(e,t+124,12),this.mtime=$i(e,t+136,12),this.cksum=Ge(e,t+148,12),this[ji](s),this[ji](i,!0),this[Q]=pt(e,t+156,1),this[Q]===""&&(this[Q]="0"),this[Q]==="0"&&this.path.slice(-1)==="/"&&(this[Q]="5"),this[Q]==="5"&&(this.size=0),this.linkpath=pt(e,t+157,100),e.slice(t+257,t+265).toString()==="ustar\x0000")if(this.uname=pt(e,t+265,32),this.gname=pt(e,t+297,32),this.devmaj=Ge(e,t+329,8),this.devmin=Ge(e,t+337,8),e[t+475]!==0){let o=pt(e,t+345,155);this.path=o+"/"+this.path}else{let o=pt(e,t+345,130);o&&(this.path=o+"/"+this.path),this.atime=$i(e,t+476,12),this.ctime=$i(e,t+488,12)}let n=8*32;for(let o=t;o<t+148;o++)n+=e[o];for(let o=t+156;o<t+512;o++)n+=e[o];this.cksumValid=n===this.cksum,this.cksum===null&&n===8*32&&(this.nullBlock=!0)}[ji](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=e[s])}encode(e,t){if(e||(e=this.block=Buffer.alloc(512),t=0),t||(t=0),!(e.length>=t+512))throw new Error("need 512 bytes for header");let s=this.ctime||this.atime?130:155,i=oh(this.path||"",s),n=i[0],o=i[1];this.needPax=i[2],this.needPax=mt(e,t,100,n)||this.needPax,this.needPax=He(e,t+100,8,this.mode)||this.needPax,this.needPax=He(e,t+108,8,this.uid)||this.needPax,this.needPax=He(e,t+116,8,this.gid)||this.needPax,this.needPax=He(e,t+124,12,this.size)||this.needPax,this.needPax=zi(e,t+136,12,this.mtime)||this.needPax,e[t+156]=this[Q].charCodeAt(0),this.needPax=mt(e,t+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",t+257,8),this.needPax=mt(e,t+265,32,this.uname)||this.needPax,this.needPax=mt(e,t+297,32,this.gname)||this.needPax,this.needPax=He(e,t+329,8,this.devmaj)||this.needPax,this.needPax=He(e,t+337,8,this.devmin)||this.needPax,this.needPax=mt(e,t+345,s,o)||this.needPax,e[t+475]!==0?this.needPax=mt(e,t+345,155,o)||this.needPax:(this.needPax=mt(e,t+345,130,o)||this.needPax,this.needPax=zi(e,t+476,12,this.atime)||this.needPax,this.needPax=zi(e,t+488,12,this.ctime)||this.needPax);let a=8*32;for(let l=t;l<t+148;l++)a+=e[l];for(let l=t+156;l<t+512;l++)a+=e[l];return this.cksum=a,He(e,t+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let t in e)e[t]!==null&&e[t]!==void 0&&(this[t]=e[t])}get type(){return Ui.name.get(this[Q])||this[Q]}get typeKey(){return this[Q]}set type(e){Ui.code.has(e)?this[Q]=Ui.code.get(e):this[Q]=e}},oh=(r,e)=>{let s=r,i="",n,o=Nt.parse(r).root||".";if(Buffer.byteLength(s)<100)n=[s,i,!1];else{i=Nt.dirname(s),s=Nt.basename(s);do Buffer.byteLength(s)<=100&&Buffer.byteLength(i)<=e?n=[s,i,!1]:Buffer.byteLength(s)>100&&Buffer.byteLength(i)<=e?n=[s.slice(0,100-1),i,!0]:(s=Nt.join(Nt.basename(i),s),i=Nt.dirname(i));while(i!==o&&!n);n||(n=[r.slice(0,100-1),"",!0])}return n},pt=(r,e,t)=>r.slice(e,e+t).toString("utf8").replace(/\0.*/,""),$i=(r,e,t)=>uh(Ge(r,e,t)),uh=r=>r===null?null:new Date(r*1e3),Ge=(r,e,t)=>r[e]&128?du.parse(r.slice(e,e+t)):ch(r,e,t),ah=r=>isNaN(r)?null:r,ch=(r,e,t)=>ah(parseInt(r.slice(e,e+t).toString("utf8").replace(/\0.*$/,"").trim(),8)),lh={12:8589934591,8:2097151},He=(r,e,t,s)=>s===null?!1:s>lh[t]||s<0?(du.encode(s,r.slice(e,e+t)),!0):(hh(r,e,t,s),!1),hh=(r,e,t,s)=>r.write(fh(s,t),e,t,"ascii"),fh=(r,e)=>ph(Math.floor(r).toString(8),e),ph=(r,e)=>(r.length===e-1?r:new Array(e-r.length-1).join("0")+r+" ")+"\0",zi=(r,e,t,s)=>s===null?!1:He(r,e,t,s.getTime()/1e3),mh=new Array(156).join("\0"),mt=(r,e,t,s)=>s===null?!1:(r.write(s+mh,e,t,"utf8"),s.length!==Buffer.byteLength(s)||s.length>t);Du.exports=Wi});var Xr=y((i0,gu)=>{"use strict";u();var dh=It(),Dh=A("path"),pr=class{constructor(e,t){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=t||!1}encode(){let e=this.encodeBody();if(e==="")return null;let t=Buffer.byteLength(e),s=512*Math.ceil(1+t/512),i=Buffer.allocUnsafe(s);for(let n=0;n<512;n++)i[n]=0;new dh({path:("PaxHeader/"+Dh.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:t,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,t,"utf8");for(let n=t+512;n<i.length;n++)i[n]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let t=this[e]instanceof Date?this[e].getTime()/1e3:this[e],s=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+t+`
`,i=Buffer.byteLength(s),n=Math.floor(Math.log(i)/Math.log(10))+1;return i+n>=Math.pow(10,n)&&(n+=1),n+i+s}};pr.parse=(r,e,t)=>new pr(gh(Eh(r),e),t);var gh=(r,e)=>e?Object.keys(r).reduce((t,s)=>(t[s]=r[s],t),e):r,Eh=r=>r.replace(/\n$/,"").split(`
`).reduce(yh,Object.create(null)),yh=(r,e)=>{let t=parseInt(e,10);if(t!==Buffer.byteLength(e)+1)return r;e=e.slice((t+" ").length);let s=e.split("="),i=s.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return r;let n=s.join("=");return r[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(n*1e3):/^[0-9]+$/.test(n)?+n:n,r};gu.exports=pr});var Mt=y((n0,Eu)=>{u();Eu.exports=r=>{let e=r.length-1,t=-1;for(;e>-1&&r.charAt(e)==="/";)t=e,e--;return t===-1?r:r.slice(0,t)}});var Qr=y((o0,yu)=>{"use strict";u();yu.exports=r=>class extends r{warn(e,t,s={}){this.file&&(s.file=this.file),this.cwd&&(s.cwd=this.cwd),s.code=t instanceof Error&&t.code||e,s.tarCode=e,!this.strict&&s.recoverable!==!1?(t instanceof Error&&(s=Object.assign(t,s),t=t.message),this.emit("warn",s.tarCode,t,s)):t instanceof Error?this.emit("error",Object.assign(t,s)):this.emit("error",Object.assign(new Error(`${e}: ${t}`),s))}}});var Hi=y((a0,Au)=>{"use strict";u();var es=["|","<",">","?",":"],Gi=es.map(r=>String.fromCharCode(61440+r.charCodeAt(0))),Ah=new Map(es.map((r,e)=>[r,Gi[e]])),Ch=new Map(Gi.map((r,e)=>[r,es[e]]));Au.exports={encode:r=>es.reduce((e,t)=>e.split(t).join(Ah.get(t)),r),decode:r=>Gi.reduce((e,t)=>e.split(t).join(Ch.get(t)),r)}});var Ji=y((c0,wu)=>{u();var{isAbsolute:wh,parse:Cu}=A("path").win32;wu.exports=r=>{let e="",t=Cu(r);for(;wh(r)||t.root;){let s=r.charAt(0)==="/"&&r.slice(0,4)!=="//?/"?"/":t.root;r=r.slice(s.length),e+=s,t=Cu(r)}return[e,r]}});var bu=y((l0,Fu)=>{"use strict";u();Fu.exports=(r,e,t)=>(r&=4095,t&&(r=(r|384)&-19),e&&(r&256&&(r|=64),r&32&&(r|=8),r&4&&(r|=1)),r)});var sn=y((p0,Mu)=>{"use strict";u();var xu=jr(),Ou=Xr(),Tu=It(),ge=A("fs"),Su=A("path"),De=Lt(),Fh=Mt(),Pu=(r,e)=>e?(r=De(r).replace(/^\.(\/|$)/,""),Fh(e)+"/"+r):De(r),bh=16*1024*1024,ku=Symbol("process"),_u=Symbol("file"),Ru=Symbol("directory"),Vi=Symbol("symlink"),Bu=Symbol("hardlink"),mr=Symbol("header"),ts=Symbol("read"),Ki=Symbol("lstat"),rs=Symbol("onlstat"),Zi=Symbol("onread"),Xi=Symbol("onreadlink"),Qi=Symbol("openfile"),en=Symbol("onopenfile"),Je=Symbol("close"),ss=Symbol("mode"),tn=Symbol("awaitDrain"),Yi=Symbol("ondrain"),Ee=Symbol("prefix"),vu=Symbol("hadError"),Lu=Qr(),Sh=Hi(),Nu=Ji(),Iu=bu(),is=Lu(class extends xu{constructor(e,t){if(t=t||{},super(t),typeof e!="string")throw new TypeError("path is required");this.path=De(e),this.portable=!!t.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=t.maxReadSize||bh,this.linkCache=t.linkCache||new Map,this.statCache=t.statCache||new Map,this.preservePaths=!!t.preservePaths,this.cwd=De(t.cwd||process.cwd()),this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.mtime=t.mtime||null,this.prefix=t.prefix?De(t.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Nu(this.path);i&&(this.path=n,s=i)}this.win32=!!t.win32||process.platform==="win32",this.win32&&(this.path=Sh.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=De(t.absolute||Su.resolve(this.cwd,e)),this.path===""&&(this.path="./"),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.statCache.has(this.absolute)?this[rs](this.statCache.get(this.absolute)):this[Ki]()}emit(e,...t){return e==="error"&&(this[vu]=!0),super.emit(e,...t)}[Ki](){ge.lstat(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[rs](t)})}[rs](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=_h(e),this.emit("stat",e),this[ku]()}[ku](){switch(this.type){case"File":return this[_u]();case"Directory":return this[Ru]();case"SymbolicLink":return this[Vi]();default:return this.end()}}[ss](e){return Iu(e,this.type==="Directory",this.portable)}[Ee](e){return Pu(e,this.prefix)}[mr](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new Tu({path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,mode:this[ss](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new Ou({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[Ru](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[mr](),this.end()}[Vi](){ge.readlink(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[Xi](t)})}[Xi](e){this.linkpath=De(e),this[mr](),this.end()}[Bu](e){this.type="Link",this.linkpath=De(Su.relative(this.cwd,e)),this.stat.size=0,this[mr](),this.end()}[_u](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let t=this.linkCache.get(e);if(t.indexOf(this.cwd)===0)return this[Bu](t)}this.linkCache.set(e,this.absolute)}if(this[mr](),this.stat.size===0)return this.end();this[Qi]()}[Qi](){ge.open(this.absolute,"r",(e,t)=>{if(e)return this.emit("error",e);this[en](t)})}[en](e){if(this.fd=e,this[vu])return this[Je]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let t=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(t),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[ts]()}[ts](){let{fd:e,buf:t,offset:s,length:i,pos:n}=this;ge.read(e,t,s,i,n,(o,a)=>{if(o)return this[Je](()=>this.emit("error",o));this[Zi](a)})}[Je](e){ge.close(this.fd,e)}[Zi](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Je](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Je](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let t=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(t)?this[Yi]():this[tn](()=>this[Yi]())}[tn](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let t=new Error("writing more data than expected");return t.path=this.absolute,this.emit("error",t)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[Yi](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[Je](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[ts]()}}),rn=class extends is{[Ki](){this[rs](ge.lstatSync(this.absolute))}[Vi](){this[Xi](ge.readlinkSync(this.absolute))}[Qi](){this[en](ge.openSync(this.absolute,"r"))}[ts](){let e=!0;try{let{fd:t,buf:s,offset:i,length:n,pos:o}=this,a=ge.readSync(t,s,i,n,o);this[Zi](a),e=!1}finally{if(e)try{this[Je](()=>{})}catch{}}}[tn](e){e()}[Je](e){ge.closeSync(this.fd),e()}},kh=Lu(class extends xu{constructor(e,t){t=t||{},super(t),this.preservePaths=!!t.preservePaths,this.portable=!!t.portable,this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=t.prefix||null,this.path=De(e.path),this.mode=this[ss](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:t.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=De(e.linkpath),typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Nu(this.path);i&&(this.path=n,s=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new Tu({path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.header.encode()&&!this.noPax&&super.write(new Ou({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[Ee](this.path),linkpath:this.type==="Link"?this[Ee](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[Ee](e){return Pu(e,this.prefix)}[ss](e){return Iu(e,this.type==="Directory",this.portable)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=t,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});is.Sync=rn;is.Tar=kh;var _h=r=>r.isFile()?"File":r.isDirectory()?"Directory":r.isSymbolicLink()?"SymbolicLink":"Unsupported";Mu.exports=is});var Uu=y((m0,qu)=>{"use strict";u();qu.exports=function(r){r.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var nn=y((d0,ju)=>{"use strict";u();ju.exports=_;_.Node=dt;_.create=_;function _(r){var e=this;if(e instanceof _||(e=new _),e.tail=null,e.head=null,e.length=0,r&&typeof r.forEach=="function")r.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var t=0,s=arguments.length;t<s;t++)e.push(arguments[t]);return e}_.prototype.removeNode=function(r){if(r.list!==this)throw new Error("removing node which does not belong to this list");var e=r.next,t=r.prev;return e&&(e.prev=t),t&&(t.next=e),r===this.head&&(this.head=e),r===this.tail&&(this.tail=t),r.list.length--,r.next=null,r.prev=null,r.list=null,e};_.prototype.unshiftNode=function(r){if(r!==this.head){r.list&&r.list.removeNode(r);var e=this.head;r.list=this,r.next=e,e&&(e.prev=r),this.head=r,this.tail||(this.tail=r),this.length++}};_.prototype.pushNode=function(r){if(r!==this.tail){r.list&&r.list.removeNode(r);var e=this.tail;r.list=this,r.prev=e,e&&(e.next=r),this.tail=r,this.head||(this.head=r),this.length++}};_.prototype.push=function(){for(var r=0,e=arguments.length;r<e;r++)Bh(this,arguments[r]);return this.length};_.prototype.unshift=function(){for(var r=0,e=arguments.length;r<e;r++)vh(this,arguments[r]);return this.length};_.prototype.pop=function(){if(!!this.tail){var r=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,r}};_.prototype.shift=function(){if(!!this.head){var r=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,r}};_.prototype.forEach=function(r,e){e=e||this;for(var t=this.head,s=0;t!==null;s++)r.call(e,t.value,s,this),t=t.next};_.prototype.forEachReverse=function(r,e){e=e||this;for(var t=this.tail,s=this.length-1;t!==null;s--)r.call(e,t.value,s,this),t=t.prev};_.prototype.get=function(r){for(var e=0,t=this.head;t!==null&&e<r;e++)t=t.next;if(e===r&&t!==null)return t.value};_.prototype.getReverse=function(r){for(var e=0,t=this.tail;t!==null&&e<r;e++)t=t.prev;if(e===r&&t!==null)return t.value};_.prototype.map=function(r,e){e=e||this;for(var t=new _,s=this.head;s!==null;)t.push(r.call(e,s.value,this)),s=s.next;return t};_.prototype.mapReverse=function(r,e){e=e||this;for(var t=new _,s=this.tail;s!==null;)t.push(r.call(e,s.value,this)),s=s.prev;return t};_.prototype.reduce=function(r,e){var t,s=this.head;if(arguments.length>1)t=e;else if(this.head)s=this.head.next,t=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;s!==null;i++)t=r(t,s.value,i),s=s.next;return t};_.prototype.reduceReverse=function(r,e){var t,s=this.tail;if(arguments.length>1)t=e;else if(this.tail)s=this.tail.prev,t=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;s!==null;i--)t=r(t,s.value,i),s=s.prev;return t};_.prototype.toArray=function(){for(var r=new Array(this.length),e=0,t=this.head;t!==null;e++)r[e]=t.value,t=t.next;return r};_.prototype.toArrayReverse=function(){for(var r=new Array(this.length),e=0,t=this.tail;t!==null;e++)r[e]=t.value,t=t.prev;return r};_.prototype.slice=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new _;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(;i!==null&&s<e;s++,i=i.next)t.push(i.value);return t};_.prototype.sliceReverse=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new _;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=this.length,i=this.tail;i!==null&&s>e;s--)i=i.prev;for(;i!==null&&s>r;s--,i=i.prev)t.push(i.value);return t};_.prototype.splice=function(r,e,...t){r>this.length&&(r=this.length-1),r<0&&(r=this.length+r);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(var n=[],s=0;i&&s<e;s++)n.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var s=0;s<t.length;s++)i=Rh(this,i,t[s]);return n};_.prototype.reverse=function(){for(var r=this.head,e=this.tail,t=r;t!==null;t=t.prev){var s=t.prev;t.prev=t.next,t.next=s}return this.head=e,this.tail=r,this};function Rh(r,e,t){var s=e===r.head?new dt(t,null,e,r):new dt(t,e,e.next,r);return s.next===null&&(r.tail=s),s.prev===null&&(r.head=s),r.length++,s}function Bh(r,e){r.tail=new dt(e,r.tail,null,r),r.head||(r.head=r.tail),r.length++}function vh(r,e){r.head=new dt(e,null,r.head,r),r.tail||(r.tail=r.head),r.length++}function dt(r,e,t,s){if(!(this instanceof dt))return new dt(r,e,t,s);this.list=s,this.value=r,e?(e.next=this,this.prev=e):this.prev=null,t?(t.prev=this,this.next=t):this.next=null}try{Uu()(_)}catch{}});var ps=y((g0,Yu)=>{"use strict";u();var hs=class{constructor(e,t){this.path=e||"./",this.absolute=t,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},xh=jr(),Oh=Ni(),Th=Kr(),mn=sn(),Ph=mn.Sync,Lh=mn.Tar,Nh=nn(),$u=Buffer.alloc(1024),us=Symbol("onStat"),ns=Symbol("ended"),ye=Symbol("queue"),qt=Symbol("current"),Dt=Symbol("process"),os=Symbol("processing"),zu=Symbol("processJob"),Ae=Symbol("jobs"),on=Symbol("jobDone"),as=Symbol("addFSEntry"),Wu=Symbol("addTarEntry"),ln=Symbol("stat"),hn=Symbol("readdir"),cs=Symbol("onreaddir"),ls=Symbol("pipe"),Gu=Symbol("entry"),un=Symbol("entryOpt"),fn=Symbol("writeEntryClass"),Ju=Symbol("write"),an=Symbol("ondrain"),fs=A("fs"),Hu=A("path"),Ih=Qr(),cn=Lt(),dn=Ih(class extends xh{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=cn(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[fn]=mn,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new Oh.Gzip(e.gzip),this.zip.on("data",t=>super.write(t)),this.zip.on("end",t=>super.end()),this.zip.on("drain",t=>this[an]()),this.on("resume",t=>this.zip.resume())):this.on("drain",this[an]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:t=>!0,this[ye]=new Nh,this[Ae]=0,this.jobs=+e.jobs||4,this[os]=!1,this[ns]=!1}[Ju](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[ns]=!0,this[Dt](),this}write(e){if(this[ns])throw new Error("write after end");return e instanceof Th?this[Wu](e):this[as](e),this.flowing}[Wu](e){let t=cn(Hu.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let s=new hs(e.path,t,!1);s.entry=new Lh(e,this[un](s)),s.entry.on("end",i=>this[on](s)),this[Ae]+=1,this[ye].push(s)}this[Dt]()}[as](e){let t=cn(Hu.resolve(this.cwd,e));this[ye].push(new hs(e,t)),this[Dt]()}[ln](e){e.pending=!0,this[Ae]+=1;let t=this.follow?"stat":"lstat";fs[t](e.absolute,(s,i)=>{e.pending=!1,this[Ae]-=1,s?this.emit("error",s):this[us](e,i)})}[us](e,t){this.statCache.set(e.absolute,t),e.stat=t,this.filter(e.path,t)||(e.ignore=!0),this[Dt]()}[hn](e){e.pending=!0,this[Ae]+=1,fs.readdir(e.absolute,(t,s)=>{if(e.pending=!1,this[Ae]-=1,t)return this.emit("error",t);this[cs](e,s)})}[cs](e,t){this.readdirCache.set(e.absolute,t),e.readdir=t,this[Dt]()}[Dt](){if(!this[os]){this[os]=!0;for(let e=this[ye].head;e!==null&&this[Ae]<this.jobs;e=e.next)if(this[zu](e.value),e.value.ignore){let t=e.next;this[ye].removeNode(e),e.next=t}this[os]=!1,this[ns]&&!this[ye].length&&this[Ae]===0&&(this.zip?this.zip.end($u):(super.write($u),super.end()))}}get[qt](){return this[ye]&&this[ye].head&&this[ye].head.value}[on](e){this[ye].shift(),this[Ae]-=1,this[Dt]()}[zu](e){if(!e.pending){if(e.entry){e===this[qt]&&!e.piped&&this[ls](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[us](e,this.statCache.get(e.absolute)):this[ln](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[cs](e,this.readdirCache.get(e.absolute)):this[hn](e),!e.readdir))){if(e.entry=this[Gu](e),!e.entry){e.ignore=!0;return}e===this[qt]&&!e.piped&&this[ls](e)}}}[un](e){return{onwarn:(t,s,i)=>this.warn(t,s,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[Gu](e){this[Ae]+=1;try{return new this[fn](e.path,this[un](e)).on("end",()=>this[on](e)).on("error",t=>this.emit("error",t))}catch(t){this.emit("error",t)}}[an](){this[qt]&&this[qt].entry&&this[qt].entry.resume()}[ls](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[as](o+i)});let t=e.entry,s=this.zip;s?t.on("data",i=>{s.write(i)||t.pause()}):t.on("data",i=>{super.write(i)||t.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),pn=class extends dn{constructor(e){super(e),this[fn]=Ph}pause(){}resume(){}[ln](e){let t=this.follow?"statSync":"lstatSync";this[us](e,fs[t](e.absolute))}[hn](e,t){this[cs](e,fs.readdirSync(e.absolute))}[ls](e){let t=e.entry,s=this.zip;e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[as](o+i)}),s?t.on("data",i=>{s.write(i)}):t.on("data",i=>{super[Ju](i)})}};dn.Sync=pn;Yu.exports=dn});var Jt=y(Dr=>{"use strict";u();var Mh=Ai(),qh=A("events").EventEmitter,K=A("fs"),En=K.writev;if(!En){let r=process.binding("fs"),e=r.FSReqWrap||r.FSReqCallback;En=(t,s,i,n)=>{let o=(l,c)=>n(l,c,s),a=new e;a.oncomplete=o,r.writeBuffers(t,s,i,a)}}var Gt=Symbol("_autoClose"),fe=Symbol("_close"),dr=Symbol("_ended"),v=Symbol("_fd"),Vu=Symbol("_finished"),Ve=Symbol("_flags"),Dn=Symbol("_flush"),yn=Symbol("_handleChunk"),An=Symbol("_makeBuf"),Es=Symbol("_mode"),ms=Symbol("_needDrain"),zt=Symbol("_onerror"),Ht=Symbol("_onopen"),gn=Symbol("_onread"),jt=Symbol("_onwrite"),Ke=Symbol("_open"),Pe=Symbol("_path"),gt=Symbol("_pos"),Ce=Symbol("_queue"),$t=Symbol("_read"),Ku=Symbol("_readSize"),Ye=Symbol("_reading"),ds=Symbol("_remain"),Zu=Symbol("_size"),Ds=Symbol("_write"),Ut=Symbol("_writing"),gs=Symbol("_defaultFlag"),Wt=Symbol("_errored"),ys=class extends Mh{constructor(e,t){if(t=t||{},super(t),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[Wt]=!1,this[v]=typeof t.fd=="number"?t.fd:null,this[Pe]=e,this[Ku]=t.readSize||16*1024*1024,this[Ye]=!1,this[Zu]=typeof t.size=="number"?t.size:1/0,this[ds]=this[Zu],this[Gt]=typeof t.autoClose=="boolean"?t.autoClose:!0,typeof this[v]=="number"?this[$t]():this[Ke]()}get fd(){return this[v]}get path(){return this[Pe]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[Ke](){K.open(this[Pe],"r",(e,t)=>this[Ht](e,t))}[Ht](e,t){e?this[zt](e):(this[v]=t,this.emit("open",t),this[$t]())}[An](){return Buffer.allocUnsafe(Math.min(this[Ku],this[ds]))}[$t](){if(!this[Ye]){this[Ye]=!0;let e=this[An]();if(e.length===0)return process.nextTick(()=>this[gn](null,0,e));K.read(this[v],e,0,e.length,null,(t,s,i)=>this[gn](t,s,i))}}[gn](e,t,s){this[Ye]=!1,e?this[zt](e):this[yn](t,s)&&this[$t]()}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}[zt](e){this[Ye]=!0,this[fe](),this.emit("error",e)}[yn](e,t){let s=!1;return this[ds]-=e,e>0&&(s=super.write(e<t.length?t.slice(0,e):t)),(e===0||this[ds]<=0)&&(s=!1,this[fe](),super.end()),s}emit(e,t){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[v]=="number"&&this[$t]();break;case"error":return this[Wt]?void 0:(this[Wt]=!0,super.emit(e,t));default:return super.emit(e,t)}}},Cn=class extends ys{[Ke](){let e=!0;try{this[Ht](null,K.openSync(this[Pe],"r")),e=!1}finally{e&&this[fe]()}}[$t](){let e=!0;try{if(!this[Ye]){this[Ye]=!0;do{let t=this[An](),s=t.length===0?0:K.readSync(this[v],t,0,t.length,null);if(!this[yn](s,t))break}while(!0);this[Ye]=!1}e=!1}finally{e&&this[fe]()}}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.closeSync(e),this.emit("close")}}},As=class extends qh{constructor(e,t){t=t||{},super(t),this.readable=!1,this.writable=!0,this[Wt]=!1,this[Ut]=!1,this[dr]=!1,this[ms]=!1,this[Ce]=[],this[Pe]=e,this[v]=typeof t.fd=="number"?t.fd:null,this[Es]=t.mode===void 0?438:t.mode,this[gt]=typeof t.start=="number"?t.start:null,this[Gt]=typeof t.autoClose=="boolean"?t.autoClose:!0;let s=this[gt]!==null?"r+":"w";this[gs]=t.flags===void 0,this[Ve]=this[gs]?s:t.flags,this[v]===null&&this[Ke]()}emit(e,t){if(e==="error"){if(this[Wt])return;this[Wt]=!0}return super.emit(e,t)}get fd(){return this[v]}get path(){return this[Pe]}[zt](e){this[fe](),this[Ut]=!0,this.emit("error",e)}[Ke](){K.open(this[Pe],this[Ve],this[Es],(e,t)=>this[Ht](e,t))}[Ht](e,t){this[gs]&&this[Ve]==="r+"&&e&&e.code==="ENOENT"?(this[Ve]="w",this[Ke]()):e?this[zt](e):(this[v]=t,this.emit("open",t),this[Dn]())}end(e,t){return e&&this.write(e,t),this[dr]=!0,!this[Ut]&&!this[Ce].length&&typeof this[v]=="number"&&this[jt](null,0),this}write(e,t){return typeof e=="string"&&(e=Buffer.from(e,t)),this[dr]?(this.emit("error",new Error("write() after end()")),!1):this[v]===null||this[Ut]||this[Ce].length?(this[Ce].push(e),this[ms]=!0,!1):(this[Ut]=!0,this[Ds](e),!0)}[Ds](e){K.write(this[v],e,0,e.length,this[gt],(t,s)=>this[jt](t,s))}[jt](e,t){e?this[zt](e):(this[gt]!==null&&(this[gt]+=t),this[Ce].length?this[Dn]():(this[Ut]=!1,this[dr]&&!this[Vu]?(this[Vu]=!0,this[fe](),this.emit("finish")):this[ms]&&(this[ms]=!1,this.emit("drain"))))}[Dn](){if(this[Ce].length===0)this[dr]&&this[jt](null,0);else if(this[Ce].length===1)this[Ds](this[Ce].pop());else{let e=this[Ce];this[Ce]=[],En(this[v],e,this[gt],(t,s)=>this[jt](t,s))}}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}},wn=class extends As{[Ke](){let e;if(this[gs]&&this[Ve]==="r+")try{e=K.openSync(this[Pe],this[Ve],this[Es])}catch(t){if(t.code==="ENOENT")return this[Ve]="w",this[Ke]();throw t}else e=K.openSync(this[Pe],this[Ve],this[Es]);this[Ht](null,e)}[fe](){if(this[Gt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.closeSync(e),this.emit("close")}}[Ds](e){let t=!0;try{this[jt](null,K.writeSync(this[v],e,0,e.length,this[gt])),t=!1}finally{if(t)try{this[fe]()}catch{}}}};Dr.ReadStream=ys;Dr.ReadStreamSync=Cn;Dr.WriteStream=As;Dr.WriteStreamSync=wn});var _s=y((A0,na)=>{"use strict";u();var Uh=Qr(),jh=It(),$h=A("events"),zh=nn(),Wh=1024*1024,Gh=Kr(),Xu=Xr(),Hh=Ni(),{nextTick:Jh}=A("process"),Fn=Buffer.from([31,139]),se=Symbol("state"),Et=Symbol("writeEntry"),Le=Symbol("readEntry"),bn=Symbol("nextEntry"),Qu=Symbol("processEntry"),ie=Symbol("extendedHeader"),gr=Symbol("globalExtendedHeader"),Ze=Symbol("meta"),ea=Symbol("emitMeta"),T=Symbol("buffer"),Ne=Symbol("queue"),yt=Symbol("ended"),ta=Symbol("emittedEnd"),At=Symbol("emit"),Z=Symbol("unzip"),Cs=Symbol("consumeChunk"),ws=Symbol("consumeChunkSub"),Sn=Symbol("consumeBody"),ra=Symbol("consumeMeta"),sa=Symbol("consumeHeader"),Fs=Symbol("consuming"),kn=Symbol("bufferConcat"),_n=Symbol("maybeEnd"),Er=Symbol("writing"),Xe=Symbol("aborted"),bs=Symbol("onDone"),Ct=Symbol("sawValidEntry"),Ss=Symbol("sawNullBlock"),ks=Symbol("sawEOF"),ia=Symbol("closeStream"),Yh=r=>!0;na.exports=Uh(class extends $h{constructor(e){e=e||{},super(e),this.file=e.file||"",this[Ct]=null,this.on(bs,t=>{(this[se]==="begin"||this[Ct]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(bs,e.ondone):this.on(bs,t=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||Wh,this.filter=typeof e.filter=="function"?e.filter:Yh,this.writable=!0,this.readable=!1,this[Ne]=new zh,this[T]=null,this[Le]=null,this[Et]=null,this[se]="begin",this[Ze]="",this[ie]=null,this[gr]=null,this[yt]=!1,this[Z]=null,this[Xe]=!1,this[Ss]=!1,this[ks]=!1,this.on("end",()=>this[ia]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[sa](e,t){this[Ct]===null&&(this[Ct]=!1);let s;try{s=new jh(e,t,this[ie],this[gr])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(s.nullBlock)this[Ss]?(this[ks]=!0,this[se]==="begin"&&(this[se]="header"),this[At]("eof")):(this[Ss]=!0,this[At]("nullBlock"));else if(this[Ss]=!1,!s.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:s});else if(!s.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:s});else{let i=s.type;if(/^(Symbolic)?Link$/.test(i)&&!s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:s});else if(!/^(Symbolic)?Link$/.test(i)&&s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:s});else{let n=this[Et]=new Gh(s,this[ie],this[gr]);if(!this[Ct])if(n.remain){let o=()=>{n.invalid||(this[Ct]=!0)};n.on("end",o)}else this[Ct]=!0;n.meta?n.size>this.maxMetaEntrySize?(n.ignore=!0,this[At]("ignoredEntry",n),this[se]="ignore",n.resume()):n.size>0&&(this[Ze]="",n.on("data",o=>this[Ze]+=o),this[se]="meta"):(this[ie]=null,n.ignore=n.ignore||!this.filter(n.path,n),n.ignore?(this[At]("ignoredEntry",n),this[se]=n.remain?"ignore":"header",n.resume()):(n.remain?this[se]="body":(this[se]="header",n.end()),this[Le]?this[Ne].push(n):(this[Ne].push(n),this[bn]())))}}}[ia](){Jh(()=>this.emit("close"))}[Qu](e){let t=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[Le]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",s=>this[bn]()),t=!1)):(this[Le]=null,t=!1),t}[bn](){do;while(this[Qu](this[Ne].shift()));if(!this[Ne].length){let e=this[Le];!e||e.flowing||e.size===e.remain?this[Er]||this.emit("drain"):e.once("drain",s=>this.emit("drain"))}}[Sn](e,t){let s=this[Et],i=s.blockRemain,n=i>=e.length&&t===0?e:e.slice(t,t+i);return s.write(n),s.blockRemain||(this[se]="header",this[Et]=null,s.end()),n.length}[ra](e,t){let s=this[Et],i=this[Sn](e,t);return this[Et]||this[ea](s),i}[At](e,t,s){!this[Ne].length&&!this[Le]?this.emit(e,t,s):this[Ne].push([e,t,s])}[ea](e){switch(this[At]("meta",this[Ze]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[ie]=Xu.parse(this[Ze],this[ie],!1);break;case"GlobalExtendedHeader":this[gr]=Xu.parse(this[Ze],this[gr],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[ie]=this[ie]||Object.create(null),this[ie].path=this[Ze].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[ie]=this[ie]||Object.create(null),this[ie].linkpath=this[Ze].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[Xe]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[Xe])return;if(this[Z]===null&&e){if(this[T]&&(e=Buffer.concat([this[T],e]),this[T]=null),e.length<Fn.length)return this[T]=e,!0;for(let s=0;this[Z]===null&&s<Fn.length;s++)e[s]!==Fn[s]&&(this[Z]=!1);if(this[Z]===null){let s=this[yt];this[yt]=!1,this[Z]=new Hh.Unzip,this[Z].on("data",n=>this[Cs](n)),this[Z].on("error",n=>this.abort(n)),this[Z].on("end",n=>{this[yt]=!0,this[Cs]()}),this[Er]=!0;let i=this[Z][s?"end":"write"](e);return this[Er]=!1,i}}this[Er]=!0,this[Z]?this[Z].write(e):this[Cs](e),this[Er]=!1;let t=this[Ne].length?!1:this[Le]?this[Le].flowing:!0;return!t&&!this[Ne].length&&this[Le].once("drain",s=>this.emit("drain")),t}[kn](e){e&&!this[Xe]&&(this[T]=this[T]?Buffer.concat([this[T],e]):e)}[_n](){if(this[yt]&&!this[ta]&&!this[Xe]&&!this[Fs]){this[ta]=!0;let e=this[Et];if(e&&e.blockRemain){let t=this[T]?this[T].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${t} available)`,{entry:e}),this[T]&&e.write(this[T]),e.end()}this[At](bs)}}[Cs](e){if(this[Fs])this[kn](e);else if(!e&&!this[T])this[_n]();else{if(this[Fs]=!0,this[T]){this[kn](e);let t=this[T];this[T]=null,this[ws](t)}else this[ws](e);for(;this[T]&&this[T].length>=512&&!this[Xe]&&!this[ks];){let t=this[T];this[T]=null,this[ws](t)}this[Fs]=!1}(!this[T]||this[yt])&&this[_n]()}[ws](e){let t=0,s=e.length;for(;t+512<=s&&!this[Xe]&&!this[ks];)switch(this[se]){case"begin":case"header":this[sa](e,t),t+=512;break;case"ignore":case"body":t+=this[Sn](e,t);break;case"meta":t+=this[ra](e,t);break;default:throw new Error("invalid state: "+this[se])}t<s&&(this[T]?this[T]=Buffer.concat([e.slice(t),this[T]]):this[T]=e.slice(t))}end(e){this[Xe]||(this[Z]?this[Z].end(e):(this[yt]=!0,this.write(e)))}})});var Rs=y((C0,ca)=>{"use strict";u();var Vh=vt(),ua=_s(),Yt=A("fs"),Kh=Jt(),oa=A("path"),Rn=Mt();ca.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=Vh(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Xh(s,e),s.noResume||Zh(s),s.file&&s.sync?Qh(s):s.file?ef(s,t):aa(s)};var Zh=r=>{let e=r.onentry;r.onentry=e?t=>{e(t),t.resume()}:t=>t.resume()},Xh=(r,e)=>{let t=new Map(e.map(n=>[Rn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||oa.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(oa.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(Rn(n)):n=>i(Rn(n))},Qh=r=>{let e=aa(r),t=r.file,s=!0,i;try{let n=Yt.statSync(t),o=r.maxReadSize||16*1024*1024;if(n.size<o)e.end(Yt.readFileSync(t));else{let a=0,l=Buffer.allocUnsafe(o);for(i=Yt.openSync(t,"r");a<n.size;){let c=Yt.readSync(i,l,0,o,a);a+=c,e.write(l.slice(0,c))}e.end()}s=!1}finally{if(s&&i)try{Yt.closeSync(i)}catch{}}},ef=(r,e)=>{let t=new ua(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("end",o),Yt.stat(i,(l,c)=>{if(l)a(l);else{let h=new Kh.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},aa=r=>new ua(r)});var da=y((w0,ma)=>{"use strict";u();var tf=vt(),Bs=ps(),la=Jt(),ha=Rs(),fa=A("path");ma.exports=(r,e,t)=>{if(typeof e=="function"&&(t=e),Array.isArray(r)&&(e=r,r={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let s=tf(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return s.file&&s.sync?rf(s,e):s.file?sf(s,e,t):s.sync?nf(s,e):of(s,e)};var rf=(r,e)=>{let t=new Bs.Sync(r),s=new la.WriteStreamSync(r.file,{mode:r.mode||438});t.pipe(s),pa(t,e)},sf=(r,e,t)=>{let s=new Bs(r),i=new la.WriteStream(r.file,{mode:r.mode||438});s.pipe(i);let n=new Promise((o,a)=>{i.on("error",a),i.on("close",o),s.on("error",a)});return Bn(s,e),t?n.then(t,t):n},pa=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?ha({file:fa.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Bn=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return ha({file:fa.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Bn(r,e));r.add(t)}r.end()},nf=(r,e)=>{let t=new Bs.Sync(r);return pa(t,e),t},of=(r,e)=>{let t=new Bs(r);return Bn(t,e),t}});var vn=y((F0,wa)=>{"use strict";u();var uf=vt(),Da=ps(),ee=A("fs"),ga=Jt(),Ea=Rs(),ya=A("path"),Aa=It();wa.exports=(r,e,t)=>{let s=uf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),s.sync?af(s,e):lf(s,e,t)};var af=(r,e)=>{let t=new Da.Sync(r),s=!0,i,n;try{try{i=ee.openSync(r.file,"r+")}catch(l){if(l.code==="ENOENT")i=ee.openSync(r.file,"w+");else throw l}let o=ee.fstatSync(i),a=Buffer.alloc(512);e:for(n=0;n<o.size;n+=512){for(let h=0,d=0;h<512;h+=d){if(d=ee.readSync(i,a,h,a.length-h,n+h),n===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!d)break e}let l=new Aa(a);if(!l.cksumValid)break;let c=512*Math.ceil(l.size/512);if(n+c+512>o.size)break;n+=c,r.mtimeCache&&r.mtimeCache.set(l.path,l.mtime)}s=!1,cf(r,t,n,i,e)}finally{if(s)try{ee.closeSync(i)}catch{}}},cf=(r,e,t,s,i)=>{let n=new ga.WriteStreamSync(r.file,{fd:s,start:t});e.pipe(n),hf(e,i)},lf=(r,e,t)=>{e=Array.from(e);let s=new Da(r),i=(o,a,l)=>{let c=(S,P)=>{S?ee.close(o,k=>l(S)):l(null,P)},h=0;if(a===0)return c(null,0);let d=0,g=Buffer.alloc(512),w=(S,P)=>{if(S)return c(S);if(d+=P,d<512&&P)return ee.read(o,g,d,g.length-d,h+d,w);if(h===0&&g[0]===31&&g[1]===139)return c(new Error("cannot append to compressed archives"));if(d<512)return c(null,h);let k=new Aa(g);if(!k.cksumValid)return c(null,h);let O=512*Math.ceil(k.size/512);if(h+O+512>a||(h+=O+512,h>=a))return c(null,h);r.mtimeCache&&r.mtimeCache.set(k.path,k.mtime),d=0,ee.read(o,g,0,512,h,w)};ee.read(o,g,0,512,h,w)},n=new Promise((o,a)=>{s.on("error",a);let l="r+",c=(h,d)=>{if(h&&h.code==="ENOENT"&&l==="r+")return l="w+",ee.open(r.file,l,c);if(h)return a(h);ee.fstat(d,(g,w)=>{if(g)return ee.close(d,()=>a(g));i(d,w.size,(S,P)=>{if(S)return a(S);let k=new ga.WriteStream(r.file,{fd:d,start:P});s.pipe(k),k.on("error",a),k.on("close",o),Ca(s,e)})})};ee.open(r.file,l,c)});return t?n.then(t,t):n},hf=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?Ea({file:ya.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Ca=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return Ea({file:ya.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Ca(r,e));r.add(t)}r.end()}});var ba=y((b0,Fa)=>{"use strict";u();var ff=vt(),pf=vn();Fa.exports=(r,e,t)=>{let s=ff(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),mf(s),pf(s,e,t)};var mf=r=>{let e=r.filter;r.mtimeCache||(r.mtimeCache=new Map),r.filter=e?(t,s)=>e(t,s)&&!(r.mtimeCache.get(t)>s.mtime):(t,s)=>!(r.mtimeCache.get(t)>s.mtime)}});var _a=y((S0,ka)=>{u();var{promisify:Sa}=A("util"),Qe=A("fs"),df=r=>{if(!r)r={mode:511,fs:Qe};else if(typeof r=="object")r={mode:511,fs:Qe,...r};else if(typeof r=="number")r={mode:r,fs:Qe};else if(typeof r=="string")r={mode:parseInt(r,8),fs:Qe};else throw new TypeError("invalid options argument");return r.mkdir=r.mkdir||r.fs.mkdir||Qe.mkdir,r.mkdirAsync=Sa(r.mkdir),r.stat=r.stat||r.fs.stat||Qe.stat,r.statAsync=Sa(r.stat),r.statSync=r.statSync||r.fs.statSync||Qe.statSync,r.mkdirSync=r.mkdirSync||r.fs.mkdirSync||Qe.mkdirSync,r};ka.exports=df});var Ba=y((k0,Ra)=>{u();var Df=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:gf,parse:Ef}=A("path"),yf=r=>{if(/\0/.test(r))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:r,code:"ERR_INVALID_ARG_VALUE"});if(r=gf(r),Df==="win32"){let e=/[*|"<>?:]/,{root:t}=Ef(r);if(e.test(r.substr(t.length)))throw Object.assign(new Error("Illegal characters in path."),{path:r,code:"EINVAL"})}return r};Ra.exports=yf});var Pa=y((_0,Ta)=>{u();var{dirname:va}=A("path"),xa=(r,e,t=void 0)=>t===e?Promise.resolve():r.statAsync(e).then(s=>s.isDirectory()?t:void 0,s=>s.code==="ENOENT"?xa(r,va(e),e):void 0),Oa=(r,e,t=void 0)=>{if(t!==e)try{return r.statSync(e).isDirectory()?t:void 0}catch(s){return s.code==="ENOENT"?Oa(r,va(e),e):void 0}};Ta.exports={findMade:xa,findMadeSync:Oa}});var Tn=y((R0,Na)=>{u();var{dirname:La}=A("path"),xn=(r,e,t)=>{e.recursive=!1;let s=La(r);return s===r?e.mkdirAsync(r,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(r,e).then(()=>t||r,i=>{if(i.code==="ENOENT")return xn(s,e).then(n=>xn(r,e,n));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(r).then(n=>{if(n.isDirectory())return t;throw i},()=>{throw i})})},On=(r,e,t)=>{let s=La(r);if(e.recursive=!1,s===r)try{return e.mkdirSync(r,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(r,e),t||r}catch(i){if(i.code==="ENOENT")return On(r,e,On(s,e,t));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(r).isDirectory())throw i}catch{throw i}}};Na.exports={mkdirpManual:xn,mkdirpManualSync:On}});var qa=y((B0,Ma)=>{u();var{dirname:Ia}=A("path"),{findMade:Af,findMadeSync:Cf}=Pa(),{mkdirpManual:wf,mkdirpManualSync:Ff}=Tn(),bf=(r,e)=>(e.recursive=!0,Ia(r)===r?e.mkdirAsync(r,e):Af(e,r).then(s=>e.mkdirAsync(r,e).then(()=>s).catch(i=>{if(i.code==="ENOENT")return wf(r,e);throw i}))),Sf=(r,e)=>{if(e.recursive=!0,Ia(r)===r)return e.mkdirSync(r,e);let s=Cf(e,r);try{return e.mkdirSync(r,e),s}catch(i){if(i.code==="ENOENT")return Ff(r,e);throw i}};Ma.exports={mkdirpNative:bf,mkdirpNativeSync:Sf}});var za=y((v0,$a)=>{u();var Ua=A("fs"),kf=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,Pn=kf.replace(/^v/,"").split("."),ja=+Pn[0]>10||+Pn[0]==10&&+Pn[1]>=12,_f=ja?r=>r.mkdir===Ua.mkdir:()=>!1,Rf=ja?r=>r.mkdirSync===Ua.mkdirSync:()=>!1;$a.exports={useNative:_f,useNativeSync:Rf}});var Va=y((x0,Ya)=>{u();var Vt=_a(),Kt=Ba(),{mkdirpNative:Wa,mkdirpNativeSync:Ga}=qa(),{mkdirpManual:Ha,mkdirpManualSync:Ja}=Tn(),{useNative:Bf,useNativeSync:vf}=za(),Zt=(r,e)=>(r=Kt(r),e=Vt(e),Bf(e)?Wa(r,e):Ha(r,e)),xf=(r,e)=>(r=Kt(r),e=Vt(e),vf(e)?Ga(r,e):Ja(r,e));Zt.sync=xf;Zt.native=(r,e)=>Wa(Kt(r),Vt(e));Zt.manual=(r,e)=>Ha(Kt(r),Vt(e));Zt.nativeSync=(r,e)=>Ga(Kt(r),Vt(e));Zt.manualSync=(r,e)=>Ja(Kt(r),Vt(e));Ya.exports=Zt});var rc=y((O0,tc)=>{"use strict";u();var ne=A("fs"),wt=A("path"),Of=ne.lchown?"lchown":"chown",Tf=ne.lchownSync?"lchownSync":"chownSync",Za=ne.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),Ka=(r,e,t)=>{try{return ne[Tf](r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Pf=(r,e,t)=>{try{return ne.chownSync(r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Lf=Za?(r,e,t,s)=>i=>{!i||i.code!=="EISDIR"?s(i):ne.chown(r,e,t,s)}:(r,e,t,s)=>s,Ln=Za?(r,e,t)=>{try{return Ka(r,e,t)}catch(s){if(s.code!=="EISDIR")throw s;Pf(r,e,t)}}:(r,e,t)=>Ka(r,e,t),Nf=process.version,Xa=(r,e,t)=>ne.readdir(r,e,t),If=(r,e)=>ne.readdirSync(r,e);/^v4\./.test(Nf)&&(Xa=(r,e,t)=>ne.readdir(r,t));var vs=(r,e,t,s)=>{ne[Of](r,e,t,Lf(r,e,t,i=>{s(i&&i.code!=="ENOENT"?i:null)}))},Qa=(r,e,t,s,i)=>{if(typeof e=="string")return ne.lstat(wt.resolve(r,e),(n,o)=>{if(n)return i(n.code!=="ENOENT"?n:null);o.name=e,Qa(r,o,t,s,i)});if(e.isDirectory())Nn(wt.resolve(r,e.name),t,s,n=>{if(n)return i(n);let o=wt.resolve(r,e.name);vs(o,t,s,i)});else{let n=wt.resolve(r,e.name);vs(n,t,s,i)}},Nn=(r,e,t,s)=>{Xa(r,{withFileTypes:!0},(i,n)=>{if(i){if(i.code==="ENOENT")return s();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return s(i)}if(i||!n.length)return vs(r,e,t,s);let o=n.length,a=null,l=c=>{if(!a){if(c)return s(a=c);if(--o===0)return vs(r,e,t,s)}};n.forEach(c=>Qa(r,c,e,t,l))})},Mf=(r,e,t,s)=>{if(typeof e=="string")try{let i=ne.lstatSync(wt.resolve(r,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&ec(wt.resolve(r,e.name),t,s),Ln(wt.resolve(r,e.name),t,s)},ec=(r,e,t)=>{let s;try{s=If(r,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Ln(r,e,t);throw i}return s&&s.length&&s.forEach(i=>Mf(r,i,e,t)),Ln(r,e,t)};tc.exports=Nn;Nn.sync=ec});var oc=y((T0,In)=>{"use strict";u();var sc=Va(),oe=A("fs"),xs=A("path"),ic=rc(),pe=Lt(),Os=class extends Error{constructor(e,t){super("Cannot extract through symbolic link"),this.path=t,this.symlink=e}get name(){return"SylinkError"}},Ts=class extends Error{constructor(e,t){super(t+": Cannot cd into '"+e+"'"),this.path=e,this.code=t}get name(){return"CwdError"}},Ps=(r,e)=>r.get(pe(e)),yr=(r,e,t)=>r.set(pe(e),t),qf=(r,e)=>{oe.stat(r,(t,s)=>{(t||!s.isDirectory())&&(t=new Ts(r,t&&t.code||"ENOTDIR")),e(t)})};In.exports=(r,e,t)=>{r=pe(r);let s=e.umask,i=e.mode|448,n=(i&s)!==0,o=e.uid,a=e.gid,l=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,h=e.unlink,d=e.cache,g=pe(e.cwd),w=(k,O)=>{k?t(k):(yr(d,r,!0),O&&l?ic(O,o,a,ot=>w(ot)):n?oe.chmod(r,i,t):t())};if(d&&Ps(d,r)===!0)return w();if(r===g)return qf(r,w);if(c)return sc(r,{mode:i}).then(k=>w(null,k),w);let P=pe(xs.relative(g,r)).split("/");Ls(g,P,i,d,h,g,null,w)};var Ls=(r,e,t,s,i,n,o,a)=>{if(!e.length)return a(null,o);let l=e.shift(),c=pe(xs.resolve(r+"/"+l));if(Ps(s,c))return Ls(c,e,t,s,i,n,o,a);oe.mkdir(c,t,nc(c,e,t,s,i,n,o,a))},nc=(r,e,t,s,i,n,o,a)=>l=>{l?oe.lstat(r,(c,h)=>{if(c)c.path=c.path&&pe(c.path),a(c);else if(h.isDirectory())Ls(r,e,t,s,i,n,o,a);else if(i)oe.unlink(r,d=>{if(d)return a(d);oe.mkdir(r,t,nc(r,e,t,s,i,n,o,a))});else{if(h.isSymbolicLink())return a(new Os(r,r+"/"+e.join("/")));a(l)}}):(o=o||r,Ls(r,e,t,s,i,n,o,a))},Uf=r=>{let e=!1,t="ENOTDIR";try{e=oe.statSync(r).isDirectory()}catch(s){t=s.code}finally{if(!e)throw new Ts(r,t)}};In.exports.sync=(r,e)=>{r=pe(r);let t=e.umask,s=e.mode|448,i=(s&t)!==0,n=e.uid,o=e.gid,a=typeof n=="number"&&typeof o=="number"&&(n!==e.processUid||o!==e.processGid),l=e.preserve,c=e.unlink,h=e.cache,d=pe(e.cwd),g=k=>{yr(h,r,!0),k&&a&&ic.sync(k,n,o),i&&oe.chmodSync(r,s)};if(h&&Ps(h,r)===!0)return g();if(r===d)return Uf(d),g();if(l)return g(sc.sync(r,s));let S=pe(xs.relative(d,r)).split("/"),P=null;for(let k=S.shift(),O=d;k&&(O+="/"+k);k=S.shift())if(O=pe(xs.resolve(O)),!Ps(h,O))try{oe.mkdirSync(O,s),P=P||O,yr(h,O,!0)}catch{let me=oe.lstatSync(O);if(me.isDirectory()){yr(h,O,!0);continue}else if(c){oe.unlinkSync(O),oe.mkdirSync(O,s),P=P||O,yr(h,O,!0);continue}else if(me.isSymbolicLink())return new Os(O,O+"/"+S.join("/"))}return g(P)}});var qn=y((P0,uc)=>{u();var Mn=Object.create(null),{hasOwnProperty:jf}=Object.prototype;uc.exports=r=>(jf.call(Mn,r)||(Mn[r]=r.normalize("NFKD")),Mn[r])});var hc=y((L0,lc)=>{u();var ac=A("assert"),$f=qn(),zf=Mt(),{join:cc}=A("path"),Wf=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Gf=Wf==="win32";lc.exports=()=>{let r=new Map,e=new Map,t=c=>c.split("/").slice(0,-1).reduce((d,g)=>(d.length&&(g=cc(d[d.length-1],g)),d.push(g||"/"),d),[]),s=new Set,i=c=>{let h=e.get(c);if(!h)throw new Error("function does not have any path reservations");return{paths:h.paths.map(d=>r.get(d)),dirs:[...h.dirs].map(d=>r.get(d))}},n=c=>{let{paths:h,dirs:d}=i(c);return h.every(g=>g[0]===c)&&d.every(g=>g[0]instanceof Set&&g[0].has(c))},o=c=>s.has(c)||!n(c)?!1:(s.add(c),c(()=>a(c)),!0),a=c=>{if(!s.has(c))return!1;let{paths:h,dirs:d}=e.get(c),g=new Set;return h.forEach(w=>{let S=r.get(w);ac.equal(S[0],c),S.length===1?r.delete(w):(S.shift(),typeof S[0]=="function"?g.add(S[0]):S[0].forEach(P=>g.add(P)))}),d.forEach(w=>{let S=r.get(w);ac(S[0]instanceof Set),S[0].size===1&&S.length===1?r.delete(w):S[0].size===1?(S.shift(),g.add(S[0])):S[0].delete(c)}),s.delete(c),g.forEach(w=>o(w)),!0};return{check:n,reserve:(c,h)=>{c=Gf?["win32 parallelization disabled"]:c.map(g=>$f(zf(cc(g))).toLowerCase());let d=new Set(c.map(g=>t(g)).reduce((g,w)=>g.concat(w)));return e.set(h,{dirs:d,paths:c}),c.forEach(g=>{let w=r.get(g);w?w.push(h):r.set(g,[h])}),d.forEach(g=>{let w=r.get(g);w?w[w.length-1]instanceof Set?w[w.length-1].add(h):w.push(new Set([h])):r.set(g,[new Set([h])])}),o(h)}}}});var mc=y((N0,pc)=>{u();var Hf=process.env.__FAKE_PLATFORM__||process.platform,Jf=Hf==="win32",Yf=global.__FAKE_TESTING_FS__||A("fs"),{O_CREAT:Vf,O_TRUNC:Kf,O_WRONLY:Zf,UV_FS_O_FILEMAP:fc=0}=Yf.constants,Xf=Jf&&!!fc,Qf=512*1024,ep=fc|Kf|Vf|Zf;pc.exports=Xf?r=>r<Qf?ep:"w":()=>"w"});var Yn=y((I0,Rc)=>{"use strict";u();var tp=A("assert"),rp=_s(),R=A("fs"),sp=Jt(),Ie=A("path"),Sc=oc(),dc=Hi(),ip=hc(),np=Ji(),te=Lt(),op=Mt(),up=qn(),Dc=Symbol("onEntry"),$n=Symbol("checkFs"),gc=Symbol("checkFs2"),Ms=Symbol("pruneCache"),zn=Symbol("isReusable"),ue=Symbol("makeFs"),Wn=Symbol("file"),Gn=Symbol("directory"),qs=Symbol("link"),Ec=Symbol("symlink"),yc=Symbol("hardlink"),Ac=Symbol("unsupported"),Cc=Symbol("checkPath"),et=Symbol("mkdir"),G=Symbol("onError"),Ns=Symbol("pending"),wc=Symbol("pend"),Xt=Symbol("unpend"),Un=Symbol("ended"),jn=Symbol("maybeClose"),Hn=Symbol("skip"),Ar=Symbol("doChown"),Cr=Symbol("uid"),wr=Symbol("gid"),Fr=Symbol("checkedCwd"),kc=A("crypto"),_c=mc(),ap=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,br=ap==="win32",cp=(r,e)=>{if(!br)return R.unlink(r,e);let t=r+".DELETE."+kc.randomBytes(16).toString("hex");R.rename(r,t,s=>{if(s)return e(s);R.unlink(t,e)})},lp=r=>{if(!br)return R.unlinkSync(r);let e=r+".DELETE."+kc.randomBytes(16).toString("hex");R.renameSync(r,e),R.unlinkSync(e)},Fc=(r,e,t)=>r===r>>>0?r:e===e>>>0?e:t,bc=r=>up(op(te(r))).toLowerCase(),hp=(r,e)=>{e=bc(e);for(let t of r.keys()){let s=bc(t);(s===e||s.indexOf(e+"/")===0)&&r.delete(t)}},fp=r=>{for(let e of r.keys())r.delete(e)},Sr=class extends rp{constructor(e){if(e||(e={}),e.ondone=t=>{this[Un]=!0,this[jn]()},super(e),this[Fr]=!1,this.reservations=ip(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[Ns]=0,this[Un]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||br,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=te(Ie.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",t=>this[Dc](t))}warn(e,t,s={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(s.recoverable=!1),super.warn(e,t,s)}[jn](){this[Un]&&this[Ns]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[Cc](e){if(this.strip){let t=te(e.path).split("/");if(t.length<this.strip)return!1;if(e.path=t.slice(this.strip).join("/"),e.type==="Link"){let s=te(e.linkpath).split("/");if(s.length>=this.strip)e.linkpath=s.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let t=te(e.path),s=t.split("/");if(s.includes("..")||br&&/^[a-z]:\.\.$/i.test(s[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:t}),!1;let[i,n]=np(t);i&&(e.path=n,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:t}))}if(Ie.isAbsolute(e.path)?e.absolute=te(Ie.resolve(e.path)):e.absolute=te(Ie.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:te(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:t}=Ie.win32.parse(e.absolute);e.absolute=t+dc.encode(e.absolute.slice(t.length));let{root:s}=Ie.win32.parse(e.path);e.path=s+dc.encode(e.path.slice(s.length))}return!0}[Dc](e){if(!this[Cc](e))return e.resume();switch(tp.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[$n](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[Ac](e)}}[G](e,t){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:t}),this[Xt](),t.resume())}[et](e,t,s){Sc(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t,noChmod:this.noChmod},s)}[Ar](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[Cr](e){return Fc(this.uid,e.uid,this.processUid)}[wr](e){return Fc(this.gid,e.gid,this.processGid)}[Wn](e,t){let s=e.mode&4095||this.fmode,i=new sp.WriteStream(e.absolute,{flags:_c(e.size),mode:s,autoClose:!1});i.on("error",l=>{i.fd&&R.close(i.fd,()=>{}),i.write=()=>!0,this[G](l,e),t()});let n=1,o=l=>{if(l){i.fd&&R.close(i.fd,()=>{}),this[G](l,e),t();return}--n===0&&R.close(i.fd,c=>{c?this[G](c,e):this[Xt](),t()})};i.on("finish",l=>{let c=e.absolute,h=i.fd;if(e.mtime&&!this.noMtime){n++;let d=e.atime||new Date,g=e.mtime;R.futimes(h,d,g,w=>w?R.utimes(c,d,g,S=>o(S&&w)):o())}if(this[Ar](e)){n++;let d=this[Cr](e),g=this[wr](e);R.fchown(h,d,g,w=>w?R.chown(c,d,g,S=>o(S&&w)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",l=>{this[G](l,e),t()}),e.pipe(a)),a.pipe(i)}[Gn](e,t){let s=e.mode&4095||this.dmode;this[et](e.absolute,s,i=>{if(i){this[G](i,e),t();return}let n=1,o=a=>{--n===0&&(t(),this[Xt](),e.resume())};e.mtime&&!this.noMtime&&(n++,R.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[Ar](e)&&(n++,R.chown(e.absolute,this[Cr](e),this[wr](e),o)),o()})}[Ac](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[Ec](e,t){this[qs](e,e.linkpath,"symlink",t)}[yc](e,t){let s=te(Ie.resolve(this.cwd,e.linkpath));this[qs](e,s,"link",t)}[wc](){this[Ns]++}[Xt](){this[Ns]--,this[jn]()}[Hn](e){this[Xt](),e.resume()}[zn](e,t){return e.type==="File"&&!this.unlink&&t.isFile()&&t.nlink<=1&&!br}[$n](e){this[wc]();let t=[e.path];e.linkpath&&t.push(e.linkpath),this.reservations.reserve(t,s=>this[gc](e,s))}[Ms](e){e.type==="SymbolicLink"?fp(this.dirCache):e.type!=="Directory"&&hp(this.dirCache,e.absolute)}[gc](e,t){this[Ms](e);let s=a=>{this[Ms](e),t(a)},i=()=>{this[et](this.cwd,this.dmode,a=>{if(a){this[G](a,e),s();return}this[Fr]=!0,n()})},n=()=>{if(e.absolute!==this.cwd){let a=te(Ie.dirname(e.absolute));if(a!==this.cwd)return this[et](a,this.dmode,l=>{if(l){this[G](l,e),s();return}o()})}o()},o=()=>{R.lstat(e.absolute,(a,l)=>{if(l&&(this.keep||this.newer&&l.mtime>e.mtime)){this[Hn](e),s();return}if(a||this[zn](e,l))return this[ue](null,e,s);if(l.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(l.mode&4095)!==e.mode,h=d=>this[ue](d,e,s);return c?R.chmod(e.absolute,e.mode,h):h()}if(e.absolute!==this.cwd)return R.rmdir(e.absolute,c=>this[ue](c,e,s))}if(e.absolute===this.cwd)return this[ue](null,e,s);cp(e.absolute,c=>this[ue](c,e,s))})};this[Fr]?n():i()}[ue](e,t,s){if(e){this[G](e,t),s();return}switch(t.type){case"File":case"OldFile":case"ContiguousFile":return this[Wn](t,s);case"Link":return this[yc](t,s);case"SymbolicLink":return this[Ec](t,s);case"Directory":case"GNUDumpDir":return this[Gn](t,s)}}[qs](e,t,s,i){R[s](t,e.absolute,n=>{n?this[G](n,e):(this[Xt](),e.resume()),i()})}},Is=r=>{try{return[null,r()]}catch(e){return[e,null]}},Jn=class extends Sr{[ue](e,t){return super[ue](e,t,()=>{})}[$n](e){if(this[Ms](e),!this[Fr]){let n=this[et](this.cwd,this.dmode);if(n)return this[G](n,e);this[Fr]=!0}if(e.absolute!==this.cwd){let n=te(Ie.dirname(e.absolute));if(n!==this.cwd){let o=this[et](n,this.dmode);if(o)return this[G](o,e)}}let[t,s]=Is(()=>R.lstatSync(e.absolute));if(s&&(this.keep||this.newer&&s.mtime>e.mtime))return this[Hn](e);if(t||this[zn](e,s))return this[ue](null,e);if(s.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(s.mode&4095)!==e.mode,[a]=o?Is(()=>{R.chmodSync(e.absolute,e.mode)}):[];return this[ue](a,e)}let[n]=Is(()=>R.rmdirSync(e.absolute));this[ue](n,e)}let[i]=e.absolute===this.cwd?[]:Is(()=>lp(e.absolute));this[ue](i,e)}[Wn](e,t){let s=e.mode&4095||this.fmode,i=a=>{let l;try{R.closeSync(n)}catch(c){l=c}(a||l)&&this[G](a||l,e),t()},n;try{n=R.openSync(e.absolute,_c(e.size),s)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[G](a,e)),e.pipe(o)),o.on("data",a=>{try{R.writeSync(n,a,0,a.length)}catch(l){i(l)}}),o.on("end",a=>{let l=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,h=e.mtime;try{R.futimesSync(n,c,h)}catch(d){try{R.utimesSync(e.absolute,c,h)}catch{l=d}}}if(this[Ar](e)){let c=this[Cr](e),h=this[wr](e);try{R.fchownSync(n,c,h)}catch(d){try{R.chownSync(e.absolute,c,h)}catch{l=l||d}}}i(l)})}[Gn](e,t){let s=e.mode&4095||this.dmode,i=this[et](e.absolute,s);if(i){this[G](i,e),t();return}if(e.mtime&&!this.noMtime)try{R.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[Ar](e))try{R.chownSync(e.absolute,this[Cr](e),this[wr](e))}catch{}t(),e.resume()}[et](e,t){try{return Sc.sync(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t})}catch(s){return s}}[qs](e,t,s,i){try{R[s+"Sync"](t,e.absolute),i(),e.resume()}catch(n){return this[G](n,e)}}};Sr.Sync=Jn;Rc.exports=Sr});var Tc=y((M0,Oc)=>{"use strict";u();var pp=vt(),Us=Yn(),vc=A("fs"),xc=Jt(),Bc=A("path"),Vn=Mt();Oc.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=pp(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&mp(s,e),s.file&&s.sync?dp(s):s.file?Dp(s,t):s.sync?gp(s):Ep(s)};var mp=(r,e)=>{let t=new Map(e.map(n=>[Vn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||Bc.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(Bc.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(Vn(n)):n=>i(Vn(n))},dp=r=>{let e=new Us.Sync(r),t=r.file,s=vc.statSync(t),i=r.maxReadSize||16*1024*1024;new xc.ReadStreamSync(t,{readSize:i,size:s.size}).pipe(e)},Dp=(r,e)=>{let t=new Us(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("close",o),vc.stat(i,(l,c)=>{if(l)a(l);else{let h=new xc.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},gp=r=>new Us.Sync(r),Ep=r=>new Us(r)});var Pc=y(I=>{"use strict";u();I.c=I.create=da();I.r=I.replace=vn();I.t=I.list=Rs();I.u=I.update=ba();I.x=I.extract=Tc();I.Pack=ps();I.Unpack=Yn();I.Parse=_s();I.ReadEntry=Kr();I.WriteEntry=sn();I.Header=It();I.Pax=Xr();I.types=qi()});var Nc=y((Y0,Lc)=>{u();function ae(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Lc.exports=ae;ae.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};ae.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};ae.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var s=this;return this._timer=setTimeout(function(){s._attempts++,s._operationTimeoutCb&&(s._timeout=setTimeout(function(){s._operationTimeoutCb(s._attempts)},s._operationTimeout),s._options.unref&&s._timeout.unref()),s._fn(s._attempts)},t),this._options.unref&&this._timer.unref(),!0};ae.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};ae.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};ae.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};ae.prototype.start=ae.prototype.try;ae.prototype.errors=function(){return this._errors};ae.prototype.attempts=function(){return this._attempts};ae.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,s=0;s<this._errors.length;s++){var i=this._errors[s],n=i.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=i,t=o)}return e}});var Ic=y(Ft=>{u();var wp=Nc();Ft.operation=function(r){var e=Ft.timeouts(r);return new wp(e,{forever:r&&(r.forever||r.retries===1/0),unref:r&&r.unref,maxRetryTime:r&&r.maxRetryTime})};Ft.timeouts=function(r){if(r instanceof Array)return[].concat(r);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in r)e[t]=r[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var s=[],i=0;i<e.retries;i++)s.push(this.createTimeout(i,e));return r&&r.forever&&!s.length&&s.push(this.createTimeout(i,e)),s.sort(function(n,o){return n-o}),s};Ft.createTimeout=function(r,e){var t=e.randomize?Math.random()+1:1,s=Math.round(t*Math.max(e.minTimeout,1)*Math.pow(e.factor,r));return s=Math.min(s,e.maxTimeout),s};Ft.wrap=function(r,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var s in r)typeof r[s]=="function"&&t.push(s)}for(var i=0;i<t.length;i++){var n=t[i],o=r[n];r[n]=function(l){var c=Ft.operation(e),h=Array.prototype.slice.call(arguments,1),d=h.pop();h.push(function(g){c.retry(g)||(g&&(arguments[0]=c.mainError()),d.apply(this,arguments))}),c.attempt(function(){l.apply(r,h)})}.bind(r,o),r[n].options=e}}});var qc=y((K0,Mc)=>{u();Mc.exports=Ic()});var jc=y((Z0,Uc)=>{u();var Fp=qc();function bp(r,e){function t(s,i){var n=e||{},o;"randomize"in n||(n.randomize=!0),o=Fp.operation(n);function a(h){i(h||new Error("Aborted"))}function l(h,d){if(h.bail){a(h);return}o.retry(h)?n.onRetry&&n.onRetry(h,d):i(o.mainError())}function c(h){var d;try{d=r(a,h)}catch(g){l(g,h);return}Promise.resolve(d).then(s).catch(function(w){l(w,h)})}o.attempt(c)}return new Promise(t)}Uc.exports=bp});var vr={};ll(vr,{bold:()=>gl,dimmed:()=>yl,error:()=>ho,grey:()=>Al,info:()=>Dl,item:()=>Cl,log:()=>Ue,turboBlue:()=>_t,turboGradient:()=>dl,turboLoader:()=>co,turboRed:()=>uo,underline:()=>El,warn:()=>lo,yellow:()=>ao});u();import ct from"chalk";import fl from"ora";import pl from"gradient-string";var no="#0099F7",oo="#F11712",ml="#FFFF00",dl=pl(no,oo),_t=ct.hex(no),uo=ct.hex(oo),ao=ct.hex(ml),co=r=>fl({text:r,spinner:{frames:["   ",_t(">  "),_t(">> "),_t(">>>")]}}),Dl=(...r)=>{Ue(_t.bold(">>>"),...r)},gl=(...r)=>{Ue(ct.bold(...r))},El=(...r)=>{Ue(ct.underline(...r))},yl=(...r)=>{Ue(ct.dim(...r))},Al=(...r)=>{Ue(ct.grey(...r))},Cl=(...r)=>{Ue(_t.bold("  \u2022"),...r)},Ue=(...r)=>{console.log(...r)},lo=(...r)=>{console.error(ao.bold(">>>"),...r)},ho=(...r)=>{console.error(uo.bold(">>>"),...r)};u();import wl from"os";import Fl from"execa";async function xr(r,e=[],t){let s={cwd:wl.tmpdir(),env:{COREPACK_ENABLE_STRICT:"0"},...t};try{let{stdout:i}=await Fl(r,e,s);return i.trim()}catch{return}}async function bl(){let[r,e,t,s]=await Promise.all([xr("yarnpkg",["--version"]),xr("npm",["--version"]),xr("pnpm",["--version"]),xr("bun",["--version"])]);return{yarn:r,pnpm:t,npm:e,bun:s}}u();u();u();var Tl=Br(Eo());u();u();function Xs(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}u();u();function ir(r){return ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ir(r)}u();function Qs(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function ei(r,e){if(e&&(ir(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Qs(r)}u();function lt(r){return lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},lt(r)}u();u();function be(r,e){return be=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,i){return s.__proto__=i,s},be(r,e)}function ti(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&be(r,e)}u();u();function ri(r){return Function.toString.call(r).indexOf("[native code]")!==-1}u();u();function si(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Rt(r,e,t){return si()?Rt=Reflect.construct.bind():Rt=function(i,n,o){var a=[null];a.push.apply(a,n);var l=Function.bind.apply(i,a),c=new l;return o&&be(c,o.prototype),c},Rt.apply(null,arguments)}function nr(r){var e=typeof Map=="function"?new Map:void 0;return nr=function(s){if(s===null||!ri(s))return s;if(typeof s!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(s))return e.get(s);e.set(s,i)}function i(){return Rt(s,arguments,lt(this).constructor)}return i.prototype=Object.create(s.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),be(i,s)},nr(r)}var No=Br(Lo());import yd from"fs-extra";var Ad=function(r){ti(e,r);function e(t){var s;return Xs(this,e),s=ei(this,lt(e).call(this,"No package.json could be found upwards from the directory ".concat(t))),s.directory=t,s}return e}(nr(Error));u();u();u();import vd from"js-yaml";import{sync as Od}from"fast-glob";u();import Id from"fs-extra";u();import Ud from"fs-extra";import $d from"chalk";u();import{Stream as yp}from"stream";import{promisify as Ap}from"util";var Cp=Br(Pc());var $0=Ap(yp.pipeline);u();import{access as G0,constants as H0}from"fs-extra";u();var xp=Br(jc());import Q0 from"chalk";import{mkdir as tD,readJsonSync as rD,existsSync as sD}from"fs-extra";u();u();u();var B=class extends Error{constructor(t,s){var i;super(t);this.name="ConvertError",this.type=(i=s==null?void 0:s.type)!=null?i:"unknown",Error.captureStackTrace(this,B)}};u();u();import er from"path";import{writeJSONSync as Gc,writeFileSync as zp,existsSync as Xn,rmSync as Qn,rm as Wp}from"fs-extra";import Gp from"execa";u();import qp from"path";import{writeJSONSync as Up}from"fs-extra";import jp from"chalk";u();import tt from"path";import Op from"execa";import{readJsonSync as Tp,existsSync as Kn,readFileSync as Pp,rmSync as $c,writeFile as Lp}from"fs-extra";import{sync as Np}from"fast-glob";import Ip from"js-yaml";var Mp=/^(?!_)(?<manager>.+)@(?<version>.+)$/;function M({workspaceRoot:r}){let e=tt.join(r,"package.json");try{return Tp(e,"utf8")}catch(t){if(t&&typeof t=="object"&&"code"in t){if(t.code==="ENOENT")throw new B(`no "package.json" found at ${r}`,{type:"package_json-missing"});if(t.code==="EJSONPARSE")throw new B(`failed to parse "package.json" at ${r}`,{type:"package_json-parse_error"})}throw new Error(`unexpected error reading "package.json" at ${r}`)}}function rt({workspaceRoot:r}){let{packageManager:e}=M({workspaceRoot:r});if(e)try{let t=Mp.exec(e);if(t){let[s,i]=t;return i}}catch{}}function Me({workspaceRoot:r}){let e=M({workspaceRoot:r}),t=tt.basename(r),{name:s=t,description:i}=e;return{name:s,description:i}}function Zn({workspaceRoot:r}){let e=tt.join(r,"pnpm-workspace.yaml");if(Kn(e))try{let t=Ip.load(Pp(e,"utf8"));if(t instanceof Object&&"packages"in t&&Array.isArray(t.packages))return t.packages}catch{throw new B(`failed to parse ${e}`,{type:"pnpm-workspace_parse_error"})}return[]}function st({root:r,lockFile:e,workspaceConfig:t}){let s=n=>tt.join(r,n),i={root:r,lockfile:s(e),packageJson:s("package.json"),nodeModules:s("node_modules")};return t&&(i.workspaceConfig=s(t)),i}function Qt({workspaces:r}){var e;return r?Array.isArray(r)?r:"packages"in r?(e=r.packages)!=null?e:[]:[]:[]}function it({workspaceRoot:r,workspaceGlobs:e}){return e?e.flatMap(t=>{let s=[`${t}/package.json`];return Np(s,{onlyFiles:!0,absolute:!0,cwd:r,ignore:["**/node_modules/**"]})}).map(t=>{let s=tt.dirname(t),{name:i,description:n}=Me({workspaceRoot:s});return{name:i,description:n,paths:{root:s,packageJson:t,nodeModules:tt.join(s,"node_modules")}}}):[]}function zc({directory:r}){let e=tt.resolve(process.cwd(),r);return{exists:Kn(e),absolute:e}}function ce({packageManager:r,action:e,project:t}){let s=t.workspaceData.globs.length>0;return`${e==="remove"?"Removing":"Adding"} ${r} ${s?"workspaces":""} ${e==="remove"?"from":"to"} ${t.name}`}function Wc({project:r}){let e=t=>!(t.includes("*")&&(t.includes("**")||t.split("/").slice(0,-1).join("/").includes("*"))||["!","[","]","{","}"].some(s=>t.includes(s)));return r.workspaceData.globs.every(e)}function re({project:r,options:e}){e!=null&&e.dry||$c(r.paths.lockfile,{force:!0})}async function js({project:r,options:e}){if(!(e!=null&&e.dry)&&Kn(r.paths.lockfile))try{let{stdout:t}=await Op("bun",["bun.lockb"],{stdin:"ignore",cwd:r.paths.root});await Lp(tt.join(r.paths.root,"yarn.lock"),t)}catch{}finally{$c(r.paths.lockfile,{force:!0})}}function $p({dependencyList:r,project:e,to:t}){let s=[];return e.workspaceData.workspaces.forEach(i=>{let{name:n}=i;if(r[n]){let o=r[n],a=o.startsWith("workspace:")?o.slice(10):o;r[n]=t.name==="pnpm"?`workspace:${a}`:a,s.push(n)}}),{dependencyList:r,updated:s}}function le({project:r,workspace:e,to:t,logger:s,options:i}){if(["yarn","npm","bun"].includes(t.name)&&["yarn","npm","bun"].includes(r.packageManager))return;let n=M({workspaceRoot:e.paths.root}),o={dependencies:[],devDependencies:[],peerDependencies:[],optionalDependencies:[]},a=["dependencies","devDependencies","peerDependencies","optionalDependencies"];a.forEach(d=>{let g=n[d];if(g){let{updated:w,dependencyList:S}=$p({dependencyList:g,project:r,to:t});n[d]=S,o[d]=w}});let l=d=>{let g=o[d].length;if(g>0)return`${jp.green(g.toString())} ${d}`},c=a.map(l).filter(Boolean),h=`./${qp.relative(r.paths.root,e.paths.packageJson)}`;if(c.length>=1){let d="updating";c.forEach((g,w)=>{c.length===1?d+=` ${g} in ${h}`:w===c.length-1?d+=`and ${g} in ${h}`:d+=` ${g}, `}),s.workspaceStep(d)}else s.workspaceStep(`no workspace dependencies found in ${h}`);i!=null&&i.dry||Up(e.paths.packageJson,n,{spaces:2})}var nt={name:"pnpm",lock:"pnpm-lock.yaml"};async function Hc(r){let e=er.join(r.workspaceRoot,nt.lock),t=er.join(r.workspaceRoot,"pnpm-workspace.yaml"),s=rt({workspaceRoot:r.workspaceRoot});return Xn(e)||Xn(t)||s===nt.name}async function Hp(r){if(!await Hc(r))throw new B("Not a pnpm project",{type:"package_manager-unexpected"});let{name:t,description:s}=Me(r);return{name:t,description:s,packageManager:nt.name,paths:st({root:r.workspaceRoot,lockFile:nt.lock,workspaceConfig:"pnpm-workspace.yaml"}),workspaceData:{globs:Zn(r),workspaces:it({workspaceGlobs:Zn(r),...r})}}}async function Jp(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(ce({action:"create",packageManager:nt.name,project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),o.packageManager=`${t.name}@${t.version}`,s.rootStep(`adding "packageManager" field to ${e.name} root "package.json"`),i!=null&&i.dry||(Gc(e.paths.packageJson,o,{spaces:2}),n&&(s.rootStep('adding "pnpm-workspace.yaml"'),zp(er.join(e.paths.root,"pnpm-workspace.yaml"),`packages:
${e.workspaceData.globs.map(a=>`  - "${a}"`).join(`
`)}`))),n&&(le({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:t,logger:s,options:i})}))}async function Yp(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({action:"remove",packageManager:nt.name,project:e}));let n=M({workspaceRoot:e.paths.root});if(e.paths.workspaceConfig&&i&&(t.subStep('removing "pnpm-workspace.yaml"'),s!=null&&s.dry||Qn(e.paths.workspaceConfig,{force:!0})),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){Gc(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>Wp(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Vp(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${er.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||Qn(e.paths.lockfile,{force:!0})}async function Kp(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${er.relative(e.paths.root,e.paths.lockfile)} to ${nt.lock}`)},n=async()=>{if(!(t!=null&&t.dry)&&Xn(e.paths.lockfile))try{await Gp(nt.name,["import"],{stdio:"ignore",cwd:e.paths.root})}catch{}finally{re({project:e,options:t})}};switch(e.packageManager){case"pnpm":break;case"bun":i(),await js({project:e,options:t}),await n(),Qn(er.join(e.paths.root,"yarn.lock"),{force:!0});break;case"npm":i(),await n();break;case"yarn":i(),await n();break}}var Jc={detect:Hc,read:Hp,create:Jp,remove:Yp,clean:Vp,convertLock:Kp};u();import $s from"path";import{writeJSONSync as eo,existsSync as Zp,rmSync as Xp,rm as Qp}from"fs-extra";var tr={name:"npm",lock:"package-lock.json"};async function Yc(r){let e=$s.join(r.workspaceRoot,tr.lock),t=rt({workspaceRoot:r.workspaceRoot});return Zp(e)||t===tr.name}async function em(r){if(!await Yc(r))throw new B("Not an npm project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=Me(r),n=Qt({workspaces:t.workspaces});return{name:s,description:i,packageManager:tr.name,paths:st({root:r.workspaceRoot,lockFile:tr.lock}),workspaceData:{globs:n,workspaces:it({workspaceGlobs:n,...r})}}}async function tm(r){let{project:e,options:t,to:s,logger:i}=r,n=e.workspaceData.globs.length>0;i.mainStep(ce({packageManager:tr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});i.rootHeader(),i.rootStep(`adding "packageManager" field to ${$s.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${s.name}@${s.version}`,n?(i.rootStep(`adding "workspaces" field to ${$s.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,t!=null&&t.dry||eo(e.paths.packageJson,o,{spaces:2}),le({workspace:{name:"root",paths:e.paths},project:e,to:s,logger:i,options:t}),i.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:s,logger:i,options:t})})):t!=null&&t.dry||eo(e.paths.packageJson,o,{spaces:2})}async function rm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({packageManager:tr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){eo(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>Qp(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function sm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${$s.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||Xp(e.paths.lockfile,{force:!0})}async function im(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":re({project:e,options:t});break;case"npm":break;case"yarn":re({project:e,options:t});break}}var Vc={detect:Yc,read:em,create:tm,remove:rm,clean:sm,convertLock:im};u();import kr from"path";import{existsSync as nm,writeJSONSync as to,rmSync as om,rm as um}from"fs-extra";var bt={name:"yarn",lock:"yarn.lock"};async function Kc(r){let e=kr.join(r.workspaceRoot,bt.lock),t=rt({workspaceRoot:r.workspaceRoot});return nm(e)||t===bt.name}async function am(r){if(!await Kc(r))throw new B("Not a yarn project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=Me(r),n=Qt({workspaces:t.workspaces});return{name:s,description:i,packageManager:bt.name,paths:st({root:r.workspaceRoot,lockFile:bt.lock}),workspaceData:{globs:n,workspaces:it({workspaceGlobs:n,...r})}}}async function cm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(ce({packageManager:bt.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${kr.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${kr.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||to(e.paths.packageJson,o,{spaces:2}),le({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||to(e.paths.packageJson,o,{spaces:2})}async function lm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({packageManager:bt.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){to(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>um(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function hm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${kr.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||om(e.paths.lockfile,{force:!0})}async function fm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${kr.relative(e.paths.root,e.paths.lockfile)} to ${bt.lock}`)};switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":i(),await js({project:e,options:t});break;case"npm":re({project:e,options:t});break;case"yarn":break}}var Zc={detect:Kc,read:am,create:cm,remove:lm,clean:hm,convertLock:fm};u();import zs from"path";import{existsSync as pm,writeJSONSync as ro,rmSync as mm,rm as dm}from"fs-extra";var rr={name:"bun",lock:"bun.lockb"};async function Xc(r){let e=zs.join(r.workspaceRoot,rr.lock),t=rt({workspaceRoot:r.workspaceRoot});return pm(e)||t===rr.name}async function Dm(r){if(!await Xc(r))throw new B("Not a bun project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=Me(r),n=Qt({workspaces:t.workspaces});return{name:s,description:i,packageManager:rr.name,paths:st({root:r.workspaceRoot,lockFile:rr.lock}),workspaceData:{globs:n,workspaces:it({workspaceGlobs:n,...r})}}}async function gm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;if(!Wc({project:e}))throw new B("Unable to convert project to bun - workspace globs unsupported",{type:"bun-workspace_glob_error"});s.mainStep(ce({packageManager:rr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${zs.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${zs.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||ro(e.paths.packageJson,o,{spaces:2}),le({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{le({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||ro(e.paths.packageJson,o,{spaces:2})}async function Em(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(ce({packageManager:rr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){ro(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>dm(a,{recursive:!0,force:!0})))}catch{throw new B("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function ym(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${zs.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||mm(e.paths.lockfile,{force:!0})}async function Am(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":re({project:e,options:t});break;case"bun":break;case"npm":re({project:e,options:t});break;case"yarn":re({project:e,options:t});break}}var Qc={detect:Xc,read:Dm,create:gm,remove:Em,clean:ym,convertLock:Am};var St={pnpm:Jc,yarn:Zc,npm:Vc,bun:Qc};u();async function Cg({root:r}){let{exists:e,absolute:t}=zc({directory:r});if(!e)throw new B(`Could not find directory at ${t}. Ensure the directory exists.`,{type:"invalid_directory"});for(let{detect:s,read:i}of Object.values(St))if(await s({workspaceRoot:t}))return i({workspaceRoot:t});throw new B("Could not determine package manager. Add `packageManager` to `package.json` or ensure a lockfile is present.",{type:"package_manager-unable_to_detect"})}u();import he from"chalk";import Cm from"gradient-string";var sr=2,Ws=class{constructor({interactive:e,dry:t}={}){this.interactive=e!=null?e:!0,this.dry=t!=null?t:!1,this.step=1}logger(...e){this.interactive&&console.log(...e)}indented(e,...t){this.logger(" ".repeat(sr*e),...t)}header(e){this.blankLine(),this.logger(he.bold(e))}installerFrames(){let e=`${" ".repeat(sr)} - ${this.dry?he.yellow("SKIPPED | "):he.green("OK | ")}`;return[`${e}   `,`${e}>  `,`${e}>> `,`${e}>>>`]}gradient(e){return Cm("#0099F7","#F11712")(e.toString())}hero(){this.logger(he.bold(this.gradient(`
>>> TURBOREPO
`)))}info(...e){this.logger(...e)}mainStep(e){this.blankLine(),this.logger(`${this.step}. ${he.underline(e)}`),this.step+=1}subStep(...e){this.logger(" ".repeat(sr),"-",this.dry?he.yellow("SKIPPED |"):he.green("OK |"),...e)}subStepFailure(...e){this.logger(" ".repeat(sr),"-",he.red("ERROR |"),...e)}rootHeader(){this.blankLine(),this.indented(2,"Root:")}rootStep(...e){this.logger(" ".repeat(sr*3),"-",this.dry?he.yellow("SKIPPED |"):he.green("OK |"),...e)}workspaceHeader(){this.blankLine(),this.indented(2,"Workspaces:")}workspaceStep(...e){this.logger(" ".repeat(sr*3),"-",this.dry?he.yellow("SKIPPED |"):he.green("OK |"),...e)}blankLine(){this.logger()}error(...e){console.error(...e)}};u();import wm from"execa";import Fm from"ora";import{satisfies as bm}from"semver";var el={npm:[{name:"npm",template:"npm",command:"npm",installArgs:["install"],version:"latest",executable:"npx",semver:"*",default:!0}],pnpm:[{name:"pnpm6",template:"pnpm",command:"pnpm",installArgs:["install"],version:"latest-6",executable:"pnpx",semver:"6.x"},{name:"pnpm",template:"pnpm",command:"pnpm",installArgs:["install","--fix-lockfile"],version:"latest",executable:"pnpm dlx",semver:">=7",default:!0}],yarn:[{name:"yarn",template:"yarn",command:"yarn",installArgs:["install"],version:"1.x",executable:"npx",semver:"<2",default:!0},{name:"berry",template:"berry",command:"yarn",installArgs:["install","--no-immutable"],version:"stable",executable:"yarn dlx",semver:">=2"}],bun:[{name:"bun",template:"bun",command:"bun",installArgs:["install"],version:"latest",executable:"bunx",semver:"^1.0.1",default:!0}]};function Sm(r){let{version:e,name:t}=r;return e?el[t].find(s=>bm(e,s.semver)):el[t].find(s=>s.default)}async function tl(r){let{to:e,logger:t,options:s}=r,i=t!=null?t:new Ws(s),n=Sm(e);if(!n)throw new B("Unsupported package manager version.",{type:"package_manager-unsupported_version"});if(i.subStep(`running "${n.command} ${n.installArgs.join(" ")}"`),!(s!=null&&s.dry)){let o;i.interactive&&(o=Fm({text:"installing dependencies...",spinner:{frames:i.installerFrames()}}).start());try{await wm(n.command,n.installArgs,{cwd:r.project.paths.root}),o&&o.stop(),i.subStep("dependencies installed")}catch(a){throw i.subStepFailure("failed to install dependencies"),a}}}u();import km from"chalk";async function Ng({project:r,convertTo:e,logger:t,options:s}){if(t.header(`Converting project from ${r.packageManager} to ${e.name}.`),r.packageManager===e.name)throw new B("You are already using this package manager",{type:"package_manager-already_in_use"});if(!e.version)throw new B(`${e.name} is not installed, or could not be located`,{type:"package_manager-could_not_be_found"});let i=e;await St[r.packageManager].remove({project:r,to:i,logger:t,options:s}),await St[i.name].create({project:r,to:i,logger:t,options:s}),t.mainStep("Installing dependencies"),s!=null&&s.skipInstall?t.subStep(km.yellow("Skipping install")):(await St[i.name].convertLock({project:r,to:i,logger:t,options:s}),await tl({project:r,to:i,logger:t,options:s})),t.mainStep(`Cleaning up ${r.packageManager} workspaces`),await St[r.packageManager].clean({project:r,logger:t})}export{u as a,vr as b,bl as c,B as d,zc as e,St as f,Cg as g,Ws as h,Sm as i,tl as j,Ng as k};
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
