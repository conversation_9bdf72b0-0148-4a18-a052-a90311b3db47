#!/usr/bin/env node
"use strict";var Pl=Object.create;var ii=Object.defineProperty;var Ll=Object.getOwnPropertyDescriptor;var Nl=Object.getOwnPropertyNames;var Ml=Object.getPrototypeOf,Il=Object.prototype.hasOwnProperty;var ql=(r,e)=>()=>(r&&(e=r(r=0)),e);var y=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),jl=(r,e)=>{for(var t in e)ii(r,t,{get:e[t],enumerable:!0})},Ul=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Nl(e))!Il.call(r,i)&&i!==t&&ii(r,i,{get:()=>e[i],enumerable:!(s=Ll(e,i))||s.enumerable});return r};var b=(r,e,t)=>(t=r!=null?Pl(Ml(r)):{},Ul(e||!r||!r.__esModule?ii(t,"default",{value:r,enumerable:!0}):t,r));var u=ql(()=>{});var mo=y((Wm,_e)=>{u();function ni(r){return _e.exports=ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_e.exports.__esModule=!0,_e.exports.default=_e.exports,ni(r)}_e.exports=ni,_e.exports.__esModule=!0,_e.exports.default=_e.exports});var Eo=y((zm,ve)=>{u();var Do=mo().default;function go(){"use strict";ve.exports=go=function(){return e},ve.exports.__esModule=!0,ve.exports.default=ve.exports;var r,e={},t=Object.prototype,s=t.hasOwnProperty,i=Object.defineProperty||function(g,m,D){g[m]=D.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(g,m,D){return Object.defineProperty(g,m,{value:D,enumerable:!0,configurable:!0,writable:!0}),g[m]}try{c({},"")}catch{c=function(D,A,F){return D[A]=F}}function h(g,m,D,A){var F=m&&m.prototype instanceof x?m:x,w=Object.create(F.prototype),P=new ri(A||[]);return i(w,"_invoke",{value:Ol(g,D,P)}),w}function d(g,m,D){try{return{type:"normal",arg:g.call(m,D)}}catch(A){return{type:"throw",arg:A}}}e.wrap=h;var p="suspendedStart",E="suspendedYield",C="executing",T="completed",k={};function x(){}function dt(){}function de(){}var Qs={};c(Qs,o,function(){return this});var ei=Object.getPrototypeOf,Mr=ei&&ei(ei(si([])));Mr&&Mr!==t&&s.call(Mr,o)&&(Qs=Mr);var Lt=de.prototype=x.prototype=Object.create(Qs);function fo(g){["next","throw","return"].forEach(function(m){c(g,m,function(D){return this._invoke(m,D)})})}function Ir(g,m){function D(F,w,P,G){var J=d(g[F],g,w);if(J.type!=="throw"){var Dt=J.arg,He=Dt.value;return He&&Do(He)=="object"&&s.call(He,"__await")?m.resolve(He.__await).then(function(gt){D("next",gt,P,G)},function(gt){D("throw",gt,P,G)}):m.resolve(He).then(function(gt){Dt.value=gt,P(Dt)},function(gt){return D("throw",gt,P,G)})}G(J.arg)}var A;i(this,"_invoke",{value:function(w,P){function G(){return new m(function(J,Dt){D(w,P,J,Dt)})}return A=A?A.then(G,G):G()}})}function Ol(g,m,D){var A=p;return function(F,w){if(A===C)throw new Error("Generator is already running");if(A===T){if(F==="throw")throw w;return{value:r,done:!0}}for(D.method=F,D.arg=w;;){var P=D.delegate;if(P){var G=po(P,D);if(G){if(G===k)continue;return G}}if(D.method==="next")D.sent=D._sent=D.arg;else if(D.method==="throw"){if(A===p)throw A=T,D.arg;D.dispatchException(D.arg)}else D.method==="return"&&D.abrupt("return",D.arg);A=C;var J=d(g,m,D);if(J.type==="normal"){if(A=D.done?T:E,J.arg===k)continue;return{value:J.arg,done:D.done}}J.type==="throw"&&(A=T,D.method="throw",D.arg=J.arg)}}}function po(g,m){var D=m.method,A=g.iterator[D];if(A===r)return m.delegate=null,D==="throw"&&g.iterator.return&&(m.method="return",m.arg=r,po(g,m),m.method==="throw")||D!=="return"&&(m.method="throw",m.arg=new TypeError("The iterator does not provide a '"+D+"' method")),k;var F=d(A,g.iterator,m.arg);if(F.type==="throw")return m.method="throw",m.arg=F.arg,m.delegate=null,k;var w=F.arg;return w?w.done?(m[g.resultName]=w.value,m.next=g.nextLoc,m.method!=="return"&&(m.method="next",m.arg=r),m.delegate=null,k):w:(m.method="throw",m.arg=new TypeError("iterator result is not an object"),m.delegate=null,k)}function Tl(g){var m={tryLoc:g[0]};1 in g&&(m.catchLoc=g[1]),2 in g&&(m.finallyLoc=g[2],m.afterLoc=g[3]),this.tryEntries.push(m)}function ti(g){var m=g.completion||{};m.type="normal",delete m.arg,g.completion=m}function ri(g){this.tryEntries=[{tryLoc:"root"}],g.forEach(Tl,this),this.reset(!0)}function si(g){if(g||g===""){var m=g[o];if(m)return m.call(g);if(typeof g.next=="function")return g;if(!isNaN(g.length)){var D=-1,A=function F(){for(;++D<g.length;)if(s.call(g,D))return F.value=g[D],F.done=!1,F;return F.value=r,F.done=!0,F};return A.next=A}}throw new TypeError(Do(g)+" is not iterable")}return dt.prototype=de,i(Lt,"constructor",{value:de,configurable:!0}),i(de,"constructor",{value:dt,configurable:!0}),dt.displayName=c(de,l,"GeneratorFunction"),e.isGeneratorFunction=function(g){var m=typeof g=="function"&&g.constructor;return!!m&&(m===dt||(m.displayName||m.name)==="GeneratorFunction")},e.mark=function(g){return Object.setPrototypeOf?Object.setPrototypeOf(g,de):(g.__proto__=de,c(g,l,"GeneratorFunction")),g.prototype=Object.create(Lt),g},e.awrap=function(g){return{__await:g}},fo(Ir.prototype),c(Ir.prototype,a,function(){return this}),e.AsyncIterator=Ir,e.async=function(g,m,D,A,F){F===void 0&&(F=Promise);var w=new Ir(h(g,m,D,A),F);return e.isGeneratorFunction(m)?w:w.next().then(function(P){return P.done?P.value:w.next()})},fo(Lt),c(Lt,l,"Generator"),c(Lt,o,function(){return this}),c(Lt,"toString",function(){return"[object Generator]"}),e.keys=function(g){var m=Object(g),D=[];for(var A in m)D.push(A);return D.reverse(),function F(){for(;D.length;){var w=D.pop();if(w in m)return F.value=w,F.done=!1,F}return F.done=!0,F}},e.values=si,ri.prototype={constructor:ri,reset:function(m){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(ti),!m)for(var D in this)D.charAt(0)==="t"&&s.call(this,D)&&!isNaN(+D.slice(1))&&(this[D]=r)},stop:function(){this.done=!0;var m=this.tryEntries[0].completion;if(m.type==="throw")throw m.arg;return this.rval},dispatchException:function(m){if(this.done)throw m;var D=this;function A(Dt,He){return P.type="throw",P.arg=m,D.next=Dt,He&&(D.method="next",D.arg=r),!!He}for(var F=this.tryEntries.length-1;F>=0;--F){var w=this.tryEntries[F],P=w.completion;if(w.tryLoc==="root")return A("end");if(w.tryLoc<=this.prev){var G=s.call(w,"catchLoc"),J=s.call(w,"finallyLoc");if(G&&J){if(this.prev<w.catchLoc)return A(w.catchLoc,!0);if(this.prev<w.finallyLoc)return A(w.finallyLoc)}else if(G){if(this.prev<w.catchLoc)return A(w.catchLoc,!0)}else{if(!J)throw new Error("try statement without catch or finally");if(this.prev<w.finallyLoc)return A(w.finallyLoc)}}}},abrupt:function(m,D){for(var A=this.tryEntries.length-1;A>=0;--A){var F=this.tryEntries[A];if(F.tryLoc<=this.prev&&s.call(F,"finallyLoc")&&this.prev<F.finallyLoc){var w=F;break}}w&&(m==="break"||m==="continue")&&w.tryLoc<=D&&D<=w.finallyLoc&&(w=null);var P=w?w.completion:{};return P.type=m,P.arg=D,w?(this.method="next",this.next=w.finallyLoc,k):this.complete(P)},complete:function(m,D){if(m.type==="throw")throw m.arg;return m.type==="break"||m.type==="continue"?this.next=m.arg:m.type==="return"?(this.rval=this.arg=m.arg,this.method="return",this.next="end"):m.type==="normal"&&D&&(this.next=D),k},finish:function(m){for(var D=this.tryEntries.length-1;D>=0;--D){var A=this.tryEntries[D];if(A.finallyLoc===m)return this.complete(A.completion,A.afterLoc),ti(A),k}},catch:function(m){for(var D=this.tryEntries.length-1;D>=0;--D){var A=this.tryEntries[D];if(A.tryLoc===m){var F=A.completion;if(F.type==="throw"){var w=F.arg;ti(A)}return w}}throw new Error("illegal catch attempt")},delegateYield:function(m,D,A){return this.delegate={iterator:si(m),resultName:D,nextLoc:A},this.method==="next"&&(this.arg=r),k}},e}ve.exports=go,ve.exports.__esModule=!0,ve.exports.default=ve.exports});var Ao=y((Gm,yo)=>{u();var qr=Eo()();yo.exports=qr;try{regeneratorRuntime=qr}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=qr:Function("r","regeneratorRuntime = r")(qr)}});var wo=y((pd,fi)=>{"use strict";u();var Co=(r,...e)=>new Promise(t=>{t(r(...e))});fi.exports=Co;fi.exports.default=Co});var bo=y((md,pi)=>{"use strict";u();var $l=wo(),Fo=r=>{if(!((Number.isInteger(r)||r===1/0)&&r>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],t=0,s=()=>{t--,e.length>0&&e.shift()()},i=(a,l,...c)=>{t++;let h=$l(a,...c);l(h),h.then(s,s)},n=(a,l,...c)=>{t<r?i(a,l,...c):e.push(i.bind(null,a,l,...c))},o=(a,...l)=>new Promise(c=>n(a,c,...l));return Object.defineProperties(o,{activeCount:{get:()=>t},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};pi.exports=Fo;pi.exports.default=Fo});var _o=y((dd,mi)=>{"use strict";u();var ko=bo(),jr=class extends Error{constructor(e){super(),this.value=e}},Wl=async(r,e)=>e(await r),zl=async r=>{let e=await Promise.all(r);if(e[1]===!0)throw new jr(e[0]);return!1},So=async(r,e,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let s=ko(t.concurrency),i=[...r].map(o=>[o,s(Wl,o,e)]),n=ko(t.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>n(zl,o)))}catch(o){if(o instanceof jr)return o.value;throw o}};mi.exports=So;mi.exports.default=So});var To=y((Dd,di)=>{"use strict";u();var vo=require("path"),Ur=require("fs"),{promisify:Ro}=require("util"),Gl=_o(),Hl=Ro(Ur.stat),Jl=Ro(Ur.lstat),Bo={directory:"isDirectory",file:"isFile"};function xo({type:r}){if(!(r in Bo))throw new Error(`Invalid type specified: ${r}`)}var Oo=(r,e)=>r===void 0||e[Bo[r]]();di.exports=async(r,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},xo(e);let t=e.allowSymlinks?Hl:Jl;return Gl(r,async s=>{try{let i=await t(vo.resolve(e.cwd,s));return Oo(e.type,i)}catch{return!1}},e)};di.exports.sync=(r,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},xo(e);let t=e.allowSymlinks?Ur.statSync:Ur.lstatSync;for(let s of r)try{let i=t(vo.resolve(e.cwd,s));if(Oo(e.type,i))return s}catch{}}});var Lo=y((gd,Di)=>{"use strict";u();var Po=require("fs"),{promisify:Yl}=require("util"),Vl=Yl(Po.access);Di.exports=async r=>{try{return await Vl(r),!0}catch{return!1}};Di.exports.sync=r=>{try{return Po.accessSync(r),!0}catch{return!1}}});var Mo=y((Ed,Mt)=>{"use strict";u();var Je=require("path"),$r=To(),No=Lo(),gi=Symbol("findUp.stop");Mt.exports=async(r,e={})=>{let t=Je.resolve(e.cwd||""),{root:s}=Je.parse(t),i=[].concat(r),n=async o=>{if(typeof r!="function")return $r(i,o);let a=await r(o.cwd);return typeof a=="string"?$r([a],o):a};for(;;){let o=await n({...e,cwd:t});if(o===gi)return;if(o)return Je.resolve(t,o);if(t===s)return;t=Je.dirname(t)}};Mt.exports.sync=(r,e={})=>{let t=Je.resolve(e.cwd||""),{root:s}=Je.parse(t),i=[].concat(r),n=o=>{if(typeof r!="function")return $r.sync(i,o);let a=r(o.cwd);return typeof a=="string"?$r.sync([a],o):a};for(;;){let o=n({...e,cwd:t});if(o===gi)return;if(o)return Je.resolve(t,o);if(t===s)return;t=Je.dirname(t)}};Mt.exports.exists=No;Mt.exports.sync.exists=No.sync;Mt.exports.stop=gi});var qt=y(($d,Xo)=>{"use strict";u();var Zo=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);Xo.exports=r=>r?Object.keys(r).map(e=>[Zo.has(e)?Zo.get(e):e,r[e]]).reduce((e,t)=>(e[t[0]]=t[1],e),Object.create(null)):{}});var Vr=y((Wd,uu)=>{"use strict";u();var Qo=typeof process=="object"&&process?process:{stdout:null,stderr:null},yh=require("events"),eu=require("stream"),tu=require("string_decoder").StringDecoder,Be=Symbol("EOF"),xe=Symbol("maybeEmitEnd"),Ke=Symbol("emittedEnd"),zr=Symbol("emittingEnd"),dr=Symbol("emittedError"),Gr=Symbol("closed"),ru=Symbol("read"),Hr=Symbol("flush"),su=Symbol("flushChunk"),Y=Symbol("encoding"),Oe=Symbol("decoder"),Jr=Symbol("flowing"),Dr=Symbol("paused"),jt=Symbol("resume"),L=Symbol("buffer"),ge=Symbol("pipes"),I=Symbol("bufferLength"),yi=Symbol("bufferPush"),Ai=Symbol("bufferShift"),j=Symbol("objectMode"),U=Symbol("destroyed"),Ci=Symbol("emitData"),iu=Symbol("emitEnd"),wi=Symbol("emitEnd2"),Te=Symbol("async"),gr=r=>Promise.resolve().then(r),nu=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Ah=nu&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),Ch=nu&&Symbol.iterator||Symbol("iterator not implemented"),wh=r=>r==="end"||r==="finish"||r==="prefinish",Fh=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,bh=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Yr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[jt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Fi=class extends Yr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};uu.exports=class ou extends eu{constructor(e){super(),this[Jr]=!1,this[Dr]=!1,this[ge]=[],this[L]=[],this[j]=e&&e.objectMode||!1,this[j]?this[Y]=null:this[Y]=e&&e.encoding||null,this[Y]==="buffer"&&(this[Y]=null),this[Te]=e&&!!e.async||!1,this[Oe]=this[Y]?new tu(this[Y]):null,this[Be]=!1,this[Ke]=!1,this[zr]=!1,this[Gr]=!1,this[dr]=null,this.writable=!0,this.readable=!0,this[I]=0,this[U]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[L]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[ge]})}get bufferLength(){return this[I]}get encoding(){return this[Y]}set encoding(e){if(this[j])throw new Error("cannot set encoding in objectMode");if(this[Y]&&e!==this[Y]&&(this[Oe]&&this[Oe].lastNeed||this[I]))throw new Error("cannot change encoding");this[Y]!==e&&(this[Oe]=e?new tu(e):null,this[L].length&&(this[L]=this[L].map(t=>this[Oe].write(t)))),this[Y]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[j]}set objectMode(e){this[j]=this[j]||!!e}get async(){return this[Te]}set async(e){this[Te]=this[Te]||!!e}write(e,t,s){if(this[Be])throw new Error("write after end");if(this[U])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Te]?gr:n=>n();return!this[j]&&!Buffer.isBuffer(e)&&(bh(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Fh(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[j]?(this.flowing&&this[I]!==0&&this[Hr](!0),this.flowing?this.emit("data",e):this[yi](e),this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[Y]&&!this[Oe].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[Y]&&(e=this[Oe].write(e)),this.flowing&&this[I]!==0&&this[Hr](!0),this.flowing?this.emit("data",e):this[yi](e),this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[U])return null;if(this[I]===0||e===0||e>this[I])return this[xe](),null;this[j]&&(e=null),this[L].length>1&&!this[j]&&(this.encoding?this[L]=[this[L].join("")]:this[L]=[Buffer.concat(this[L],this[I])]);let t=this[ru](e||null,this[L][0]);return this[xe](),t}[ru](e,t){return e===t.length||e===null?this[Ai]():(this[L][0]=t.slice(e),t=t.slice(0,e),this[I]-=e),this.emit("data",t),!this[L].length&&!this[Be]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[Be]=!0,this.writable=!1,(this.flowing||!this[Dr])&&this[xe](),this}[jt](){this[U]||(this[Dr]=!1,this[Jr]=!0,this.emit("resume"),this[L].length?this[Hr]():this[Be]?this[xe]():this.emit("drain"))}resume(){return this[jt]()}pause(){this[Jr]=!1,this[Dr]=!0}get destroyed(){return this[U]}get flowing(){return this[Jr]}get paused(){return this[Dr]}[yi](e){this[j]?this[I]+=1:this[I]+=e.length,this[L].push(e)}[Ai](){return this[L].length&&(this[j]?this[I]-=1:this[I]-=this[L][0].length),this[L].shift()}[Hr](e){do;while(this[su](this[Ai]()));!e&&!this[L].length&&!this[Be]&&this.emit("drain")}[su](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[U])return;let s=this[Ke];return t=t||{},e===Qo.stdout||e===Qo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this[ge].push(t.proxyErrors?new Fi(this,e,t):new Yr(this,e,t)),this[Te]?gr(()=>this[jt]()):this[jt]()),e}unpipe(e){let t=this[ge].find(s=>s.dest===e);t&&(this[ge].splice(this[ge].indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this[ge].length&&!this.flowing?this[jt]():e==="readable"&&this[I]!==0?super.emit("readable"):wh(e)&&this[Ke]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[dr]&&(this[Te]?gr(()=>t.call(this,this[dr])):t.call(this,this[dr])),s}get emittedEnd(){return this[Ke]}[xe](){!this[zr]&&!this[Ke]&&!this[U]&&this[L].length===0&&this[Be]&&(this[zr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Gr]&&this.emit("close"),this[zr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==U&&this[U])return;if(e==="data")return t?this[Te]?gr(()=>this[Ci](t)):this[Ci](t):!1;if(e==="end")return this[iu]();if(e==="close"){if(this[Gr]=!0,!this[Ke]&&!this[U])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[dr]=t;let n=super.emit("error",t);return this[xe](),n}else if(e==="resume"){let n=super.emit("resume");return this[xe](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[xe](),i}[Ci](e){for(let s of this[ge])s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[xe](),t}[iu](){this[Ke]||(this[Ke]=!0,this.readable=!1,this[Te]?gr(()=>this[wi]()):this[wi]())}[wi](){if(this[Oe]){let t=this[Oe].end();if(t){for(let s of this[ge])s.dest.write(t);super.emit("data",t)}}for(let t of this[ge])t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[j]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[j]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[j]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[j]?Promise.reject(new Error("cannot concat in objectMode")):this[Y]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(U,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Ah](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[Be])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[Be]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(U,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[Ch](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[U]?(e?this.emit("error",e):this.emit(U),this):(this[U]=!0,this[L].length=0,this[I]=0,typeof this.close=="function"&&!this[Gr]&&this.close(),e?this.emit("error",e):this.emit(U),this)}static isStream(e){return!!e&&(e instanceof ou||e instanceof eu||e instanceof yh&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var cu=y((zd,au)=>{u();var kh=require("zlib").constants||{ZLIB_VERNUM:4736};au.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},kh))});var Ri=y((Gd,Eu)=>{"use strict";u();var lu=typeof process=="object"&&process?process:{stdout:null,stderr:null},Sh=require("events"),hu=require("stream"),fu=require("string_decoder").StringDecoder,Pe=Symbol("EOF"),Le=Symbol("maybeEmitEnd"),Ze=Symbol("emittedEnd"),Kr=Symbol("emittingEnd"),Er=Symbol("emittedError"),Zr=Symbol("closed"),pu=Symbol("read"),Xr=Symbol("flush"),mu=Symbol("flushChunk"),V=Symbol("encoding"),Ne=Symbol("decoder"),Qr=Symbol("flowing"),yr=Symbol("paused"),Ut=Symbol("resume"),q=Symbol("bufferLength"),bi=Symbol("bufferPush"),ki=Symbol("bufferShift"),$=Symbol("objectMode"),W=Symbol("destroyed"),Si=Symbol("emitData"),du=Symbol("emitEnd"),_i=Symbol("emitEnd2"),Me=Symbol("async"),Ar=r=>Promise.resolve().then(r),Du=global._MP_NO_ITERATOR_SYMBOLS_!=="1",_h=Du&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),vh=Du&&Symbol.iterator||Symbol("iterator not implemented"),Rh=r=>r==="end"||r==="finish"||r==="prefinish",Bh=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,xh=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),es=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Ut](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},vi=class extends es{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};Eu.exports=class gu extends hu{constructor(e){super(),this[Qr]=!1,this[yr]=!1,this.pipes=[],this.buffer=[],this[$]=e&&e.objectMode||!1,this[$]?this[V]=null:this[V]=e&&e.encoding||null,this[V]==="buffer"&&(this[V]=null),this[Me]=e&&!!e.async||!1,this[Ne]=this[V]?new fu(this[V]):null,this[Pe]=!1,this[Ze]=!1,this[Kr]=!1,this[Zr]=!1,this[Er]=null,this.writable=!0,this.readable=!0,this[q]=0,this[W]=!1}get bufferLength(){return this[q]}get encoding(){return this[V]}set encoding(e){if(this[$])throw new Error("cannot set encoding in objectMode");if(this[V]&&e!==this[V]&&(this[Ne]&&this[Ne].lastNeed||this[q]))throw new Error("cannot change encoding");this[V]!==e&&(this[Ne]=e?new fu(e):null,this.buffer.length&&(this.buffer=this.buffer.map(t=>this[Ne].write(t)))),this[V]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[$]}set objectMode(e){this[$]=this[$]||!!e}get async(){return this[Me]}set async(e){this[Me]=this[Me]||!!e}write(e,t,s){if(this[Pe])throw new Error("write after end");if(this[W])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Me]?Ar:n=>n();return!this[$]&&!Buffer.isBuffer(e)&&(xh(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Bh(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[$]?(this.flowing&&this[q]!==0&&this[Xr](!0),this.flowing?this.emit("data",e):this[bi](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[V]&&!this[Ne].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[V]&&(e=this[Ne].write(e)),this.flowing&&this[q]!==0&&this[Xr](!0),this.flowing?this.emit("data",e):this[bi](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[W])return null;if(this[q]===0||e===0||e>this[q])return this[Le](),null;this[$]&&(e=null),this.buffer.length>1&&!this[$]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[q])]);let t=this[pu](e||null,this.buffer[0]);return this[Le](),t}[pu](e,t){return e===t.length||e===null?this[ki]():(this.buffer[0]=t.slice(e),t=t.slice(0,e),this[q]-=e),this.emit("data",t),!this.buffer.length&&!this[Pe]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[Pe]=!0,this.writable=!1,(this.flowing||!this[yr])&&this[Le](),this}[Ut](){this[W]||(this[yr]=!1,this[Qr]=!0,this.emit("resume"),this.buffer.length?this[Xr]():this[Pe]?this[Le]():this.emit("drain"))}resume(){return this[Ut]()}pause(){this[Qr]=!1,this[yr]=!0}get destroyed(){return this[W]}get flowing(){return this[Qr]}get paused(){return this[yr]}[bi](e){this[$]?this[q]+=1:this[q]+=e.length,this.buffer.push(e)}[ki](){return this.buffer.length&&(this[$]?this[q]-=1:this[q]-=this.buffer[0].length),this.buffer.shift()}[Xr](e){do;while(this[mu](this[ki]()));!e&&!this.buffer.length&&!this[Pe]&&this.emit("drain")}[mu](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[W])return;let s=this[Ze];return t=t||{},e===lu.stdout||e===lu.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this.pipes.push(t.proxyErrors?new vi(this,e,t):new es(this,e,t)),this[Me]?Ar(()=>this[Ut]()):this[Ut]()),e}unpipe(e){let t=this.pipes.find(s=>s.dest===e);t&&(this.pipes.splice(this.pipes.indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this.pipes.length&&!this.flowing?this[Ut]():e==="readable"&&this[q]!==0?super.emit("readable"):Rh(e)&&this[Ze]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[Er]&&(this[Me]?Ar(()=>t.call(this,this[Er])):t.call(this,this[Er])),s}get emittedEnd(){return this[Ze]}[Le](){!this[Kr]&&!this[Ze]&&!this[W]&&this.buffer.length===0&&this[Pe]&&(this[Kr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Zr]&&this.emit("close"),this[Kr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==W&&this[W])return;if(e==="data")return t?this[Me]?Ar(()=>this[Si](t)):this[Si](t):!1;if(e==="end")return this[du]();if(e==="close"){if(this[Zr]=!0,!this[Ze]&&!this[W])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[Er]=t;let n=super.emit("error",t);return this[Le](),n}else if(e==="resume"){let n=super.emit("resume");return this[Le](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[Le](),i}[Si](e){for(let s of this.pipes)s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[Le](),t}[du](){this[Ze]||(this[Ze]=!0,this.readable=!1,this[Me]?Ar(()=>this[_i]()):this[_i]())}[_i](){if(this[Ne]){let t=this[Ne].end();if(t){for(let s of this.pipes)s.dest.write(t);super.emit("data",t)}}for(let t of this.pipes)t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[$]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[$]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[$]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[$]?Promise.reject(new Error("cannot concat in objectMode")):this[V]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(W,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[_h](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[Pe])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[Pe]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(W,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[vh](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[W]?(e?this.emit("error",e):this.emit(W),this):(this[W]=!0,this.buffer.length=0,this[q]=0,typeof this.close=="function"&&!this[Zr]&&this.close(),e?this.emit("error",e):this.emit(W),this)}static isStream(e){return!!e&&(e instanceof gu||e instanceof hu||e instanceof Sh&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Gi=y(X=>{"use strict";u();var Pi=require("assert"),Xe=require("buffer").Buffer,Cu=require("zlib"),yt=X.constants=cu(),Oh=Ri(),yu=Xe.concat,At=Symbol("_superWrite"),Wt=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},Th=Symbol("opts"),Cr=Symbol("flushFlag"),Au=Symbol("finishFlushFlag"),zi=Symbol("fullFlushFlag"),B=Symbol("handle"),ts=Symbol("onError"),$t=Symbol("sawError"),Bi=Symbol("level"),xi=Symbol("strategy"),Oi=Symbol("ended"),Hd=Symbol("_defaultFullFlush"),rs=class extends Oh{constructor(e,t){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[$t]=!1,this[Oi]=!1,this[Th]=e,this[Cr]=e.flush,this[Au]=e.finishFlush;try{this[B]=new Cu[t](e)}catch(s){throw new Wt(s)}this[ts]=s=>{this[$t]||(this[$t]=!0,this.close(),this.emit("error",s))},this[B].on("error",s=>this[ts](new Wt(s))),this.once("end",()=>this.close)}close(){this[B]&&(this[B].close(),this[B]=null,this.emit("close"))}reset(){if(!this[$t])return Pi(this[B],"zlib binding closed"),this[B].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[zi]),this.write(Object.assign(Xe.alloc(0),{[Cr]:e})))}end(e,t,s){return e&&this.write(e,t),this.flush(this[Au]),this[Oi]=!0,super.end(null,null,s)}get ended(){return this[Oi]}write(e,t,s){if(typeof t=="function"&&(s=t,t="utf8"),typeof e=="string"&&(e=Xe.from(e,t)),this[$t])return;Pi(this[B],"zlib binding closed");let i=this[B]._handle,n=i.close;i.close=()=>{};let o=this[B].close;this[B].close=()=>{},Xe.concat=c=>c;let a;try{let c=typeof e[Cr]=="number"?e[Cr]:this[Cr];a=this[B]._processChunk(e,c),Xe.concat=yu}catch(c){Xe.concat=yu,this[ts](new Wt(c))}finally{this[B]&&(this[B]._handle=i,i.close=n,this[B].close=o,this[B].removeAllListeners("error"))}this[B]&&this[B].on("error",c=>this[ts](new Wt(c)));let l;if(a)if(Array.isArray(a)&&a.length>0){l=this[At](Xe.from(a[0]));for(let c=1;c<a.length;c++)l=this[At](a[c])}else l=this[At](Xe.from(a));return s&&s(),l}[At](e){return super.write(e)}},Ie=class extends rs{constructor(e,t){e=e||{},e.flush=e.flush||yt.Z_NO_FLUSH,e.finishFlush=e.finishFlush||yt.Z_FINISH,super(e,t),this[zi]=yt.Z_FULL_FLUSH,this[Bi]=e.level,this[xi]=e.strategy}params(e,t){if(!this[$t]){if(!this[B])throw new Error("cannot switch params when binding is closed");if(!this[B].params)throw new Error("not supported in this implementation");if(this[Bi]!==e||this[xi]!==t){this.flush(yt.Z_SYNC_FLUSH),Pi(this[B],"zlib binding closed");let s=this[B].flush;this[B].flush=(i,n)=>{this.flush(i),n()};try{this[B].params(e,t)}finally{this[B].flush=s}this[B]&&(this[Bi]=e,this[xi]=t)}}}},Li=class extends Ie{constructor(e){super(e,"Deflate")}},Ni=class extends Ie{constructor(e){super(e,"Inflate")}},Ti=Symbol("_portable"),Mi=class extends Ie{constructor(e){super(e,"Gzip"),this[Ti]=e&&!!e.portable}[At](e){return this[Ti]?(this[Ti]=!1,e[9]=255,super[At](e)):super[At](e)}},Ii=class extends Ie{constructor(e){super(e,"Gunzip")}},qi=class extends Ie{constructor(e){super(e,"DeflateRaw")}},ji=class extends Ie{constructor(e){super(e,"InflateRaw")}},Ui=class extends Ie{constructor(e){super(e,"Unzip")}},ss=class extends rs{constructor(e,t){e=e||{},e.flush=e.flush||yt.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||yt.BROTLI_OPERATION_FINISH,super(e,t),this[zi]=yt.BROTLI_OPERATION_FLUSH}},$i=class extends ss{constructor(e){super(e,"BrotliCompress")}},Wi=class extends ss{constructor(e){super(e,"BrotliDecompress")}};X.Deflate=Li;X.Inflate=Ni;X.Gzip=Mi;X.Gunzip=Ii;X.DeflateRaw=qi;X.InflateRaw=ji;X.Unzip=Ui;typeof Cu.BrotliCompress=="function"?(X.BrotliCompress=$i,X.BrotliDecompress=Wi):X.BrotliCompress=X.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var zt=y((Vd,wu)=>{u();var Ph=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;wu.exports=Ph!=="win32"?r=>r:r=>r&&r.replace(/\\/g,"/")});var is=y((Zd,Fu)=>{"use strict";u();var Lh=Vr(),Hi=zt(),Ji=Symbol("slurp");Fu.exports=class extends Lh{constructor(e,t,s){switch(super(),this.pause(),this.extended=t,this.globalExtended=s,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=Hi(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=Hi(e.linkpath),this.uname=e.uname,this.gname=e.gname,t&&this[Ji](t),s&&this[Ji](s,!0)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");let s=this.remain,i=this.blockRemain;return this.remain=Math.max(0,s-t),this.blockRemain=Math.max(0,i-t),this.ignore?!0:s>=t?super.write(e):super.write(e.slice(0,s))}[Ji](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=s==="path"||s==="linkpath"?Hi(e[s]):e[s])}}});var Yi=y(ns=>{"use strict";u();ns.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);ns.code=new Map(Array.from(ns.name).map(r=>[r[1],r[0]]))});var _u=y((Qd,Su)=>{"use strict";u();var Nh=(r,e)=>{if(Number.isSafeInteger(r))r<0?Ih(r,e):Mh(r,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},Mh=(r,e)=>{e[0]=128;for(var t=e.length;t>1;t--)e[t-1]=r&255,r=Math.floor(r/256)},Ih=(r,e)=>{e[0]=255;var t=!1;r=r*-1;for(var s=e.length;s>1;s--){var i=r&255;r=Math.floor(r/256),t?e[s-1]=bu(i):i===0?e[s-1]=0:(t=!0,e[s-1]=ku(i))}},qh=r=>{let e=r[0],t=e===128?Uh(r.slice(1,r.length)):e===255?jh(r):null;if(t===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(t))throw Error("parsed number outside of javascript safe integer range");return t},jh=r=>{for(var e=r.length,t=0,s=!1,i=e-1;i>-1;i--){var n=r[i],o;s?o=bu(n):n===0?o=n:(s=!0,o=ku(n)),o!==0&&(t-=o*Math.pow(256,e-i-1))}return t},Uh=r=>{for(var e=r.length,t=0,s=e-1;s>-1;s--){var i=r[s];i!==0&&(t+=i*Math.pow(256,e-s-1))}return t},bu=r=>(255^r)&255,ku=r=>(255^r)+1&255;Su.exports={encode:Nh,parse:qh}});var Ht=y((e0,Ru)=>{"use strict";u();var Vi=Yi(),Gt=require("path").posix,vu=_u(),Ki=Symbol("slurp"),Q=Symbol("type"),Qi=class{constructor(e,t,s,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[Q]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,t||0,s,i):e&&this.set(e)}decode(e,t,s,i){if(t||(t=0),!e||!(e.length>=t+512))throw new Error("need 512 bytes for header");if(this.path=Ct(e,t,100),this.mode=Qe(e,t+100,8),this.uid=Qe(e,t+108,8),this.gid=Qe(e,t+116,8),this.size=Qe(e,t+124,12),this.mtime=Zi(e,t+136,12),this.cksum=Qe(e,t+148,12),this[Ki](s),this[Ki](i,!0),this[Q]=Ct(e,t+156,1),this[Q]===""&&(this[Q]="0"),this[Q]==="0"&&this.path.slice(-1)==="/"&&(this[Q]="5"),this[Q]==="5"&&(this.size=0),this.linkpath=Ct(e,t+157,100),e.slice(t+257,t+265).toString()==="ustar\x0000")if(this.uname=Ct(e,t+265,32),this.gname=Ct(e,t+297,32),this.devmaj=Qe(e,t+329,8),this.devmin=Qe(e,t+337,8),e[t+475]!==0){let o=Ct(e,t+345,155);this.path=o+"/"+this.path}else{let o=Ct(e,t+345,130);o&&(this.path=o+"/"+this.path),this.atime=Zi(e,t+476,12),this.ctime=Zi(e,t+488,12)}let n=8*32;for(let o=t;o<t+148;o++)n+=e[o];for(let o=t+156;o<t+512;o++)n+=e[o];this.cksumValid=n===this.cksum,this.cksum===null&&n===8*32&&(this.nullBlock=!0)}[Ki](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=e[s])}encode(e,t){if(e||(e=this.block=Buffer.alloc(512),t=0),t||(t=0),!(e.length>=t+512))throw new Error("need 512 bytes for header");let s=this.ctime||this.atime?130:155,i=$h(this.path||"",s),n=i[0],o=i[1];this.needPax=i[2],this.needPax=wt(e,t,100,n)||this.needPax,this.needPax=et(e,t+100,8,this.mode)||this.needPax,this.needPax=et(e,t+108,8,this.uid)||this.needPax,this.needPax=et(e,t+116,8,this.gid)||this.needPax,this.needPax=et(e,t+124,12,this.size)||this.needPax,this.needPax=Xi(e,t+136,12,this.mtime)||this.needPax,e[t+156]=this[Q].charCodeAt(0),this.needPax=wt(e,t+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",t+257,8),this.needPax=wt(e,t+265,32,this.uname)||this.needPax,this.needPax=wt(e,t+297,32,this.gname)||this.needPax,this.needPax=et(e,t+329,8,this.devmaj)||this.needPax,this.needPax=et(e,t+337,8,this.devmin)||this.needPax,this.needPax=wt(e,t+345,s,o)||this.needPax,e[t+475]!==0?this.needPax=wt(e,t+345,155,o)||this.needPax:(this.needPax=wt(e,t+345,130,o)||this.needPax,this.needPax=Xi(e,t+476,12,this.atime)||this.needPax,this.needPax=Xi(e,t+488,12,this.ctime)||this.needPax);let a=8*32;for(let l=t;l<t+148;l++)a+=e[l];for(let l=t+156;l<t+512;l++)a+=e[l];return this.cksum=a,et(e,t+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let t in e)e[t]!==null&&e[t]!==void 0&&(this[t]=e[t])}get type(){return Vi.name.get(this[Q])||this[Q]}get typeKey(){return this[Q]}set type(e){Vi.code.has(e)?this[Q]=Vi.code.get(e):this[Q]=e}},$h=(r,e)=>{let s=r,i="",n,o=Gt.parse(r).root||".";if(Buffer.byteLength(s)<100)n=[s,i,!1];else{i=Gt.dirname(s),s=Gt.basename(s);do Buffer.byteLength(s)<=100&&Buffer.byteLength(i)<=e?n=[s,i,!1]:Buffer.byteLength(s)>100&&Buffer.byteLength(i)<=e?n=[s.slice(0,100-1),i,!0]:(s=Gt.join(Gt.basename(i),s),i=Gt.dirname(i));while(i!==o&&!n);n||(n=[r.slice(0,100-1),"",!0])}return n},Ct=(r,e,t)=>r.slice(e,e+t).toString("utf8").replace(/\0.*/,""),Zi=(r,e,t)=>Wh(Qe(r,e,t)),Wh=r=>r===null?null:new Date(r*1e3),Qe=(r,e,t)=>r[e]&128?vu.parse(r.slice(e,e+t)):Gh(r,e,t),zh=r=>isNaN(r)?null:r,Gh=(r,e,t)=>zh(parseInt(r.slice(e,e+t).toString("utf8").replace(/\0.*$/,"").trim(),8)),Hh={12:8589934591,8:2097151},et=(r,e,t,s)=>s===null?!1:s>Hh[t]||s<0?(vu.encode(s,r.slice(e,e+t)),!0):(Jh(r,e,t,s),!1),Jh=(r,e,t,s)=>r.write(Yh(s,t),e,t,"ascii"),Yh=(r,e)=>Vh(Math.floor(r).toString(8),e),Vh=(r,e)=>(r.length===e-1?r:new Array(e-r.length-1).join("0")+r+" ")+"\0",Xi=(r,e,t,s)=>s===null?!1:et(r,e,t,s.getTime()/1e3),Kh=new Array(156).join("\0"),wt=(r,e,t,s)=>s===null?!1:(r.write(s+Kh,e,t,"utf8"),s.length!==Buffer.byteLength(s)||s.length>t);Ru.exports=Qi});var os=y((t0,Bu)=>{"use strict";u();var Zh=Ht(),Xh=require("path"),wr=class{constructor(e,t){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=t||!1}encode(){let e=this.encodeBody();if(e==="")return null;let t=Buffer.byteLength(e),s=512*Math.ceil(1+t/512),i=Buffer.allocUnsafe(s);for(let n=0;n<512;n++)i[n]=0;new Zh({path:("PaxHeader/"+Xh.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:t,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,t,"utf8");for(let n=t+512;n<i.length;n++)i[n]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let t=this[e]instanceof Date?this[e].getTime()/1e3:this[e],s=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+t+`
`,i=Buffer.byteLength(s),n=Math.floor(Math.log(i)/Math.log(10))+1;return i+n>=Math.pow(10,n)&&(n+=1),n+i+s}};wr.parse=(r,e,t)=>new wr(Qh(ef(r),e),t);var Qh=(r,e)=>e?Object.keys(r).reduce((t,s)=>(t[s]=r[s],t),e):r,ef=r=>r.replace(/\n$/,"").split(`
`).reduce(tf,Object.create(null)),tf=(r,e)=>{let t=parseInt(e,10);if(t!==Buffer.byteLength(e)+1)return r;e=e.slice((t+" ").length);let s=e.split("="),i=s.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return r;let n=s.join("=");return r[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(n*1e3):/^[0-9]+$/.test(n)?+n:n,r};Bu.exports=wr});var Jt=y((r0,xu)=>{u();xu.exports=r=>{let e=r.length-1,t=-1;for(;e>-1&&r.charAt(e)==="/";)t=e,e--;return t===-1?r:r.slice(0,t)}});var us=y((s0,Ou)=>{"use strict";u();Ou.exports=r=>class extends r{warn(e,t,s={}){this.file&&(s.file=this.file),this.cwd&&(s.cwd=this.cwd),s.code=t instanceof Error&&t.code||e,s.tarCode=e,!this.strict&&s.recoverable!==!1?(t instanceof Error&&(s=Object.assign(t,s),t=t.message),this.emit("warn",s.tarCode,t,s)):t instanceof Error?this.emit("error",Object.assign(t,s)):this.emit("error",Object.assign(new Error(`${e}: ${t}`),s))}}});var tn=y((n0,Tu)=>{"use strict";u();var as=["|","<",">","?",":"],en=as.map(r=>String.fromCharCode(61440+r.charCodeAt(0))),rf=new Map(as.map((r,e)=>[r,en[e]])),sf=new Map(en.map((r,e)=>[r,as[e]]));Tu.exports={encode:r=>as.reduce((e,t)=>e.split(t).join(rf.get(t)),r),decode:r=>en.reduce((e,t)=>e.split(t).join(sf.get(t)),r)}});var rn=y((o0,Lu)=>{u();var{isAbsolute:nf,parse:Pu}=require("path").win32;Lu.exports=r=>{let e="",t=Pu(r);for(;nf(r)||t.root;){let s=r.charAt(0)==="/"&&r.slice(0,4)!=="//?/"?"/":t.root;r=r.slice(s.length),e+=s,t=Pu(r)}return[e,r]}});var Mu=y((u0,Nu)=>{"use strict";u();Nu.exports=(r,e,t)=>(r&=4095,t&&(r=(r|384)&-19),e&&(r&256&&(r|=64),r&32&&(r|=8),r&4&&(r|=1)),r)});var pn=y((l0,Zu)=>{"use strict";u();var zu=Vr(),Gu=os(),Hu=Ht(),ye=require("fs"),Iu=require("path"),Ee=zt(),of=Jt(),Ju=(r,e)=>e?(r=Ee(r).replace(/^\.(\/|$)/,""),of(e)+"/"+r):Ee(r),uf=16*1024*1024,qu=Symbol("process"),ju=Symbol("file"),Uu=Symbol("directory"),nn=Symbol("symlink"),$u=Symbol("hardlink"),Fr=Symbol("header"),cs=Symbol("read"),on=Symbol("lstat"),ls=Symbol("onlstat"),un=Symbol("onread"),an=Symbol("onreadlink"),cn=Symbol("openfile"),ln=Symbol("onopenfile"),tt=Symbol("close"),hs=Symbol("mode"),hn=Symbol("awaitDrain"),sn=Symbol("ondrain"),Ae=Symbol("prefix"),Wu=Symbol("hadError"),Yu=us(),af=tn(),Vu=rn(),Ku=Mu(),fs=Yu(class extends zu{constructor(e,t){if(t=t||{},super(t),typeof e!="string")throw new TypeError("path is required");this.path=Ee(e),this.portable=!!t.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=t.maxReadSize||uf,this.linkCache=t.linkCache||new Map,this.statCache=t.statCache||new Map,this.preservePaths=!!t.preservePaths,this.cwd=Ee(t.cwd||process.cwd()),this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.mtime=t.mtime||null,this.prefix=t.prefix?Ee(t.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Vu(this.path);i&&(this.path=n,s=i)}this.win32=!!t.win32||process.platform==="win32",this.win32&&(this.path=af.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=Ee(t.absolute||Iu.resolve(this.cwd,e)),this.path===""&&(this.path="./"),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.statCache.has(this.absolute)?this[ls](this.statCache.get(this.absolute)):this[on]()}emit(e,...t){return e==="error"&&(this[Wu]=!0),super.emit(e,...t)}[on](){ye.lstat(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[ls](t)})}[ls](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=lf(e),this.emit("stat",e),this[qu]()}[qu](){switch(this.type){case"File":return this[ju]();case"Directory":return this[Uu]();case"SymbolicLink":return this[nn]();default:return this.end()}}[hs](e){return Ku(e,this.type==="Directory",this.portable)}[Ae](e){return Ju(e,this.prefix)}[Fr](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new Hu({path:this[Ae](this.path),linkpath:this.type==="Link"?this[Ae](this.linkpath):this.linkpath,mode:this[hs](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new Gu({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[Ae](this.path),linkpath:this.type==="Link"?this[Ae](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[Uu](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[Fr](),this.end()}[nn](){ye.readlink(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[an](t)})}[an](e){this.linkpath=Ee(e),this[Fr](),this.end()}[$u](e){this.type="Link",this.linkpath=Ee(Iu.relative(this.cwd,e)),this.stat.size=0,this[Fr](),this.end()}[ju](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let t=this.linkCache.get(e);if(t.indexOf(this.cwd)===0)return this[$u](t)}this.linkCache.set(e,this.absolute)}if(this[Fr](),this.stat.size===0)return this.end();this[cn]()}[cn](){ye.open(this.absolute,"r",(e,t)=>{if(e)return this.emit("error",e);this[ln](t)})}[ln](e){if(this.fd=e,this[Wu])return this[tt]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let t=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(t),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[cs]()}[cs](){let{fd:e,buf:t,offset:s,length:i,pos:n}=this;ye.read(e,t,s,i,n,(o,a)=>{if(o)return this[tt](()=>this.emit("error",o));this[un](a)})}[tt](e){ye.close(this.fd,e)}[un](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[tt](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[tt](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let t=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(t)?this[sn]():this[hn](()=>this[sn]())}[hn](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let t=new Error("writing more data than expected");return t.path=this.absolute,this.emit("error",t)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[sn](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[tt](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[cs]()}}),fn=class extends fs{[on](){this[ls](ye.lstatSync(this.absolute))}[nn](){this[an](ye.readlinkSync(this.absolute))}[cn](){this[ln](ye.openSync(this.absolute,"r"))}[cs](){let e=!0;try{let{fd:t,buf:s,offset:i,length:n,pos:o}=this,a=ye.readSync(t,s,i,n,o);this[un](a),e=!1}finally{if(e)try{this[tt](()=>{})}catch{}}}[hn](e){e()}[tt](e){ye.closeSync(this.fd),e()}},cf=Yu(class extends zu{constructor(e,t){t=t||{},super(t),this.preservePaths=!!t.preservePaths,this.portable=!!t.portable,this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=t.prefix||null,this.path=Ee(e.path),this.mode=this[hs](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:t.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=Ee(e.linkpath),typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Vu(this.path);i&&(this.path=n,s=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new Hu({path:this[Ae](this.path),linkpath:this.type==="Link"?this[Ae](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.header.encode()&&!this.noPax&&super.write(new Gu({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[Ae](this.path),linkpath:this.type==="Link"?this[Ae](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[Ae](e){return Ju(e,this.prefix)}[hs](e){return Ku(e,this.type==="Directory",this.portable)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=t,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});fs.Sync=fn;fs.Tar=cf;var lf=r=>r.isFile()?"File":r.isDirectory()?"Directory":r.isSymbolicLink()?"SymbolicLink":"Unsupported";Zu.exports=fs});var Qu=y((h0,Xu)=>{"use strict";u();Xu.exports=function(r){r.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var mn=y((f0,ea)=>{"use strict";u();ea.exports=S;S.Node=Ft;S.create=S;function S(r){var e=this;if(e instanceof S||(e=new S),e.tail=null,e.head=null,e.length=0,r&&typeof r.forEach=="function")r.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var t=0,s=arguments.length;t<s;t++)e.push(arguments[t]);return e}S.prototype.removeNode=function(r){if(r.list!==this)throw new Error("removing node which does not belong to this list");var e=r.next,t=r.prev;return e&&(e.prev=t),t&&(t.next=e),r===this.head&&(this.head=e),r===this.tail&&(this.tail=t),r.list.length--,r.next=null,r.prev=null,r.list=null,e};S.prototype.unshiftNode=function(r){if(r!==this.head){r.list&&r.list.removeNode(r);var e=this.head;r.list=this,r.next=e,e&&(e.prev=r),this.head=r,this.tail||(this.tail=r),this.length++}};S.prototype.pushNode=function(r){if(r!==this.tail){r.list&&r.list.removeNode(r);var e=this.tail;r.list=this,r.prev=e,e&&(e.next=r),this.tail=r,this.head||(this.head=r),this.length++}};S.prototype.push=function(){for(var r=0,e=arguments.length;r<e;r++)ff(this,arguments[r]);return this.length};S.prototype.unshift=function(){for(var r=0,e=arguments.length;r<e;r++)pf(this,arguments[r]);return this.length};S.prototype.pop=function(){if(!!this.tail){var r=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,r}};S.prototype.shift=function(){if(!!this.head){var r=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,r}};S.prototype.forEach=function(r,e){e=e||this;for(var t=this.head,s=0;t!==null;s++)r.call(e,t.value,s,this),t=t.next};S.prototype.forEachReverse=function(r,e){e=e||this;for(var t=this.tail,s=this.length-1;t!==null;s--)r.call(e,t.value,s,this),t=t.prev};S.prototype.get=function(r){for(var e=0,t=this.head;t!==null&&e<r;e++)t=t.next;if(e===r&&t!==null)return t.value};S.prototype.getReverse=function(r){for(var e=0,t=this.tail;t!==null&&e<r;e++)t=t.prev;if(e===r&&t!==null)return t.value};S.prototype.map=function(r,e){e=e||this;for(var t=new S,s=this.head;s!==null;)t.push(r.call(e,s.value,this)),s=s.next;return t};S.prototype.mapReverse=function(r,e){e=e||this;for(var t=new S,s=this.tail;s!==null;)t.push(r.call(e,s.value,this)),s=s.prev;return t};S.prototype.reduce=function(r,e){var t,s=this.head;if(arguments.length>1)t=e;else if(this.head)s=this.head.next,t=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;s!==null;i++)t=r(t,s.value,i),s=s.next;return t};S.prototype.reduceReverse=function(r,e){var t,s=this.tail;if(arguments.length>1)t=e;else if(this.tail)s=this.tail.prev,t=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;s!==null;i--)t=r(t,s.value,i),s=s.prev;return t};S.prototype.toArray=function(){for(var r=new Array(this.length),e=0,t=this.head;t!==null;e++)r[e]=t.value,t=t.next;return r};S.prototype.toArrayReverse=function(){for(var r=new Array(this.length),e=0,t=this.tail;t!==null;e++)r[e]=t.value,t=t.prev;return r};S.prototype.slice=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new S;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(;i!==null&&s<e;s++,i=i.next)t.push(i.value);return t};S.prototype.sliceReverse=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new S;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=this.length,i=this.tail;i!==null&&s>e;s--)i=i.prev;for(;i!==null&&s>r;s--,i=i.prev)t.push(i.value);return t};S.prototype.splice=function(r,e,...t){r>this.length&&(r=this.length-1),r<0&&(r=this.length+r);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(var n=[],s=0;i&&s<e;s++)n.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var s=0;s<t.length;s++)i=hf(this,i,t[s]);return n};S.prototype.reverse=function(){for(var r=this.head,e=this.tail,t=r;t!==null;t=t.prev){var s=t.prev;t.prev=t.next,t.next=s}return this.head=e,this.tail=r,this};function hf(r,e,t){var s=e===r.head?new Ft(t,null,e,r):new Ft(t,e,e.next,r);return s.next===null&&(r.tail=s),s.prev===null&&(r.head=s),r.length++,s}function ff(r,e){r.tail=new Ft(e,r.tail,null,r),r.head||(r.head=r.tail),r.length++}function pf(r,e){r.head=new Ft(e,null,r.head,r),r.tail||(r.tail=r.head),r.length++}function Ft(r,e,t,s){if(!(this instanceof Ft))return new Ft(r,e,t,s);this.list=s,this.value=r,e?(e.next=this,this.prev=e):this.prev=null,t?(t.prev=this,this.next=t):this.next=null}try{Qu()(S)}catch{}});var Cs=y((m0,ua)=>{"use strict";u();var ys=class{constructor(e,t){this.path=e||"./",this.absolute=t,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},mf=Vr(),df=Gi(),Df=is(),Fn=pn(),gf=Fn.Sync,Ef=Fn.Tar,yf=mn(),ta=Buffer.alloc(1024),ds=Symbol("onStat"),ps=Symbol("ended"),Ce=Symbol("queue"),Yt=Symbol("current"),bt=Symbol("process"),ms=Symbol("processing"),ra=Symbol("processJob"),we=Symbol("jobs"),dn=Symbol("jobDone"),Ds=Symbol("addFSEntry"),sa=Symbol("addTarEntry"),yn=Symbol("stat"),An=Symbol("readdir"),gs=Symbol("onreaddir"),Es=Symbol("pipe"),ia=Symbol("entry"),Dn=Symbol("entryOpt"),Cn=Symbol("writeEntryClass"),oa=Symbol("write"),gn=Symbol("ondrain"),As=require("fs"),na=require("path"),Af=us(),En=zt(),bn=Af(class extends mf{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=En(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[Cn]=Fn,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new df.Gzip(e.gzip),this.zip.on("data",t=>super.write(t)),this.zip.on("end",t=>super.end()),this.zip.on("drain",t=>this[gn]()),this.on("resume",t=>this.zip.resume())):this.on("drain",this[gn]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:t=>!0,this[Ce]=new yf,this[we]=0,this.jobs=+e.jobs||4,this[ms]=!1,this[ps]=!1}[oa](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[ps]=!0,this[bt](),this}write(e){if(this[ps])throw new Error("write after end");return e instanceof Df?this[sa](e):this[Ds](e),this.flowing}[sa](e){let t=En(na.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let s=new ys(e.path,t,!1);s.entry=new Ef(e,this[Dn](s)),s.entry.on("end",i=>this[dn](s)),this[we]+=1,this[Ce].push(s)}this[bt]()}[Ds](e){let t=En(na.resolve(this.cwd,e));this[Ce].push(new ys(e,t)),this[bt]()}[yn](e){e.pending=!0,this[we]+=1;let t=this.follow?"stat":"lstat";As[t](e.absolute,(s,i)=>{e.pending=!1,this[we]-=1,s?this.emit("error",s):this[ds](e,i)})}[ds](e,t){this.statCache.set(e.absolute,t),e.stat=t,this.filter(e.path,t)||(e.ignore=!0),this[bt]()}[An](e){e.pending=!0,this[we]+=1,As.readdir(e.absolute,(t,s)=>{if(e.pending=!1,this[we]-=1,t)return this.emit("error",t);this[gs](e,s)})}[gs](e,t){this.readdirCache.set(e.absolute,t),e.readdir=t,this[bt]()}[bt](){if(!this[ms]){this[ms]=!0;for(let e=this[Ce].head;e!==null&&this[we]<this.jobs;e=e.next)if(this[ra](e.value),e.value.ignore){let t=e.next;this[Ce].removeNode(e),e.next=t}this[ms]=!1,this[ps]&&!this[Ce].length&&this[we]===0&&(this.zip?this.zip.end(ta):(super.write(ta),super.end()))}}get[Yt](){return this[Ce]&&this[Ce].head&&this[Ce].head.value}[dn](e){this[Ce].shift(),this[we]-=1,this[bt]()}[ra](e){if(!e.pending){if(e.entry){e===this[Yt]&&!e.piped&&this[Es](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[ds](e,this.statCache.get(e.absolute)):this[yn](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[gs](e,this.readdirCache.get(e.absolute)):this[An](e),!e.readdir))){if(e.entry=this[ia](e),!e.entry){e.ignore=!0;return}e===this[Yt]&&!e.piped&&this[Es](e)}}}[Dn](e){return{onwarn:(t,s,i)=>this.warn(t,s,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[ia](e){this[we]+=1;try{return new this[Cn](e.path,this[Dn](e)).on("end",()=>this[dn](e)).on("error",t=>this.emit("error",t))}catch(t){this.emit("error",t)}}[gn](){this[Yt]&&this[Yt].entry&&this[Yt].entry.resume()}[Es](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[Ds](o+i)});let t=e.entry,s=this.zip;s?t.on("data",i=>{s.write(i)||t.pause()}):t.on("data",i=>{super.write(i)||t.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),wn=class extends bn{constructor(e){super(e),this[Cn]=gf}pause(){}resume(){}[yn](e){let t=this.follow?"statSync":"lstatSync";this[ds](e,As[t](e.absolute))}[An](e,t){this[gs](e,As.readdirSync(e.absolute))}[Es](e){let t=e.entry,s=this.zip;e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[Ds](o+i)}),s?t.on("data",i=>{s.write(i)}):t.on("data",i=>{super[oa](i)})}};bn.Sync=wn;ua.exports=bn});var rr=y(kr=>{"use strict";u();var Cf=Ri(),wf=require("events").EventEmitter,K=require("fs"),_n=K.writev;if(!_n){let r=process.binding("fs"),e=r.FSReqWrap||r.FSReqCallback;_n=(t,s,i,n)=>{let o=(l,c)=>n(l,c,s),a=new e;a.oncomplete=o,r.writeBuffers(t,s,i,a)}}var er=Symbol("_autoClose"),pe=Symbol("_close"),br=Symbol("_ended"),R=Symbol("_fd"),aa=Symbol("_finished"),st=Symbol("_flags"),kn=Symbol("_flush"),vn=Symbol("_handleChunk"),Rn=Symbol("_makeBuf"),Ss=Symbol("_mode"),ws=Symbol("_needDrain"),Xt=Symbol("_onerror"),tr=Symbol("_onopen"),Sn=Symbol("_onread"),Kt=Symbol("_onwrite"),it=Symbol("_open"),qe=Symbol("_path"),kt=Symbol("_pos"),Fe=Symbol("_queue"),Zt=Symbol("_read"),ca=Symbol("_readSize"),rt=Symbol("_reading"),Fs=Symbol("_remain"),la=Symbol("_size"),bs=Symbol("_write"),Vt=Symbol("_writing"),ks=Symbol("_defaultFlag"),Qt=Symbol("_errored"),_s=class extends Cf{constructor(e,t){if(t=t||{},super(t),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[Qt]=!1,this[R]=typeof t.fd=="number"?t.fd:null,this[qe]=e,this[ca]=t.readSize||16*1024*1024,this[rt]=!1,this[la]=typeof t.size=="number"?t.size:1/0,this[Fs]=this[la],this[er]=typeof t.autoClose=="boolean"?t.autoClose:!0,typeof this[R]=="number"?this[Zt]():this[it]()}get fd(){return this[R]}get path(){return this[qe]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[it](){K.open(this[qe],"r",(e,t)=>this[tr](e,t))}[tr](e,t){e?this[Xt](e):(this[R]=t,this.emit("open",t),this[Zt]())}[Rn](){return Buffer.allocUnsafe(Math.min(this[ca],this[Fs]))}[Zt](){if(!this[rt]){this[rt]=!0;let e=this[Rn]();if(e.length===0)return process.nextTick(()=>this[Sn](null,0,e));K.read(this[R],e,0,e.length,null,(t,s,i)=>this[Sn](t,s,i))}}[Sn](e,t,s){this[rt]=!1,e?this[Xt](e):this[vn](t,s)&&this[Zt]()}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}[Xt](e){this[rt]=!0,this[pe](),this.emit("error",e)}[vn](e,t){let s=!1;return this[Fs]-=e,e>0&&(s=super.write(e<t.length?t.slice(0,e):t)),(e===0||this[Fs]<=0)&&(s=!1,this[pe](),super.end()),s}emit(e,t){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[R]=="number"&&this[Zt]();break;case"error":return this[Qt]?void 0:(this[Qt]=!0,super.emit(e,t));default:return super.emit(e,t)}}},Bn=class extends _s{[it](){let e=!0;try{this[tr](null,K.openSync(this[qe],"r")),e=!1}finally{e&&this[pe]()}}[Zt](){let e=!0;try{if(!this[rt]){this[rt]=!0;do{let t=this[Rn](),s=t.length===0?0:K.readSync(this[R],t,0,t.length,null);if(!this[vn](s,t))break}while(!0);this[rt]=!1}e=!1}finally{e&&this[pe]()}}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.closeSync(e),this.emit("close")}}},vs=class extends wf{constructor(e,t){t=t||{},super(t),this.readable=!1,this.writable=!0,this[Qt]=!1,this[Vt]=!1,this[br]=!1,this[ws]=!1,this[Fe]=[],this[qe]=e,this[R]=typeof t.fd=="number"?t.fd:null,this[Ss]=t.mode===void 0?438:t.mode,this[kt]=typeof t.start=="number"?t.start:null,this[er]=typeof t.autoClose=="boolean"?t.autoClose:!0;let s=this[kt]!==null?"r+":"w";this[ks]=t.flags===void 0,this[st]=this[ks]?s:t.flags,this[R]===null&&this[it]()}emit(e,t){if(e==="error"){if(this[Qt])return;this[Qt]=!0}return super.emit(e,t)}get fd(){return this[R]}get path(){return this[qe]}[Xt](e){this[pe](),this[Vt]=!0,this.emit("error",e)}[it](){K.open(this[qe],this[st],this[Ss],(e,t)=>this[tr](e,t))}[tr](e,t){this[ks]&&this[st]==="r+"&&e&&e.code==="ENOENT"?(this[st]="w",this[it]()):e?this[Xt](e):(this[R]=t,this.emit("open",t),this[kn]())}end(e,t){return e&&this.write(e,t),this[br]=!0,!this[Vt]&&!this[Fe].length&&typeof this[R]=="number"&&this[Kt](null,0),this}write(e,t){return typeof e=="string"&&(e=Buffer.from(e,t)),this[br]?(this.emit("error",new Error("write() after end()")),!1):this[R]===null||this[Vt]||this[Fe].length?(this[Fe].push(e),this[ws]=!0,!1):(this[Vt]=!0,this[bs](e),!0)}[bs](e){K.write(this[R],e,0,e.length,this[kt],(t,s)=>this[Kt](t,s))}[Kt](e,t){e?this[Xt](e):(this[kt]!==null&&(this[kt]+=t),this[Fe].length?this[kn]():(this[Vt]=!1,this[br]&&!this[aa]?(this[aa]=!0,this[pe](),this.emit("finish")):this[ws]&&(this[ws]=!1,this.emit("drain"))))}[kn](){if(this[Fe].length===0)this[br]&&this[Kt](null,0);else if(this[Fe].length===1)this[bs](this[Fe].pop());else{let e=this[Fe];this[Fe]=[],_n(this[R],e,this[kt],(t,s)=>this[Kt](t,s))}}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}},xn=class extends vs{[it](){let e;if(this[ks]&&this[st]==="r+")try{e=K.openSync(this[qe],this[st],this[Ss])}catch(t){if(t.code==="ENOENT")return this[st]="w",this[it]();throw t}else e=K.openSync(this[qe],this[st],this[Ss]);this[tr](null,e)}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.closeSync(e),this.emit("close")}}[bs](e){let t=!0;try{this[Kt](null,K.writeSync(this[R],e,0,e.length,this[kt])),t=!1}finally{if(t)try{this[pe]()}catch{}}}};kr.ReadStream=_s;kr.ReadStreamSync=Bn;kr.WriteStream=vs;kr.WriteStreamSync=xn});var Ls=y((g0,Ea)=>{"use strict";u();var Ff=us(),bf=Ht(),kf=require("events"),Sf=mn(),_f=1024*1024,vf=is(),ha=os(),Rf=Gi(),{nextTick:Bf}=require("process"),On=Buffer.from([31,139]),ne=Symbol("state"),St=Symbol("writeEntry"),je=Symbol("readEntry"),Tn=Symbol("nextEntry"),fa=Symbol("processEntry"),oe=Symbol("extendedHeader"),Sr=Symbol("globalExtendedHeader"),nt=Symbol("meta"),pa=Symbol("emitMeta"),O=Symbol("buffer"),Ue=Symbol("queue"),_t=Symbol("ended"),ma=Symbol("emittedEnd"),vt=Symbol("emit"),Z=Symbol("unzip"),Rs=Symbol("consumeChunk"),Bs=Symbol("consumeChunkSub"),Pn=Symbol("consumeBody"),da=Symbol("consumeMeta"),Da=Symbol("consumeHeader"),xs=Symbol("consuming"),Ln=Symbol("bufferConcat"),Nn=Symbol("maybeEnd"),_r=Symbol("writing"),ot=Symbol("aborted"),Os=Symbol("onDone"),Rt=Symbol("sawValidEntry"),Ts=Symbol("sawNullBlock"),Ps=Symbol("sawEOF"),ga=Symbol("closeStream"),xf=r=>!0;Ea.exports=Ff(class extends kf{constructor(e){e=e||{},super(e),this.file=e.file||"",this[Rt]=null,this.on(Os,t=>{(this[ne]==="begin"||this[Rt]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(Os,e.ondone):this.on(Os,t=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||_f,this.filter=typeof e.filter=="function"?e.filter:xf,this.writable=!0,this.readable=!1,this[Ue]=new Sf,this[O]=null,this[je]=null,this[St]=null,this[ne]="begin",this[nt]="",this[oe]=null,this[Sr]=null,this[_t]=!1,this[Z]=null,this[ot]=!1,this[Ts]=!1,this[Ps]=!1,this.on("end",()=>this[ga]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[Da](e,t){this[Rt]===null&&(this[Rt]=!1);let s;try{s=new bf(e,t,this[oe],this[Sr])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(s.nullBlock)this[Ts]?(this[Ps]=!0,this[ne]==="begin"&&(this[ne]="header"),this[vt]("eof")):(this[Ts]=!0,this[vt]("nullBlock"));else if(this[Ts]=!1,!s.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:s});else if(!s.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:s});else{let i=s.type;if(/^(Symbolic)?Link$/.test(i)&&!s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:s});else if(!/^(Symbolic)?Link$/.test(i)&&s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:s});else{let n=this[St]=new vf(s,this[oe],this[Sr]);if(!this[Rt])if(n.remain){let o=()=>{n.invalid||(this[Rt]=!0)};n.on("end",o)}else this[Rt]=!0;n.meta?n.size>this.maxMetaEntrySize?(n.ignore=!0,this[vt]("ignoredEntry",n),this[ne]="ignore",n.resume()):n.size>0&&(this[nt]="",n.on("data",o=>this[nt]+=o),this[ne]="meta"):(this[oe]=null,n.ignore=n.ignore||!this.filter(n.path,n),n.ignore?(this[vt]("ignoredEntry",n),this[ne]=n.remain?"ignore":"header",n.resume()):(n.remain?this[ne]="body":(this[ne]="header",n.end()),this[je]?this[Ue].push(n):(this[Ue].push(n),this[Tn]())))}}}[ga](){Bf(()=>this.emit("close"))}[fa](e){let t=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[je]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",s=>this[Tn]()),t=!1)):(this[je]=null,t=!1),t}[Tn](){do;while(this[fa](this[Ue].shift()));if(!this[Ue].length){let e=this[je];!e||e.flowing||e.size===e.remain?this[_r]||this.emit("drain"):e.once("drain",s=>this.emit("drain"))}}[Pn](e,t){let s=this[St],i=s.blockRemain,n=i>=e.length&&t===0?e:e.slice(t,t+i);return s.write(n),s.blockRemain||(this[ne]="header",this[St]=null,s.end()),n.length}[da](e,t){let s=this[St],i=this[Pn](e,t);return this[St]||this[pa](s),i}[vt](e,t,s){!this[Ue].length&&!this[je]?this.emit(e,t,s):this[Ue].push([e,t,s])}[pa](e){switch(this[vt]("meta",this[nt]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[oe]=ha.parse(this[nt],this[oe],!1);break;case"GlobalExtendedHeader":this[Sr]=ha.parse(this[nt],this[Sr],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[oe]=this[oe]||Object.create(null),this[oe].path=this[nt].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[oe]=this[oe]||Object.create(null),this[oe].linkpath=this[nt].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[ot]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[ot])return;if(this[Z]===null&&e){if(this[O]&&(e=Buffer.concat([this[O],e]),this[O]=null),e.length<On.length)return this[O]=e,!0;for(let s=0;this[Z]===null&&s<On.length;s++)e[s]!==On[s]&&(this[Z]=!1);if(this[Z]===null){let s=this[_t];this[_t]=!1,this[Z]=new Rf.Unzip,this[Z].on("data",n=>this[Rs](n)),this[Z].on("error",n=>this.abort(n)),this[Z].on("end",n=>{this[_t]=!0,this[Rs]()}),this[_r]=!0;let i=this[Z][s?"end":"write"](e);return this[_r]=!1,i}}this[_r]=!0,this[Z]?this[Z].write(e):this[Rs](e),this[_r]=!1;let t=this[Ue].length?!1:this[je]?this[je].flowing:!0;return!t&&!this[Ue].length&&this[je].once("drain",s=>this.emit("drain")),t}[Ln](e){e&&!this[ot]&&(this[O]=this[O]?Buffer.concat([this[O],e]):e)}[Nn](){if(this[_t]&&!this[ma]&&!this[ot]&&!this[xs]){this[ma]=!0;let e=this[St];if(e&&e.blockRemain){let t=this[O]?this[O].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${t} available)`,{entry:e}),this[O]&&e.write(this[O]),e.end()}this[vt](Os)}}[Rs](e){if(this[xs])this[Ln](e);else if(!e&&!this[O])this[Nn]();else{if(this[xs]=!0,this[O]){this[Ln](e);let t=this[O];this[O]=null,this[Bs](t)}else this[Bs](e);for(;this[O]&&this[O].length>=512&&!this[ot]&&!this[Ps];){let t=this[O];this[O]=null,this[Bs](t)}this[xs]=!1}(!this[O]||this[_t])&&this[Nn]()}[Bs](e){let t=0,s=e.length;for(;t+512<=s&&!this[ot]&&!this[Ps];)switch(this[ne]){case"begin":case"header":this[Da](e,t),t+=512;break;case"ignore":case"body":t+=this[Pn](e,t);break;case"meta":t+=this[da](e,t);break;default:throw new Error("invalid state: "+this[ne])}t<s&&(this[O]?this[O]=Buffer.concat([e.slice(t),this[O]]):this[O]=e.slice(t))}end(e){this[ot]||(this[Z]?this[Z].end(e):(this[_t]=!0,this.write(e)))}})});var Ns=y((E0,wa)=>{"use strict";u();var Of=qt(),Aa=Ls(),sr=require("fs"),Tf=rr(),ya=require("path"),Mn=Jt();wa.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=Of(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Lf(s,e),s.noResume||Pf(s),s.file&&s.sync?Nf(s):s.file?Mf(s,t):Ca(s)};var Pf=r=>{let e=r.onentry;r.onentry=e?t=>{e(t),t.resume()}:t=>t.resume()},Lf=(r,e)=>{let t=new Map(e.map(n=>[Mn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||ya.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(ya.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(Mn(n)):n=>i(Mn(n))},Nf=r=>{let e=Ca(r),t=r.file,s=!0,i;try{let n=sr.statSync(t),o=r.maxReadSize||16*1024*1024;if(n.size<o)e.end(sr.readFileSync(t));else{let a=0,l=Buffer.allocUnsafe(o);for(i=sr.openSync(t,"r");a<n.size;){let c=sr.readSync(i,l,0,o,a);a+=c,e.write(l.slice(0,c))}e.end()}s=!1}finally{if(s&&i)try{sr.closeSync(i)}catch{}}},Mf=(r,e)=>{let t=new Aa(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("end",o),sr.stat(i,(l,c)=>{if(l)a(l);else{let h=new Tf.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},Ca=r=>new Aa(r)});var va=y((y0,_a)=>{"use strict";u();var If=qt(),Ms=Cs(),Fa=rr(),ba=Ns(),ka=require("path");_a.exports=(r,e,t)=>{if(typeof e=="function"&&(t=e),Array.isArray(r)&&(e=r,r={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let s=If(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return s.file&&s.sync?qf(s,e):s.file?jf(s,e,t):s.sync?Uf(s,e):$f(s,e)};var qf=(r,e)=>{let t=new Ms.Sync(r),s=new Fa.WriteStreamSync(r.file,{mode:r.mode||438});t.pipe(s),Sa(t,e)},jf=(r,e,t)=>{let s=new Ms(r),i=new Fa.WriteStream(r.file,{mode:r.mode||438});s.pipe(i);let n=new Promise((o,a)=>{i.on("error",a),i.on("close",o),s.on("error",a)});return In(s,e),t?n.then(t,t):n},Sa=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?ba({file:ka.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},In=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return ba({file:ka.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>In(r,e));r.add(t)}r.end()},Uf=(r,e)=>{let t=new Ms.Sync(r);return Sa(t,e),t},$f=(r,e)=>{let t=new Ms(r);return In(t,e),t}});var qn=y((A0,La)=>{"use strict";u();var Wf=qt(),Ra=Cs(),ee=require("fs"),Ba=rr(),xa=Ns(),Oa=require("path"),Ta=Ht();La.exports=(r,e,t)=>{let s=Wf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),s.sync?zf(s,e):Hf(s,e,t)};var zf=(r,e)=>{let t=new Ra.Sync(r),s=!0,i,n;try{try{i=ee.openSync(r.file,"r+")}catch(l){if(l.code==="ENOENT")i=ee.openSync(r.file,"w+");else throw l}let o=ee.fstatSync(i),a=Buffer.alloc(512);e:for(n=0;n<o.size;n+=512){for(let h=0,d=0;h<512;h+=d){if(d=ee.readSync(i,a,h,a.length-h,n+h),n===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!d)break e}let l=new Ta(a);if(!l.cksumValid)break;let c=512*Math.ceil(l.size/512);if(n+c+512>o.size)break;n+=c,r.mtimeCache&&r.mtimeCache.set(l.path,l.mtime)}s=!1,Gf(r,t,n,i,e)}finally{if(s)try{ee.closeSync(i)}catch{}}},Gf=(r,e,t,s,i)=>{let n=new Ba.WriteStreamSync(r.file,{fd:s,start:t});e.pipe(n),Jf(e,i)},Hf=(r,e,t)=>{e=Array.from(e);let s=new Ra(r),i=(o,a,l)=>{let c=(C,T)=>{C?ee.close(o,k=>l(C)):l(null,T)},h=0;if(a===0)return c(null,0);let d=0,p=Buffer.alloc(512),E=(C,T)=>{if(C)return c(C);if(d+=T,d<512&&T)return ee.read(o,p,d,p.length-d,h+d,E);if(h===0&&p[0]===31&&p[1]===139)return c(new Error("cannot append to compressed archives"));if(d<512)return c(null,h);let k=new Ta(p);if(!k.cksumValid)return c(null,h);let x=512*Math.ceil(k.size/512);if(h+x+512>a||(h+=x+512,h>=a))return c(null,h);r.mtimeCache&&r.mtimeCache.set(k.path,k.mtime),d=0,ee.read(o,p,0,512,h,E)};ee.read(o,p,0,512,h,E)},n=new Promise((o,a)=>{s.on("error",a);let l="r+",c=(h,d)=>{if(h&&h.code==="ENOENT"&&l==="r+")return l="w+",ee.open(r.file,l,c);if(h)return a(h);ee.fstat(d,(p,E)=>{if(p)return ee.close(d,()=>a(p));i(d,E.size,(C,T)=>{if(C)return a(C);let k=new Ba.WriteStream(r.file,{fd:d,start:T});s.pipe(k),k.on("error",a),k.on("close",o),Pa(s,e)})})};ee.open(r.file,l,c)});return t?n.then(t,t):n},Jf=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?xa({file:Oa.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Pa=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return xa({file:Oa.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Pa(r,e));r.add(t)}r.end()}});var Ma=y((C0,Na)=>{"use strict";u();var Yf=qt(),Vf=qn();Na.exports=(r,e,t)=>{let s=Yf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),Kf(s),Vf(s,e,t)};var Kf=r=>{let e=r.filter;r.mtimeCache||(r.mtimeCache=new Map),r.filter=e?(t,s)=>e(t,s)&&!(r.mtimeCache.get(t)>s.mtime):(t,s)=>!(r.mtimeCache.get(t)>s.mtime)}});var ja=y((w0,qa)=>{u();var{promisify:Ia}=require("util"),ut=require("fs"),Zf=r=>{if(!r)r={mode:511,fs:ut};else if(typeof r=="object")r={mode:511,fs:ut,...r};else if(typeof r=="number")r={mode:r,fs:ut};else if(typeof r=="string")r={mode:parseInt(r,8),fs:ut};else throw new TypeError("invalid options argument");return r.mkdir=r.mkdir||r.fs.mkdir||ut.mkdir,r.mkdirAsync=Ia(r.mkdir),r.stat=r.stat||r.fs.stat||ut.stat,r.statAsync=Ia(r.stat),r.statSync=r.statSync||r.fs.statSync||ut.statSync,r.mkdirSync=r.mkdirSync||r.fs.mkdirSync||ut.mkdirSync,r};qa.exports=Zf});var $a=y((F0,Ua)=>{u();var Xf=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:Qf,parse:ep}=require("path"),tp=r=>{if(/\0/.test(r))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:r,code:"ERR_INVALID_ARG_VALUE"});if(r=Qf(r),Xf==="win32"){let e=/[*|"<>?:]/,{root:t}=ep(r);if(e.test(r.substr(t.length)))throw Object.assign(new Error("Illegal characters in path."),{path:r,code:"EINVAL"})}return r};Ua.exports=tp});var Ja=y((b0,Ha)=>{u();var{dirname:Wa}=require("path"),za=(r,e,t=void 0)=>t===e?Promise.resolve():r.statAsync(e).then(s=>s.isDirectory()?t:void 0,s=>s.code==="ENOENT"?za(r,Wa(e),e):void 0),Ga=(r,e,t=void 0)=>{if(t!==e)try{return r.statSync(e).isDirectory()?t:void 0}catch(s){return s.code==="ENOENT"?Ga(r,Wa(e),e):void 0}};Ha.exports={findMade:za,findMadeSync:Ga}});var $n=y((k0,Va)=>{u();var{dirname:Ya}=require("path"),jn=(r,e,t)=>{e.recursive=!1;let s=Ya(r);return s===r?e.mkdirAsync(r,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(r,e).then(()=>t||r,i=>{if(i.code==="ENOENT")return jn(s,e).then(n=>jn(r,e,n));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(r).then(n=>{if(n.isDirectory())return t;throw i},()=>{throw i})})},Un=(r,e,t)=>{let s=Ya(r);if(e.recursive=!1,s===r)try{return e.mkdirSync(r,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(r,e),t||r}catch(i){if(i.code==="ENOENT")return Un(r,e,Un(s,e,t));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(r).isDirectory())throw i}catch{throw i}}};Va.exports={mkdirpManual:jn,mkdirpManualSync:Un}});var Xa=y((S0,Za)=>{u();var{dirname:Ka}=require("path"),{findMade:rp,findMadeSync:sp}=Ja(),{mkdirpManual:ip,mkdirpManualSync:np}=$n(),op=(r,e)=>(e.recursive=!0,Ka(r)===r?e.mkdirAsync(r,e):rp(e,r).then(s=>e.mkdirAsync(r,e).then(()=>s).catch(i=>{if(i.code==="ENOENT")return ip(r,e);throw i}))),up=(r,e)=>{if(e.recursive=!0,Ka(r)===r)return e.mkdirSync(r,e);let s=sp(e,r);try{return e.mkdirSync(r,e),s}catch(i){if(i.code==="ENOENT")return np(r,e);throw i}};Za.exports={mkdirpNative:op,mkdirpNativeSync:up}});var rc=y((_0,tc)=>{u();var Qa=require("fs"),ap=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,Wn=ap.replace(/^v/,"").split("."),ec=+Wn[0]>10||+Wn[0]==10&&+Wn[1]>=12,cp=ec?r=>r.mkdir===Qa.mkdir:()=>!1,lp=ec?r=>r.mkdirSync===Qa.mkdirSync:()=>!1;tc.exports={useNative:cp,useNativeSync:lp}});var ac=y((v0,uc)=>{u();var ir=ja(),nr=$a(),{mkdirpNative:sc,mkdirpNativeSync:ic}=Xa(),{mkdirpManual:nc,mkdirpManualSync:oc}=$n(),{useNative:hp,useNativeSync:fp}=rc(),or=(r,e)=>(r=nr(r),e=ir(e),hp(e)?sc(r,e):nc(r,e)),pp=(r,e)=>(r=nr(r),e=ir(e),fp(e)?ic(r,e):oc(r,e));or.sync=pp;or.native=(r,e)=>sc(nr(r),ir(e));or.manual=(r,e)=>nc(nr(r),ir(e));or.nativeSync=(r,e)=>ic(nr(r),ir(e));or.manualSync=(r,e)=>oc(nr(r),ir(e));uc.exports=or});var dc=y((R0,mc)=>{"use strict";u();var ue=require("fs"),Bt=require("path"),mp=ue.lchown?"lchown":"chown",dp=ue.lchownSync?"lchownSync":"chownSync",lc=ue.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),cc=(r,e,t)=>{try{return ue[dp](r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Dp=(r,e,t)=>{try{return ue.chownSync(r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},gp=lc?(r,e,t,s)=>i=>{!i||i.code!=="EISDIR"?s(i):ue.chown(r,e,t,s)}:(r,e,t,s)=>s,zn=lc?(r,e,t)=>{try{return cc(r,e,t)}catch(s){if(s.code!=="EISDIR")throw s;Dp(r,e,t)}}:(r,e,t)=>cc(r,e,t),Ep=process.version,hc=(r,e,t)=>ue.readdir(r,e,t),yp=(r,e)=>ue.readdirSync(r,e);/^v4\./.test(Ep)&&(hc=(r,e,t)=>ue.readdir(r,t));var Is=(r,e,t,s)=>{ue[mp](r,e,t,gp(r,e,t,i=>{s(i&&i.code!=="ENOENT"?i:null)}))},fc=(r,e,t,s,i)=>{if(typeof e=="string")return ue.lstat(Bt.resolve(r,e),(n,o)=>{if(n)return i(n.code!=="ENOENT"?n:null);o.name=e,fc(r,o,t,s,i)});if(e.isDirectory())Gn(Bt.resolve(r,e.name),t,s,n=>{if(n)return i(n);let o=Bt.resolve(r,e.name);Is(o,t,s,i)});else{let n=Bt.resolve(r,e.name);Is(n,t,s,i)}},Gn=(r,e,t,s)=>{hc(r,{withFileTypes:!0},(i,n)=>{if(i){if(i.code==="ENOENT")return s();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return s(i)}if(i||!n.length)return Is(r,e,t,s);let o=n.length,a=null,l=c=>{if(!a){if(c)return s(a=c);if(--o===0)return Is(r,e,t,s)}};n.forEach(c=>fc(r,c,e,t,l))})},Ap=(r,e,t,s)=>{if(typeof e=="string")try{let i=ue.lstatSync(Bt.resolve(r,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&pc(Bt.resolve(r,e.name),t,s),zn(Bt.resolve(r,e.name),t,s)},pc=(r,e,t)=>{let s;try{s=yp(r,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return zn(r,e,t);throw i}return s&&s.length&&s.forEach(i=>Ap(r,i,e,t)),zn(r,e,t)};mc.exports=Gn;Gn.sync=pc});var yc=y((B0,Hn)=>{"use strict";u();var Dc=ac(),ae=require("fs"),qs=require("path"),gc=dc(),me=zt(),js=class extends Error{constructor(e,t){super("Cannot extract through symbolic link"),this.path=t,this.symlink=e}get name(){return"SylinkError"}},Us=class extends Error{constructor(e,t){super(t+": Cannot cd into '"+e+"'"),this.path=e,this.code=t}get name(){return"CwdError"}},$s=(r,e)=>r.get(me(e)),vr=(r,e,t)=>r.set(me(e),t),Cp=(r,e)=>{ae.stat(r,(t,s)=>{(t||!s.isDirectory())&&(t=new Us(r,t&&t.code||"ENOTDIR")),e(t)})};Hn.exports=(r,e,t)=>{r=me(r);let s=e.umask,i=e.mode|448,n=(i&s)!==0,o=e.uid,a=e.gid,l=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,h=e.unlink,d=e.cache,p=me(e.cwd),E=(k,x)=>{k?t(k):(vr(d,r,!0),x&&l?gc(x,o,a,dt=>E(dt)):n?ae.chmod(r,i,t):t())};if(d&&$s(d,r)===!0)return E();if(r===p)return Cp(r,E);if(c)return Dc(r,{mode:i}).then(k=>E(null,k),E);let T=me(qs.relative(p,r)).split("/");Ws(p,T,i,d,h,p,null,E)};var Ws=(r,e,t,s,i,n,o,a)=>{if(!e.length)return a(null,o);let l=e.shift(),c=me(qs.resolve(r+"/"+l));if($s(s,c))return Ws(c,e,t,s,i,n,o,a);ae.mkdir(c,t,Ec(c,e,t,s,i,n,o,a))},Ec=(r,e,t,s,i,n,o,a)=>l=>{l?ae.lstat(r,(c,h)=>{if(c)c.path=c.path&&me(c.path),a(c);else if(h.isDirectory())Ws(r,e,t,s,i,n,o,a);else if(i)ae.unlink(r,d=>{if(d)return a(d);ae.mkdir(r,t,Ec(r,e,t,s,i,n,o,a))});else{if(h.isSymbolicLink())return a(new js(r,r+"/"+e.join("/")));a(l)}}):(o=o||r,Ws(r,e,t,s,i,n,o,a))},wp=r=>{let e=!1,t="ENOTDIR";try{e=ae.statSync(r).isDirectory()}catch(s){t=s.code}finally{if(!e)throw new Us(r,t)}};Hn.exports.sync=(r,e)=>{r=me(r);let t=e.umask,s=e.mode|448,i=(s&t)!==0,n=e.uid,o=e.gid,a=typeof n=="number"&&typeof o=="number"&&(n!==e.processUid||o!==e.processGid),l=e.preserve,c=e.unlink,h=e.cache,d=me(e.cwd),p=k=>{vr(h,r,!0),k&&a&&gc.sync(k,n,o),i&&ae.chmodSync(r,s)};if(h&&$s(h,r)===!0)return p();if(r===d)return wp(d),p();if(l)return p(Dc.sync(r,s));let C=me(qs.relative(d,r)).split("/"),T=null;for(let k=C.shift(),x=d;k&&(x+="/"+k);k=C.shift())if(x=me(qs.resolve(x)),!$s(h,x))try{ae.mkdirSync(x,s),T=T||x,vr(h,x,!0)}catch{let de=ae.lstatSync(x);if(de.isDirectory()){vr(h,x,!0);continue}else if(c){ae.unlinkSync(x),ae.mkdirSync(x,s),T=T||x,vr(h,x,!0);continue}else if(de.isSymbolicLink())return new js(x,x+"/"+C.join("/"))}return p(T)}});var Yn=y((x0,Ac)=>{u();var Jn=Object.create(null),{hasOwnProperty:Fp}=Object.prototype;Ac.exports=r=>(Fp.call(Jn,r)||(Jn[r]=r.normalize("NFKD")),Jn[r])});var bc=y((O0,Fc)=>{u();var Cc=require("assert"),bp=Yn(),kp=Jt(),{join:wc}=require("path"),Sp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,_p=Sp==="win32";Fc.exports=()=>{let r=new Map,e=new Map,t=c=>c.split("/").slice(0,-1).reduce((d,p)=>(d.length&&(p=wc(d[d.length-1],p)),d.push(p||"/"),d),[]),s=new Set,i=c=>{let h=e.get(c);if(!h)throw new Error("function does not have any path reservations");return{paths:h.paths.map(d=>r.get(d)),dirs:[...h.dirs].map(d=>r.get(d))}},n=c=>{let{paths:h,dirs:d}=i(c);return h.every(p=>p[0]===c)&&d.every(p=>p[0]instanceof Set&&p[0].has(c))},o=c=>s.has(c)||!n(c)?!1:(s.add(c),c(()=>a(c)),!0),a=c=>{if(!s.has(c))return!1;let{paths:h,dirs:d}=e.get(c),p=new Set;return h.forEach(E=>{let C=r.get(E);Cc.equal(C[0],c),C.length===1?r.delete(E):(C.shift(),typeof C[0]=="function"?p.add(C[0]):C[0].forEach(T=>p.add(T)))}),d.forEach(E=>{let C=r.get(E);Cc(C[0]instanceof Set),C[0].size===1&&C.length===1?r.delete(E):C[0].size===1?(C.shift(),p.add(C[0])):C[0].delete(c)}),s.delete(c),p.forEach(E=>o(E)),!0};return{check:n,reserve:(c,h)=>{c=_p?["win32 parallelization disabled"]:c.map(p=>bp(kp(wc(p))).toLowerCase());let d=new Set(c.map(p=>t(p)).reduce((p,E)=>p.concat(E)));return e.set(h,{dirs:d,paths:c}),c.forEach(p=>{let E=r.get(p);E?E.push(h):r.set(p,[h])}),d.forEach(p=>{let E=r.get(p);E?E[E.length-1]instanceof Set?E[E.length-1].add(h):E.push(new Set([h])):r.set(p,[new Set([h])])}),o(h)}}}});var _c=y((T0,Sc)=>{u();var vp=process.env.__FAKE_PLATFORM__||process.platform,Rp=vp==="win32",Bp=global.__FAKE_TESTING_FS__||require("fs"),{O_CREAT:xp,O_TRUNC:Op,O_WRONLY:Tp,UV_FS_O_FILEMAP:kc=0}=Bp.constants,Pp=Rp&&!!kc,Lp=512*1024,Np=kc|Op|xp|Tp;Sc.exports=Pp?r=>r<Lp?Np:"w":()=>"w"});var so=y((P0,Uc)=>{"use strict";u();var Mp=require("assert"),Ip=Ls(),_=require("fs"),qp=rr(),$e=require("path"),Ic=yc(),vc=tn(),jp=bc(),Up=rn(),te=zt(),$p=Jt(),Wp=Yn(),Rc=Symbol("onEntry"),Zn=Symbol("checkFs"),Bc=Symbol("checkFs2"),Hs=Symbol("pruneCache"),Xn=Symbol("isReusable"),ce=Symbol("makeFs"),Qn=Symbol("file"),eo=Symbol("directory"),Js=Symbol("link"),xc=Symbol("symlink"),Oc=Symbol("hardlink"),Tc=Symbol("unsupported"),Pc=Symbol("checkPath"),at=Symbol("mkdir"),z=Symbol("onError"),zs=Symbol("pending"),Lc=Symbol("pend"),ur=Symbol("unpend"),Vn=Symbol("ended"),Kn=Symbol("maybeClose"),to=Symbol("skip"),Rr=Symbol("doChown"),Br=Symbol("uid"),xr=Symbol("gid"),Or=Symbol("checkedCwd"),qc=require("crypto"),jc=_c(),zp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Tr=zp==="win32",Gp=(r,e)=>{if(!Tr)return _.unlink(r,e);let t=r+".DELETE."+qc.randomBytes(16).toString("hex");_.rename(r,t,s=>{if(s)return e(s);_.unlink(t,e)})},Hp=r=>{if(!Tr)return _.unlinkSync(r);let e=r+".DELETE."+qc.randomBytes(16).toString("hex");_.renameSync(r,e),_.unlinkSync(e)},Nc=(r,e,t)=>r===r>>>0?r:e===e>>>0?e:t,Mc=r=>Wp($p(te(r))).toLowerCase(),Jp=(r,e)=>{e=Mc(e);for(let t of r.keys()){let s=Mc(t);(s===e||s.indexOf(e+"/")===0)&&r.delete(t)}},Yp=r=>{for(let e of r.keys())r.delete(e)},Pr=class extends Ip{constructor(e){if(e||(e={}),e.ondone=t=>{this[Vn]=!0,this[Kn]()},super(e),this[Or]=!1,this.reservations=jp(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[zs]=0,this[Vn]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||Tr,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=te($e.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",t=>this[Rc](t))}warn(e,t,s={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(s.recoverable=!1),super.warn(e,t,s)}[Kn](){this[Vn]&&this[zs]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[Pc](e){if(this.strip){let t=te(e.path).split("/");if(t.length<this.strip)return!1;if(e.path=t.slice(this.strip).join("/"),e.type==="Link"){let s=te(e.linkpath).split("/");if(s.length>=this.strip)e.linkpath=s.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let t=te(e.path),s=t.split("/");if(s.includes("..")||Tr&&/^[a-z]:\.\.$/i.test(s[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:t}),!1;let[i,n]=Up(t);i&&(e.path=n,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:t}))}if($e.isAbsolute(e.path)?e.absolute=te($e.resolve(e.path)):e.absolute=te($e.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:te(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:t}=$e.win32.parse(e.absolute);e.absolute=t+vc.encode(e.absolute.slice(t.length));let{root:s}=$e.win32.parse(e.path);e.path=s+vc.encode(e.path.slice(s.length))}return!0}[Rc](e){if(!this[Pc](e))return e.resume();switch(Mp.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[Zn](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[Tc](e)}}[z](e,t){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:t}),this[ur](),t.resume())}[at](e,t,s){Ic(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t,noChmod:this.noChmod},s)}[Rr](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[Br](e){return Nc(this.uid,e.uid,this.processUid)}[xr](e){return Nc(this.gid,e.gid,this.processGid)}[Qn](e,t){let s=e.mode&4095||this.fmode,i=new qp.WriteStream(e.absolute,{flags:jc(e.size),mode:s,autoClose:!1});i.on("error",l=>{i.fd&&_.close(i.fd,()=>{}),i.write=()=>!0,this[z](l,e),t()});let n=1,o=l=>{if(l){i.fd&&_.close(i.fd,()=>{}),this[z](l,e),t();return}--n===0&&_.close(i.fd,c=>{c?this[z](c,e):this[ur](),t()})};i.on("finish",l=>{let c=e.absolute,h=i.fd;if(e.mtime&&!this.noMtime){n++;let d=e.atime||new Date,p=e.mtime;_.futimes(h,d,p,E=>E?_.utimes(c,d,p,C=>o(C&&E)):o())}if(this[Rr](e)){n++;let d=this[Br](e),p=this[xr](e);_.fchown(h,d,p,E=>E?_.chown(c,d,p,C=>o(C&&E)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",l=>{this[z](l,e),t()}),e.pipe(a)),a.pipe(i)}[eo](e,t){let s=e.mode&4095||this.dmode;this[at](e.absolute,s,i=>{if(i){this[z](i,e),t();return}let n=1,o=a=>{--n===0&&(t(),this[ur](),e.resume())};e.mtime&&!this.noMtime&&(n++,_.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[Rr](e)&&(n++,_.chown(e.absolute,this[Br](e),this[xr](e),o)),o()})}[Tc](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[xc](e,t){this[Js](e,e.linkpath,"symlink",t)}[Oc](e,t){let s=te($e.resolve(this.cwd,e.linkpath));this[Js](e,s,"link",t)}[Lc](){this[zs]++}[ur](){this[zs]--,this[Kn]()}[to](e){this[ur](),e.resume()}[Xn](e,t){return e.type==="File"&&!this.unlink&&t.isFile()&&t.nlink<=1&&!Tr}[Zn](e){this[Lc]();let t=[e.path];e.linkpath&&t.push(e.linkpath),this.reservations.reserve(t,s=>this[Bc](e,s))}[Hs](e){e.type==="SymbolicLink"?Yp(this.dirCache):e.type!=="Directory"&&Jp(this.dirCache,e.absolute)}[Bc](e,t){this[Hs](e);let s=a=>{this[Hs](e),t(a)},i=()=>{this[at](this.cwd,this.dmode,a=>{if(a){this[z](a,e),s();return}this[Or]=!0,n()})},n=()=>{if(e.absolute!==this.cwd){let a=te($e.dirname(e.absolute));if(a!==this.cwd)return this[at](a,this.dmode,l=>{if(l){this[z](l,e),s();return}o()})}o()},o=()=>{_.lstat(e.absolute,(a,l)=>{if(l&&(this.keep||this.newer&&l.mtime>e.mtime)){this[to](e),s();return}if(a||this[Xn](e,l))return this[ce](null,e,s);if(l.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(l.mode&4095)!==e.mode,h=d=>this[ce](d,e,s);return c?_.chmod(e.absolute,e.mode,h):h()}if(e.absolute!==this.cwd)return _.rmdir(e.absolute,c=>this[ce](c,e,s))}if(e.absolute===this.cwd)return this[ce](null,e,s);Gp(e.absolute,c=>this[ce](c,e,s))})};this[Or]?n():i()}[ce](e,t,s){if(e){this[z](e,t),s();return}switch(t.type){case"File":case"OldFile":case"ContiguousFile":return this[Qn](t,s);case"Link":return this[Oc](t,s);case"SymbolicLink":return this[xc](t,s);case"Directory":case"GNUDumpDir":return this[eo](t,s)}}[Js](e,t,s,i){_[s](t,e.absolute,n=>{n?this[z](n,e):(this[ur](),e.resume()),i()})}},Gs=r=>{try{return[null,r()]}catch(e){return[e,null]}},ro=class extends Pr{[ce](e,t){return super[ce](e,t,()=>{})}[Zn](e){if(this[Hs](e),!this[Or]){let n=this[at](this.cwd,this.dmode);if(n)return this[z](n,e);this[Or]=!0}if(e.absolute!==this.cwd){let n=te($e.dirname(e.absolute));if(n!==this.cwd){let o=this[at](n,this.dmode);if(o)return this[z](o,e)}}let[t,s]=Gs(()=>_.lstatSync(e.absolute));if(s&&(this.keep||this.newer&&s.mtime>e.mtime))return this[to](e);if(t||this[Xn](e,s))return this[ce](null,e);if(s.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(s.mode&4095)!==e.mode,[a]=o?Gs(()=>{_.chmodSync(e.absolute,e.mode)}):[];return this[ce](a,e)}let[n]=Gs(()=>_.rmdirSync(e.absolute));this[ce](n,e)}let[i]=e.absolute===this.cwd?[]:Gs(()=>Hp(e.absolute));this[ce](i,e)}[Qn](e,t){let s=e.mode&4095||this.fmode,i=a=>{let l;try{_.closeSync(n)}catch(c){l=c}(a||l)&&this[z](a||l,e),t()},n;try{n=_.openSync(e.absolute,jc(e.size),s)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[z](a,e)),e.pipe(o)),o.on("data",a=>{try{_.writeSync(n,a,0,a.length)}catch(l){i(l)}}),o.on("end",a=>{let l=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,h=e.mtime;try{_.futimesSync(n,c,h)}catch(d){try{_.utimesSync(e.absolute,c,h)}catch{l=d}}}if(this[Rr](e)){let c=this[Br](e),h=this[xr](e);try{_.fchownSync(n,c,h)}catch(d){try{_.chownSync(e.absolute,c,h)}catch{l=l||d}}}i(l)})}[eo](e,t){let s=e.mode&4095||this.dmode,i=this[at](e.absolute,s);if(i){this[z](i,e),t();return}if(e.mtime&&!this.noMtime)try{_.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[Rr](e))try{_.chownSync(e.absolute,this[Br](e),this[xr](e))}catch{}t(),e.resume()}[at](e,t){try{return Ic.sync(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t})}catch(s){return s}}[Js](e,t,s,i){try{_[s+"Sync"](t,e.absolute),i(),e.resume()}catch(n){return this[z](n,e)}}};Pr.Sync=ro;Uc.exports=Pr});var Hc=y((L0,Gc)=>{"use strict";u();var Vp=qt(),Ys=so(),Wc=require("fs"),zc=rr(),$c=require("path"),io=Jt();Gc.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=Vp(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Kp(s,e),s.file&&s.sync?Zp(s):s.file?Xp(s,t):s.sync?Qp(s):em(s)};var Kp=(r,e)=>{let t=new Map(e.map(n=>[io(n),!0])),s=r.filter,i=(n,o)=>{let a=o||$c.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i($c.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(io(n)):n=>i(io(n))},Zp=r=>{let e=new Ys.Sync(r),t=r.file,s=Wc.statSync(t),i=r.maxReadSize||16*1024*1024;new zc.ReadStreamSync(t,{readSize:i,size:s.size}).pipe(e)},Xp=(r,e)=>{let t=new Ys(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("close",o),Wc.stat(i,(l,c)=>{if(l)a(l);else{let h=new zc.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},Qp=r=>new Ys.Sync(r),em=r=>new Ys(r)});var Jc=y(N=>{"use strict";u();N.c=N.create=va();N.r=N.replace=qn();N.t=N.list=Ns();N.u=N.update=Ma();N.x=N.extract=Hc();N.Pack=Cs();N.Unpack=so();N.Parse=Ls();N.ReadEntry=is();N.WriteEntry=pn();N.Header=Ht();N.Pax=os();N.types=Yi()});var Qc=y((j0,Xc)=>{u();function le(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Xc.exports=le;le.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};le.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};le.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var s=this;return this._timer=setTimeout(function(){s._attempts++,s._operationTimeoutCb&&(s._timeout=setTimeout(function(){s._operationTimeoutCb(s._attempts)},s._operationTimeout),s._options.unref&&s._timeout.unref()),s._fn(s._attempts)},t),this._options.unref&&this._timer.unref(),!0};le.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};le.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};le.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};le.prototype.start=le.prototype.try;le.prototype.errors=function(){return this._errors};le.prototype.attempts=function(){return this._attempts};le.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,s=0;s<this._errors.length;s++){var i=this._errors[s],n=i.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=i,t=o)}return e}});var el=y(xt=>{u();var im=Qc();xt.operation=function(r){var e=xt.timeouts(r);return new im(e,{forever:r&&(r.forever||r.retries===1/0),unref:r&&r.unref,maxRetryTime:r&&r.maxRetryTime})};xt.timeouts=function(r){if(r instanceof Array)return[].concat(r);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in r)e[t]=r[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var s=[],i=0;i<e.retries;i++)s.push(this.createTimeout(i,e));return r&&r.forever&&!s.length&&s.push(this.createTimeout(i,e)),s.sort(function(n,o){return n-o}),s};xt.createTimeout=function(r,e){var t=e.randomize?Math.random()+1:1,s=Math.round(t*Math.max(e.minTimeout,1)*Math.pow(e.factor,r));return s=Math.min(s,e.maxTimeout),s};xt.wrap=function(r,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var s in r)typeof r[s]=="function"&&t.push(s)}for(var i=0;i<t.length;i++){var n=t[i],o=r[n];r[n]=function(l){var c=xt.operation(e),h=Array.prototype.slice.call(arguments,1),d=h.pop();h.push(function(p){c.retry(p)||(p&&(arguments[0]=c.mainError()),d.apply(this,arguments))}),c.attempt(function(){l.apply(r,h)})}.bind(r,o),r[n].options=e}}});var rl=y(($0,tl)=>{u();tl.exports=el()});var il=y((W0,sl)=>{u();var nm=rl();function om(r,e){function t(s,i){var n=e||{},o;"randomize"in n||(n.randomize=!0),o=nm.operation(n);function a(h){i(h||new Error("Aborted"))}function l(h,d){if(h.bail){a(h);return}o.retry(h)?n.onRetry&&n.onRetry(h,d):i(o.mainError())}function c(h){var d;try{d=r(a,h)}catch(p){l(p,h);return}Promise.resolve(d).then(s).catch(function(E){l(E,h)})}o.attempt(c)}return new Promise(t)}sl.exports=om});u();var ho=b(require("chalk")),xl=require("commander");u();u();u();var Kl=b(Ao());u();u();function oi(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}u();u();function pr(r){return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pr(r)}u();function ui(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function ai(r,e){if(e&&(pr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ui(r)}u();function Et(r){return Et=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Et(r)}u();u();function Re(r,e){return Re=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,i){return s.__proto__=i,s},Re(r,e)}function ci(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&Re(r,e)}u();u();function li(r){return Function.toString.call(r).indexOf("[native code]")!==-1}u();u();function hi(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Nt(r,e,t){return hi()?Nt=Reflect.construct.bind():Nt=function(i,n,o){var a=[null];a.push.apply(a,n);var l=Function.bind.apply(i,a),c=new l;return o&&Re(c,o.prototype),c},Nt.apply(null,arguments)}function mr(r){var e=typeof Map=="function"?new Map:void 0;return mr=function(s){if(s===null||!li(s))return s;if(typeof s!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(s))return e.get(s);e.set(s,i)}function i(){return Nt(s,arguments,Et(this).constructor)}return i.prototype=Object.create(s.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Re(i,s)},mr(r)}var Io=b(Mo());var Zl=b(require("fs-extra")),Sd=function(r){ci(e,r);function e(t){var s;return oi(this,e),s=ai(this,Et(e).call(this,"No package.json could be found upwards from the directory ".concat(t))),s.directory=t,s}return e}(mr(Error));u();u();var Xl=b(require("fs")),Ql=b(require("path"));u();var hh=b(require("fs")),fh=b(require("path")),ph=b(require("js-yaml")),mh=require("fast-glob");var De={};jl(De,{bold:()=>nh,dimmed:()=>uh,error:()=>Jo,grey:()=>ah,info:()=>ih,item:()=>ch,log:()=>Ve,turboBlue:()=>It,turboGradient:()=>sh,turboLoader:()=>Go,turboRed:()=>Wo,underline:()=>oh,warn:()=>Ho,yellow:()=>zo});u();var Ye=b(require("chalk")),qo=b(require("ora")),jo=b(require("gradient-string")),Uo="#0099F7",$o="#F11712",rh="#FFFF00",sh=(0,jo.default)(Uo,$o),It=Ye.default.hex(Uo),Wo=Ye.default.hex($o),zo=Ye.default.hex(rh),Go=r=>(0,qo.default)({text:r,spinner:{frames:["   ",It(">  "),It(">> "),It(">>>")]}}),ih=(...r)=>{Ve(It.bold(">>>"),...r)},nh=(...r)=>{Ve(Ye.default.bold(...r))},oh=(...r)=>{Ve(Ye.default.underline(...r))},uh=(...r)=>{Ve(Ye.default.dim(...r))},ah=(...r)=>{Ve(Ye.default.grey(...r))},ch=(...r)=>{Ve(It.bold("  \u2022"),...r)},Ve=(...r)=>{console.log(...r)},Ho=(...r)=>{console.error(zo.bold(">>>"),...r)},Jo=(...r)=>{console.error(Wo.bold(">>>"),...r)};u();var Yo=b(require("os")),Vo=b(require("execa"));async function Wr(r,e=[],t){let s={cwd:Yo.default.tmpdir(),env:{COREPACK_ENABLE_STRICT:"0"},...t};try{let{stdout:i}=await(0,Vo.default)(r,e,s);return i.trim()}catch{return}}async function Ei(){let[r,e,t,s]=await Promise.all([Wr("yarnpkg",["--version"]),Wr("npm",["--version"]),Wr("pnpm",["--version"]),Wr("bun",["--version"])]);return{yarn:r,pnpm:t,npm:e,bun:s}}u();var dh=b(require("fs-extra"));u();var Dh=b(require("path")),gh=b(require("fs-extra")),Eh=b(require("chalk"));u();var Yc=require("stream"),Vc=require("util"),tm=require("path"),rm=require("os"),Kc=require("fs"),sm=b(Jc());var M0=(0,Vc.promisify)(Yc.Stream.pipeline);u();var Zc=require("fs-extra");u();var pm=b(require("path")),mm=b(il()),dm=b(require("chalk")),no=require("fs-extra");u();u();var nl={name:"@turbo/workspaces",version:"1.13.4",description:"Tools for working with package managers",homepage:"https://turbo.build/repo",license:"MPL-2.0",repository:{type:"git",url:"https://github.com/vercel/turbo",directory:"packages/turbo-workspaces"},bugs:{url:"https://github.com/vercel/turbo/issues"},bin:"dist/cli.js",module:"dist/index.mjs",main:"dist/index.js",types:"dist/index.d.ts",scripts:{build:"tsup",dev:"tsup --watch",test:"jest",lint:"eslint src/","check-types":"tsc --noEmit","lint:prettier":"prettier -c . --cache --ignore-path=../../.prettierignore"},dependencies:{chalk:"2.4.2",commander:"^10.0.0",execa:"5.1.1","fast-glob":"^3.2.12","fs-extra":"^10.1.0","gradient-string":"^2.0.0",inquirer:"^8.0.0","js-yaml":"^4.1.0",ora:"4.1.1",rimraf:"^3.0.2",semver:"^7.3.5","update-check":"^1.5.4"},devDependencies:{"@turbo/eslint-config":"workspace:*","@turbo/test-utils":"workspace:*","@turbo/tsconfig":"workspace:*","@turbo/utils":"workspace:*","@types/chalk-animation":"^1.6.0","@types/fs-extra":"^9.0.13","@types/gradient-string":"^1.1.2","@types/inquirer":"^7.3.1","@types/jest":"^27.4.0","@types/js-yaml":"^4.0.5","@types/node":"^18.17.2","@types/rimraf":"^3.0.2","@types/semver":"^7.3.9",jest:"^27.4.3",semver:"^7.3.5","strip-ansi":"^6.0.1","ts-jest":"^27.1.1",tsup:"^5.10.3",typescript:"5.3.3"},files:["dist"],publishConfig:{access:"public"}};u();u();var Zs=b(require("path")),Fl=b(require("inquirer")),Ge=b(require("chalk"));u();var re=b(require("chalk")),ol=b(require("gradient-string")),ar=2,ct=class{constructor({interactive:e,dry:t}={}){this.interactive=e!=null?e:!0,this.dry=t!=null?t:!1,this.step=1}logger(...e){this.interactive&&console.log(...e)}indented(e,...t){this.logger(" ".repeat(ar*e),...t)}header(e){this.blankLine(),this.logger(re.default.bold(e))}installerFrames(){let e=`${" ".repeat(ar)} - ${this.dry?re.default.yellow("SKIPPED | "):re.default.green("OK | ")}`;return[`${e}   `,`${e}>  `,`${e}>> `,`${e}>>>`]}gradient(e){return(0,ol.default)("#0099F7","#F11712")(e.toString())}hero(){this.logger(re.default.bold(this.gradient(`
>>> TURBOREPO
`)))}info(...e){this.logger(...e)}mainStep(e){this.blankLine(),this.logger(`${this.step}. ${re.default.underline(e)}`),this.step+=1}subStep(...e){this.logger(" ".repeat(ar),"-",this.dry?re.default.yellow("SKIPPED |"):re.default.green("OK |"),...e)}subStepFailure(...e){this.logger(" ".repeat(ar),"-",re.default.red("ERROR |"),...e)}rootHeader(){this.blankLine(),this.indented(2,"Root:")}rootStep(...e){this.logger(" ".repeat(ar*3),"-",this.dry?re.default.yellow("SKIPPED |"):re.default.green("OK |"),...e)}workspaceHeader(){this.blankLine(),this.indented(2,"Workspaces:")}workspaceStep(...e){this.logger(" ".repeat(ar*3),"-",this.dry?re.default.yellow("SKIPPED |"):re.default.green("OK |"),...e)}blankLine(){this.logger()}error(...e){console.error(...e)}};u();var We=b(require("path")),ul=b(require("execa")),se=require("fs-extra"),al=require("fast-glob"),cl=b(require("js-yaml"));u();var v=class extends Error{constructor(t,s){var i;super(t);this.name="ConvertError",this.type=(i=s==null?void 0:s.type)!=null?i:"unknown",Error.captureStackTrace(this,v)}};var gm=/^(?!_)(?<manager>.+)@(?<version>.+)$/;function M({workspaceRoot:r}){let e=We.default.join(r,"package.json");try{return(0,se.readJsonSync)(e,"utf8")}catch(t){if(t&&typeof t=="object"&&"code"in t){if(t.code==="ENOENT")throw new v(`no "package.json" found at ${r}`,{type:"package_json-missing"});if(t.code==="EJSONPARSE")throw new v(`failed to parse "package.json" at ${r}`,{type:"package_json-parse_error"})}throw new Error(`unexpected error reading "package.json" at ${r}`)}}function lt({workspaceRoot:r}){let{packageManager:e}=M({workspaceRoot:r});if(e)try{let t=gm.exec(e);if(t){let[s,i]=t;return i}}catch{}}function ze({workspaceRoot:r}){let e=M({workspaceRoot:r}),t=We.default.basename(r),{name:s=t,description:i}=e;return{name:s,description:i}}function oo({workspaceRoot:r}){let e=We.default.join(r,"pnpm-workspace.yaml");if((0,se.existsSync)(e))try{let t=cl.default.load((0,se.readFileSync)(e,"utf8"));if(t instanceof Object&&"packages"in t&&Array.isArray(t.packages))return t.packages}catch{throw new v(`failed to parse ${e}`,{type:"pnpm-workspace_parse_error"})}return[]}function ht({root:r,lockFile:e,workspaceConfig:t}){let s=n=>We.default.join(r,n),i={root:r,lockfile:s(e),packageJson:s("package.json"),nodeModules:s("node_modules")};return t&&(i.workspaceConfig=s(t)),i}function cr({workspaces:r}){var e;return r?Array.isArray(r)?r:"packages"in r?(e=r.packages)!=null?e:[]:[]:[]}function ft({workspaceRoot:r,workspaceGlobs:e}){return e?e.flatMap(t=>{let s=[`${t}/package.json`];return(0,al.sync)(s,{onlyFiles:!0,absolute:!0,cwd:r,ignore:["**/node_modules/**"]})}).map(t=>{let s=We.default.dirname(t),{name:i,description:n}=ze({workspaceRoot:s});return{name:i,description:n,paths:{root:s,packageJson:t,nodeModules:We.default.join(s,"node_modules")}}}):[]}function pt({directory:r}){let e=We.default.resolve(process.cwd(),r);return{exists:(0,se.existsSync)(e),absolute:e}}function he({packageManager:r,action:e,project:t}){let s=t.workspaceData.globs.length>0;return`${e==="remove"?"Removing":"Adding"} ${r} ${s?"workspaces":""} ${e==="remove"?"from":"to"} ${t.name}`}function ll({project:r}){let e=t=>!(t.includes("*")&&(t.includes("**")||t.split("/").slice(0,-1).join("/").includes("*"))||["!","[","]","{","}"].some(s=>t.includes(s)));return r.workspaceData.globs.every(e)}function ie({project:r,options:e}){e!=null&&e.dry||(0,se.rmSync)(r.paths.lockfile,{force:!0})}async function Vs({project:r,options:e}){if(!(e!=null&&e.dry)&&(0,se.existsSync)(r.paths.lockfile))try{let{stdout:t}=await(0,ul.default)("bun",["bun.lockb"],{stdin:"ignore",cwd:r.paths.root});await(0,se.writeFile)(We.default.join(r.paths.root,"yarn.lock"),t)}catch{}finally{(0,se.rmSync)(r.paths.lockfile,{force:!0})}}u();u();u();var Ot=b(require("path")),H=require("fs-extra"),ml=b(require("execa"));u();var hl=b(require("path")),fl=require("fs-extra"),pl=b(require("chalk"));function Em({dependencyList:r,project:e,to:t}){let s=[];return e.workspaceData.workspaces.forEach(i=>{let{name:n}=i;if(r[n]){let o=r[n],a=o.startsWith("workspace:")?o.slice(10):o;r[n]=t.name==="pnpm"?`workspace:${a}`:a,s.push(n)}}),{dependencyList:r,updated:s}}function fe({project:r,workspace:e,to:t,logger:s,options:i}){if(["yarn","npm","bun"].includes(t.name)&&["yarn","npm","bun"].includes(r.packageManager))return;let n=M({workspaceRoot:e.paths.root}),o={dependencies:[],devDependencies:[],peerDependencies:[],optionalDependencies:[]},a=["dependencies","devDependencies","peerDependencies","optionalDependencies"];a.forEach(d=>{let p=n[d];if(p){let{updated:E,dependencyList:C}=Em({dependencyList:p,project:r,to:t});n[d]=C,o[d]=E}});let l=d=>{let p=o[d].length;if(p>0)return`${pl.default.green(p.toString())} ${d}`},c=a.map(l).filter(Boolean),h=`./${hl.default.relative(r.paths.root,e.paths.packageJson)}`;if(c.length>=1){let d="updating";c.forEach((p,E)=>{c.length===1?d+=` ${p} in ${h}`:E===c.length-1?d+=`and ${p} in ${h}`:d+=` ${p}, `}),s.workspaceStep(d)}else s.workspaceStep(`no workspace dependencies found in ${h}`);i!=null&&i.dry||(0,fl.writeJSONSync)(e.paths.packageJson,n,{spaces:2})}var mt={name:"pnpm",lock:"pnpm-lock.yaml"};async function dl(r){let e=Ot.default.join(r.workspaceRoot,mt.lock),t=Ot.default.join(r.workspaceRoot,"pnpm-workspace.yaml"),s=lt({workspaceRoot:r.workspaceRoot});return(0,H.existsSync)(e)||(0,H.existsSync)(t)||s===mt.name}async function ym(r){if(!await dl(r))throw new v("Not a pnpm project",{type:"package_manager-unexpected"});let{name:t,description:s}=ze(r);return{name:t,description:s,packageManager:mt.name,paths:ht({root:r.workspaceRoot,lockFile:mt.lock,workspaceConfig:"pnpm-workspace.yaml"}),workspaceData:{globs:oo(r),workspaces:ft({workspaceGlobs:oo(r),...r})}}}async function Am(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(he({action:"create",packageManager:mt.name,project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),o.packageManager=`${t.name}@${t.version}`,s.rootStep(`adding "packageManager" field to ${e.name} root "package.json"`),i!=null&&i.dry||((0,H.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),n&&(s.rootStep('adding "pnpm-workspace.yaml"'),(0,H.writeFileSync)(Ot.default.join(e.paths.root,"pnpm-workspace.yaml"),`packages:
${e.workspaceData.globs.map(a=>`  - "${a}"`).join(`
`)}`))),n&&(fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})}))}async function Cm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({action:"remove",packageManager:mt.name,project:e}));let n=M({workspaceRoot:e.paths.root});if(e.paths.workspaceConfig&&i&&(t.subStep('removing "pnpm-workspace.yaml"'),s!=null&&s.dry||(0,H.rmSync)(e.paths.workspaceConfig,{force:!0})),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,H.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,H.rm)(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function wm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Ot.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,H.rmSync)(e.paths.lockfile,{force:!0})}async function Fm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${Ot.default.relative(e.paths.root,e.paths.lockfile)} to ${mt.lock}`)},n=async()=>{if(!(t!=null&&t.dry)&&(0,H.existsSync)(e.paths.lockfile))try{await(0,ml.default)(mt.name,["import"],{stdio:"ignore",cwd:e.paths.root})}catch{}finally{ie({project:e,options:t})}};switch(e.packageManager){case"pnpm":break;case"bun":i(),await Vs({project:e,options:t}),await n(),(0,H.rmSync)(Ot.default.join(e.paths.root,"yarn.lock"),{force:!0});break;case"npm":i(),await n();break;case"yarn":i(),await n();break}}var Dl={detect:dl,read:ym,create:Am,remove:Cm,clean:wm,convertLock:Fm};u();var Lr=b(require("path")),be=require("fs-extra");var lr={name:"npm",lock:"package-lock.json"};async function gl(r){let e=Lr.default.join(r.workspaceRoot,lr.lock),t=lt({workspaceRoot:r.workspaceRoot});return(0,be.existsSync)(e)||t===lr.name}async function bm(r){if(!await gl(r))throw new v("Not an npm project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=ze(r),n=cr({workspaces:t.workspaces});return{name:s,description:i,packageManager:lr.name,paths:ht({root:r.workspaceRoot,lockFile:lr.lock}),workspaceData:{globs:n,workspaces:ft({workspaceGlobs:n,...r})}}}async function km(r){let{project:e,options:t,to:s,logger:i}=r,n=e.workspaceData.globs.length>0;i.mainStep(he({packageManager:lr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});i.rootHeader(),i.rootStep(`adding "packageManager" field to ${Lr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${s.name}@${s.version}`,n?(i.rootStep(`adding "workspaces" field to ${Lr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,t!=null&&t.dry||(0,be.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:s,logger:i,options:t}),i.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:s,logger:i,options:t})})):t!=null&&t.dry||(0,be.writeJSONSync)(e.paths.packageJson,o,{spaces:2})}async function Sm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:lr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,be.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,be.rm)(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function _m(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Lr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,be.rmSync)(e.paths.lockfile,{force:!0})}async function vm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":ie({project:e,options:t});break;case"bun":ie({project:e,options:t});break;case"npm":break;case"yarn":ie({project:e,options:t});break}}var El={detect:gl,read:bm,create:km,remove:Sm,clean:_m,convertLock:vm};u();var hr=b(require("path")),ke=require("fs-extra");var Tt={name:"yarn",lock:"yarn.lock"};async function yl(r){let e=hr.default.join(r.workspaceRoot,Tt.lock),t=lt({workspaceRoot:r.workspaceRoot});return(0,ke.existsSync)(e)||t===Tt.name}async function Rm(r){if(!await yl(r))throw new v("Not a yarn project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=ze(r),n=cr({workspaces:t.workspaces});return{name:s,description:i,packageManager:Tt.name,paths:ht({root:r.workspaceRoot,lockFile:Tt.lock}),workspaceData:{globs:n,workspaces:ft({workspaceGlobs:n,...r})}}}async function Bm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(he({packageManager:Tt.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${hr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${hr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||(0,ke.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||(0,ke.writeJSONSync)(e.paths.packageJson,o,{spaces:2})}async function xm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:Tt.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,ke.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,ke.rm)(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Om(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${hr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,ke.rmSync)(e.paths.lockfile,{force:!0})}async function Tm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${hr.default.relative(e.paths.root,e.paths.lockfile)} to ${Tt.lock}`)};switch(e.packageManager){case"pnpm":ie({project:e,options:t});break;case"bun":i(),await Vs({project:e,options:t});break;case"npm":ie({project:e,options:t});break;case"yarn":break}}var Al={detect:yl,read:Rm,create:Bm,remove:xm,clean:Om,convertLock:Tm};u();var Nr=b(require("path")),Se=require("fs-extra");var fr={name:"bun",lock:"bun.lockb"};async function Cl(r){let e=Nr.default.join(r.workspaceRoot,fr.lock),t=lt({workspaceRoot:r.workspaceRoot});return(0,Se.existsSync)(e)||t===fr.name}async function Pm(r){if(!await Cl(r))throw new v("Not a bun project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=ze(r),n=cr({workspaces:t.workspaces});return{name:s,description:i,packageManager:fr.name,paths:ht({root:r.workspaceRoot,lockFile:fr.lock}),workspaceData:{globs:n,workspaces:ft({workspaceGlobs:n,...r})}}}async function Lm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;if(!ll({project:e}))throw new v("Unable to convert project to bun - workspace globs unsupported",{type:"bun-workspace_glob_error"});s.mainStep(he({packageManager:fr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${Nr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${Nr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||(0,Se.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||(0,Se.writeJSONSync)(e.paths.packageJson,o,{spaces:2})}async function Nm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:fr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,Se.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,Se.rm)(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Mm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Nr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,Se.rmSync)(e.paths.lockfile,{force:!0})}async function Im(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":ie({project:e,options:t});break;case"bun":break;case"npm":ie({project:e,options:t});break;case"yarn":ie({project:e,options:t});break}}var wl={detect:Cl,read:Pm,create:Lm,remove:Nm,clean:Mm,convertLock:Im};var Pt={pnpm:Dl,yarn:Al,npm:El,bun:wl};async function Ks({root:r}){let{exists:e,absolute:t}=pt({directory:r});if(!e)throw new v(`Could not find directory at ${t}. Ensure the directory exists.`,{type:"invalid_directory"});for(let{detect:s,read:i}of Object.values(Pt))if(await s({workspaceRoot:t}))return i({workspaceRoot:t});throw new v("Could not determine package manager. Add `packageManager` to `package.json` or ensure a lockfile is present.",{type:"package_manager-unable_to_detect"})}async function uo(r){let e=new ct;e.hero();let t=await Fl.default.prompt({type:"input",name:"directoryInput",message:"Where is the root of the repo?",when:!r,default:".",validate:p=>{let{exists:E,absolute:C}=pt({directory:p});return E?!0:`Directory ${Ge.default.dim(`(${C})`)} does not exist`},filter:p=>p.trim()}),{directoryInput:s=r}=t,{exists:i,absolute:n}=pt({directory:s});if(!i)return e.error(`Directory ${Ge.default.dim(`(${n})`)} does not exist`),process.exit(1);let o=await Ks({root:n}),a=o.workspaceData.workspaces.length,l=a>0,c={};o.workspaceData.workspaces.forEach(p=>{let C=Zs.default.relative(n,p.paths.root).split(Zs.default.sep)[0];C in c||(c[C]=[]),c[C].push(p)});let h=p=>`${p.name} (${Ge.default.italic(`./${Zs.default.relative(n,p.paths.root)}`)})`,d=({number:p,dir:E,workspaces:C})=>{e.indented(2,`${p}. ${Ge.default.bold(E)}`),C.forEach((T,k)=>{e.indented(3,`${k+1}. ${h(T)}`)})};e.header("Repository Summary"),e.indented(1,`${Ge.default.underline(o.name)}:`),e.indented(1,`Package Manager: ${Ge.default.bold(Ge.default.italic(o.packageManager))}`),l&&(e.indented(1,`Workspaces (${Ge.default.bold(a.toString())}):`),Object.keys(c).forEach((p,E)=>{d({number:E+1,workspaces:c[p],dir:p})}),e.blankLine())}u();var ao=b(require("inquirer")),co=b(require("chalk"));u();var Rl=b(require("chalk"));u();var kl=b(require("execa")),Sl=b(require("ora")),_l=require("semver");var bl={npm:[{name:"npm",template:"npm",command:"npm",installArgs:["install"],version:"latest",executable:"npx",semver:"*",default:!0}],pnpm:[{name:"pnpm6",template:"pnpm",command:"pnpm",installArgs:["install"],version:"latest-6",executable:"pnpx",semver:"6.x"},{name:"pnpm",template:"pnpm",command:"pnpm",installArgs:["install","--fix-lockfile"],version:"latest",executable:"pnpm dlx",semver:">=7",default:!0}],yarn:[{name:"yarn",template:"yarn",command:"yarn",installArgs:["install"],version:"1.x",executable:"npx",semver:"<2",default:!0},{name:"berry",template:"berry",command:"yarn",installArgs:["install","--no-immutable"],version:"stable",executable:"yarn dlx",semver:">=2"}],bun:[{name:"bun",template:"bun",command:"bun",installArgs:["install"],version:"latest",executable:"bunx",semver:"^1.0.1",default:!0}]};function qm(r){let{version:e,name:t}=r;return e?bl[t].find(s=>(0,_l.satisfies)(e,s.semver)):bl[t].find(s=>s.default)}async function vl(r){let{to:e,logger:t,options:s}=r,i=t!=null?t:new ct(s),n=qm(e);if(!n)throw new v("Unsupported package manager version.",{type:"package_manager-unsupported_version"});if(i.subStep(`running "${n.command} ${n.installArgs.join(" ")}"`),!(s!=null&&s.dry)){let o;i.interactive&&(o=(0,Sl.default)({text:"installing dependencies...",spinner:{frames:i.installerFrames()}}).start());try{await(0,kl.default)(n.command,n.installArgs,{cwd:r.project.paths.root}),o&&o.stop(),i.subStep("dependencies installed")}catch(a){throw i.subStepFailure("failed to install dependencies"),a}}}async function Bl({project:r,convertTo:e,logger:t,options:s}){if(t.header(`Converting project from ${r.packageManager} to ${e.name}.`),r.packageManager===e.name)throw new v("You are already using this package manager",{type:"package_manager-already_in_use"});if(!e.version)throw new v(`${e.name} is not installed, or could not be located`,{type:"package_manager-could_not_be_found"});let i=e;await Pt[r.packageManager].remove({project:r,to:i,logger:t,options:s}),await Pt[i.name].create({project:r,to:i,logger:t,options:s}),t.mainStep("Installing dependencies"),s!=null&&s.skipInstall?t.subStep(Rl.default.yellow("Skipping install")):(await Pt[i.name].convertLock({project:r,to:i,logger:t,options:s}),await vl({project:r,to:i,logger:t,options:s})),t.mainStep(`Cleaning up ${r.packageManager} workspaces`),await Pt[r.packageManager].clean({project:r,logger:t})}function jm({packageManager:r,currentWorkspaceManger:e,availablePackageManagers:t}){return e===r?"already in use":t[r]?!1:"not installed"}async function lo(r,e,t){let s=new ct(t);s.hero(),s.header("Welcome, let's convert your project."),s.blankLine();let i=await ao.default.prompt({type:"input",name:"directoryInput",message:"Where is the root of your repo?",when:!r,default:".",validate:p=>{let{exists:E,absolute:C}=pt({directory:p});return E?!0:`Directory ${co.default.dim(`(${C})`)} does not exist`},filter:p=>p.trim()}),{directoryInput:n=r}=i,{exists:o,absolute:a}=pt({directory:n});if(!o)return s.error(`Directory ${co.default.dim(`(${a})`)} does not exist`),process.exit(1);let[l,c]=await Promise.all([Ks({root:a}),Ei()]),h=await ao.default.prompt({name:"packageManagerInput",type:"list",message:`Convert from ${l.packageManager} workspaces to:`,when:!e||!Object.keys(c).includes(e),choices:[{pm:"npm",label:"npm workspaces"},{pm:"pnpm",label:"pnpm workspaces"},{pm:"yarn",label:"yarn workspaces"},{pm:"bun",label:"bun workspaces (beta)"}].map(({pm:p,label:E})=>({name:E,value:p,disabled:jm({packageManager:p,currentWorkspaceManger:l.packageManager,availablePackageManagers:c})}))}),{packageManagerInput:d=e}=h;await Bl({project:l,convertTo:{name:d,version:c[d]},logger:s,options:t})}var Xs=new xl.Command;Xs.name("@turbo/workspaces").description("Tools for working with package manager workspaces").version(nl.version,"-v, --version","output the current version");Xs.command("convert").description("Convert project between workspace managers").argument("[path]","Project root").argument("[package-manager]","Package manager to convert to").option("--skip-install","Do not run a package manager install after conversion",!1).option("--dry","Dry run (no changes are made to files)",!1).option("--force","Bypass Git safety checks and forcibly run conversion",!1).action(lo);Xs.command("summary").description("Display a summary of the specified project").argument("[path]","Project root").action(uo);Xs.parseAsync().catch(r=>{De.log(),r instanceof v?De.log(ho.default.red(r.message)):(De.log(ho.default.red("Unexpected error. Please report it as a bug:")),De.log(r)),De.log(),process.exit(1)});
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
