#!/usr/bin/env node
import{a as n,b as u,c as $,d as D,e as d,g as f,h,k as W}from"./chunk-7VWHKJIK.mjs";n();import L from"chalk";import{Command as B}from"commander";var A={name:"@turbo/workspaces",version:"1.13.4",description:"Tools for working with package managers",homepage:"https://turbo.build/repo",license:"MPL-2.0",repository:{type:"git",url:"https://github.com/vercel/turbo",directory:"packages/turbo-workspaces"},bugs:{url:"https://github.com/vercel/turbo/issues"},bin:"dist/cli.js",module:"dist/index.mjs",main:"dist/index.js",types:"dist/index.d.ts",scripts:{build:"tsup",dev:"tsup --watch",test:"jest",lint:"eslint src/","check-types":"tsc --noEmit","lint:prettier":"prettier -c . --cache --ignore-path=../../.prettierignore"},dependencies:{chalk:"2.4.2",commander:"^10.0.0",execa:"5.1.1","fast-glob":"^3.2.12","fs-extra":"^10.1.0","gradient-string":"^2.0.0",inquirer:"^8.0.0","js-yaml":"^4.1.0",ora:"4.1.1",rimraf:"^3.0.2",semver:"^7.3.5","update-check":"^1.5.4"},devDependencies:{"@turbo/eslint-config":"workspace:*","@turbo/test-utils":"workspace:*","@turbo/tsconfig":"workspace:*","@turbo/utils":"workspace:*","@types/chalk-animation":"^1.6.0","@types/fs-extra":"^9.0.13","@types/gradient-string":"^1.1.2","@types/inquirer":"^7.3.1","@types/jest":"^27.4.0","@types/js-yaml":"^4.0.5","@types/node":"^18.17.2","@types/rimraf":"^3.0.2","@types/semver":"^7.3.9",jest:"^27.4.3",semver:"^7.3.5","strip-ansi":"^6.0.1","ts-jest":"^27.1.1",tsup:"^5.10.3",typescript:"5.3.3"},files:["dist"],publishConfig:{access:"public"}};n();n();import C from"path";import R from"inquirer";import i from"chalk";async function M(t){let r=new h;r.hero();let p=await R.prompt({type:"input",name:"directoryInput",message:"Where is the root of the repo?",when:!t,default:".",validate:e=>{let{exists:o,absolute:s}=d({directory:e});return o?!0:`Directory ${i.dim(`(${s})`)} does not exist`},filter:e=>e.trim()}),{directoryInput:c=t}=p,{exists:v,absolute:m}=d({directory:c});if(!v)return r.error(`Directory ${i.dim(`(${m})`)} does not exist`),process.exit(1);let g=await f({root:m}),l=g.workspaceData.workspaces.length,y=l>0,a={};g.workspaceData.workspaces.forEach(e=>{let s=C.relative(m,e.paths.root).split(C.sep)[0];s in a||(a[s]=[]),a[s].push(e)});let w=e=>`${e.name} (${i.italic(`./${C.relative(m,e.paths.root)}`)})`,k=({number:e,dir:o,workspaces:s})=>{r.indented(2,`${e}. ${i.bold(o)}`),s.forEach((O,S)=>{r.indented(3,`${S+1}. ${w(O)}`)})};r.header("Repository Summary"),r.indented(1,`${i.underline(g.name)}:`),r.indented(1,`Package Manager: ${i.bold(i.italic(g.packageManager))}`),y&&(r.indented(1,`Workspaces (${i.bold(l.toString())}):`),Object.keys(a).forEach((e,o)=>{k({number:o+1,workspaces:a[e],dir:e})}),r.blankLine())}n();import I from"inquirer";import E from"chalk";function T({packageManager:t,currentWorkspaceManger:r,availablePackageManagers:p}){return r===t?"already in use":p[t]?!1:"not installed"}async function j(t,r,p){let c=new h(p);c.hero(),c.header("Welcome, let's convert your project."),c.blankLine();let v=await I.prompt({type:"input",name:"directoryInput",message:"Where is the root of your repo?",when:!t,default:".",validate:e=>{let{exists:o,absolute:s}=d({directory:e});return o?!0:`Directory ${E.dim(`(${s})`)} does not exist`},filter:e=>e.trim()}),{directoryInput:m=t}=v,{exists:g,absolute:l}=d({directory:m});if(!g)return c.error(`Directory ${E.dim(`(${l})`)} does not exist`),process.exit(1);let[y,a]=await Promise.all([f({root:l}),$()]),w=await I.prompt({name:"packageManagerInput",type:"list",message:`Convert from ${y.packageManager} workspaces to:`,when:!r||!Object.keys(a).includes(r),choices:[{pm:"npm",label:"npm workspaces"},{pm:"pnpm",label:"pnpm workspaces"},{pm:"yarn",label:"yarn workspaces"},{pm:"bun",label:"bun workspaces (beta)"}].map(({pm:e,label:o})=>({name:o,value:e,disabled:T({packageManager:e,currentWorkspaceManger:y.packageManager,availablePackageManagers:a})}))}),{packageManagerInput:k=r}=w;await W({project:y,convertTo:{name:k,version:a[k]},logger:c,options:p})}var b=new B;b.name("@turbo/workspaces").description("Tools for working with package manager workspaces").version(A.version,"-v, --version","output the current version");b.command("convert").description("Convert project between workspace managers").argument("[path]","Project root").argument("[package-manager]","Package manager to convert to").option("--skip-install","Do not run a package manager install after conversion",!1).option("--dry","Dry run (no changes are made to files)",!1).option("--force","Bypass Git safety checks and forcibly run conversion",!1).action(j);b.command("summary").description("Display a summary of the specified project").argument("[path]","Project root").action(M);b.parseAsync().catch(t=>{u.log(),t instanceof D?u.log(L.red(t.message)):(u.log(L.red("Unexpected error. Please report it as a bug:")),u.log(t)),u.log(),process.exit(1)});
