"use strict";var gl=Object.create;var Tr=Object.defineProperty;var El=Object.getOwnPropertyDescriptor;var yl=Object.getOwnPropertyNames;var Al=Object.getPrototypeOf,Cl=Object.prototype.hasOwnProperty;var wl=(r,e)=>()=>(r&&(e=r(r=0)),e);var E=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),Fl=(r,e)=>{for(var t in e)Tr(r,t,{get:e[t],enumerable:!0})},no=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of yl(e))!Cl.call(r,i)&&i!==t&&Tr(r,i,{get:()=>e[i],enumerable:!(s=El(e,i))||s.enumerable});return r};var b=(r,e,t)=>(t=r!=null?gl(Al(r)):{},no(e||!r||!r.__esModule?Tr(t,"default",{value:r,enumerable:!0}):t,r)),bl=r=>no(Tr({},"__esModule",{value:!0}),r);var u=wl(()=>{});var oo=E((ym,ke)=>{u();function Xs(r){return ke.exports=Xs=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ke.exports.__esModule=!0,ke.exports.default=ke.exports,Xs(r)}ke.exports=Xs,ke.exports.__esModule=!0,ke.exports.default=ke.exports});var co=E((Am,_e)=>{u();var uo=oo().default;function ao(){"use strict";_e.exports=ao=function(){return e},_e.exports.__esModule=!0,_e.exports.default=_e.exports;var r,e={},t=Object.prototype,s=t.hasOwnProperty,i=Object.defineProperty||function(g,p,d){g[p]=d.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(g,p,d){return Object.defineProperty(g,p,{value:d,enumerable:!0,configurable:!0,writable:!0}),g[p]}try{c({},"")}catch{c=function(d,y,w){return d[y]=w}}function h(g,p,d,y){var w=p&&p.prototype instanceof x?p:x,C=Object.create(w.prototype),P=new Ks(y||[]);return i(C,"_invoke",{value:dl(g,d,P)}),C}function m(g,p,d){try{return{type:"normal",arg:g.call(p,d)}}catch(y){return{type:"throw",arg:y}}}e.wrap=h;var D="suspendedStart",A="suspendedYield",F="executing",T="completed",S={};function x(){}function lt(){}function de(){}var Js={};c(Js,o,function(){return this});var Ys=Object.getPrototypeOf,xr=Ys&&Ys(Ys(Zs([])));xr&&xr!==t&&s.call(xr,o)&&(Js=xr);var vt=de.prototype=x.prototype=Object.create(Js);function so(g){["next","throw","return"].forEach(function(p){c(g,p,function(d){return this._invoke(p,d)})})}function Or(g,p){function d(w,C,P,G){var J=m(g[w],g,C);if(J.type!=="throw"){var ht=J.arg,Ge=ht.value;return Ge&&uo(Ge)=="object"&&s.call(Ge,"__await")?p.resolve(Ge.__await).then(function(ft){d("next",ft,P,G)},function(ft){d("throw",ft,P,G)}):p.resolve(Ge).then(function(ft){ht.value=ft,P(ht)},function(ft){return d("throw",ft,P,G)})}G(J.arg)}var y;i(this,"_invoke",{value:function(C,P){function G(){return new p(function(J,ht){d(C,P,J,ht)})}return y=y?y.then(G,G):G()}})}function dl(g,p,d){var y=D;return function(w,C){if(y===F)throw new Error("Generator is already running");if(y===T){if(w==="throw")throw C;return{value:r,done:!0}}for(d.method=w,d.arg=C;;){var P=d.delegate;if(P){var G=io(P,d);if(G){if(G===S)continue;return G}}if(d.method==="next")d.sent=d._sent=d.arg;else if(d.method==="throw"){if(y===D)throw y=T,d.arg;d.dispatchException(d.arg)}else d.method==="return"&&d.abrupt("return",d.arg);y=F;var J=m(g,p,d);if(J.type==="normal"){if(y=d.done?T:A,J.arg===S)continue;return{value:J.arg,done:d.done}}J.type==="throw"&&(y=T,d.method="throw",d.arg=J.arg)}}}function io(g,p){var d=p.method,y=g.iterator[d];if(y===r)return p.delegate=null,d==="throw"&&g.iterator.return&&(p.method="return",p.arg=r,io(g,p),p.method==="throw")||d!=="return"&&(p.method="throw",p.arg=new TypeError("The iterator does not provide a '"+d+"' method")),S;var w=m(y,g.iterator,p.arg);if(w.type==="throw")return p.method="throw",p.arg=w.arg,p.delegate=null,S;var C=w.arg;return C?C.done?(p[g.resultName]=C.value,p.next=g.nextLoc,p.method!=="return"&&(p.method="next",p.arg=r),p.delegate=null,S):C:(p.method="throw",p.arg=new TypeError("iterator result is not an object"),p.delegate=null,S)}function Dl(g){var p={tryLoc:g[0]};1 in g&&(p.catchLoc=g[1]),2 in g&&(p.finallyLoc=g[2],p.afterLoc=g[3]),this.tryEntries.push(p)}function Vs(g){var p=g.completion||{};p.type="normal",delete p.arg,g.completion=p}function Ks(g){this.tryEntries=[{tryLoc:"root"}],g.forEach(Dl,this),this.reset(!0)}function Zs(g){if(g||g===""){var p=g[o];if(p)return p.call(g);if(typeof g.next=="function")return g;if(!isNaN(g.length)){var d=-1,y=function w(){for(;++d<g.length;)if(s.call(g,d))return w.value=g[d],w.done=!1,w;return w.value=r,w.done=!0,w};return y.next=y}}throw new TypeError(uo(g)+" is not iterable")}return lt.prototype=de,i(vt,"constructor",{value:de,configurable:!0}),i(de,"constructor",{value:lt,configurable:!0}),lt.displayName=c(de,l,"GeneratorFunction"),e.isGeneratorFunction=function(g){var p=typeof g=="function"&&g.constructor;return!!p&&(p===lt||(p.displayName||p.name)==="GeneratorFunction")},e.mark=function(g){return Object.setPrototypeOf?Object.setPrototypeOf(g,de):(g.__proto__=de,c(g,l,"GeneratorFunction")),g.prototype=Object.create(vt),g},e.awrap=function(g){return{__await:g}},so(Or.prototype),c(Or.prototype,a,function(){return this}),e.AsyncIterator=Or,e.async=function(g,p,d,y,w){w===void 0&&(w=Promise);var C=new Or(h(g,p,d,y),w);return e.isGeneratorFunction(p)?C:C.next().then(function(P){return P.done?P.value:C.next()})},so(vt),c(vt,l,"Generator"),c(vt,o,function(){return this}),c(vt,"toString",function(){return"[object Generator]"}),e.keys=function(g){var p=Object(g),d=[];for(var y in p)d.push(y);return d.reverse(),function w(){for(;d.length;){var C=d.pop();if(C in p)return w.value=C,w.done=!1,w}return w.done=!0,w}},e.values=Zs,Ks.prototype={constructor:Ks,reset:function(p){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(Vs),!p)for(var d in this)d.charAt(0)==="t"&&s.call(this,d)&&!isNaN(+d.slice(1))&&(this[d]=r)},stop:function(){this.done=!0;var p=this.tryEntries[0].completion;if(p.type==="throw")throw p.arg;return this.rval},dispatchException:function(p){if(this.done)throw p;var d=this;function y(ht,Ge){return P.type="throw",P.arg=p,d.next=ht,Ge&&(d.method="next",d.arg=r),!!Ge}for(var w=this.tryEntries.length-1;w>=0;--w){var C=this.tryEntries[w],P=C.completion;if(C.tryLoc==="root")return y("end");if(C.tryLoc<=this.prev){var G=s.call(C,"catchLoc"),J=s.call(C,"finallyLoc");if(G&&J){if(this.prev<C.catchLoc)return y(C.catchLoc,!0);if(this.prev<C.finallyLoc)return y(C.finallyLoc)}else if(G){if(this.prev<C.catchLoc)return y(C.catchLoc,!0)}else{if(!J)throw new Error("try statement without catch or finally");if(this.prev<C.finallyLoc)return y(C.finallyLoc)}}}},abrupt:function(p,d){for(var y=this.tryEntries.length-1;y>=0;--y){var w=this.tryEntries[y];if(w.tryLoc<=this.prev&&s.call(w,"finallyLoc")&&this.prev<w.finallyLoc){var C=w;break}}C&&(p==="break"||p==="continue")&&C.tryLoc<=d&&d<=C.finallyLoc&&(C=null);var P=C?C.completion:{};return P.type=p,P.arg=d,C?(this.method="next",this.next=C.finallyLoc,S):this.complete(P)},complete:function(p,d){if(p.type==="throw")throw p.arg;return p.type==="break"||p.type==="continue"?this.next=p.arg:p.type==="return"?(this.rval=this.arg=p.arg,this.method="return",this.next="end"):p.type==="normal"&&d&&(this.next=d),S},finish:function(p){for(var d=this.tryEntries.length-1;d>=0;--d){var y=this.tryEntries[d];if(y.finallyLoc===p)return this.complete(y.completion,y.afterLoc),Vs(y),S}},catch:function(p){for(var d=this.tryEntries.length-1;d>=0;--d){var y=this.tryEntries[d];if(y.tryLoc===p){var w=y.completion;if(w.type==="throw"){var C=w.arg;Vs(y)}return C}}throw new Error("illegal catch attempt")},delegateYield:function(p,d,y){return this.delegate={iterator:Zs(p),resultName:d,nextLoc:y},this.method==="next"&&(this.arg=r),S}},e}_e.exports=ao,_e.exports.__esModule=!0,_e.exports.default=_e.exports});var ho=E((Cm,lo)=>{u();var Pr=co()();lo.exports=Pr;try{regeneratorRuntime=Pr}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=Pr:Function("r","regeneratorRuntime = r")(Pr)}});var po=E((zm,ni)=>{"use strict";u();var fo=(r,...e)=>new Promise(t=>{t(r(...e))});ni.exports=fo;ni.exports.default=fo});var Do=E((Wm,oi)=>{"use strict";u();var Sl=po(),mo=r=>{if(!((Number.isInteger(r)||r===1/0)&&r>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],t=0,s=()=>{t--,e.length>0&&e.shift()()},i=(a,l,...c)=>{t++;let h=Sl(a,...c);l(h),h.then(s,s)},n=(a,l,...c)=>{t<r?i(a,l,...c):e.push(i.bind(null,a,l,...c))},o=(a,...l)=>new Promise(c=>n(a,c,...l));return Object.defineProperties(o,{activeCount:{get:()=>t},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};oi.exports=mo;oi.exports.default=mo});var yo=E((Gm,ui)=>{"use strict";u();var go=Do(),Lr=class extends Error{constructor(e){super(),this.value=e}},kl=async(r,e)=>e(await r),_l=async r=>{let e=await Promise.all(r);if(e[1]===!0)throw new Lr(e[0]);return!1},Eo=async(r,e,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let s=go(t.concurrency),i=[...r].map(o=>[o,s(kl,o,e)]),n=go(t.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>n(_l,o)))}catch(o){if(o instanceof Lr)return o.value;throw o}};ui.exports=Eo;ui.exports.default=Eo});var So=E((Hm,ai)=>{"use strict";u();var Ao=require("path"),Nr=require("fs"),{promisify:Co}=require("util"),Rl=yo(),vl=Co(Nr.stat),Bl=Co(Nr.lstat),wo={directory:"isDirectory",file:"isFile"};function Fo({type:r}){if(!(r in wo))throw new Error(`Invalid type specified: ${r}`)}var bo=(r,e)=>r===void 0||e[wo[r]]();ai.exports=async(r,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},Fo(e);let t=e.allowSymlinks?vl:Bl;return Rl(r,async s=>{try{let i=await t(Ao.resolve(e.cwd,s));return bo(e.type,i)}catch{return!1}},e)};ai.exports.sync=(r,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},Fo(e);let t=e.allowSymlinks?Nr.statSync:Nr.lstatSync;for(let s of r)try{let i=t(Ao.resolve(e.cwd,s));if(bo(e.type,i))return s}catch{}}});var _o=E((Jm,ci)=>{"use strict";u();var ko=require("fs"),{promisify:xl}=require("util"),Ol=xl(ko.access);ci.exports=async r=>{try{return await Ol(r),!0}catch{return!1}};ci.exports.sync=r=>{try{return ko.accessSync(r),!0}catch{return!1}}});var vo=E((Ym,xt)=>{"use strict";u();var He=require("path"),Ir=So(),Ro=_o(),li=Symbol("findUp.stop");xt.exports=async(r,e={})=>{let t=He.resolve(e.cwd||""),{root:s}=He.parse(t),i=[].concat(r),n=async o=>{if(typeof r!="function")return Ir(i,o);let a=await r(o.cwd);return typeof a=="string"?Ir([a],o):a};for(;;){let o=await n({...e,cwd:t});if(o===li)return;if(o)return He.resolve(t,o);if(t===s)return;t=He.dirname(t)}};xt.exports.sync=(r,e={})=>{let t=He.resolve(e.cwd||""),{root:s}=He.parse(t),i=[].concat(r),n=o=>{if(typeof r!="function")return Ir.sync(i,o);let a=r(o.cwd);return typeof a=="string"?Ir.sync([a],o):a};for(;;){let o=n({...e,cwd:t});if(o===li)return;if(o)return He.resolve(t,o);if(t===s)return;t=He.dirname(t)}};xt.exports.exists=Ro;xt.exports.sync.exists=Ro.sync;xt.exports.stop=li});var Ot=E((Fd,Mo)=>{"use strict";u();var Io=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);Mo.exports=r=>r?Object.keys(r).map(e=>[Io.has(e)?Io.get(e):e,r[e]]).reduce((e,t)=>(e[t[0]]=t[1],e),Object.create(null)):{}});var Gr=E((bd,Jo)=>{"use strict";u();var qo=typeof process=="object"&&process?process:{stdout:null,stderr:null},Kl=require("events"),Uo=require("stream"),jo=require("string_decoder").StringDecoder,ve=Symbol("EOF"),Be=Symbol("maybeEmitEnd"),Je=Symbol("emittedEnd"),Ur=Symbol("emittingEnd"),cr=Symbol("emittedError"),jr=Symbol("closed"),$o=Symbol("read"),$r=Symbol("flush"),zo=Symbol("flushChunk"),Y=Symbol("encoding"),xe=Symbol("decoder"),zr=Symbol("flowing"),lr=Symbol("paused"),Tt=Symbol("resume"),L=Symbol("buffer"),De=Symbol("pipes"),M=Symbol("bufferLength"),pi=Symbol("bufferPush"),mi=Symbol("bufferShift"),U=Symbol("objectMode"),j=Symbol("destroyed"),di=Symbol("emitData"),Wo=Symbol("emitEnd"),Di=Symbol("emitEnd2"),Oe=Symbol("async"),hr=r=>Promise.resolve().then(r),Go=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Zl=Go&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),Xl=Go&&Symbol.iterator||Symbol("iterator not implemented"),Ql=r=>r==="end"||r==="finish"||r==="prefinish",eh=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,th=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Wr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Tt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},gi=class extends Wr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};Jo.exports=class Ho extends Uo{constructor(e){super(),this[zr]=!1,this[lr]=!1,this[De]=[],this[L]=[],this[U]=e&&e.objectMode||!1,this[U]?this[Y]=null:this[Y]=e&&e.encoding||null,this[Y]==="buffer"&&(this[Y]=null),this[Oe]=e&&!!e.async||!1,this[xe]=this[Y]?new jo(this[Y]):null,this[ve]=!1,this[Je]=!1,this[Ur]=!1,this[jr]=!1,this[cr]=null,this.writable=!0,this.readable=!0,this[M]=0,this[j]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[L]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[De]})}get bufferLength(){return this[M]}get encoding(){return this[Y]}set encoding(e){if(this[U])throw new Error("cannot set encoding in objectMode");if(this[Y]&&e!==this[Y]&&(this[xe]&&this[xe].lastNeed||this[M]))throw new Error("cannot change encoding");this[Y]!==e&&(this[xe]=e?new jo(e):null,this[L].length&&(this[L]=this[L].map(t=>this[xe].write(t)))),this[Y]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[U]}set objectMode(e){this[U]=this[U]||!!e}get async(){return this[Oe]}set async(e){this[Oe]=this[Oe]||!!e}write(e,t,s){if(this[ve])throw new Error("write after end");if(this[j])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Oe]?hr:n=>n();return!this[U]&&!Buffer.isBuffer(e)&&(th(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):eh(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[U]?(this.flowing&&this[M]!==0&&this[$r](!0),this.flowing?this.emit("data",e):this[pi](e),this[M]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[Y]&&!this[xe].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[Y]&&(e=this[xe].write(e)),this.flowing&&this[M]!==0&&this[$r](!0),this.flowing?this.emit("data",e):this[pi](e),this[M]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[M]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[j])return null;if(this[M]===0||e===0||e>this[M])return this[Be](),null;this[U]&&(e=null),this[L].length>1&&!this[U]&&(this.encoding?this[L]=[this[L].join("")]:this[L]=[Buffer.concat(this[L],this[M])]);let t=this[$o](e||null,this[L][0]);return this[Be](),t}[$o](e,t){return e===t.length||e===null?this[mi]():(this[L][0]=t.slice(e),t=t.slice(0,e),this[M]-=e),this.emit("data",t),!this[L].length&&!this[ve]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[ve]=!0,this.writable=!1,(this.flowing||!this[lr])&&this[Be](),this}[Tt](){this[j]||(this[lr]=!1,this[zr]=!0,this.emit("resume"),this[L].length?this[$r]():this[ve]?this[Be]():this.emit("drain"))}resume(){return this[Tt]()}pause(){this[zr]=!1,this[lr]=!0}get destroyed(){return this[j]}get flowing(){return this[zr]}get paused(){return this[lr]}[pi](e){this[U]?this[M]+=1:this[M]+=e.length,this[L].push(e)}[mi](){return this[L].length&&(this[U]?this[M]-=1:this[M]-=this[L][0].length),this[L].shift()}[$r](e){do;while(this[zo](this[mi]()));!e&&!this[L].length&&!this[ve]&&this.emit("drain")}[zo](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[j])return;let s=this[Je];return t=t||{},e===qo.stdout||e===qo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this[De].push(t.proxyErrors?new gi(this,e,t):new Wr(this,e,t)),this[Oe]?hr(()=>this[Tt]()):this[Tt]()),e}unpipe(e){let t=this[De].find(s=>s.dest===e);t&&(this[De].splice(this[De].indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this[De].length&&!this.flowing?this[Tt]():e==="readable"&&this[M]!==0?super.emit("readable"):Ql(e)&&this[Je]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[cr]&&(this[Oe]?hr(()=>t.call(this,this[cr])):t.call(this,this[cr])),s}get emittedEnd(){return this[Je]}[Be](){!this[Ur]&&!this[Je]&&!this[j]&&this[L].length===0&&this[ve]&&(this[Ur]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[jr]&&this.emit("close"),this[Ur]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==j&&this[j])return;if(e==="data")return t?this[Oe]?hr(()=>this[di](t)):this[di](t):!1;if(e==="end")return this[Wo]();if(e==="close"){if(this[jr]=!0,!this[Je]&&!this[j])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[cr]=t;let n=super.emit("error",t);return this[Be](),n}else if(e==="resume"){let n=super.emit("resume");return this[Be](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[Be](),i}[di](e){for(let s of this[De])s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[Be](),t}[Wo](){this[Je]||(this[Je]=!0,this.readable=!1,this[Oe]?hr(()=>this[Di]()):this[Di]())}[Di](){if(this[xe]){let t=this[xe].end();if(t){for(let s of this[De])s.dest.write(t);super.emit("data",t)}}for(let t of this[De])t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[U]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[U]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[U]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[U]?Promise.reject(new Error("cannot concat in objectMode")):this[Y]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(j,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Zl](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[ve])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[ve]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(j,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[Xl](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[j]?(e?this.emit("error",e):this.emit(j),this):(this[j]=!0,this[L].length=0,this[M]=0,typeof this.close=="function"&&!this[jr]&&this.close(),e?this.emit("error",e):this.emit(j),this)}static isStream(e){return!!e&&(e instanceof Ho||e instanceof Uo||e instanceof Kl&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Vo=E((Sd,Yo)=>{u();var rh=require("zlib").constants||{ZLIB_VERNUM:4736};Yo.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},rh))});var Fi=E((kd,iu)=>{"use strict";u();var Ko=typeof process=="object"&&process?process:{stdout:null,stderr:null},sh=require("events"),Zo=require("stream"),Xo=require("string_decoder").StringDecoder,Te=Symbol("EOF"),Pe=Symbol("maybeEmitEnd"),Ye=Symbol("emittedEnd"),Hr=Symbol("emittingEnd"),fr=Symbol("emittedError"),Jr=Symbol("closed"),Qo=Symbol("read"),Yr=Symbol("flush"),eu=Symbol("flushChunk"),V=Symbol("encoding"),Le=Symbol("decoder"),Vr=Symbol("flowing"),pr=Symbol("paused"),Pt=Symbol("resume"),q=Symbol("bufferLength"),Ei=Symbol("bufferPush"),yi=Symbol("bufferShift"),$=Symbol("objectMode"),z=Symbol("destroyed"),Ai=Symbol("emitData"),tu=Symbol("emitEnd"),Ci=Symbol("emitEnd2"),Ne=Symbol("async"),mr=r=>Promise.resolve().then(r),ru=global._MP_NO_ITERATOR_SYMBOLS_!=="1",ih=ru&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),nh=ru&&Symbol.iterator||Symbol("iterator not implemented"),oh=r=>r==="end"||r==="finish"||r==="prefinish",uh=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,ah=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Kr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[Pt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},wi=class extends Kr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};iu.exports=class su extends Zo{constructor(e){super(),this[Vr]=!1,this[pr]=!1,this.pipes=[],this.buffer=[],this[$]=e&&e.objectMode||!1,this[$]?this[V]=null:this[V]=e&&e.encoding||null,this[V]==="buffer"&&(this[V]=null),this[Ne]=e&&!!e.async||!1,this[Le]=this[V]?new Xo(this[V]):null,this[Te]=!1,this[Ye]=!1,this[Hr]=!1,this[Jr]=!1,this[fr]=null,this.writable=!0,this.readable=!0,this[q]=0,this[z]=!1}get bufferLength(){return this[q]}get encoding(){return this[V]}set encoding(e){if(this[$])throw new Error("cannot set encoding in objectMode");if(this[V]&&e!==this[V]&&(this[Le]&&this[Le].lastNeed||this[q]))throw new Error("cannot change encoding");this[V]!==e&&(this[Le]=e?new Xo(e):null,this.buffer.length&&(this.buffer=this.buffer.map(t=>this[Le].write(t)))),this[V]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[$]}set objectMode(e){this[$]=this[$]||!!e}get async(){return this[Ne]}set async(e){this[Ne]=this[Ne]||!!e}write(e,t,s){if(this[Te])throw new Error("write after end");if(this[z])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Ne]?mr:n=>n();return!this[$]&&!Buffer.isBuffer(e)&&(ah(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):uh(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[$]?(this.flowing&&this[q]!==0&&this[Yr](!0),this.flowing?this.emit("data",e):this[Ei](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[V]&&!this[Le].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[V]&&(e=this[Le].write(e)),this.flowing&&this[q]!==0&&this[Yr](!0),this.flowing?this.emit("data",e):this[Ei](e),this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[q]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[z])return null;if(this[q]===0||e===0||e>this[q])return this[Pe](),null;this[$]&&(e=null),this.buffer.length>1&&!this[$]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[q])]);let t=this[Qo](e||null,this.buffer[0]);return this[Pe](),t}[Qo](e,t){return e===t.length||e===null?this[yi]():(this.buffer[0]=t.slice(e),t=t.slice(0,e),this[q]-=e),this.emit("data",t),!this.buffer.length&&!this[Te]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[Te]=!0,this.writable=!1,(this.flowing||!this[pr])&&this[Pe](),this}[Pt](){this[z]||(this[pr]=!1,this[Vr]=!0,this.emit("resume"),this.buffer.length?this[Yr]():this[Te]?this[Pe]():this.emit("drain"))}resume(){return this[Pt]()}pause(){this[Vr]=!1,this[pr]=!0}get destroyed(){return this[z]}get flowing(){return this[Vr]}get paused(){return this[pr]}[Ei](e){this[$]?this[q]+=1:this[q]+=e.length,this.buffer.push(e)}[yi](){return this.buffer.length&&(this[$]?this[q]-=1:this[q]-=this.buffer[0].length),this.buffer.shift()}[Yr](e){do;while(this[eu](this[yi]()));!e&&!this.buffer.length&&!this[Te]&&this.emit("drain")}[eu](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[z])return;let s=this[Ye];return t=t||{},e===Ko.stdout||e===Ko.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this.pipes.push(t.proxyErrors?new wi(this,e,t):new Kr(this,e,t)),this[Ne]?mr(()=>this[Pt]()):this[Pt]()),e}unpipe(e){let t=this.pipes.find(s=>s.dest===e);t&&(this.pipes.splice(this.pipes.indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this.pipes.length&&!this.flowing?this[Pt]():e==="readable"&&this[q]!==0?super.emit("readable"):oh(e)&&this[Ye]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[fr]&&(this[Ne]?mr(()=>t.call(this,this[fr])):t.call(this,this[fr])),s}get emittedEnd(){return this[Ye]}[Pe](){!this[Hr]&&!this[Ye]&&!this[z]&&this.buffer.length===0&&this[Te]&&(this[Hr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Jr]&&this.emit("close"),this[Hr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==z&&this[z])return;if(e==="data")return t?this[Ne]?mr(()=>this[Ai](t)):this[Ai](t):!1;if(e==="end")return this[tu]();if(e==="close"){if(this[Jr]=!0,!this[Ye]&&!this[z])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[fr]=t;let n=super.emit("error",t);return this[Pe](),n}else if(e==="resume"){let n=super.emit("resume");return this[Pe](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[Pe](),i}[Ai](e){for(let s of this.pipes)s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[Pe](),t}[tu](){this[Ye]||(this[Ye]=!0,this.readable=!1,this[Ne]?mr(()=>this[Ci]()):this[Ci]())}[Ci](){if(this[Le]){let t=this[Le].end();if(t){for(let s of this.pipes)s.dest.write(t);super.emit("data",t)}}for(let t of this.pipes)t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[$]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[$]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[$]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[$]?Promise.reject(new Error("cannot concat in objectMode")):this[V]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(z,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[ih](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[Te])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[Te]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(z,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[nh](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[z]?(e?this.emit("error",e):this.emit(z),this):(this[z]=!0,this.buffer.length=0,this[q]=0,typeof this.close=="function"&&!this[Jr]&&this.close(),e?this.emit("error",e):this.emit(z),this)}static isStream(e){return!!e&&(e instanceof su||e instanceof Zo||e instanceof sh&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var qi=E(X=>{"use strict";u();var Ri=require("assert"),Ve=require("buffer").Buffer,uu=require("zlib"),mt=X.constants=Vo(),ch=Fi(),nu=Ve.concat,dt=Symbol("_superWrite"),Nt=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},lh=Symbol("opts"),dr=Symbol("flushFlag"),ou=Symbol("finishFlushFlag"),Mi=Symbol("fullFlushFlag"),B=Symbol("handle"),Zr=Symbol("onError"),Lt=Symbol("sawError"),bi=Symbol("level"),Si=Symbol("strategy"),ki=Symbol("ended"),_d=Symbol("_defaultFullFlush"),Xr=class extends ch{constructor(e,t){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[Lt]=!1,this[ki]=!1,this[lh]=e,this[dr]=e.flush,this[ou]=e.finishFlush;try{this[B]=new uu[t](e)}catch(s){throw new Nt(s)}this[Zr]=s=>{this[Lt]||(this[Lt]=!0,this.close(),this.emit("error",s))},this[B].on("error",s=>this[Zr](new Nt(s))),this.once("end",()=>this.close)}close(){this[B]&&(this[B].close(),this[B]=null,this.emit("close"))}reset(){if(!this[Lt])return Ri(this[B],"zlib binding closed"),this[B].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[Mi]),this.write(Object.assign(Ve.alloc(0),{[dr]:e})))}end(e,t,s){return e&&this.write(e,t),this.flush(this[ou]),this[ki]=!0,super.end(null,null,s)}get ended(){return this[ki]}write(e,t,s){if(typeof t=="function"&&(s=t,t="utf8"),typeof e=="string"&&(e=Ve.from(e,t)),this[Lt])return;Ri(this[B],"zlib binding closed");let i=this[B]._handle,n=i.close;i.close=()=>{};let o=this[B].close;this[B].close=()=>{},Ve.concat=c=>c;let a;try{let c=typeof e[dr]=="number"?e[dr]:this[dr];a=this[B]._processChunk(e,c),Ve.concat=nu}catch(c){Ve.concat=nu,this[Zr](new Nt(c))}finally{this[B]&&(this[B]._handle=i,i.close=n,this[B].close=o,this[B].removeAllListeners("error"))}this[B]&&this[B].on("error",c=>this[Zr](new Nt(c)));let l;if(a)if(Array.isArray(a)&&a.length>0){l=this[dt](Ve.from(a[0]));for(let c=1;c<a.length;c++)l=this[dt](a[c])}else l=this[dt](Ve.from(a));return s&&s(),l}[dt](e){return super.write(e)}},Ie=class extends Xr{constructor(e,t){e=e||{},e.flush=e.flush||mt.Z_NO_FLUSH,e.finishFlush=e.finishFlush||mt.Z_FINISH,super(e,t),this[Mi]=mt.Z_FULL_FLUSH,this[bi]=e.level,this[Si]=e.strategy}params(e,t){if(!this[Lt]){if(!this[B])throw new Error("cannot switch params when binding is closed");if(!this[B].params)throw new Error("not supported in this implementation");if(this[bi]!==e||this[Si]!==t){this.flush(mt.Z_SYNC_FLUSH),Ri(this[B],"zlib binding closed");let s=this[B].flush;this[B].flush=(i,n)=>{this.flush(i),n()};try{this[B].params(e,t)}finally{this[B].flush=s}this[B]&&(this[bi]=e,this[Si]=t)}}}},vi=class extends Ie{constructor(e){super(e,"Deflate")}},Bi=class extends Ie{constructor(e){super(e,"Inflate")}},_i=Symbol("_portable"),xi=class extends Ie{constructor(e){super(e,"Gzip"),this[_i]=e&&!!e.portable}[dt](e){return this[_i]?(this[_i]=!1,e[9]=255,super[dt](e)):super[dt](e)}},Oi=class extends Ie{constructor(e){super(e,"Gunzip")}},Ti=class extends Ie{constructor(e){super(e,"DeflateRaw")}},Pi=class extends Ie{constructor(e){super(e,"InflateRaw")}},Li=class extends Ie{constructor(e){super(e,"Unzip")}},Qr=class extends Xr{constructor(e,t){e=e||{},e.flush=e.flush||mt.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||mt.BROTLI_OPERATION_FINISH,super(e,t),this[Mi]=mt.BROTLI_OPERATION_FLUSH}},Ni=class extends Qr{constructor(e){super(e,"BrotliCompress")}},Ii=class extends Qr{constructor(e){super(e,"BrotliDecompress")}};X.Deflate=vi;X.Inflate=Bi;X.Gzip=xi;X.Gunzip=Oi;X.DeflateRaw=Ti;X.InflateRaw=Pi;X.Unzip=Li;typeof uu.BrotliCompress=="function"?(X.BrotliCompress=Ni,X.BrotliDecompress=Ii):X.BrotliCompress=X.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var It=E((Bd,au)=>{u();var hh=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;au.exports=hh!=="win32"?r=>r:r=>r&&r.replace(/\\/g,"/")});var es=E((Od,cu)=>{"use strict";u();var fh=Gr(),Ui=It(),ji=Symbol("slurp");cu.exports=class extends fh{constructor(e,t,s){switch(super(),this.pause(),this.extended=t,this.globalExtended=s,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=Ui(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=Ui(e.linkpath),this.uname=e.uname,this.gname=e.gname,t&&this[ji](t),s&&this[ji](s,!0)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");let s=this.remain,i=this.blockRemain;return this.remain=Math.max(0,s-t),this.blockRemain=Math.max(0,i-t),this.ignore?!0:s>=t?super.write(e):super.write(e.slice(0,s))}[ji](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=s==="path"||s==="linkpath"?Ui(e[s]):e[s])}}});var $i=E(ts=>{"use strict";u();ts.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);ts.code=new Map(Array.from(ts.name).map(r=>[r[1],r[0]]))});var pu=E((Pd,fu)=>{"use strict";u();var ph=(r,e)=>{if(Number.isSafeInteger(r))r<0?dh(r,e):mh(r,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},mh=(r,e)=>{e[0]=128;for(var t=e.length;t>1;t--)e[t-1]=r&255,r=Math.floor(r/256)},dh=(r,e)=>{e[0]=255;var t=!1;r=r*-1;for(var s=e.length;s>1;s--){var i=r&255;r=Math.floor(r/256),t?e[s-1]=lu(i):i===0?e[s-1]=0:(t=!0,e[s-1]=hu(i))}},Dh=r=>{let e=r[0],t=e===128?Eh(r.slice(1,r.length)):e===255?gh(r):null;if(t===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(t))throw Error("parsed number outside of javascript safe integer range");return t},gh=r=>{for(var e=r.length,t=0,s=!1,i=e-1;i>-1;i--){var n=r[i],o;s?o=lu(n):n===0?o=n:(s=!0,o=hu(n)),o!==0&&(t-=o*Math.pow(256,e-i-1))}return t},Eh=r=>{for(var e=r.length,t=0,s=e-1;s>-1;s--){var i=r[s];i!==0&&(t+=i*Math.pow(256,e-s-1))}return t},lu=r=>(255^r)&255,hu=r=>(255^r)+1&255;fu.exports={encode:ph,parse:Dh}});var qt=E((Ld,du)=>{"use strict";u();var zi=$i(),Mt=require("path").posix,mu=pu(),Wi=Symbol("slurp"),Q=Symbol("type"),Ji=class{constructor(e,t,s,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[Q]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,t||0,s,i):e&&this.set(e)}decode(e,t,s,i){if(t||(t=0),!e||!(e.length>=t+512))throw new Error("need 512 bytes for header");if(this.path=Dt(e,t,100),this.mode=Ke(e,t+100,8),this.uid=Ke(e,t+108,8),this.gid=Ke(e,t+116,8),this.size=Ke(e,t+124,12),this.mtime=Gi(e,t+136,12),this.cksum=Ke(e,t+148,12),this[Wi](s),this[Wi](i,!0),this[Q]=Dt(e,t+156,1),this[Q]===""&&(this[Q]="0"),this[Q]==="0"&&this.path.slice(-1)==="/"&&(this[Q]="5"),this[Q]==="5"&&(this.size=0),this.linkpath=Dt(e,t+157,100),e.slice(t+257,t+265).toString()==="ustar\x0000")if(this.uname=Dt(e,t+265,32),this.gname=Dt(e,t+297,32),this.devmaj=Ke(e,t+329,8),this.devmin=Ke(e,t+337,8),e[t+475]!==0){let o=Dt(e,t+345,155);this.path=o+"/"+this.path}else{let o=Dt(e,t+345,130);o&&(this.path=o+"/"+this.path),this.atime=Gi(e,t+476,12),this.ctime=Gi(e,t+488,12)}let n=8*32;for(let o=t;o<t+148;o++)n+=e[o];for(let o=t+156;o<t+512;o++)n+=e[o];this.cksumValid=n===this.cksum,this.cksum===null&&n===8*32&&(this.nullBlock=!0)}[Wi](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=e[s])}encode(e,t){if(e||(e=this.block=Buffer.alloc(512),t=0),t||(t=0),!(e.length>=t+512))throw new Error("need 512 bytes for header");let s=this.ctime||this.atime?130:155,i=yh(this.path||"",s),n=i[0],o=i[1];this.needPax=i[2],this.needPax=gt(e,t,100,n)||this.needPax,this.needPax=Ze(e,t+100,8,this.mode)||this.needPax,this.needPax=Ze(e,t+108,8,this.uid)||this.needPax,this.needPax=Ze(e,t+116,8,this.gid)||this.needPax,this.needPax=Ze(e,t+124,12,this.size)||this.needPax,this.needPax=Hi(e,t+136,12,this.mtime)||this.needPax,e[t+156]=this[Q].charCodeAt(0),this.needPax=gt(e,t+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",t+257,8),this.needPax=gt(e,t+265,32,this.uname)||this.needPax,this.needPax=gt(e,t+297,32,this.gname)||this.needPax,this.needPax=Ze(e,t+329,8,this.devmaj)||this.needPax,this.needPax=Ze(e,t+337,8,this.devmin)||this.needPax,this.needPax=gt(e,t+345,s,o)||this.needPax,e[t+475]!==0?this.needPax=gt(e,t+345,155,o)||this.needPax:(this.needPax=gt(e,t+345,130,o)||this.needPax,this.needPax=Hi(e,t+476,12,this.atime)||this.needPax,this.needPax=Hi(e,t+488,12,this.ctime)||this.needPax);let a=8*32;for(let l=t;l<t+148;l++)a+=e[l];for(let l=t+156;l<t+512;l++)a+=e[l];return this.cksum=a,Ze(e,t+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let t in e)e[t]!==null&&e[t]!==void 0&&(this[t]=e[t])}get type(){return zi.name.get(this[Q])||this[Q]}get typeKey(){return this[Q]}set type(e){zi.code.has(e)?this[Q]=zi.code.get(e):this[Q]=e}},yh=(r,e)=>{let s=r,i="",n,o=Mt.parse(r).root||".";if(Buffer.byteLength(s)<100)n=[s,i,!1];else{i=Mt.dirname(s),s=Mt.basename(s);do Buffer.byteLength(s)<=100&&Buffer.byteLength(i)<=e?n=[s,i,!1]:Buffer.byteLength(s)>100&&Buffer.byteLength(i)<=e?n=[s.slice(0,100-1),i,!0]:(s=Mt.join(Mt.basename(i),s),i=Mt.dirname(i));while(i!==o&&!n);n||(n=[r.slice(0,100-1),"",!0])}return n},Dt=(r,e,t)=>r.slice(e,e+t).toString("utf8").replace(/\0.*/,""),Gi=(r,e,t)=>Ah(Ke(r,e,t)),Ah=r=>r===null?null:new Date(r*1e3),Ke=(r,e,t)=>r[e]&128?mu.parse(r.slice(e,e+t)):wh(r,e,t),Ch=r=>isNaN(r)?null:r,wh=(r,e,t)=>Ch(parseInt(r.slice(e,e+t).toString("utf8").replace(/\0.*$/,"").trim(),8)),Fh={12:8589934591,8:2097151},Ze=(r,e,t,s)=>s===null?!1:s>Fh[t]||s<0?(mu.encode(s,r.slice(e,e+t)),!0):(bh(r,e,t,s),!1),bh=(r,e,t,s)=>r.write(Sh(s,t),e,t,"ascii"),Sh=(r,e)=>kh(Math.floor(r).toString(8),e),kh=(r,e)=>(r.length===e-1?r:new Array(e-r.length-1).join("0")+r+" ")+"\0",Hi=(r,e,t,s)=>s===null?!1:Ze(r,e,t,s.getTime()/1e3),_h=new Array(156).join("\0"),gt=(r,e,t,s)=>s===null?!1:(r.write(s+_h,e,t,"utf8"),s.length!==Buffer.byteLength(s)||s.length>t);du.exports=Ji});var rs=E((Nd,Du)=>{"use strict";u();var Rh=qt(),vh=require("path"),Dr=class{constructor(e,t){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=t||!1}encode(){let e=this.encodeBody();if(e==="")return null;let t=Buffer.byteLength(e),s=512*Math.ceil(1+t/512),i=Buffer.allocUnsafe(s);for(let n=0;n<512;n++)i[n]=0;new Rh({path:("PaxHeader/"+vh.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:t,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,t,"utf8");for(let n=t+512;n<i.length;n++)i[n]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let t=this[e]instanceof Date?this[e].getTime()/1e3:this[e],s=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+t+`
`,i=Buffer.byteLength(s),n=Math.floor(Math.log(i)/Math.log(10))+1;return i+n>=Math.pow(10,n)&&(n+=1),n+i+s}};Dr.parse=(r,e,t)=>new Dr(Bh(xh(r),e),t);var Bh=(r,e)=>e?Object.keys(r).reduce((t,s)=>(t[s]=r[s],t),e):r,xh=r=>r.replace(/\n$/,"").split(`
`).reduce(Oh,Object.create(null)),Oh=(r,e)=>{let t=parseInt(e,10);if(t!==Buffer.byteLength(e)+1)return r;e=e.slice((t+" ").length);let s=e.split("="),i=s.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return r;let n=s.join("=");return r[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(n*1e3):/^[0-9]+$/.test(n)?+n:n,r};Du.exports=Dr});var Ut=E((Id,gu)=>{u();gu.exports=r=>{let e=r.length-1,t=-1;for(;e>-1&&r.charAt(e)==="/";)t=e,e--;return t===-1?r:r.slice(0,t)}});var ss=E((Md,Eu)=>{"use strict";u();Eu.exports=r=>class extends r{warn(e,t,s={}){this.file&&(s.file=this.file),this.cwd&&(s.cwd=this.cwd),s.code=t instanceof Error&&t.code||e,s.tarCode=e,!this.strict&&s.recoverable!==!1?(t instanceof Error&&(s=Object.assign(t,s),t=t.message),this.emit("warn",s.tarCode,t,s)):t instanceof Error?this.emit("error",Object.assign(t,s)):this.emit("error",Object.assign(new Error(`${e}: ${t}`),s))}}});var Vi=E((Ud,yu)=>{"use strict";u();var is=["|","<",">","?",":"],Yi=is.map(r=>String.fromCharCode(61440+r.charCodeAt(0))),Th=new Map(is.map((r,e)=>[r,Yi[e]])),Ph=new Map(Yi.map((r,e)=>[r,is[e]]));yu.exports={encode:r=>is.reduce((e,t)=>e.split(t).join(Th.get(t)),r),decode:r=>Yi.reduce((e,t)=>e.split(t).join(Ph.get(t)),r)}});var Ki=E((jd,Cu)=>{u();var{isAbsolute:Lh,parse:Au}=require("path").win32;Cu.exports=r=>{let e="",t=Au(r);for(;Lh(r)||t.root;){let s=r.charAt(0)==="/"&&r.slice(0,4)!=="//?/"?"/":t.root;r=r.slice(s.length),e+=s,t=Au(r)}return[e,r]}});var Fu=E(($d,wu)=>{"use strict";u();wu.exports=(r,e,t)=>(r&=4095,t&&(r=(r|384)&-19),e&&(r&256&&(r|=64),r&32&&(r|=8),r&4&&(r|=1)),r)});var un=E((Gd,Iu)=>{"use strict";u();var Bu=Gr(),xu=rs(),Ou=qt(),Ee=require("fs"),bu=require("path"),ge=It(),Nh=Ut(),Tu=(r,e)=>e?(r=ge(r).replace(/^\.(\/|$)/,""),Nh(e)+"/"+r):ge(r),Ih=16*1024*1024,Su=Symbol("process"),ku=Symbol("file"),_u=Symbol("directory"),Xi=Symbol("symlink"),Ru=Symbol("hardlink"),gr=Symbol("header"),ns=Symbol("read"),Qi=Symbol("lstat"),os=Symbol("onlstat"),en=Symbol("onread"),tn=Symbol("onreadlink"),rn=Symbol("openfile"),sn=Symbol("onopenfile"),Xe=Symbol("close"),us=Symbol("mode"),nn=Symbol("awaitDrain"),Zi=Symbol("ondrain"),ye=Symbol("prefix"),vu=Symbol("hadError"),Pu=ss(),Mh=Vi(),Lu=Ki(),Nu=Fu(),as=Pu(class extends Bu{constructor(e,t){if(t=t||{},super(t),typeof e!="string")throw new TypeError("path is required");this.path=ge(e),this.portable=!!t.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=t.maxReadSize||Ih,this.linkCache=t.linkCache||new Map,this.statCache=t.statCache||new Map,this.preservePaths=!!t.preservePaths,this.cwd=ge(t.cwd||process.cwd()),this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.mtime=t.mtime||null,this.prefix=t.prefix?ge(t.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Lu(this.path);i&&(this.path=n,s=i)}this.win32=!!t.win32||process.platform==="win32",this.win32&&(this.path=Mh.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=ge(t.absolute||bu.resolve(this.cwd,e)),this.path===""&&(this.path="./"),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.statCache.has(this.absolute)?this[os](this.statCache.get(this.absolute)):this[Qi]()}emit(e,...t){return e==="error"&&(this[vu]=!0),super.emit(e,...t)}[Qi](){Ee.lstat(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[os](t)})}[os](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=Uh(e),this.emit("stat",e),this[Su]()}[Su](){switch(this.type){case"File":return this[ku]();case"Directory":return this[_u]();case"SymbolicLink":return this[Xi]();default:return this.end()}}[us](e){return Nu(e,this.type==="Directory",this.portable)}[ye](e){return Tu(e,this.prefix)}[gr](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new Ou({path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,mode:this[us](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new xu({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[_u](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[gr](),this.end()}[Xi](){Ee.readlink(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[tn](t)})}[tn](e){this.linkpath=ge(e),this[gr](),this.end()}[Ru](e){this.type="Link",this.linkpath=ge(bu.relative(this.cwd,e)),this.stat.size=0,this[gr](),this.end()}[ku](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let t=this.linkCache.get(e);if(t.indexOf(this.cwd)===0)return this[Ru](t)}this.linkCache.set(e,this.absolute)}if(this[gr](),this.stat.size===0)return this.end();this[rn]()}[rn](){Ee.open(this.absolute,"r",(e,t)=>{if(e)return this.emit("error",e);this[sn](t)})}[sn](e){if(this.fd=e,this[vu])return this[Xe]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let t=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(t),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[ns]()}[ns](){let{fd:e,buf:t,offset:s,length:i,pos:n}=this;Ee.read(e,t,s,i,n,(o,a)=>{if(o)return this[Xe](()=>this.emit("error",o));this[en](a)})}[Xe](e){Ee.close(this.fd,e)}[en](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Xe](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Xe](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let t=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(t)?this[Zi]():this[nn](()=>this[Zi]())}[nn](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let t=new Error("writing more data than expected");return t.path=this.absolute,this.emit("error",t)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[Zi](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[Xe](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[ns]()}}),on=class extends as{[Qi](){this[os](Ee.lstatSync(this.absolute))}[Xi](){this[tn](Ee.readlinkSync(this.absolute))}[rn](){this[sn](Ee.openSync(this.absolute,"r"))}[ns](){let e=!0;try{let{fd:t,buf:s,offset:i,length:n,pos:o}=this,a=Ee.readSync(t,s,i,n,o);this[en](a),e=!1}finally{if(e)try{this[Xe](()=>{})}catch{}}}[nn](e){e()}[Xe](e){Ee.closeSync(this.fd),e()}},qh=Pu(class extends Bu{constructor(e,t){t=t||{},super(t),this.preservePaths=!!t.preservePaths,this.portable=!!t.portable,this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=t.prefix||null,this.path=ge(e.path),this.mode=this[us](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:t.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=ge(e.linkpath),typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Lu(this.path);i&&(this.path=n,s=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new Ou({path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.header.encode()&&!this.noPax&&super.write(new xu({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[ye](this.path),linkpath:this.type==="Link"?this[ye](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[ye](e){return Tu(e,this.prefix)}[us](e){return Nu(e,this.type==="Directory",this.portable)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=t,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});as.Sync=on;as.Tar=qh;var Uh=r=>r.isFile()?"File":r.isDirectory()?"Directory":r.isSymbolicLink()?"SymbolicLink":"Unsupported";Iu.exports=as});var qu=E((Hd,Mu)=>{"use strict";u();Mu.exports=function(r){r.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var an=E((Jd,Uu)=>{"use strict";u();Uu.exports=k;k.Node=Et;k.create=k;function k(r){var e=this;if(e instanceof k||(e=new k),e.tail=null,e.head=null,e.length=0,r&&typeof r.forEach=="function")r.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var t=0,s=arguments.length;t<s;t++)e.push(arguments[t]);return e}k.prototype.removeNode=function(r){if(r.list!==this)throw new Error("removing node which does not belong to this list");var e=r.next,t=r.prev;return e&&(e.prev=t),t&&(t.next=e),r===this.head&&(this.head=e),r===this.tail&&(this.tail=t),r.list.length--,r.next=null,r.prev=null,r.list=null,e};k.prototype.unshiftNode=function(r){if(r!==this.head){r.list&&r.list.removeNode(r);var e=this.head;r.list=this,r.next=e,e&&(e.prev=r),this.head=r,this.tail||(this.tail=r),this.length++}};k.prototype.pushNode=function(r){if(r!==this.tail){r.list&&r.list.removeNode(r);var e=this.tail;r.list=this,r.prev=e,e&&(e.next=r),this.tail=r,this.head||(this.head=r),this.length++}};k.prototype.push=function(){for(var r=0,e=arguments.length;r<e;r++)$h(this,arguments[r]);return this.length};k.prototype.unshift=function(){for(var r=0,e=arguments.length;r<e;r++)zh(this,arguments[r]);return this.length};k.prototype.pop=function(){if(!!this.tail){var r=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,r}};k.prototype.shift=function(){if(!!this.head){var r=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,r}};k.prototype.forEach=function(r,e){e=e||this;for(var t=this.head,s=0;t!==null;s++)r.call(e,t.value,s,this),t=t.next};k.prototype.forEachReverse=function(r,e){e=e||this;for(var t=this.tail,s=this.length-1;t!==null;s--)r.call(e,t.value,s,this),t=t.prev};k.prototype.get=function(r){for(var e=0,t=this.head;t!==null&&e<r;e++)t=t.next;if(e===r&&t!==null)return t.value};k.prototype.getReverse=function(r){for(var e=0,t=this.tail;t!==null&&e<r;e++)t=t.prev;if(e===r&&t!==null)return t.value};k.prototype.map=function(r,e){e=e||this;for(var t=new k,s=this.head;s!==null;)t.push(r.call(e,s.value,this)),s=s.next;return t};k.prototype.mapReverse=function(r,e){e=e||this;for(var t=new k,s=this.tail;s!==null;)t.push(r.call(e,s.value,this)),s=s.prev;return t};k.prototype.reduce=function(r,e){var t,s=this.head;if(arguments.length>1)t=e;else if(this.head)s=this.head.next,t=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;s!==null;i++)t=r(t,s.value,i),s=s.next;return t};k.prototype.reduceReverse=function(r,e){var t,s=this.tail;if(arguments.length>1)t=e;else if(this.tail)s=this.tail.prev,t=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;s!==null;i--)t=r(t,s.value,i),s=s.prev;return t};k.prototype.toArray=function(){for(var r=new Array(this.length),e=0,t=this.head;t!==null;e++)r[e]=t.value,t=t.next;return r};k.prototype.toArrayReverse=function(){for(var r=new Array(this.length),e=0,t=this.tail;t!==null;e++)r[e]=t.value,t=t.prev;return r};k.prototype.slice=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new k;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(;i!==null&&s<e;s++,i=i.next)t.push(i.value);return t};k.prototype.sliceReverse=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new k;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=this.length,i=this.tail;i!==null&&s>e;s--)i=i.prev;for(;i!==null&&s>r;s--,i=i.prev)t.push(i.value);return t};k.prototype.splice=function(r,e,...t){r>this.length&&(r=this.length-1),r<0&&(r=this.length+r);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(var n=[],s=0;i&&s<e;s++)n.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var s=0;s<t.length;s++)i=jh(this,i,t[s]);return n};k.prototype.reverse=function(){for(var r=this.head,e=this.tail,t=r;t!==null;t=t.prev){var s=t.prev;t.prev=t.next,t.next=s}return this.head=e,this.tail=r,this};function jh(r,e,t){var s=e===r.head?new Et(t,null,e,r):new Et(t,e,e.next,r);return s.next===null&&(r.tail=s),s.prev===null&&(r.head=s),r.length++,s}function $h(r,e){r.tail=new Et(e,r.tail,null,r),r.head||(r.head=r.tail),r.length++}function zh(r,e){r.head=new Et(e,null,r.head,r),r.tail||(r.tail=r.head),r.length++}function Et(r,e,t,s){if(!(this instanceof Et))return new Et(r,e,t,s);this.list=s,this.value=r,e?(e.next=this,this.prev=e):this.prev=null,t?(t.prev=this,this.next=t):this.next=null}try{qu()(k)}catch{}});var gs=E((Vd,Ju)=>{"use strict";u();var ds=class{constructor(e,t){this.path=e||"./",this.absolute=t,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},Wh=Gr(),Gh=qi(),Hh=es(),gn=un(),Jh=gn.Sync,Yh=gn.Tar,Vh=an(),ju=Buffer.alloc(1024),hs=Symbol("onStat"),cs=Symbol("ended"),Ae=Symbol("queue"),jt=Symbol("current"),yt=Symbol("process"),ls=Symbol("processing"),$u=Symbol("processJob"),Ce=Symbol("jobs"),cn=Symbol("jobDone"),fs=Symbol("addFSEntry"),zu=Symbol("addTarEntry"),pn=Symbol("stat"),mn=Symbol("readdir"),ps=Symbol("onreaddir"),ms=Symbol("pipe"),Wu=Symbol("entry"),ln=Symbol("entryOpt"),dn=Symbol("writeEntryClass"),Hu=Symbol("write"),hn=Symbol("ondrain"),Ds=require("fs"),Gu=require("path"),Kh=ss(),fn=It(),En=Kh(class extends Wh{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=fn(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[dn]=gn,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new Gh.Gzip(e.gzip),this.zip.on("data",t=>super.write(t)),this.zip.on("end",t=>super.end()),this.zip.on("drain",t=>this[hn]()),this.on("resume",t=>this.zip.resume())):this.on("drain",this[hn]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:t=>!0,this[Ae]=new Vh,this[Ce]=0,this.jobs=+e.jobs||4,this[ls]=!1,this[cs]=!1}[Hu](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[cs]=!0,this[yt](),this}write(e){if(this[cs])throw new Error("write after end");return e instanceof Hh?this[zu](e):this[fs](e),this.flowing}[zu](e){let t=fn(Gu.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let s=new ds(e.path,t,!1);s.entry=new Yh(e,this[ln](s)),s.entry.on("end",i=>this[cn](s)),this[Ce]+=1,this[Ae].push(s)}this[yt]()}[fs](e){let t=fn(Gu.resolve(this.cwd,e));this[Ae].push(new ds(e,t)),this[yt]()}[pn](e){e.pending=!0,this[Ce]+=1;let t=this.follow?"stat":"lstat";Ds[t](e.absolute,(s,i)=>{e.pending=!1,this[Ce]-=1,s?this.emit("error",s):this[hs](e,i)})}[hs](e,t){this.statCache.set(e.absolute,t),e.stat=t,this.filter(e.path,t)||(e.ignore=!0),this[yt]()}[mn](e){e.pending=!0,this[Ce]+=1,Ds.readdir(e.absolute,(t,s)=>{if(e.pending=!1,this[Ce]-=1,t)return this.emit("error",t);this[ps](e,s)})}[ps](e,t){this.readdirCache.set(e.absolute,t),e.readdir=t,this[yt]()}[yt](){if(!this[ls]){this[ls]=!0;for(let e=this[Ae].head;e!==null&&this[Ce]<this.jobs;e=e.next)if(this[$u](e.value),e.value.ignore){let t=e.next;this[Ae].removeNode(e),e.next=t}this[ls]=!1,this[cs]&&!this[Ae].length&&this[Ce]===0&&(this.zip?this.zip.end(ju):(super.write(ju),super.end()))}}get[jt](){return this[Ae]&&this[Ae].head&&this[Ae].head.value}[cn](e){this[Ae].shift(),this[Ce]-=1,this[yt]()}[$u](e){if(!e.pending){if(e.entry){e===this[jt]&&!e.piped&&this[ms](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[hs](e,this.statCache.get(e.absolute)):this[pn](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[ps](e,this.readdirCache.get(e.absolute)):this[mn](e),!e.readdir))){if(e.entry=this[Wu](e),!e.entry){e.ignore=!0;return}e===this[jt]&&!e.piped&&this[ms](e)}}}[ln](e){return{onwarn:(t,s,i)=>this.warn(t,s,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[Wu](e){this[Ce]+=1;try{return new this[dn](e.path,this[ln](e)).on("end",()=>this[cn](e)).on("error",t=>this.emit("error",t))}catch(t){this.emit("error",t)}}[hn](){this[jt]&&this[jt].entry&&this[jt].entry.resume()}[ms](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[fs](o+i)});let t=e.entry,s=this.zip;s?t.on("data",i=>{s.write(i)||t.pause()}):t.on("data",i=>{super.write(i)||t.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),Dn=class extends En{constructor(e){super(e),this[dn]=Jh}pause(){}resume(){}[pn](e){let t=this.follow?"statSync":"lstatSync";this[hs](e,Ds[t](e.absolute))}[mn](e,t){this[ps](e,Ds.readdirSync(e.absolute))}[ms](e){let t=e.entry,s=this.zip;e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[fs](o+i)}),s?t.on("data",i=>{s.write(i)}):t.on("data",i=>{super[Hu](i)})}};En.Sync=Dn;Ju.exports=En});var Vt=E(yr=>{"use strict";u();var Zh=Fi(),Xh=require("events").EventEmitter,K=require("fs"),Cn=K.writev;if(!Cn){let r=process.binding("fs"),e=r.FSReqWrap||r.FSReqCallback;Cn=(t,s,i,n)=>{let o=(l,c)=>n(l,c,s),a=new e;a.oncomplete=o,r.writeBuffers(t,s,i,a)}}var Jt=Symbol("_autoClose"),pe=Symbol("_close"),Er=Symbol("_ended"),v=Symbol("_fd"),Yu=Symbol("_finished"),et=Symbol("_flags"),yn=Symbol("_flush"),wn=Symbol("_handleChunk"),Fn=Symbol("_makeBuf"),ws=Symbol("_mode"),Es=Symbol("_needDrain"),Gt=Symbol("_onerror"),Yt=Symbol("_onopen"),An=Symbol("_onread"),zt=Symbol("_onwrite"),tt=Symbol("_open"),Me=Symbol("_path"),At=Symbol("_pos"),we=Symbol("_queue"),Wt=Symbol("_read"),Vu=Symbol("_readSize"),Qe=Symbol("_reading"),ys=Symbol("_remain"),Ku=Symbol("_size"),As=Symbol("_write"),$t=Symbol("_writing"),Cs=Symbol("_defaultFlag"),Ht=Symbol("_errored"),Fs=class extends Zh{constructor(e,t){if(t=t||{},super(t),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[Ht]=!1,this[v]=typeof t.fd=="number"?t.fd:null,this[Me]=e,this[Vu]=t.readSize||16*1024*1024,this[Qe]=!1,this[Ku]=typeof t.size=="number"?t.size:1/0,this[ys]=this[Ku],this[Jt]=typeof t.autoClose=="boolean"?t.autoClose:!0,typeof this[v]=="number"?this[Wt]():this[tt]()}get fd(){return this[v]}get path(){return this[Me]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[tt](){K.open(this[Me],"r",(e,t)=>this[Yt](e,t))}[Yt](e,t){e?this[Gt](e):(this[v]=t,this.emit("open",t),this[Wt]())}[Fn](){return Buffer.allocUnsafe(Math.min(this[Vu],this[ys]))}[Wt](){if(!this[Qe]){this[Qe]=!0;let e=this[Fn]();if(e.length===0)return process.nextTick(()=>this[An](null,0,e));K.read(this[v],e,0,e.length,null,(t,s,i)=>this[An](t,s,i))}}[An](e,t,s){this[Qe]=!1,e?this[Gt](e):this[wn](t,s)&&this[Wt]()}[pe](){if(this[Jt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}[Gt](e){this[Qe]=!0,this[pe](),this.emit("error",e)}[wn](e,t){let s=!1;return this[ys]-=e,e>0&&(s=super.write(e<t.length?t.slice(0,e):t)),(e===0||this[ys]<=0)&&(s=!1,this[pe](),super.end()),s}emit(e,t){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[v]=="number"&&this[Wt]();break;case"error":return this[Ht]?void 0:(this[Ht]=!0,super.emit(e,t));default:return super.emit(e,t)}}},bn=class extends Fs{[tt](){let e=!0;try{this[Yt](null,K.openSync(this[Me],"r")),e=!1}finally{e&&this[pe]()}}[Wt](){let e=!0;try{if(!this[Qe]){this[Qe]=!0;do{let t=this[Fn](),s=t.length===0?0:K.readSync(this[v],t,0,t.length,null);if(!this[wn](s,t))break}while(!0);this[Qe]=!1}e=!1}finally{e&&this[pe]()}}[pe](){if(this[Jt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.closeSync(e),this.emit("close")}}},bs=class extends Xh{constructor(e,t){t=t||{},super(t),this.readable=!1,this.writable=!0,this[Ht]=!1,this[$t]=!1,this[Er]=!1,this[Es]=!1,this[we]=[],this[Me]=e,this[v]=typeof t.fd=="number"?t.fd:null,this[ws]=t.mode===void 0?438:t.mode,this[At]=typeof t.start=="number"?t.start:null,this[Jt]=typeof t.autoClose=="boolean"?t.autoClose:!0;let s=this[At]!==null?"r+":"w";this[Cs]=t.flags===void 0,this[et]=this[Cs]?s:t.flags,this[v]===null&&this[tt]()}emit(e,t){if(e==="error"){if(this[Ht])return;this[Ht]=!0}return super.emit(e,t)}get fd(){return this[v]}get path(){return this[Me]}[Gt](e){this[pe](),this[$t]=!0,this.emit("error",e)}[tt](){K.open(this[Me],this[et],this[ws],(e,t)=>this[Yt](e,t))}[Yt](e,t){this[Cs]&&this[et]==="r+"&&e&&e.code==="ENOENT"?(this[et]="w",this[tt]()):e?this[Gt](e):(this[v]=t,this.emit("open",t),this[yn]())}end(e,t){return e&&this.write(e,t),this[Er]=!0,!this[$t]&&!this[we].length&&typeof this[v]=="number"&&this[zt](null,0),this}write(e,t){return typeof e=="string"&&(e=Buffer.from(e,t)),this[Er]?(this.emit("error",new Error("write() after end()")),!1):this[v]===null||this[$t]||this[we].length?(this[we].push(e),this[Es]=!0,!1):(this[$t]=!0,this[As](e),!0)}[As](e){K.write(this[v],e,0,e.length,this[At],(t,s)=>this[zt](t,s))}[zt](e,t){e?this[Gt](e):(this[At]!==null&&(this[At]+=t),this[we].length?this[yn]():(this[$t]=!1,this[Er]&&!this[Yu]?(this[Yu]=!0,this[pe](),this.emit("finish")):this[Es]&&(this[Es]=!1,this.emit("drain"))))}[yn](){if(this[we].length===0)this[Er]&&this[zt](null,0);else if(this[we].length===1)this[As](this[we].pop());else{let e=this[we];this[we]=[],Cn(this[v],e,this[At],(t,s)=>this[zt](t,s))}}[pe](){if(this[Jt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}},Sn=class extends bs{[tt](){let e;if(this[Cs]&&this[et]==="r+")try{e=K.openSync(this[Me],this[et],this[ws])}catch(t){if(t.code==="ENOENT")return this[et]="w",this[tt]();throw t}else e=K.openSync(this[Me],this[et],this[ws]);this[Yt](null,e)}[pe](){if(this[Jt]&&typeof this[v]=="number"){let e=this[v];this[v]=null,K.closeSync(e),this.emit("close")}}[As](e){let t=!0;try{this[zt](null,K.writeSync(this[v],e,0,e.length,this[At])),t=!1}finally{if(t)try{this[pe]()}catch{}}}};yr.ReadStream=Fs;yr.ReadStreamSync=bn;yr.WriteStream=bs;yr.WriteStreamSync=Sn});var xs=E((Xd,ia)=>{"use strict";u();var Qh=ss(),ef=qt(),tf=require("events"),rf=an(),sf=1024*1024,nf=es(),Zu=rs(),of=qi(),{nextTick:uf}=require("process"),kn=Buffer.from([31,139]),ne=Symbol("state"),Ct=Symbol("writeEntry"),qe=Symbol("readEntry"),_n=Symbol("nextEntry"),Xu=Symbol("processEntry"),oe=Symbol("extendedHeader"),Ar=Symbol("globalExtendedHeader"),rt=Symbol("meta"),Qu=Symbol("emitMeta"),O=Symbol("buffer"),Ue=Symbol("queue"),wt=Symbol("ended"),ea=Symbol("emittedEnd"),Ft=Symbol("emit"),Z=Symbol("unzip"),Ss=Symbol("consumeChunk"),ks=Symbol("consumeChunkSub"),Rn=Symbol("consumeBody"),ta=Symbol("consumeMeta"),ra=Symbol("consumeHeader"),_s=Symbol("consuming"),vn=Symbol("bufferConcat"),Bn=Symbol("maybeEnd"),Cr=Symbol("writing"),st=Symbol("aborted"),Rs=Symbol("onDone"),bt=Symbol("sawValidEntry"),vs=Symbol("sawNullBlock"),Bs=Symbol("sawEOF"),sa=Symbol("closeStream"),af=r=>!0;ia.exports=Qh(class extends tf{constructor(e){e=e||{},super(e),this.file=e.file||"",this[bt]=null,this.on(Rs,t=>{(this[ne]==="begin"||this[bt]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(Rs,e.ondone):this.on(Rs,t=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||sf,this.filter=typeof e.filter=="function"?e.filter:af,this.writable=!0,this.readable=!1,this[Ue]=new rf,this[O]=null,this[qe]=null,this[Ct]=null,this[ne]="begin",this[rt]="",this[oe]=null,this[Ar]=null,this[wt]=!1,this[Z]=null,this[st]=!1,this[vs]=!1,this[Bs]=!1,this.on("end",()=>this[sa]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[ra](e,t){this[bt]===null&&(this[bt]=!1);let s;try{s=new ef(e,t,this[oe],this[Ar])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(s.nullBlock)this[vs]?(this[Bs]=!0,this[ne]==="begin"&&(this[ne]="header"),this[Ft]("eof")):(this[vs]=!0,this[Ft]("nullBlock"));else if(this[vs]=!1,!s.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:s});else if(!s.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:s});else{let i=s.type;if(/^(Symbolic)?Link$/.test(i)&&!s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:s});else if(!/^(Symbolic)?Link$/.test(i)&&s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:s});else{let n=this[Ct]=new nf(s,this[oe],this[Ar]);if(!this[bt])if(n.remain){let o=()=>{n.invalid||(this[bt]=!0)};n.on("end",o)}else this[bt]=!0;n.meta?n.size>this.maxMetaEntrySize?(n.ignore=!0,this[Ft]("ignoredEntry",n),this[ne]="ignore",n.resume()):n.size>0&&(this[rt]="",n.on("data",o=>this[rt]+=o),this[ne]="meta"):(this[oe]=null,n.ignore=n.ignore||!this.filter(n.path,n),n.ignore?(this[Ft]("ignoredEntry",n),this[ne]=n.remain?"ignore":"header",n.resume()):(n.remain?this[ne]="body":(this[ne]="header",n.end()),this[qe]?this[Ue].push(n):(this[Ue].push(n),this[_n]())))}}}[sa](){uf(()=>this.emit("close"))}[Xu](e){let t=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[qe]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",s=>this[_n]()),t=!1)):(this[qe]=null,t=!1),t}[_n](){do;while(this[Xu](this[Ue].shift()));if(!this[Ue].length){let e=this[qe];!e||e.flowing||e.size===e.remain?this[Cr]||this.emit("drain"):e.once("drain",s=>this.emit("drain"))}}[Rn](e,t){let s=this[Ct],i=s.blockRemain,n=i>=e.length&&t===0?e:e.slice(t,t+i);return s.write(n),s.blockRemain||(this[ne]="header",this[Ct]=null,s.end()),n.length}[ta](e,t){let s=this[Ct],i=this[Rn](e,t);return this[Ct]||this[Qu](s),i}[Ft](e,t,s){!this[Ue].length&&!this[qe]?this.emit(e,t,s):this[Ue].push([e,t,s])}[Qu](e){switch(this[Ft]("meta",this[rt]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[oe]=Zu.parse(this[rt],this[oe],!1);break;case"GlobalExtendedHeader":this[Ar]=Zu.parse(this[rt],this[Ar],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[oe]=this[oe]||Object.create(null),this[oe].path=this[rt].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[oe]=this[oe]||Object.create(null),this[oe].linkpath=this[rt].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[st]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[st])return;if(this[Z]===null&&e){if(this[O]&&(e=Buffer.concat([this[O],e]),this[O]=null),e.length<kn.length)return this[O]=e,!0;for(let s=0;this[Z]===null&&s<kn.length;s++)e[s]!==kn[s]&&(this[Z]=!1);if(this[Z]===null){let s=this[wt];this[wt]=!1,this[Z]=new of.Unzip,this[Z].on("data",n=>this[Ss](n)),this[Z].on("error",n=>this.abort(n)),this[Z].on("end",n=>{this[wt]=!0,this[Ss]()}),this[Cr]=!0;let i=this[Z][s?"end":"write"](e);return this[Cr]=!1,i}}this[Cr]=!0,this[Z]?this[Z].write(e):this[Ss](e),this[Cr]=!1;let t=this[Ue].length?!1:this[qe]?this[qe].flowing:!0;return!t&&!this[Ue].length&&this[qe].once("drain",s=>this.emit("drain")),t}[vn](e){e&&!this[st]&&(this[O]=this[O]?Buffer.concat([this[O],e]):e)}[Bn](){if(this[wt]&&!this[ea]&&!this[st]&&!this[_s]){this[ea]=!0;let e=this[Ct];if(e&&e.blockRemain){let t=this[O]?this[O].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${t} available)`,{entry:e}),this[O]&&e.write(this[O]),e.end()}this[Ft](Rs)}}[Ss](e){if(this[_s])this[vn](e);else if(!e&&!this[O])this[Bn]();else{if(this[_s]=!0,this[O]){this[vn](e);let t=this[O];this[O]=null,this[ks](t)}else this[ks](e);for(;this[O]&&this[O].length>=512&&!this[st]&&!this[Bs];){let t=this[O];this[O]=null,this[ks](t)}this[_s]=!1}(!this[O]||this[wt])&&this[Bn]()}[ks](e){let t=0,s=e.length;for(;t+512<=s&&!this[st]&&!this[Bs];)switch(this[ne]){case"begin":case"header":this[ra](e,t),t+=512;break;case"ignore":case"body":t+=this[Rn](e,t);break;case"meta":t+=this[ta](e,t);break;default:throw new Error("invalid state: "+this[ne])}t<s&&(this[O]?this[O]=Buffer.concat([e.slice(t),this[O]]):this[O]=e.slice(t))}end(e){this[st]||(this[Z]?this[Z].end(e):(this[wt]=!0,this.write(e)))}})});var Os=E((Qd,aa)=>{"use strict";u();var cf=Ot(),oa=xs(),Kt=require("fs"),lf=Vt(),na=require("path"),xn=Ut();aa.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=cf(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&ff(s,e),s.noResume||hf(s),s.file&&s.sync?pf(s):s.file?mf(s,t):ua(s)};var hf=r=>{let e=r.onentry;r.onentry=e?t=>{e(t),t.resume()}:t=>t.resume()},ff=(r,e)=>{let t=new Map(e.map(n=>[xn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||na.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(na.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(xn(n)):n=>i(xn(n))},pf=r=>{let e=ua(r),t=r.file,s=!0,i;try{let n=Kt.statSync(t),o=r.maxReadSize||16*1024*1024;if(n.size<o)e.end(Kt.readFileSync(t));else{let a=0,l=Buffer.allocUnsafe(o);for(i=Kt.openSync(t,"r");a<n.size;){let c=Kt.readSync(i,l,0,o,a);a+=c,e.write(l.slice(0,c))}e.end()}s=!1}finally{if(s&&i)try{Kt.closeSync(i)}catch{}}},mf=(r,e)=>{let t=new oa(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("end",o),Kt.stat(i,(l,c)=>{if(l)a(l);else{let h=new lf.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},ua=r=>new oa(r)});var ma=E((e0,pa)=>{"use strict";u();var df=Ot(),Ts=gs(),ca=Vt(),la=Os(),ha=require("path");pa.exports=(r,e,t)=>{if(typeof e=="function"&&(t=e),Array.isArray(r)&&(e=r,r={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let s=df(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return s.file&&s.sync?Df(s,e):s.file?gf(s,e,t):s.sync?Ef(s,e):yf(s,e)};var Df=(r,e)=>{let t=new Ts.Sync(r),s=new ca.WriteStreamSync(r.file,{mode:r.mode||438});t.pipe(s),fa(t,e)},gf=(r,e,t)=>{let s=new Ts(r),i=new ca.WriteStream(r.file,{mode:r.mode||438});s.pipe(i);let n=new Promise((o,a)=>{i.on("error",a),i.on("close",o),s.on("error",a)});return On(s,e),t?n.then(t,t):n},fa=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?la({file:ha.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},On=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return la({file:ha.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>On(r,e));r.add(t)}r.end()},Ef=(r,e)=>{let t=new Ts.Sync(r);return fa(t,e),t},yf=(r,e)=>{let t=new Ts(r);return On(t,e),t}});var Tn=E((t0,Ca)=>{"use strict";u();var Af=Ot(),da=gs(),ee=require("fs"),Da=Vt(),ga=Os(),Ea=require("path"),ya=qt();Ca.exports=(r,e,t)=>{let s=Af(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),s.sync?Cf(s,e):Ff(s,e,t)};var Cf=(r,e)=>{let t=new da.Sync(r),s=!0,i,n;try{try{i=ee.openSync(r.file,"r+")}catch(l){if(l.code==="ENOENT")i=ee.openSync(r.file,"w+");else throw l}let o=ee.fstatSync(i),a=Buffer.alloc(512);e:for(n=0;n<o.size;n+=512){for(let h=0,m=0;h<512;h+=m){if(m=ee.readSync(i,a,h,a.length-h,n+h),n===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!m)break e}let l=new ya(a);if(!l.cksumValid)break;let c=512*Math.ceil(l.size/512);if(n+c+512>o.size)break;n+=c,r.mtimeCache&&r.mtimeCache.set(l.path,l.mtime)}s=!1,wf(r,t,n,i,e)}finally{if(s)try{ee.closeSync(i)}catch{}}},wf=(r,e,t,s,i)=>{let n=new Da.WriteStreamSync(r.file,{fd:s,start:t});e.pipe(n),bf(e,i)},Ff=(r,e,t)=>{e=Array.from(e);let s=new da(r),i=(o,a,l)=>{let c=(F,T)=>{F?ee.close(o,S=>l(F)):l(null,T)},h=0;if(a===0)return c(null,0);let m=0,D=Buffer.alloc(512),A=(F,T)=>{if(F)return c(F);if(m+=T,m<512&&T)return ee.read(o,D,m,D.length-m,h+m,A);if(h===0&&D[0]===31&&D[1]===139)return c(new Error("cannot append to compressed archives"));if(m<512)return c(null,h);let S=new ya(D);if(!S.cksumValid)return c(null,h);let x=512*Math.ceil(S.size/512);if(h+x+512>a||(h+=x+512,h>=a))return c(null,h);r.mtimeCache&&r.mtimeCache.set(S.path,S.mtime),m=0,ee.read(o,D,0,512,h,A)};ee.read(o,D,0,512,h,A)},n=new Promise((o,a)=>{s.on("error",a);let l="r+",c=(h,m)=>{if(h&&h.code==="ENOENT"&&l==="r+")return l="w+",ee.open(r.file,l,c);if(h)return a(h);ee.fstat(m,(D,A)=>{if(D)return ee.close(m,()=>a(D));i(m,A.size,(F,T)=>{if(F)return a(F);let S=new Da.WriteStream(r.file,{fd:m,start:T});s.pipe(S),S.on("error",a),S.on("close",o),Aa(s,e)})})};ee.open(r.file,l,c)});return t?n.then(t,t):n},bf=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?ga({file:Ea.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Aa=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return ga({file:Ea.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Aa(r,e));r.add(t)}r.end()}});var Fa=E((r0,wa)=>{"use strict";u();var Sf=Ot(),kf=Tn();wa.exports=(r,e,t)=>{let s=Sf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),_f(s),kf(s,e,t)};var _f=r=>{let e=r.filter;r.mtimeCache||(r.mtimeCache=new Map),r.filter=e?(t,s)=>e(t,s)&&!(r.mtimeCache.get(t)>s.mtime):(t,s)=>!(r.mtimeCache.get(t)>s.mtime)}});var ka=E((s0,Sa)=>{u();var{promisify:ba}=require("util"),it=require("fs"),Rf=r=>{if(!r)r={mode:511,fs:it};else if(typeof r=="object")r={mode:511,fs:it,...r};else if(typeof r=="number")r={mode:r,fs:it};else if(typeof r=="string")r={mode:parseInt(r,8),fs:it};else throw new TypeError("invalid options argument");return r.mkdir=r.mkdir||r.fs.mkdir||it.mkdir,r.mkdirAsync=ba(r.mkdir),r.stat=r.stat||r.fs.stat||it.stat,r.statAsync=ba(r.stat),r.statSync=r.statSync||r.fs.statSync||it.statSync,r.mkdirSync=r.mkdirSync||r.fs.mkdirSync||it.mkdirSync,r};Sa.exports=Rf});var Ra=E((i0,_a)=>{u();var vf=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:Bf,parse:xf}=require("path"),Of=r=>{if(/\0/.test(r))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:r,code:"ERR_INVALID_ARG_VALUE"});if(r=Bf(r),vf==="win32"){let e=/[*|"<>?:]/,{root:t}=xf(r);if(e.test(r.substr(t.length)))throw Object.assign(new Error("Illegal characters in path."),{path:r,code:"EINVAL"})}return r};_a.exports=Of});var Ta=E((n0,Oa)=>{u();var{dirname:va}=require("path"),Ba=(r,e,t=void 0)=>t===e?Promise.resolve():r.statAsync(e).then(s=>s.isDirectory()?t:void 0,s=>s.code==="ENOENT"?Ba(r,va(e),e):void 0),xa=(r,e,t=void 0)=>{if(t!==e)try{return r.statSync(e).isDirectory()?t:void 0}catch(s){return s.code==="ENOENT"?xa(r,va(e),e):void 0}};Oa.exports={findMade:Ba,findMadeSync:xa}});var Nn=E((o0,La)=>{u();var{dirname:Pa}=require("path"),Pn=(r,e,t)=>{e.recursive=!1;let s=Pa(r);return s===r?e.mkdirAsync(r,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(r,e).then(()=>t||r,i=>{if(i.code==="ENOENT")return Pn(s,e).then(n=>Pn(r,e,n));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(r).then(n=>{if(n.isDirectory())return t;throw i},()=>{throw i})})},Ln=(r,e,t)=>{let s=Pa(r);if(e.recursive=!1,s===r)try{return e.mkdirSync(r,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(r,e),t||r}catch(i){if(i.code==="ENOENT")return Ln(r,e,Ln(s,e,t));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(r).isDirectory())throw i}catch{throw i}}};La.exports={mkdirpManual:Pn,mkdirpManualSync:Ln}});var Ma=E((u0,Ia)=>{u();var{dirname:Na}=require("path"),{findMade:Tf,findMadeSync:Pf}=Ta(),{mkdirpManual:Lf,mkdirpManualSync:Nf}=Nn(),If=(r,e)=>(e.recursive=!0,Na(r)===r?e.mkdirAsync(r,e):Tf(e,r).then(s=>e.mkdirAsync(r,e).then(()=>s).catch(i=>{if(i.code==="ENOENT")return Lf(r,e);throw i}))),Mf=(r,e)=>{if(e.recursive=!0,Na(r)===r)return e.mkdirSync(r,e);let s=Pf(e,r);try{return e.mkdirSync(r,e),s}catch(i){if(i.code==="ENOENT")return Nf(r,e);throw i}};Ia.exports={mkdirpNative:If,mkdirpNativeSync:Mf}});var $a=E((a0,ja)=>{u();var qa=require("fs"),qf=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,In=qf.replace(/^v/,"").split("."),Ua=+In[0]>10||+In[0]==10&&+In[1]>=12,Uf=Ua?r=>r.mkdir===qa.mkdir:()=>!1,jf=Ua?r=>r.mkdirSync===qa.mkdirSync:()=>!1;ja.exports={useNative:Uf,useNativeSync:jf}});var Ya=E((c0,Ja)=>{u();var Zt=ka(),Xt=Ra(),{mkdirpNative:za,mkdirpNativeSync:Wa}=Ma(),{mkdirpManual:Ga,mkdirpManualSync:Ha}=Nn(),{useNative:$f,useNativeSync:zf}=$a(),Qt=(r,e)=>(r=Xt(r),e=Zt(e),$f(e)?za(r,e):Ga(r,e)),Wf=(r,e)=>(r=Xt(r),e=Zt(e),zf(e)?Wa(r,e):Ha(r,e));Qt.sync=Wf;Qt.native=(r,e)=>za(Xt(r),Zt(e));Qt.manual=(r,e)=>Ga(Xt(r),Zt(e));Qt.nativeSync=(r,e)=>Wa(Xt(r),Zt(e));Qt.manualSync=(r,e)=>Ha(Xt(r),Zt(e));Ja.exports=Qt});var tc=E((l0,ec)=>{"use strict";u();var ue=require("fs"),St=require("path"),Gf=ue.lchown?"lchown":"chown",Hf=ue.lchownSync?"lchownSync":"chownSync",Ka=ue.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),Va=(r,e,t)=>{try{return ue[Hf](r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Jf=(r,e,t)=>{try{return ue.chownSync(r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Yf=Ka?(r,e,t,s)=>i=>{!i||i.code!=="EISDIR"?s(i):ue.chown(r,e,t,s)}:(r,e,t,s)=>s,Mn=Ka?(r,e,t)=>{try{return Va(r,e,t)}catch(s){if(s.code!=="EISDIR")throw s;Jf(r,e,t)}}:(r,e,t)=>Va(r,e,t),Vf=process.version,Za=(r,e,t)=>ue.readdir(r,e,t),Kf=(r,e)=>ue.readdirSync(r,e);/^v4\./.test(Vf)&&(Za=(r,e,t)=>ue.readdir(r,t));var Ps=(r,e,t,s)=>{ue[Gf](r,e,t,Yf(r,e,t,i=>{s(i&&i.code!=="ENOENT"?i:null)}))},Xa=(r,e,t,s,i)=>{if(typeof e=="string")return ue.lstat(St.resolve(r,e),(n,o)=>{if(n)return i(n.code!=="ENOENT"?n:null);o.name=e,Xa(r,o,t,s,i)});if(e.isDirectory())qn(St.resolve(r,e.name),t,s,n=>{if(n)return i(n);let o=St.resolve(r,e.name);Ps(o,t,s,i)});else{let n=St.resolve(r,e.name);Ps(n,t,s,i)}},qn=(r,e,t,s)=>{Za(r,{withFileTypes:!0},(i,n)=>{if(i){if(i.code==="ENOENT")return s();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return s(i)}if(i||!n.length)return Ps(r,e,t,s);let o=n.length,a=null,l=c=>{if(!a){if(c)return s(a=c);if(--o===0)return Ps(r,e,t,s)}};n.forEach(c=>Xa(r,c,e,t,l))})},Zf=(r,e,t,s)=>{if(typeof e=="string")try{let i=ue.lstatSync(St.resolve(r,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&Qa(St.resolve(r,e.name),t,s),Mn(St.resolve(r,e.name),t,s)},Qa=(r,e,t)=>{let s;try{s=Kf(r,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Mn(r,e,t);throw i}return s&&s.length&&s.forEach(i=>Zf(r,i,e,t)),Mn(r,e,t)};ec.exports=qn;qn.sync=Qa});var nc=E((h0,Un)=>{"use strict";u();var rc=Ya(),ae=require("fs"),Ls=require("path"),sc=tc(),me=It(),Ns=class extends Error{constructor(e,t){super("Cannot extract through symbolic link"),this.path=t,this.symlink=e}get name(){return"SylinkError"}},Is=class extends Error{constructor(e,t){super(t+": Cannot cd into '"+e+"'"),this.path=e,this.code=t}get name(){return"CwdError"}},Ms=(r,e)=>r.get(me(e)),wr=(r,e,t)=>r.set(me(e),t),Xf=(r,e)=>{ae.stat(r,(t,s)=>{(t||!s.isDirectory())&&(t=new Is(r,t&&t.code||"ENOTDIR")),e(t)})};Un.exports=(r,e,t)=>{r=me(r);let s=e.umask,i=e.mode|448,n=(i&s)!==0,o=e.uid,a=e.gid,l=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,h=e.unlink,m=e.cache,D=me(e.cwd),A=(S,x)=>{S?t(S):(wr(m,r,!0),x&&l?sc(x,o,a,lt=>A(lt)):n?ae.chmod(r,i,t):t())};if(m&&Ms(m,r)===!0)return A();if(r===D)return Xf(r,A);if(c)return rc(r,{mode:i}).then(S=>A(null,S),A);let T=me(Ls.relative(D,r)).split("/");qs(D,T,i,m,h,D,null,A)};var qs=(r,e,t,s,i,n,o,a)=>{if(!e.length)return a(null,o);let l=e.shift(),c=me(Ls.resolve(r+"/"+l));if(Ms(s,c))return qs(c,e,t,s,i,n,o,a);ae.mkdir(c,t,ic(c,e,t,s,i,n,o,a))},ic=(r,e,t,s,i,n,o,a)=>l=>{l?ae.lstat(r,(c,h)=>{if(c)c.path=c.path&&me(c.path),a(c);else if(h.isDirectory())qs(r,e,t,s,i,n,o,a);else if(i)ae.unlink(r,m=>{if(m)return a(m);ae.mkdir(r,t,ic(r,e,t,s,i,n,o,a))});else{if(h.isSymbolicLink())return a(new Ns(r,r+"/"+e.join("/")));a(l)}}):(o=o||r,qs(r,e,t,s,i,n,o,a))},Qf=r=>{let e=!1,t="ENOTDIR";try{e=ae.statSync(r).isDirectory()}catch(s){t=s.code}finally{if(!e)throw new Is(r,t)}};Un.exports.sync=(r,e)=>{r=me(r);let t=e.umask,s=e.mode|448,i=(s&t)!==0,n=e.uid,o=e.gid,a=typeof n=="number"&&typeof o=="number"&&(n!==e.processUid||o!==e.processGid),l=e.preserve,c=e.unlink,h=e.cache,m=me(e.cwd),D=S=>{wr(h,r,!0),S&&a&&sc.sync(S,n,o),i&&ae.chmodSync(r,s)};if(h&&Ms(h,r)===!0)return D();if(r===m)return Qf(m),D();if(l)return D(rc.sync(r,s));let F=me(Ls.relative(m,r)).split("/"),T=null;for(let S=F.shift(),x=m;S&&(x+="/"+S);S=F.shift())if(x=me(Ls.resolve(x)),!Ms(h,x))try{ae.mkdirSync(x,s),T=T||x,wr(h,x,!0)}catch{let de=ae.lstatSync(x);if(de.isDirectory()){wr(h,x,!0);continue}else if(c){ae.unlinkSync(x),ae.mkdirSync(x,s),T=T||x,wr(h,x,!0);continue}else if(de.isSymbolicLink())return new Ns(x,x+"/"+F.join("/"))}return D(T)}});var $n=E((f0,oc)=>{u();var jn=Object.create(null),{hasOwnProperty:ep}=Object.prototype;oc.exports=r=>(ep.call(jn,r)||(jn[r]=r.normalize("NFKD")),jn[r])});var lc=E((p0,cc)=>{u();var uc=require("assert"),tp=$n(),rp=Ut(),{join:ac}=require("path"),sp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,ip=sp==="win32";cc.exports=()=>{let r=new Map,e=new Map,t=c=>c.split("/").slice(0,-1).reduce((m,D)=>(m.length&&(D=ac(m[m.length-1],D)),m.push(D||"/"),m),[]),s=new Set,i=c=>{let h=e.get(c);if(!h)throw new Error("function does not have any path reservations");return{paths:h.paths.map(m=>r.get(m)),dirs:[...h.dirs].map(m=>r.get(m))}},n=c=>{let{paths:h,dirs:m}=i(c);return h.every(D=>D[0]===c)&&m.every(D=>D[0]instanceof Set&&D[0].has(c))},o=c=>s.has(c)||!n(c)?!1:(s.add(c),c(()=>a(c)),!0),a=c=>{if(!s.has(c))return!1;let{paths:h,dirs:m}=e.get(c),D=new Set;return h.forEach(A=>{let F=r.get(A);uc.equal(F[0],c),F.length===1?r.delete(A):(F.shift(),typeof F[0]=="function"?D.add(F[0]):F[0].forEach(T=>D.add(T)))}),m.forEach(A=>{let F=r.get(A);uc(F[0]instanceof Set),F[0].size===1&&F.length===1?r.delete(A):F[0].size===1?(F.shift(),D.add(F[0])):F[0].delete(c)}),s.delete(c),D.forEach(A=>o(A)),!0};return{check:n,reserve:(c,h)=>{c=ip?["win32 parallelization disabled"]:c.map(D=>tp(rp(ac(D))).toLowerCase());let m=new Set(c.map(D=>t(D)).reduce((D,A)=>D.concat(A)));return e.set(h,{dirs:m,paths:c}),c.forEach(D=>{let A=r.get(D);A?A.push(h):r.set(D,[h])}),m.forEach(D=>{let A=r.get(D);A?A[A.length-1]instanceof Set?A[A.length-1].add(h):A.push(new Set([h])):r.set(D,[new Set([h])])}),o(h)}}}});var pc=E((m0,fc)=>{u();var np=process.env.__FAKE_PLATFORM__||process.platform,op=np==="win32",up=global.__FAKE_TESTING_FS__||require("fs"),{O_CREAT:ap,O_TRUNC:cp,O_WRONLY:lp,UV_FS_O_FILEMAP:hc=0}=up.constants,hp=op&&!!hc,fp=512*1024,pp=hc|cp|ap|lp;fc.exports=hp?r=>r<fp?pp:"w":()=>"w"});var Zn=E((d0,_c)=>{"use strict";u();var mp=require("assert"),dp=xs(),_=require("fs"),Dp=Vt(),je=require("path"),bc=nc(),mc=Vi(),gp=lc(),Ep=Ki(),te=It(),yp=Ut(),Ap=$n(),dc=Symbol("onEntry"),Gn=Symbol("checkFs"),Dc=Symbol("checkFs2"),$s=Symbol("pruneCache"),Hn=Symbol("isReusable"),ce=Symbol("makeFs"),Jn=Symbol("file"),Yn=Symbol("directory"),zs=Symbol("link"),gc=Symbol("symlink"),Ec=Symbol("hardlink"),yc=Symbol("unsupported"),Ac=Symbol("checkPath"),nt=Symbol("mkdir"),W=Symbol("onError"),Us=Symbol("pending"),Cc=Symbol("pend"),er=Symbol("unpend"),zn=Symbol("ended"),Wn=Symbol("maybeClose"),Vn=Symbol("skip"),Fr=Symbol("doChown"),br=Symbol("uid"),Sr=Symbol("gid"),kr=Symbol("checkedCwd"),Sc=require("crypto"),kc=pc(),Cp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,_r=Cp==="win32",wp=(r,e)=>{if(!_r)return _.unlink(r,e);let t=r+".DELETE."+Sc.randomBytes(16).toString("hex");_.rename(r,t,s=>{if(s)return e(s);_.unlink(t,e)})},Fp=r=>{if(!_r)return _.unlinkSync(r);let e=r+".DELETE."+Sc.randomBytes(16).toString("hex");_.renameSync(r,e),_.unlinkSync(e)},wc=(r,e,t)=>r===r>>>0?r:e===e>>>0?e:t,Fc=r=>Ap(yp(te(r))).toLowerCase(),bp=(r,e)=>{e=Fc(e);for(let t of r.keys()){let s=Fc(t);(s===e||s.indexOf(e+"/")===0)&&r.delete(t)}},Sp=r=>{for(let e of r.keys())r.delete(e)},Rr=class extends dp{constructor(e){if(e||(e={}),e.ondone=t=>{this[zn]=!0,this[Wn]()},super(e),this[kr]=!1,this.reservations=gp(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[Us]=0,this[zn]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||_r,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=te(je.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",t=>this[dc](t))}warn(e,t,s={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(s.recoverable=!1),super.warn(e,t,s)}[Wn](){this[zn]&&this[Us]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[Ac](e){if(this.strip){let t=te(e.path).split("/");if(t.length<this.strip)return!1;if(e.path=t.slice(this.strip).join("/"),e.type==="Link"){let s=te(e.linkpath).split("/");if(s.length>=this.strip)e.linkpath=s.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let t=te(e.path),s=t.split("/");if(s.includes("..")||_r&&/^[a-z]:\.\.$/i.test(s[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:t}),!1;let[i,n]=Ep(t);i&&(e.path=n,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:t}))}if(je.isAbsolute(e.path)?e.absolute=te(je.resolve(e.path)):e.absolute=te(je.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:te(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:t}=je.win32.parse(e.absolute);e.absolute=t+mc.encode(e.absolute.slice(t.length));let{root:s}=je.win32.parse(e.path);e.path=s+mc.encode(e.path.slice(s.length))}return!0}[dc](e){if(!this[Ac](e))return e.resume();switch(mp.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[Gn](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[yc](e)}}[W](e,t){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:t}),this[er](),t.resume())}[nt](e,t,s){bc(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t,noChmod:this.noChmod},s)}[Fr](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[br](e){return wc(this.uid,e.uid,this.processUid)}[Sr](e){return wc(this.gid,e.gid,this.processGid)}[Jn](e,t){let s=e.mode&4095||this.fmode,i=new Dp.WriteStream(e.absolute,{flags:kc(e.size),mode:s,autoClose:!1});i.on("error",l=>{i.fd&&_.close(i.fd,()=>{}),i.write=()=>!0,this[W](l,e),t()});let n=1,o=l=>{if(l){i.fd&&_.close(i.fd,()=>{}),this[W](l,e),t();return}--n===0&&_.close(i.fd,c=>{c?this[W](c,e):this[er](),t()})};i.on("finish",l=>{let c=e.absolute,h=i.fd;if(e.mtime&&!this.noMtime){n++;let m=e.atime||new Date,D=e.mtime;_.futimes(h,m,D,A=>A?_.utimes(c,m,D,F=>o(F&&A)):o())}if(this[Fr](e)){n++;let m=this[br](e),D=this[Sr](e);_.fchown(h,m,D,A=>A?_.chown(c,m,D,F=>o(F&&A)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",l=>{this[W](l,e),t()}),e.pipe(a)),a.pipe(i)}[Yn](e,t){let s=e.mode&4095||this.dmode;this[nt](e.absolute,s,i=>{if(i){this[W](i,e),t();return}let n=1,o=a=>{--n===0&&(t(),this[er](),e.resume())};e.mtime&&!this.noMtime&&(n++,_.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[Fr](e)&&(n++,_.chown(e.absolute,this[br](e),this[Sr](e),o)),o()})}[yc](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[gc](e,t){this[zs](e,e.linkpath,"symlink",t)}[Ec](e,t){let s=te(je.resolve(this.cwd,e.linkpath));this[zs](e,s,"link",t)}[Cc](){this[Us]++}[er](){this[Us]--,this[Wn]()}[Vn](e){this[er](),e.resume()}[Hn](e,t){return e.type==="File"&&!this.unlink&&t.isFile()&&t.nlink<=1&&!_r}[Gn](e){this[Cc]();let t=[e.path];e.linkpath&&t.push(e.linkpath),this.reservations.reserve(t,s=>this[Dc](e,s))}[$s](e){e.type==="SymbolicLink"?Sp(this.dirCache):e.type!=="Directory"&&bp(this.dirCache,e.absolute)}[Dc](e,t){this[$s](e);let s=a=>{this[$s](e),t(a)},i=()=>{this[nt](this.cwd,this.dmode,a=>{if(a){this[W](a,e),s();return}this[kr]=!0,n()})},n=()=>{if(e.absolute!==this.cwd){let a=te(je.dirname(e.absolute));if(a!==this.cwd)return this[nt](a,this.dmode,l=>{if(l){this[W](l,e),s();return}o()})}o()},o=()=>{_.lstat(e.absolute,(a,l)=>{if(l&&(this.keep||this.newer&&l.mtime>e.mtime)){this[Vn](e),s();return}if(a||this[Hn](e,l))return this[ce](null,e,s);if(l.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(l.mode&4095)!==e.mode,h=m=>this[ce](m,e,s);return c?_.chmod(e.absolute,e.mode,h):h()}if(e.absolute!==this.cwd)return _.rmdir(e.absolute,c=>this[ce](c,e,s))}if(e.absolute===this.cwd)return this[ce](null,e,s);wp(e.absolute,c=>this[ce](c,e,s))})};this[kr]?n():i()}[ce](e,t,s){if(e){this[W](e,t),s();return}switch(t.type){case"File":case"OldFile":case"ContiguousFile":return this[Jn](t,s);case"Link":return this[Ec](t,s);case"SymbolicLink":return this[gc](t,s);case"Directory":case"GNUDumpDir":return this[Yn](t,s)}}[zs](e,t,s,i){_[s](t,e.absolute,n=>{n?this[W](n,e):(this[er](),e.resume()),i()})}},js=r=>{try{return[null,r()]}catch(e){return[e,null]}},Kn=class extends Rr{[ce](e,t){return super[ce](e,t,()=>{})}[Gn](e){if(this[$s](e),!this[kr]){let n=this[nt](this.cwd,this.dmode);if(n)return this[W](n,e);this[kr]=!0}if(e.absolute!==this.cwd){let n=te(je.dirname(e.absolute));if(n!==this.cwd){let o=this[nt](n,this.dmode);if(o)return this[W](o,e)}}let[t,s]=js(()=>_.lstatSync(e.absolute));if(s&&(this.keep||this.newer&&s.mtime>e.mtime))return this[Vn](e);if(t||this[Hn](e,s))return this[ce](null,e);if(s.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(s.mode&4095)!==e.mode,[a]=o?js(()=>{_.chmodSync(e.absolute,e.mode)}):[];return this[ce](a,e)}let[n]=js(()=>_.rmdirSync(e.absolute));this[ce](n,e)}let[i]=e.absolute===this.cwd?[]:js(()=>Fp(e.absolute));this[ce](i,e)}[Jn](e,t){let s=e.mode&4095||this.fmode,i=a=>{let l;try{_.closeSync(n)}catch(c){l=c}(a||l)&&this[W](a||l,e),t()},n;try{n=_.openSync(e.absolute,kc(e.size),s)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[W](a,e)),e.pipe(o)),o.on("data",a=>{try{_.writeSync(n,a,0,a.length)}catch(l){i(l)}}),o.on("end",a=>{let l=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,h=e.mtime;try{_.futimesSync(n,c,h)}catch(m){try{_.utimesSync(e.absolute,c,h)}catch{l=m}}}if(this[Fr](e)){let c=this[br](e),h=this[Sr](e);try{_.fchownSync(n,c,h)}catch(m){try{_.chownSync(e.absolute,c,h)}catch{l=l||m}}}i(l)})}[Yn](e,t){let s=e.mode&4095||this.dmode,i=this[nt](e.absolute,s);if(i){this[W](i,e),t();return}if(e.mtime&&!this.noMtime)try{_.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[Fr](e))try{_.chownSync(e.absolute,this[br](e),this[Sr](e))}catch{}t(),e.resume()}[nt](e,t){try{return bc.sync(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t})}catch(s){return s}}[zs](e,t,s,i){try{_[s+"Sync"](t,e.absolute),i(),e.resume()}catch(n){return this[W](n,e)}}};Rr.Sync=Kn;_c.exports=Rr});var Oc=E((D0,xc)=>{"use strict";u();var kp=Ot(),Ws=Zn(),vc=require("fs"),Bc=Vt(),Rc=require("path"),Xn=Ut();xc.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=kp(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&_p(s,e),s.file&&s.sync?Rp(s):s.file?vp(s,t):s.sync?Bp(s):xp(s)};var _p=(r,e)=>{let t=new Map(e.map(n=>[Xn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||Rc.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(Rc.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(Xn(n)):n=>i(Xn(n))},Rp=r=>{let e=new Ws.Sync(r),t=r.file,s=vc.statSync(t),i=r.maxReadSize||16*1024*1024;new Bc.ReadStreamSync(t,{readSize:i,size:s.size}).pipe(e)},vp=(r,e)=>{let t=new Ws(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("close",o),vc.stat(i,(l,c)=>{if(l)a(l);else{let h=new Bc.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},Bp=r=>new Ws.Sync(r),xp=r=>new Ws(r)});var Tc=E(N=>{"use strict";u();N.c=N.create=ma();N.r=N.replace=Tn();N.t=N.list=Os();N.u=N.update=Fa();N.x=N.extract=Oc();N.Pack=gs();N.Unpack=Zn();N.Parse=xs();N.ReadEntry=es();N.WriteEntry=un();N.Header=qt();N.Pax=rs();N.types=$i()});var qc=E((C0,Mc)=>{u();function le(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Mc.exports=le;le.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};le.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};le.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var s=this;return this._timer=setTimeout(function(){s._attempts++,s._operationTimeoutCb&&(s._timeout=setTimeout(function(){s._operationTimeoutCb(s._attempts)},s._operationTimeout),s._options.unref&&s._timeout.unref()),s._fn(s._attempts)},t),this._options.unref&&this._timer.unref(),!0};le.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};le.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};le.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};le.prototype.start=le.prototype.try;le.prototype.errors=function(){return this._errors};le.prototype.attempts=function(){return this._attempts};le.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,s=0;s<this._errors.length;s++){var i=this._errors[s],n=i.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=i,t=o)}return e}});var Uc=E(kt=>{u();var Lp=qc();kt.operation=function(r){var e=kt.timeouts(r);return new Lp(e,{forever:r&&(r.forever||r.retries===1/0),unref:r&&r.unref,maxRetryTime:r&&r.maxRetryTime})};kt.timeouts=function(r){if(r instanceof Array)return[].concat(r);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in r)e[t]=r[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var s=[],i=0;i<e.retries;i++)s.push(this.createTimeout(i,e));return r&&r.forever&&!s.length&&s.push(this.createTimeout(i,e)),s.sort(function(n,o){return n-o}),s};kt.createTimeout=function(r,e){var t=e.randomize?Math.random()+1:1,s=Math.round(t*Math.max(e.minTimeout,1)*Math.pow(e.factor,r));return s=Math.min(s,e.maxTimeout),s};kt.wrap=function(r,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var s in r)typeof r[s]=="function"&&t.push(s)}for(var i=0;i<t.length;i++){var n=t[i],o=r[n];r[n]=function(l){var c=kt.operation(e),h=Array.prototype.slice.call(arguments,1),m=h.pop();h.push(function(D){c.retry(D)||(D&&(arguments[0]=c.mainError()),m.apply(this,arguments))}),c.attempt(function(){l.apply(r,h)})}.bind(r,o),r[n].options=e}}});var $c=E((F0,jc)=>{u();jc.exports=Uc()});var Wc=E((b0,zc)=>{u();var Np=$c();function Ip(r,e){function t(s,i){var n=e||{},o;"randomize"in n||(n.randomize=!0),o=Np.operation(n);function a(h){i(h||new Error("Aborted"))}function l(h,m){if(h.bail){a(h);return}o.retry(h)?n.onRetry&&n.onRetry(h,m):i(o.mainError())}function c(h){var m;try{m=r(a,h)}catch(D){l(D,h);return}Promise.resolve(m).then(s).catch(function(A){l(A,h)})}o.attempt(c)}return new Promise(t)}zc.exports=Ip});var Dm={};Fl(Dm,{ConvertError:()=>R,MANAGERS:()=>We,convert:()=>dm,getPackageManagerMeta:()=>ro,getWorkspaceDetails:()=>to,install:()=>Hs});module.exports=bl(Dm);u();u();u();u();var Tl=b(ho());u();u();function Qs(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}u();u();function ur(r){return ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ur(r)}u();function ei(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function ti(r,e){if(e&&(ur(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ei(r)}u();function pt(r){return pt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},pt(r)}u();u();function Re(r,e){return Re=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,i){return s.__proto__=i,s},Re(r,e)}function ri(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&Re(r,e)}u();u();function si(r){return Function.toString.call(r).indexOf("[native code]")!==-1}u();u();function ii(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Bt(r,e,t){return ii()?Bt=Reflect.construct.bind():Bt=function(i,n,o){var a=[null];a.push.apply(a,n);var l=Function.bind.apply(i,a),c=new l;return o&&Re(c,o.prototype),c},Bt.apply(null,arguments)}function ar(r){var e=typeof Map=="function"?new Map:void 0;return ar=function(s){if(s===null||!si(s))return s;if(typeof s!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(s))return e.get(s);e.set(s,i)}function i(){return Bt(s,arguments,pt(this).constructor)}return i.prototype=Object.create(s.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Re(i,s)},ar(r)}var Bo=b(vo());var Pl=b(require("fs-extra")),rd=function(r){ri(e,r);function e(t){var s;return Qs(this,e),s=ti(this,pt(e).call(this,"No package.json could be found upwards from the directory ".concat(t))),s.directory=t,s}return e}(ar(Error));u();u();var Ll=b(require("fs")),Nl=b(require("path"));u();var $l=b(require("fs")),zl=b(require("path")),Wl=b(require("js-yaml")),Gl=require("fast-glob");u();var Mr=b(require("chalk")),ql=b(require("ora")),xo=b(require("gradient-string")),Oo="#0099F7",To="#F11712",Ul="#FFFF00",hd=(0,xo.default)(Oo,To),fd=Mr.default.hex(Oo),pd=Mr.default.hex(To),md=Mr.default.hex(Ul);u();var Po=b(require("os")),Lo=b(require("execa"));async function qr(r,e=[],t){let s={cwd:Po.default.tmpdir(),env:{COREPACK_ENABLE_STRICT:"0"},...t};try{let{stdout:i}=await(0,Lo.default)(r,e,s);return i.trim()}catch{return}}async function fi(){let[r,e,t,s]=await Promise.all([qr("yarnpkg",["--version"]),qr("npm",["--version"]),qr("pnpm",["--version"]),qr("bun",["--version"])]);return{yarn:r,pnpm:t,npm:e,bun:s}}u();var Hl=b(require("fs-extra"));u();var Jl=b(require("path")),Yl=b(require("fs-extra")),Vl=b(require("chalk"));u();var Pc=require("stream"),Lc=require("util"),Op=require("path"),Tp=require("os"),Nc=require("fs"),Pp=b(Tc());var E0=(0,Lc.promisify)(Pc.Stream.pipeline);u();var Ic=require("fs-extra");u();var Wp=b(require("path")),Gp=b(Wc()),Hp=b(require("chalk")),Qn=require("fs-extra");u();u();u();u();var R=class extends Error{constructor(t,s){var i;super(t);this.name="ConvertError",this.type=(i=s==null?void 0:s.type)!=null?i:"unknown",Error.captureStackTrace(this,R)}};u();u();var _t=b(require("path")),H=require("fs-extra"),Qc=b(require("execa"));u();var Kc=b(require("path")),Zc=require("fs-extra"),Xc=b(require("chalk"));u();var $e=b(require("path")),Gc=b(require("execa")),re=require("fs-extra"),Hc=require("fast-glob"),Jc=b(require("js-yaml"));var Jp=/^(?!_)(?<manager>.+)@(?<version>.+)$/;function I({workspaceRoot:r}){let e=$e.default.join(r,"package.json");try{return(0,re.readJsonSync)(e,"utf8")}catch(t){if(t&&typeof t=="object"&&"code"in t){if(t.code==="ENOENT")throw new R(`no "package.json" found at ${r}`,{type:"package_json-missing"});if(t.code==="EJSONPARSE")throw new R(`failed to parse "package.json" at ${r}`,{type:"package_json-parse_error"})}throw new Error(`unexpected error reading "package.json" at ${r}`)}}function ot({workspaceRoot:r}){let{packageManager:e}=I({workspaceRoot:r});if(e)try{let t=Jp.exec(e);if(t){let[s,i]=t;return i}}catch{}}function ze({workspaceRoot:r}){let e=I({workspaceRoot:r}),t=$e.default.basename(r),{name:s=t,description:i}=e;return{name:s,description:i}}function eo({workspaceRoot:r}){let e=$e.default.join(r,"pnpm-workspace.yaml");if((0,re.existsSync)(e))try{let t=Jc.default.load((0,re.readFileSync)(e,"utf8"));if(t instanceof Object&&"packages"in t&&Array.isArray(t.packages))return t.packages}catch{throw new R(`failed to parse ${e}`,{type:"pnpm-workspace_parse_error"})}return[]}function ut({root:r,lockFile:e,workspaceConfig:t}){let s=n=>$e.default.join(r,n),i={root:r,lockfile:s(e),packageJson:s("package.json"),nodeModules:s("node_modules")};return t&&(i.workspaceConfig=s(t)),i}function tr({workspaces:r}){var e;return r?Array.isArray(r)?r:"packages"in r?(e=r.packages)!=null?e:[]:[]:[]}function at({workspaceRoot:r,workspaceGlobs:e}){return e?e.flatMap(t=>{let s=[`${t}/package.json`];return(0,Hc.sync)(s,{onlyFiles:!0,absolute:!0,cwd:r,ignore:["**/node_modules/**"]})}).map(t=>{let s=$e.default.dirname(t),{name:i,description:n}=ze({workspaceRoot:s});return{name:i,description:n,paths:{root:s,packageJson:t,nodeModules:$e.default.join(s,"node_modules")}}}):[]}function Yc({directory:r}){let e=$e.default.resolve(process.cwd(),r);return{exists:(0,re.existsSync)(e),absolute:e}}function he({packageManager:r,action:e,project:t}){let s=t.workspaceData.globs.length>0;return`${e==="remove"?"Removing":"Adding"} ${r} ${s?"workspaces":""} ${e==="remove"?"from":"to"} ${t.name}`}function Vc({project:r}){let e=t=>!(t.includes("*")&&(t.includes("**")||t.split("/").slice(0,-1).join("/").includes("*"))||["!","[","]","{","}"].some(s=>t.includes(s)));return r.workspaceData.globs.every(e)}function se({project:r,options:e}){e!=null&&e.dry||(0,re.rmSync)(r.paths.lockfile,{force:!0})}async function Gs({project:r,options:e}){if(!(e!=null&&e.dry)&&(0,re.existsSync)(r.paths.lockfile))try{let{stdout:t}=await(0,Gc.default)("bun",["bun.lockb"],{stdin:"ignore",cwd:r.paths.root});await(0,re.writeFile)($e.default.join(r.paths.root,"yarn.lock"),t)}catch{}finally{(0,re.rmSync)(r.paths.lockfile,{force:!0})}}function Yp({dependencyList:r,project:e,to:t}){let s=[];return e.workspaceData.workspaces.forEach(i=>{let{name:n}=i;if(r[n]){let o=r[n],a=o.startsWith("workspace:")?o.slice(10):o;r[n]=t.name==="pnpm"?`workspace:${a}`:a,s.push(n)}}),{dependencyList:r,updated:s}}function fe({project:r,workspace:e,to:t,logger:s,options:i}){if(["yarn","npm","bun"].includes(t.name)&&["yarn","npm","bun"].includes(r.packageManager))return;let n=I({workspaceRoot:e.paths.root}),o={dependencies:[],devDependencies:[],peerDependencies:[],optionalDependencies:[]},a=["dependencies","devDependencies","peerDependencies","optionalDependencies"];a.forEach(m=>{let D=n[m];if(D){let{updated:A,dependencyList:F}=Yp({dependencyList:D,project:r,to:t});n[m]=F,o[m]=A}});let l=m=>{let D=o[m].length;if(D>0)return`${Xc.default.green(D.toString())} ${m}`},c=a.map(l).filter(Boolean),h=`./${Kc.default.relative(r.paths.root,e.paths.packageJson)}`;if(c.length>=1){let m="updating";c.forEach((D,A)=>{c.length===1?m+=` ${D} in ${h}`:A===c.length-1?m+=`and ${D} in ${h}`:m+=` ${D}, `}),s.workspaceStep(m)}else s.workspaceStep(`no workspace dependencies found in ${h}`);i!=null&&i.dry||(0,Zc.writeJSONSync)(e.paths.packageJson,n,{spaces:2})}var ct={name:"pnpm",lock:"pnpm-lock.yaml"};async function el(r){let e=_t.default.join(r.workspaceRoot,ct.lock),t=_t.default.join(r.workspaceRoot,"pnpm-workspace.yaml"),s=ot({workspaceRoot:r.workspaceRoot});return(0,H.existsSync)(e)||(0,H.existsSync)(t)||s===ct.name}async function Vp(r){if(!await el(r))throw new R("Not a pnpm project",{type:"package_manager-unexpected"});let{name:t,description:s}=ze(r);return{name:t,description:s,packageManager:ct.name,paths:ut({root:r.workspaceRoot,lockFile:ct.lock,workspaceConfig:"pnpm-workspace.yaml"}),workspaceData:{globs:eo(r),workspaces:at({workspaceGlobs:eo(r),...r})}}}async function Kp(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(he({action:"create",packageManager:ct.name,project:e}));let o=I({workspaceRoot:e.paths.root});s.rootHeader(),o.packageManager=`${t.name}@${t.version}`,s.rootStep(`adding "packageManager" field to ${e.name} root "package.json"`),i!=null&&i.dry||((0,H.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),n&&(s.rootStep('adding "pnpm-workspace.yaml"'),(0,H.writeFileSync)(_t.default.join(e.paths.root,"pnpm-workspace.yaml"),`packages:
${e.workspaceData.globs.map(a=>`  - "${a}"`).join(`
`)}`))),n&&(fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})}))}async function Zp(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({action:"remove",packageManager:ct.name,project:e}));let n=I({workspaceRoot:e.paths.root});if(e.paths.workspaceConfig&&i&&(t.subStep('removing "pnpm-workspace.yaml"'),s!=null&&s.dry||(0,H.rmSync)(e.paths.workspaceConfig,{force:!0})),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,H.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,H.rm)(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Xp(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${_t.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,H.rmSync)(e.paths.lockfile,{force:!0})}async function Qp(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${_t.default.relative(e.paths.root,e.paths.lockfile)} to ${ct.lock}`)},n=async()=>{if(!(t!=null&&t.dry)&&(0,H.existsSync)(e.paths.lockfile))try{await(0,Qc.default)(ct.name,["import"],{stdio:"ignore",cwd:e.paths.root})}catch{}finally{se({project:e,options:t})}};switch(e.packageManager){case"pnpm":break;case"bun":i(),await Gs({project:e,options:t}),await n(),(0,H.rmSync)(_t.default.join(e.paths.root,"yarn.lock"),{force:!0});break;case"npm":i(),await n();break;case"yarn":i(),await n();break}}var tl={detect:el,read:Vp,create:Kp,remove:Zp,clean:Xp,convertLock:Qp};u();var vr=b(require("path")),Fe=require("fs-extra");var rr={name:"npm",lock:"package-lock.json"};async function rl(r){let e=vr.default.join(r.workspaceRoot,rr.lock),t=ot({workspaceRoot:r.workspaceRoot});return(0,Fe.existsSync)(e)||t===rr.name}async function em(r){if(!await rl(r))throw new R("Not an npm project",{type:"package_manager-unexpected"});let t=I(r),{name:s,description:i}=ze(r),n=tr({workspaces:t.workspaces});return{name:s,description:i,packageManager:rr.name,paths:ut({root:r.workspaceRoot,lockFile:rr.lock}),workspaceData:{globs:n,workspaces:at({workspaceGlobs:n,...r})}}}async function tm(r){let{project:e,options:t,to:s,logger:i}=r,n=e.workspaceData.globs.length>0;i.mainStep(he({packageManager:rr.name,action:"create",project:e}));let o=I({workspaceRoot:e.paths.root});i.rootHeader(),i.rootStep(`adding "packageManager" field to ${vr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${s.name}@${s.version}`,n?(i.rootStep(`adding "workspaces" field to ${vr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,t!=null&&t.dry||(0,Fe.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:s,logger:i,options:t}),i.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:s,logger:i,options:t})})):t!=null&&t.dry||(0,Fe.writeJSONSync)(e.paths.packageJson,o,{spaces:2})}async function rm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:rr.name,action:"remove",project:e}));let n=I({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,Fe.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,Fe.rm)(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function sm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${vr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,Fe.rmSync)(e.paths.lockfile,{force:!0})}async function im(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":se({project:e,options:t});break;case"bun":se({project:e,options:t});break;case"npm":break;case"yarn":se({project:e,options:t});break}}var sl={detect:rl,read:em,create:tm,remove:rm,clean:sm,convertLock:im};u();var sr=b(require("path")),be=require("fs-extra");var Rt={name:"yarn",lock:"yarn.lock"};async function il(r){let e=sr.default.join(r.workspaceRoot,Rt.lock),t=ot({workspaceRoot:r.workspaceRoot});return(0,be.existsSync)(e)||t===Rt.name}async function nm(r){if(!await il(r))throw new R("Not a yarn project",{type:"package_manager-unexpected"});let t=I(r),{name:s,description:i}=ze(r),n=tr({workspaces:t.workspaces});return{name:s,description:i,packageManager:Rt.name,paths:ut({root:r.workspaceRoot,lockFile:Rt.lock}),workspaceData:{globs:n,workspaces:at({workspaceGlobs:n,...r})}}}async function om(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(he({packageManager:Rt.name,action:"create",project:e}));let o=I({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${sr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${sr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||(0,be.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||(0,be.writeJSONSync)(e.paths.packageJson,o,{spaces:2})}async function um(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:Rt.name,action:"remove",project:e}));let n=I({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,be.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,be.rm)(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function am(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${sr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,be.rmSync)(e.paths.lockfile,{force:!0})}async function cm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${sr.default.relative(e.paths.root,e.paths.lockfile)} to ${Rt.lock}`)};switch(e.packageManager){case"pnpm":se({project:e,options:t});break;case"bun":i(),await Gs({project:e,options:t});break;case"npm":se({project:e,options:t});break;case"yarn":break}}var nl={detect:il,read:nm,create:om,remove:um,clean:am,convertLock:cm};u();var Br=b(require("path")),Se=require("fs-extra");var ir={name:"bun",lock:"bun.lockb"};async function ol(r){let e=Br.default.join(r.workspaceRoot,ir.lock),t=ot({workspaceRoot:r.workspaceRoot});return(0,Se.existsSync)(e)||t===ir.name}async function lm(r){if(!await ol(r))throw new R("Not a bun project",{type:"package_manager-unexpected"});let t=I(r),{name:s,description:i}=ze(r),n=tr({workspaces:t.workspaces});return{name:s,description:i,packageManager:ir.name,paths:ut({root:r.workspaceRoot,lockFile:ir.lock}),workspaceData:{globs:n,workspaces:at({workspaceGlobs:n,...r})}}}async function hm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;if(!Vc({project:e}))throw new R("Unable to convert project to bun - workspace globs unsupported",{type:"bun-workspace_glob_error"});s.mainStep(he({packageManager:ir.name,action:"create",project:e}));let o=I({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${Br.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${Br.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||(0,Se.writeJSONSync)(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||(0,Se.writeJSONSync)(e.paths.packageJson,o,{spaces:2})}async function fm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:ir.name,action:"remove",project:e}));let n=I({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){(0,Se.writeJSONSync)(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>(0,Se.rm)(a,{recursive:!0,force:!0})))}catch{throw new R("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function pm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Br.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||(0,Se.rmSync)(e.paths.lockfile,{force:!0})}async function mm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":se({project:e,options:t});break;case"bun":break;case"npm":se({project:e,options:t});break;case"yarn":se({project:e,options:t});break}}var ul={detect:ol,read:lm,create:hm,remove:fm,clean:pm,convertLock:mm};var We={pnpm:tl,yarn:nl,npm:sl,bun:ul};async function to({root:r}){let{exists:e,absolute:t}=Yc({directory:r});if(!e)throw new R(`Could not find directory at ${t}. Ensure the directory exists.`,{type:"invalid_directory"});for(let{detect:s,read:i}of Object.values(We))if(await s({workspaceRoot:t}))return i({workspaceRoot:t});throw new R("Could not determine package manager. Add `packageManager` to `package.json` or ensure a lockfile is present.",{type:"package_manager-unable_to_detect"})}u();var pl=b(require("chalk"));u();var ll=b(require("execa")),hl=b(require("ora")),fl=require("semver");u();var ie=b(require("chalk")),al=b(require("gradient-string")),nr=2,or=class{constructor({interactive:e,dry:t}={}){this.interactive=e!=null?e:!0,this.dry=t!=null?t:!1,this.step=1}logger(...e){this.interactive&&console.log(...e)}indented(e,...t){this.logger(" ".repeat(nr*e),...t)}header(e){this.blankLine(),this.logger(ie.default.bold(e))}installerFrames(){let e=`${" ".repeat(nr)} - ${this.dry?ie.default.yellow("SKIPPED | "):ie.default.green("OK | ")}`;return[`${e}   `,`${e}>  `,`${e}>> `,`${e}>>>`]}gradient(e){return(0,al.default)("#0099F7","#F11712")(e.toString())}hero(){this.logger(ie.default.bold(this.gradient(`
>>> TURBOREPO
`)))}info(...e){this.logger(...e)}mainStep(e){this.blankLine(),this.logger(`${this.step}. ${ie.default.underline(e)}`),this.step+=1}subStep(...e){this.logger(" ".repeat(nr),"-",this.dry?ie.default.yellow("SKIPPED |"):ie.default.green("OK |"),...e)}subStepFailure(...e){this.logger(" ".repeat(nr),"-",ie.default.red("ERROR |"),...e)}rootHeader(){this.blankLine(),this.indented(2,"Root:")}rootStep(...e){this.logger(" ".repeat(nr*3),"-",this.dry?ie.default.yellow("SKIPPED |"):ie.default.green("OK |"),...e)}workspaceHeader(){this.blankLine(),this.indented(2,"Workspaces:")}workspaceStep(...e){this.logger(" ".repeat(nr*3),"-",this.dry?ie.default.yellow("SKIPPED |"):ie.default.green("OK |"),...e)}blankLine(){this.logger()}error(...e){console.error(...e)}};var cl={npm:[{name:"npm",template:"npm",command:"npm",installArgs:["install"],version:"latest",executable:"npx",semver:"*",default:!0}],pnpm:[{name:"pnpm6",template:"pnpm",command:"pnpm",installArgs:["install"],version:"latest-6",executable:"pnpx",semver:"6.x"},{name:"pnpm",template:"pnpm",command:"pnpm",installArgs:["install","--fix-lockfile"],version:"latest",executable:"pnpm dlx",semver:">=7",default:!0}],yarn:[{name:"yarn",template:"yarn",command:"yarn",installArgs:["install"],version:"1.x",executable:"npx",semver:"<2",default:!0},{name:"berry",template:"berry",command:"yarn",installArgs:["install","--no-immutable"],version:"stable",executable:"yarn dlx",semver:">=2"}],bun:[{name:"bun",template:"bun",command:"bun",installArgs:["install"],version:"latest",executable:"bunx",semver:"^1.0.1",default:!0}]};function ro(r){let{version:e,name:t}=r;return e?cl[t].find(s=>(0,fl.satisfies)(e,s.semver)):cl[t].find(s=>s.default)}async function Hs(r){let{to:e,logger:t,options:s}=r,i=t!=null?t:new or(s),n=ro(e);if(!n)throw new R("Unsupported package manager version.",{type:"package_manager-unsupported_version"});if(i.subStep(`running "${n.command} ${n.installArgs.join(" ")}"`),!(s!=null&&s.dry)){let o;i.interactive&&(o=(0,hl.default)({text:"installing dependencies...",spinner:{frames:i.installerFrames()}}).start());try{await(0,ll.default)(n.command,n.installArgs,{cwd:r.project.paths.root}),o&&o.stop(),i.subStep("dependencies installed")}catch(a){throw i.subStepFailure("failed to install dependencies"),a}}}async function ml({project:r,convertTo:e,logger:t,options:s}){if(t.header(`Converting project from ${r.packageManager} to ${e.name}.`),r.packageManager===e.name)throw new R("You are already using this package manager",{type:"package_manager-already_in_use"});if(!e.version)throw new R(`${e.name} is not installed, or could not be located`,{type:"package_manager-could_not_be_found"});let i=e;await We[r.packageManager].remove({project:r,to:i,logger:t,options:s}),await We[i.name].create({project:r,to:i,logger:t,options:s}),t.mainStep("Installing dependencies"),s!=null&&s.skipInstall?t.subStep(pl.default.yellow("Skipping install")):(await We[i.name].convertLock({project:r,to:i,logger:t,options:s}),await Hs({project:r,to:i,logger:t,options:s})),t.mainStep(`Cleaning up ${r.packageManager} workspaces`),await We[r.packageManager].clean({project:r,logger:t})}async function dm({root:r,to:e,options:t}){let s=new or({...t,interactive:!1}),[i,n]=await Promise.all([to({root:r}),fi()]);await ml({project:i,convertTo:{name:e,version:n[e]},logger:s,options:t})}0&&(module.exports={ConvertError,MANAGERS,convert,getPackageManagerMeta,getWorkspaceDetails,install});
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
