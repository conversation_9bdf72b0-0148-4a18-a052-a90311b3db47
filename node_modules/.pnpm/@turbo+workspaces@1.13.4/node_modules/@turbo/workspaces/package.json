{"name": "@turbo/workspaces", "version": "1.13.4", "description": "Tools for working with package managers", "homepage": "https://turbo.build/repo", "license": "MPL-2.0", "repository": {"type": "git", "url": "https://github.com/vercel/turbo", "directory": "packages/turbo-workspaces"}, "bugs": {"url": "https://github.com/vercel/turbo/issues"}, "bin": "dist/cli.js", "module": "dist/index.mjs", "main": "dist/index.js", "types": "dist/index.d.ts", "dependencies": {"chalk": "2.4.2", "commander": "^10.0.0", "execa": "5.1.1", "fast-glob": "^3.2.12", "fs-extra": "^10.1.0", "gradient-string": "^2.0.0", "inquirer": "^8.0.0", "js-yaml": "^4.1.0", "ora": "4.1.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "update-check": "^1.5.4"}, "devDependencies": {"@types/chalk-animation": "^1.6.0", "@types/fs-extra": "^9.0.13", "@types/gradient-string": "^1.1.2", "@types/inquirer": "^7.3.1", "@types/jest": "^27.4.0", "@types/js-yaml": "^4.0.5", "@types/node": "^18.17.2", "@types/rimraf": "^3.0.2", "@types/semver": "^7.3.9", "jest": "^27.4.3", "semver": "^7.3.5", "strip-ansi": "^6.0.1", "ts-jest": "^27.1.1", "tsup": "^5.10.3", "typescript": "5.3.3", "@turbo/eslint-config": "0.0.0", "@turbo/test-utils": "0.0.0", "@turbo/tsconfig": "0.0.0", "@turbo/utils": "0.0.0"}, "files": ["dist"], "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "jest", "lint": "eslint src/", "check-types": "tsc --noEmit", "lint:prettier": "prettier -c . --cache --ignore-path=../../.prettierignore"}}