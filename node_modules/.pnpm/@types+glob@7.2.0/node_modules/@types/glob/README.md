# Installation
> `npm install --save @types/glob`

# Summary
This package contains type definitions for Glob (https://github.com/isaacs/node-glob).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/glob.

### Additional Details
 * Last updated: <PERSON><PERSON>, 19 Oct 2021 22:01:22 GMT
 * Dependencies: [@types/minimatch](https://npmjs.com/package/@types/minimatch), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [vvakame](https://github.com/vvakame), [voy](https://github.com/voy), [<PERSON>](https://github.com/ajafff), and [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>).
