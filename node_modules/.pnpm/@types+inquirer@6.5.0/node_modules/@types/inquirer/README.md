# Installation
> `npm install --save @types/inquirer`

# Summary
This package contains type definitions for inquirer (https://github.com/SBoudrias/Inquirer.js).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/inquirer

Additional Details
 * Last updated: Wed, 31 Jul 2019 17:22:21 GMT
 * Dependencies: @types/rxjs, @types/through
 * Global values: none

# Credits
These definitions were written by <PERSON><PERSON><PERSON> <https://github.com/tkQubo>, <PERSON><PERSON><PERSON> <https://github.com/ppathan>, <PERSON><PERSON><PERSON> <https://github.com/jouderianjr>, <PERSON><PERSON> <https://github.com/bang88>, <PERSON> <https://github.com/bitjson>, Synarque <https://github.com/synarque>, <PERSON> <https://github.com/jrockwood>, <PERSON> <https://github.com/kwkelly>, <PERSON> <https://github.com/chigix>, <PERSON> <https://github.com/jedmao>, and <PERSON> <https://github.com/manuth>.
