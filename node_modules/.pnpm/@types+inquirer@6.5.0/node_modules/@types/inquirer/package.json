{"name": "@types/inquirer", "version": "6.5.0", "description": "TypeScript definitions for inquirer", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/tkQubo", "githubUsername": "tkQubo"}, {"name": "Pa<PERSON><PERSON>", "url": "https://github.com/ppathan", "githubUsername": "ppathan"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/jouderianjr", "githubUsername": "jouderianjr"}, {"name": "Qibang", "url": "https://github.com/bang88", "githubUsername": "bang88"}, {"name": "<PERSON>", "url": "https://github.com/bitjson", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "Synarque", "url": "https://github.com/synarque", "githubUsername": "synarque"}, {"name": "<PERSON>", "url": "https://github.com/jrockwood", "githubUsername": "jrockwood"}, {"name": "<PERSON>", "url": "https://github.com/kwkelly", "githubUsername": "kwkelly"}, {"name": "<PERSON>", "url": "https://github.com/chigix", "githubUsername": "chigix"}, {"name": "<PERSON>", "url": "https://github.com/jedmao", "githubUsername": "jed<PERSON>o"}, {"name": "<PERSON>", "url": "https://github.com/manuth", "githubUsername": "manuth"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/inquirer"}, "scripts": {}, "dependencies": {"@types/through": "*", "rxjs": "^6.4.0"}, "typesPublisherContentHash": "3ad7bbf17647a468e5db6e137a5e3edcc026c56aa33f9421315fcb94ca58cf0a", "typeScriptVersion": "3.3"}