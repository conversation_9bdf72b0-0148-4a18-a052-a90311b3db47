{"name": "camel-case", "version": "3.0.0", "description": "Camel case a string", "main": "camel-case.js", "typings": "camel-case.d.ts", "files": ["camel-case.js", "camel-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-spec": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/camel-case.git"}, "keywords": ["camel", "case", "camelcase", "camel-case", "dash", "hyphen", "dot", "underscore", "lodash", "separator", "string", "text", "convert"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/camel-case/issues"}, "homepage": "https://github.com/blakeembrey/camel-case", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^2.2.1", "standard": "^7.1.2"}, "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}