{"name": "change-case", "version": "3.1.0", "description": "Convert a string between camelCase, PascalCase, Title Case, snake_case and more.", "main": "change-case.js", "typings": "change-case.d.ts", "files": ["change-case.js", "change-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": "https://github.com/blakeembrey/change-case", "keywords": ["camel", "pascal", "title", "case", "lower", "upper", "param", "kebab", "hyphen", "dot", "path", "constant", "cases", "check"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^5.0.0", "standard": "^12.0.1"}, "dependencies": {"camel-case": "^3.0.0", "constant-case": "^2.0.0", "dot-case": "^2.1.0", "header-case": "^1.0.0", "is-lower-case": "^1.1.0", "is-upper-case": "^1.1.0", "lower-case": "^1.1.1", "lower-case-first": "^1.0.0", "no-case": "^2.3.2", "param-case": "^2.1.0", "pascal-case": "^2.0.0", "path-case": "^2.1.0", "sentence-case": "^2.1.0", "snake-case": "^2.1.0", "swap-case": "^1.1.0", "title-case": "^2.1.0", "upper-case": "^1.1.1", "upper-case-first": "^1.1.0"}}